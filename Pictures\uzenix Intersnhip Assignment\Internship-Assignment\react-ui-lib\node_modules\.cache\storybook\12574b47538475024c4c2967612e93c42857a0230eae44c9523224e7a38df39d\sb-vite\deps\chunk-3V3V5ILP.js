import {
  require_react
} from "./chunk-AZZHOME7.js";
import {
  require_preview_api
} from "./chunk-ZLWVYHQP.js";
import {
  __toESM
} from "./chunk-7D4SUZUM.js";

// node_modules/@storybook/react/dist/chunk-XLZBPYSH.mjs
var import_react = __toESM(require_react(), 1);
var import_preview_api = __toESM(require_preview_api(), 1);
var applyDecorators = (storyFn, decorators) => (0, import_preview_api.defaultDecorateStory)((context) => import_react.default.createElement(storyFn, context), decorators);

export {
  applyDecorators
};
//# sourceMappingURL=chunk-3V3V5ILP.js.map
