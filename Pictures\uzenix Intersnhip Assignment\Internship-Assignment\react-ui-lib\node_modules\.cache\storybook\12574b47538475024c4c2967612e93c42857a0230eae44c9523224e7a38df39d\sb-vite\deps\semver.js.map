{"version": 3, "sources": ["../../../../../semver/semver.js"], "sourcesContent": ["exports = module.exports = SemVer\n\nvar debug\n/* istanbul ignore next */\nif (typeof process === 'object' &&\n    process.env &&\n    process.env.NODE_DEBUG &&\n    /\\bsemver\\b/i.test(process.env.NODE_DEBUG)) {\n  debug = function () {\n    var args = Array.prototype.slice.call(arguments, 0)\n    args.unshift('SEMVER')\n    console.log.apply(console, args)\n  }\n} else {\n  debug = function () {}\n}\n\n// Note: this is the semver.org version of the spec that it implements\n// Not necessarily the package version of this code.\nexports.SEMVER_SPEC_VERSION = '2.0.0'\n\nvar MAX_LENGTH = 256\nvar MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER ||\n  /* istanbul ignore next */ 9007199254740991\n\n// Max safe segment length for coercion.\nvar MAX_SAFE_COMPONENT_LENGTH = 16\n\nvar MAX_SAFE_BUILD_LENGTH = MAX_LENGTH - 6\n\n// The actual regexps go on exports.re\nvar re = exports.re = []\nvar safeRe = exports.safeRe = []\nvar src = exports.src = []\nvar t = exports.tokens = {}\nvar R = 0\n\nfunction tok (n) {\n  t[n] = R++\n}\n\nvar LETTERDASHNUMBER = '[a-zA-Z0-9-]'\n\n// Replace some greedy regex tokens to prevent regex dos issues. These regex are\n// used internally via the safeRe object since all inputs in this library get\n// normalized first to trim and collapse all extra whitespace. The original\n// regexes are exported for userland consumption and lower level usage. A\n// future breaking change could export the safer regex only with a note that\n// all input should have extra whitespace removed.\nvar safeRegexReplacements = [\n  ['\\\\s', 1],\n  ['\\\\d', MAX_LENGTH],\n  [LETTERDASHNUMBER, MAX_SAFE_BUILD_LENGTH],\n]\n\nfunction makeSafeRe (value) {\n  for (var i = 0; i < safeRegexReplacements.length; i++) {\n    var token = safeRegexReplacements[i][0]\n    var max = safeRegexReplacements[i][1]\n    value = value\n      .split(token + '*').join(token + '{0,' + max + '}')\n      .split(token + '+').join(token + '{1,' + max + '}')\n  }\n  return value\n}\n\n// The following Regular Expressions can be used for tokenizing,\n// validating, and parsing SemVer version strings.\n\n// ## Numeric Identifier\n// A single `0`, or a non-zero digit followed by zero or more digits.\n\ntok('NUMERICIDENTIFIER')\nsrc[t.NUMERICIDENTIFIER] = '0|[1-9]\\\\d*'\ntok('NUMERICIDENTIFIERLOOSE')\nsrc[t.NUMERICIDENTIFIERLOOSE] = '\\\\d+'\n\n// ## Non-numeric Identifier\n// Zero or more digits, followed by a letter or hyphen, and then zero or\n// more letters, digits, or hyphens.\n\ntok('NONNUMERICIDENTIFIER')\nsrc[t.NONNUMERICIDENTIFIER] = '\\\\d*[a-zA-Z-]' + LETTERDASHNUMBER + '*'\n\n// ## Main Version\n// Three dot-separated numeric identifiers.\n\ntok('MAINVERSION')\nsrc[t.MAINVERSION] = '(' + src[t.NUMERICIDENTIFIER] + ')\\\\.' +\n                   '(' + src[t.NUMERICIDENTIFIER] + ')\\\\.' +\n                   '(' + src[t.NUMERICIDENTIFIER] + ')'\n\ntok('MAINVERSIONLOOSE')\nsrc[t.MAINVERSIONLOOSE] = '(' + src[t.NUMERICIDENTIFIERLOOSE] + ')\\\\.' +\n                        '(' + src[t.NUMERICIDENTIFIERLOOSE] + ')\\\\.' +\n                        '(' + src[t.NUMERICIDENTIFIERLOOSE] + ')'\n\n// ## Pre-release Version Identifier\n// A numeric identifier, or a non-numeric identifier.\n\ntok('PRERELEASEIDENTIFIER')\nsrc[t.PRERELEASEIDENTIFIER] = '(?:' + src[t.NUMERICIDENTIFIER] +\n                            '|' + src[t.NONNUMERICIDENTIFIER] + ')'\n\ntok('PRERELEASEIDENTIFIERLOOSE')\nsrc[t.PRERELEASEIDENTIFIERLOOSE] = '(?:' + src[t.NUMERICIDENTIFIERLOOSE] +\n                                 '|' + src[t.NONNUMERICIDENTIFIER] + ')'\n\n// ## Pre-release Version\n// Hyphen, followed by one or more dot-separated pre-release version\n// identifiers.\n\ntok('PRERELEASE')\nsrc[t.PRERELEASE] = '(?:-(' + src[t.PRERELEASEIDENTIFIER] +\n                  '(?:\\\\.' + src[t.PRERELEASEIDENTIFIER] + ')*))'\n\ntok('PRERELEASELOOSE')\nsrc[t.PRERELEASELOOSE] = '(?:-?(' + src[t.PRERELEASEIDENTIFIERLOOSE] +\n                       '(?:\\\\.' + src[t.PRERELEASEIDENTIFIERLOOSE] + ')*))'\n\n// ## Build Metadata Identifier\n// Any combination of digits, letters, or hyphens.\n\ntok('BUILDIDENTIFIER')\nsrc[t.BUILDIDENTIFIER] = LETTERDASHNUMBER + '+'\n\n// ## Build Metadata\n// Plus sign, followed by one or more period-separated build metadata\n// identifiers.\n\ntok('BUILD')\nsrc[t.BUILD] = '(?:\\\\+(' + src[t.BUILDIDENTIFIER] +\n             '(?:\\\\.' + src[t.BUILDIDENTIFIER] + ')*))'\n\n// ## Full Version String\n// A main version, followed optionally by a pre-release version and\n// build metadata.\n\n// Note that the only major, minor, patch, and pre-release sections of\n// the version string are capturing groups.  The build metadata is not a\n// capturing group, because it should not ever be used in version\n// comparison.\n\ntok('FULL')\ntok('FULLPLAIN')\nsrc[t.FULLPLAIN] = 'v?' + src[t.MAINVERSION] +\n                  src[t.PRERELEASE] + '?' +\n                  src[t.BUILD] + '?'\n\nsrc[t.FULL] = '^' + src[t.FULLPLAIN] + '$'\n\n// like full, but allows v1.2.3 and =1.2.3, which people do sometimes.\n// also, 1.0.0alpha1 (prerelease without the hyphen) which is pretty\n// common in the npm registry.\ntok('LOOSEPLAIN')\nsrc[t.LOOSEPLAIN] = '[v=\\\\s]*' + src[t.MAINVERSIONLOOSE] +\n                  src[t.PRERELEASELOOSE] + '?' +\n                  src[t.BUILD] + '?'\n\ntok('LOOSE')\nsrc[t.LOOSE] = '^' + src[t.LOOSEPLAIN] + '$'\n\ntok('GTLT')\nsrc[t.GTLT] = '((?:<|>)?=?)'\n\n// Something like \"2.*\" or \"1.2.x\".\n// Note that \"x.x\" is a valid xRange identifer, meaning \"any version\"\n// Only the first item is strictly required.\ntok('XRANGEIDENTIFIERLOOSE')\nsrc[t.XRANGEIDENTIFIERLOOSE] = src[t.NUMERICIDENTIFIERLOOSE] + '|x|X|\\\\*'\ntok('XRANGEIDENTIFIER')\nsrc[t.XRANGEIDENTIFIER] = src[t.NUMERICIDENTIFIER] + '|x|X|\\\\*'\n\ntok('XRANGEPLAIN')\nsrc[t.XRANGEPLAIN] = '[v=\\\\s]*(' + src[t.XRANGEIDENTIFIER] + ')' +\n                   '(?:\\\\.(' + src[t.XRANGEIDENTIFIER] + ')' +\n                   '(?:\\\\.(' + src[t.XRANGEIDENTIFIER] + ')' +\n                   '(?:' + src[t.PRERELEASE] + ')?' +\n                   src[t.BUILD] + '?' +\n                   ')?)?'\n\ntok('XRANGEPLAINLOOSE')\nsrc[t.XRANGEPLAINLOOSE] = '[v=\\\\s]*(' + src[t.XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:\\\\.(' + src[t.XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:\\\\.(' + src[t.XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:' + src[t.PRERELEASELOOSE] + ')?' +\n                        src[t.BUILD] + '?' +\n                        ')?)?'\n\ntok('XRANGE')\nsrc[t.XRANGE] = '^' + src[t.GTLT] + '\\\\s*' + src[t.XRANGEPLAIN] + '$'\ntok('XRANGELOOSE')\nsrc[t.XRANGELOOSE] = '^' + src[t.GTLT] + '\\\\s*' + src[t.XRANGEPLAINLOOSE] + '$'\n\n// Coercion.\n// Extract anything that could conceivably be a part of a valid semver\ntok('COERCE')\nsrc[t.COERCE] = '(^|[^\\\\d])' +\n              '(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '})' +\n              '(?:\\\\.(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '}))?' +\n              '(?:\\\\.(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '}))?' +\n              '(?:$|[^\\\\d])'\ntok('COERCERTL')\nre[t.COERCERTL] = new RegExp(src[t.COERCE], 'g')\nsafeRe[t.COERCERTL] = new RegExp(makeSafeRe(src[t.COERCE]), 'g')\n\n// Tilde ranges.\n// Meaning is \"reasonably at or greater than\"\ntok('LONETILDE')\nsrc[t.LONETILDE] = '(?:~>?)'\n\ntok('TILDETRIM')\nsrc[t.TILDETRIM] = '(\\\\s*)' + src[t.LONETILDE] + '\\\\s+'\nre[t.TILDETRIM] = new RegExp(src[t.TILDETRIM], 'g')\nsafeRe[t.TILDETRIM] = new RegExp(makeSafeRe(src[t.TILDETRIM]), 'g')\nvar tildeTrimReplace = '$1~'\n\ntok('TILDE')\nsrc[t.TILDE] = '^' + src[t.LONETILDE] + src[t.XRANGEPLAIN] + '$'\ntok('TILDELOOSE')\nsrc[t.TILDELOOSE] = '^' + src[t.LONETILDE] + src[t.XRANGEPLAINLOOSE] + '$'\n\n// Caret ranges.\n// Meaning is \"at least and backwards compatible with\"\ntok('LONECARET')\nsrc[t.LONECARET] = '(?:\\\\^)'\n\ntok('CARETTRIM')\nsrc[t.CARETTRIM] = '(\\\\s*)' + src[t.LONECARET] + '\\\\s+'\nre[t.CARETTRIM] = new RegExp(src[t.CARETTRIM], 'g')\nsafeRe[t.CARETTRIM] = new RegExp(makeSafeRe(src[t.CARETTRIM]), 'g')\nvar caretTrimReplace = '$1^'\n\ntok('CARET')\nsrc[t.CARET] = '^' + src[t.LONECARET] + src[t.XRANGEPLAIN] + '$'\ntok('CARETLOOSE')\nsrc[t.CARETLOOSE] = '^' + src[t.LONECARET] + src[t.XRANGEPLAINLOOSE] + '$'\n\n// A simple gt/lt/eq thing, or just \"\" to indicate \"any version\"\ntok('COMPARATORLOOSE')\nsrc[t.COMPARATORLOOSE] = '^' + src[t.GTLT] + '\\\\s*(' + src[t.LOOSEPLAIN] + ')$|^$'\ntok('COMPARATOR')\nsrc[t.COMPARATOR] = '^' + src[t.GTLT] + '\\\\s*(' + src[t.FULLPLAIN] + ')$|^$'\n\n// An expression to strip any whitespace between the gtlt and the thing\n// it modifies, so that `> 1.2.3` ==> `>1.2.3`\ntok('COMPARATORTRIM')\nsrc[t.COMPARATORTRIM] = '(\\\\s*)' + src[t.GTLT] +\n                      '\\\\s*(' + src[t.LOOSEPLAIN] + '|' + src[t.XRANGEPLAIN] + ')'\n\n// this one has to use the /g flag\nre[t.COMPARATORTRIM] = new RegExp(src[t.COMPARATORTRIM], 'g')\nsafeRe[t.COMPARATORTRIM] = new RegExp(makeSafeRe(src[t.COMPARATORTRIM]), 'g')\nvar comparatorTrimReplace = '$1$2$3'\n\n// Something like `1.2.3 - 1.2.4`\n// Note that these all use the loose form, because they'll be\n// checked against either the strict or loose comparator form\n// later.\ntok('HYPHENRANGE')\nsrc[t.HYPHENRANGE] = '^\\\\s*(' + src[t.XRANGEPLAIN] + ')' +\n                   '\\\\s+-\\\\s+' +\n                   '(' + src[t.XRANGEPLAIN] + ')' +\n                   '\\\\s*$'\n\ntok('HYPHENRANGELOOSE')\nsrc[t.HYPHENRANGELOOSE] = '^\\\\s*(' + src[t.XRANGEPLAINLOOSE] + ')' +\n                        '\\\\s+-\\\\s+' +\n                        '(' + src[t.XRANGEPLAINLOOSE] + ')' +\n                        '\\\\s*$'\n\n// Star ranges basically just allow anything at all.\ntok('STAR')\nsrc[t.STAR] = '(<|>)?=?\\\\s*\\\\*'\n\n// Compile to actual regexp objects.\n// All are flag-free, unless they were created above with a flag.\nfor (var i = 0; i < R; i++) {\n  debug(i, src[i])\n  if (!re[i]) {\n    re[i] = new RegExp(src[i])\n\n    // Replace all greedy whitespace to prevent regex dos issues. These regex are\n    // used internally via the safeRe object since all inputs in this library get\n    // normalized first to trim and collapse all extra whitespace. The original\n    // regexes are exported for userland consumption and lower level usage. A\n    // future breaking change could export the safer regex only with a note that\n    // all input should have extra whitespace removed.\n    safeRe[i] = new RegExp(makeSafeRe(src[i]))\n  }\n}\n\nexports.parse = parse\nfunction parse (version, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  if (version.length > MAX_LENGTH) {\n    return null\n  }\n\n  var r = options.loose ? safeRe[t.LOOSE] : safeRe[t.FULL]\n  if (!r.test(version)) {\n    return null\n  }\n\n  try {\n    return new SemVer(version, options)\n  } catch (er) {\n    return null\n  }\n}\n\nexports.valid = valid\nfunction valid (version, options) {\n  var v = parse(version, options)\n  return v ? v.version : null\n}\n\nexports.clean = clean\nfunction clean (version, options) {\n  var s = parse(version.trim().replace(/^[=v]+/, ''), options)\n  return s ? s.version : null\n}\n\nexports.SemVer = SemVer\n\nfunction SemVer (version, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n  if (version instanceof SemVer) {\n    if (version.loose === options.loose) {\n      return version\n    } else {\n      version = version.version\n    }\n  } else if (typeof version !== 'string') {\n    throw new TypeError('Invalid Version: ' + version)\n  }\n\n  if (version.length > MAX_LENGTH) {\n    throw new TypeError('version is longer than ' + MAX_LENGTH + ' characters')\n  }\n\n  if (!(this instanceof SemVer)) {\n    return new SemVer(version, options)\n  }\n\n  debug('SemVer', version, options)\n  this.options = options\n  this.loose = !!options.loose\n\n  var m = version.trim().match(options.loose ? safeRe[t.LOOSE] : safeRe[t.FULL])\n\n  if (!m) {\n    throw new TypeError('Invalid Version: ' + version)\n  }\n\n  this.raw = version\n\n  // these are actually numbers\n  this.major = +m[1]\n  this.minor = +m[2]\n  this.patch = +m[3]\n\n  if (this.major > MAX_SAFE_INTEGER || this.major < 0) {\n    throw new TypeError('Invalid major version')\n  }\n\n  if (this.minor > MAX_SAFE_INTEGER || this.minor < 0) {\n    throw new TypeError('Invalid minor version')\n  }\n\n  if (this.patch > MAX_SAFE_INTEGER || this.patch < 0) {\n    throw new TypeError('Invalid patch version')\n  }\n\n  // numberify any prerelease numeric ids\n  if (!m[4]) {\n    this.prerelease = []\n  } else {\n    this.prerelease = m[4].split('.').map(function (id) {\n      if (/^[0-9]+$/.test(id)) {\n        var num = +id\n        if (num >= 0 && num < MAX_SAFE_INTEGER) {\n          return num\n        }\n      }\n      return id\n    })\n  }\n\n  this.build = m[5] ? m[5].split('.') : []\n  this.format()\n}\n\nSemVer.prototype.format = function () {\n  this.version = this.major + '.' + this.minor + '.' + this.patch\n  if (this.prerelease.length) {\n    this.version += '-' + this.prerelease.join('.')\n  }\n  return this.version\n}\n\nSemVer.prototype.toString = function () {\n  return this.version\n}\n\nSemVer.prototype.compare = function (other) {\n  debug('SemVer.compare', this.version, this.options, other)\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  return this.compareMain(other) || this.comparePre(other)\n}\n\nSemVer.prototype.compareMain = function (other) {\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  return compareIdentifiers(this.major, other.major) ||\n         compareIdentifiers(this.minor, other.minor) ||\n         compareIdentifiers(this.patch, other.patch)\n}\n\nSemVer.prototype.comparePre = function (other) {\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  // NOT having a prerelease is > having one\n  if (this.prerelease.length && !other.prerelease.length) {\n    return -1\n  } else if (!this.prerelease.length && other.prerelease.length) {\n    return 1\n  } else if (!this.prerelease.length && !other.prerelease.length) {\n    return 0\n  }\n\n  var i = 0\n  do {\n    var a = this.prerelease[i]\n    var b = other.prerelease[i]\n    debug('prerelease compare', i, a, b)\n    if (a === undefined && b === undefined) {\n      return 0\n    } else if (b === undefined) {\n      return 1\n    } else if (a === undefined) {\n      return -1\n    } else if (a === b) {\n      continue\n    } else {\n      return compareIdentifiers(a, b)\n    }\n  } while (++i)\n}\n\nSemVer.prototype.compareBuild = function (other) {\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  var i = 0\n  do {\n    var a = this.build[i]\n    var b = other.build[i]\n    debug('prerelease compare', i, a, b)\n    if (a === undefined && b === undefined) {\n      return 0\n    } else if (b === undefined) {\n      return 1\n    } else if (a === undefined) {\n      return -1\n    } else if (a === b) {\n      continue\n    } else {\n      return compareIdentifiers(a, b)\n    }\n  } while (++i)\n}\n\n// preminor will bump the version up to the next minor release, and immediately\n// down to pre-release. premajor and prepatch work the same way.\nSemVer.prototype.inc = function (release, identifier) {\n  switch (release) {\n    case 'premajor':\n      this.prerelease.length = 0\n      this.patch = 0\n      this.minor = 0\n      this.major++\n      this.inc('pre', identifier)\n      break\n    case 'preminor':\n      this.prerelease.length = 0\n      this.patch = 0\n      this.minor++\n      this.inc('pre', identifier)\n      break\n    case 'prepatch':\n      // If this is already a prerelease, it will bump to the next version\n      // drop any prereleases that might already exist, since they are not\n      // relevant at this point.\n      this.prerelease.length = 0\n      this.inc('patch', identifier)\n      this.inc('pre', identifier)\n      break\n    // If the input is a non-prerelease version, this acts the same as\n    // prepatch.\n    case 'prerelease':\n      if (this.prerelease.length === 0) {\n        this.inc('patch', identifier)\n      }\n      this.inc('pre', identifier)\n      break\n\n    case 'major':\n      // If this is a pre-major version, bump up to the same major version.\n      // Otherwise increment major.\n      // 1.0.0-5 bumps to 1.0.0\n      // 1.1.0 bumps to 2.0.0\n      if (this.minor !== 0 ||\n          this.patch !== 0 ||\n          this.prerelease.length === 0) {\n        this.major++\n      }\n      this.minor = 0\n      this.patch = 0\n      this.prerelease = []\n      break\n    case 'minor':\n      // If this is a pre-minor version, bump up to the same minor version.\n      // Otherwise increment minor.\n      // 1.2.0-5 bumps to 1.2.0\n      // 1.2.1 bumps to 1.3.0\n      if (this.patch !== 0 || this.prerelease.length === 0) {\n        this.minor++\n      }\n      this.patch = 0\n      this.prerelease = []\n      break\n    case 'patch':\n      // If this is not a pre-release version, it will increment the patch.\n      // If it is a pre-release it will bump up to the same patch version.\n      // 1.2.0-5 patches to 1.2.0\n      // 1.2.0 patches to 1.2.1\n      if (this.prerelease.length === 0) {\n        this.patch++\n      }\n      this.prerelease = []\n      break\n    // This probably shouldn't be used publicly.\n    // 1.0.0 \"pre\" would become 1.0.0-0 which is the wrong direction.\n    case 'pre':\n      if (this.prerelease.length === 0) {\n        this.prerelease = [0]\n      } else {\n        var i = this.prerelease.length\n        while (--i >= 0) {\n          if (typeof this.prerelease[i] === 'number') {\n            this.prerelease[i]++\n            i = -2\n          }\n        }\n        if (i === -1) {\n          // didn't increment anything\n          this.prerelease.push(0)\n        }\n      }\n      if (identifier) {\n        // 1.2.0-beta.1 bumps to 1.2.0-beta.2,\n        // 1.2.0-beta.fooblz or 1.2.0-beta bumps to 1.2.0-beta.0\n        if (this.prerelease[0] === identifier) {\n          if (isNaN(this.prerelease[1])) {\n            this.prerelease = [identifier, 0]\n          }\n        } else {\n          this.prerelease = [identifier, 0]\n        }\n      }\n      break\n\n    default:\n      throw new Error('invalid increment argument: ' + release)\n  }\n  this.format()\n  this.raw = this.version\n  return this\n}\n\nexports.inc = inc\nfunction inc (version, release, loose, identifier) {\n  if (typeof (loose) === 'string') {\n    identifier = loose\n    loose = undefined\n  }\n\n  try {\n    return new SemVer(version, loose).inc(release, identifier).version\n  } catch (er) {\n    return null\n  }\n}\n\nexports.diff = diff\nfunction diff (version1, version2) {\n  if (eq(version1, version2)) {\n    return null\n  } else {\n    var v1 = parse(version1)\n    var v2 = parse(version2)\n    var prefix = ''\n    if (v1.prerelease.length || v2.prerelease.length) {\n      prefix = 'pre'\n      var defaultResult = 'prerelease'\n    }\n    for (var key in v1) {\n      if (key === 'major' || key === 'minor' || key === 'patch') {\n        if (v1[key] !== v2[key]) {\n          return prefix + key\n        }\n      }\n    }\n    return defaultResult // may be undefined\n  }\n}\n\nexports.compareIdentifiers = compareIdentifiers\n\nvar numeric = /^[0-9]+$/\nfunction compareIdentifiers (a, b) {\n  var anum = numeric.test(a)\n  var bnum = numeric.test(b)\n\n  if (anum && bnum) {\n    a = +a\n    b = +b\n  }\n\n  return a === b ? 0\n    : (anum && !bnum) ? -1\n    : (bnum && !anum) ? 1\n    : a < b ? -1\n    : 1\n}\n\nexports.rcompareIdentifiers = rcompareIdentifiers\nfunction rcompareIdentifiers (a, b) {\n  return compareIdentifiers(b, a)\n}\n\nexports.major = major\nfunction major (a, loose) {\n  return new SemVer(a, loose).major\n}\n\nexports.minor = minor\nfunction minor (a, loose) {\n  return new SemVer(a, loose).minor\n}\n\nexports.patch = patch\nfunction patch (a, loose) {\n  return new SemVer(a, loose).patch\n}\n\nexports.compare = compare\nfunction compare (a, b, loose) {\n  return new SemVer(a, loose).compare(new SemVer(b, loose))\n}\n\nexports.compareLoose = compareLoose\nfunction compareLoose (a, b) {\n  return compare(a, b, true)\n}\n\nexports.compareBuild = compareBuild\nfunction compareBuild (a, b, loose) {\n  var versionA = new SemVer(a, loose)\n  var versionB = new SemVer(b, loose)\n  return versionA.compare(versionB) || versionA.compareBuild(versionB)\n}\n\nexports.rcompare = rcompare\nfunction rcompare (a, b, loose) {\n  return compare(b, a, loose)\n}\n\nexports.sort = sort\nfunction sort (list, loose) {\n  return list.sort(function (a, b) {\n    return exports.compareBuild(a, b, loose)\n  })\n}\n\nexports.rsort = rsort\nfunction rsort (list, loose) {\n  return list.sort(function (a, b) {\n    return exports.compareBuild(b, a, loose)\n  })\n}\n\nexports.gt = gt\nfunction gt (a, b, loose) {\n  return compare(a, b, loose) > 0\n}\n\nexports.lt = lt\nfunction lt (a, b, loose) {\n  return compare(a, b, loose) < 0\n}\n\nexports.eq = eq\nfunction eq (a, b, loose) {\n  return compare(a, b, loose) === 0\n}\n\nexports.neq = neq\nfunction neq (a, b, loose) {\n  return compare(a, b, loose) !== 0\n}\n\nexports.gte = gte\nfunction gte (a, b, loose) {\n  return compare(a, b, loose) >= 0\n}\n\nexports.lte = lte\nfunction lte (a, b, loose) {\n  return compare(a, b, loose) <= 0\n}\n\nexports.cmp = cmp\nfunction cmp (a, op, b, loose) {\n  switch (op) {\n    case '===':\n      if (typeof a === 'object')\n        a = a.version\n      if (typeof b === 'object')\n        b = b.version\n      return a === b\n\n    case '!==':\n      if (typeof a === 'object')\n        a = a.version\n      if (typeof b === 'object')\n        b = b.version\n      return a !== b\n\n    case '':\n    case '=':\n    case '==':\n      return eq(a, b, loose)\n\n    case '!=':\n      return neq(a, b, loose)\n\n    case '>':\n      return gt(a, b, loose)\n\n    case '>=':\n      return gte(a, b, loose)\n\n    case '<':\n      return lt(a, b, loose)\n\n    case '<=':\n      return lte(a, b, loose)\n\n    default:\n      throw new TypeError('Invalid operator: ' + op)\n  }\n}\n\nexports.Comparator = Comparator\nfunction Comparator (comp, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (comp instanceof Comparator) {\n    if (comp.loose === !!options.loose) {\n      return comp\n    } else {\n      comp = comp.value\n    }\n  }\n\n  if (!(this instanceof Comparator)) {\n    return new Comparator(comp, options)\n  }\n\n  comp = comp.trim().split(/\\s+/).join(' ')\n  debug('comparator', comp, options)\n  this.options = options\n  this.loose = !!options.loose\n  this.parse(comp)\n\n  if (this.semver === ANY) {\n    this.value = ''\n  } else {\n    this.value = this.operator + this.semver.version\n  }\n\n  debug('comp', this)\n}\n\nvar ANY = {}\nComparator.prototype.parse = function (comp) {\n  var r = this.options.loose ? safeRe[t.COMPARATORLOOSE] : safeRe[t.COMPARATOR]\n  var m = comp.match(r)\n\n  if (!m) {\n    throw new TypeError('Invalid comparator: ' + comp)\n  }\n\n  this.operator = m[1] !== undefined ? m[1] : ''\n  if (this.operator === '=') {\n    this.operator = ''\n  }\n\n  // if it literally is just '>' or '' then allow anything.\n  if (!m[2]) {\n    this.semver = ANY\n  } else {\n    this.semver = new SemVer(m[2], this.options.loose)\n  }\n}\n\nComparator.prototype.toString = function () {\n  return this.value\n}\n\nComparator.prototype.test = function (version) {\n  debug('Comparator.test', version, this.options.loose)\n\n  if (this.semver === ANY || version === ANY) {\n    return true\n  }\n\n  if (typeof version === 'string') {\n    try {\n      version = new SemVer(version, this.options)\n    } catch (er) {\n      return false\n    }\n  }\n\n  return cmp(version, this.operator, this.semver, this.options)\n}\n\nComparator.prototype.intersects = function (comp, options) {\n  if (!(comp instanceof Comparator)) {\n    throw new TypeError('a Comparator is required')\n  }\n\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  var rangeTmp\n\n  if (this.operator === '') {\n    if (this.value === '') {\n      return true\n    }\n    rangeTmp = new Range(comp.value, options)\n    return satisfies(this.value, rangeTmp, options)\n  } else if (comp.operator === '') {\n    if (comp.value === '') {\n      return true\n    }\n    rangeTmp = new Range(this.value, options)\n    return satisfies(comp.semver, rangeTmp, options)\n  }\n\n  var sameDirectionIncreasing =\n    (this.operator === '>=' || this.operator === '>') &&\n    (comp.operator === '>=' || comp.operator === '>')\n  var sameDirectionDecreasing =\n    (this.operator === '<=' || this.operator === '<') &&\n    (comp.operator === '<=' || comp.operator === '<')\n  var sameSemVer = this.semver.version === comp.semver.version\n  var differentDirectionsInclusive =\n    (this.operator === '>=' || this.operator === '<=') &&\n    (comp.operator === '>=' || comp.operator === '<=')\n  var oppositeDirectionsLessThan =\n    cmp(this.semver, '<', comp.semver, options) &&\n    ((this.operator === '>=' || this.operator === '>') &&\n    (comp.operator === '<=' || comp.operator === '<'))\n  var oppositeDirectionsGreaterThan =\n    cmp(this.semver, '>', comp.semver, options) &&\n    ((this.operator === '<=' || this.operator === '<') &&\n    (comp.operator === '>=' || comp.operator === '>'))\n\n  return sameDirectionIncreasing || sameDirectionDecreasing ||\n    (sameSemVer && differentDirectionsInclusive) ||\n    oppositeDirectionsLessThan || oppositeDirectionsGreaterThan\n}\n\nexports.Range = Range\nfunction Range (range, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (range instanceof Range) {\n    if (range.loose === !!options.loose &&\n        range.includePrerelease === !!options.includePrerelease) {\n      return range\n    } else {\n      return new Range(range.raw, options)\n    }\n  }\n\n  if (range instanceof Comparator) {\n    return new Range(range.value, options)\n  }\n\n  if (!(this instanceof Range)) {\n    return new Range(range, options)\n  }\n\n  this.options = options\n  this.loose = !!options.loose\n  this.includePrerelease = !!options.includePrerelease\n\n  // First reduce all whitespace as much as possible so we do not have to rely\n  // on potentially slow regexes like \\s*. This is then stored and used for\n  // future error messages as well.\n  this.raw = range\n    .trim()\n    .split(/\\s+/)\n    .join(' ')\n\n  // First, split based on boolean or ||\n  this.set = this.raw.split('||').map(function (range) {\n    return this.parseRange(range.trim())\n  }, this).filter(function (c) {\n    // throw out any that are not relevant for whatever reason\n    return c.length\n  })\n\n  if (!this.set.length) {\n    throw new TypeError('Invalid SemVer Range: ' + this.raw)\n  }\n\n  this.format()\n}\n\nRange.prototype.format = function () {\n  this.range = this.set.map(function (comps) {\n    return comps.join(' ').trim()\n  }).join('||').trim()\n  return this.range\n}\n\nRange.prototype.toString = function () {\n  return this.range\n}\n\nRange.prototype.parseRange = function (range) {\n  var loose = this.options.loose\n  // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n  var hr = loose ? safeRe[t.HYPHENRANGELOOSE] : safeRe[t.HYPHENRANGE]\n  range = range.replace(hr, hyphenReplace)\n  debug('hyphen replace', range)\n  // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n  range = range.replace(safeRe[t.COMPARATORTRIM], comparatorTrimReplace)\n  debug('comparator trim', range, safeRe[t.COMPARATORTRIM])\n\n  // `~ 1.2.3` => `~1.2.3`\n  range = range.replace(safeRe[t.TILDETRIM], tildeTrimReplace)\n\n  // `^ 1.2.3` => `^1.2.3`\n  range = range.replace(safeRe[t.CARETTRIM], caretTrimReplace)\n\n  // normalize spaces\n  range = range.split(/\\s+/).join(' ')\n\n  // At this point, the range is completely trimmed and\n  // ready to be split into comparators.\n\n  var compRe = loose ? safeRe[t.COMPARATORLOOSE] : safeRe[t.COMPARATOR]\n  var set = range.split(' ').map(function (comp) {\n    return parseComparator(comp, this.options)\n  }, this).join(' ').split(/\\s+/)\n  if (this.options.loose) {\n    // in loose mode, throw out any that are not valid comparators\n    set = set.filter(function (comp) {\n      return !!comp.match(compRe)\n    })\n  }\n  set = set.map(function (comp) {\n    return new Comparator(comp, this.options)\n  }, this)\n\n  return set\n}\n\nRange.prototype.intersects = function (range, options) {\n  if (!(range instanceof Range)) {\n    throw new TypeError('a Range is required')\n  }\n\n  return this.set.some(function (thisComparators) {\n    return (\n      isSatisfiable(thisComparators, options) &&\n      range.set.some(function (rangeComparators) {\n        return (\n          isSatisfiable(rangeComparators, options) &&\n          thisComparators.every(function (thisComparator) {\n            return rangeComparators.every(function (rangeComparator) {\n              return thisComparator.intersects(rangeComparator, options)\n            })\n          })\n        )\n      })\n    )\n  })\n}\n\n// take a set of comparators and determine whether there\n// exists a version which can satisfy it\nfunction isSatisfiable (comparators, options) {\n  var result = true\n  var remainingComparators = comparators.slice()\n  var testComparator = remainingComparators.pop()\n\n  while (result && remainingComparators.length) {\n    result = remainingComparators.every(function (otherComparator) {\n      return testComparator.intersects(otherComparator, options)\n    })\n\n    testComparator = remainingComparators.pop()\n  }\n\n  return result\n}\n\n// Mostly just for testing and legacy API reasons\nexports.toComparators = toComparators\nfunction toComparators (range, options) {\n  return new Range(range, options).set.map(function (comp) {\n    return comp.map(function (c) {\n      return c.value\n    }).join(' ').trim().split(' ')\n  })\n}\n\n// comprised of xranges, tildes, stars, and gtlt's at this point.\n// already replaced the hyphen ranges\n// turn into a set of JUST comparators.\nfunction parseComparator (comp, options) {\n  debug('comp', comp, options)\n  comp = replaceCarets(comp, options)\n  debug('caret', comp)\n  comp = replaceTildes(comp, options)\n  debug('tildes', comp)\n  comp = replaceXRanges(comp, options)\n  debug('xrange', comp)\n  comp = replaceStars(comp, options)\n  debug('stars', comp)\n  return comp\n}\n\nfunction isX (id) {\n  return !id || id.toLowerCase() === 'x' || id === '*'\n}\n\n// ~, ~> --> * (any, kinda silly)\n// ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0\n// ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0\n// ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0\n// ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0\n// ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0\nfunction replaceTildes (comp, options) {\n  return comp.trim().split(/\\s+/).map(function (comp) {\n    return replaceTilde(comp, options)\n  }).join(' ')\n}\n\nfunction replaceTilde (comp, options) {\n  var r = options.loose ? safeRe[t.TILDELOOSE] : safeRe[t.TILDE]\n  return comp.replace(r, function (_, M, m, p, pr) {\n    debug('tilde', comp, _, M, m, p, pr)\n    var ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (isX(p)) {\n      // ~1.2 == >=1.2.0 <1.3.0\n      ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n    } else if (pr) {\n      debug('replaceTilde pr', pr)\n      ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n            ' <' + M + '.' + (+m + 1) + '.0'\n    } else {\n      // ~1.2.3 == >=1.2.3 <1.3.0\n      ret = '>=' + M + '.' + m + '.' + p +\n            ' <' + M + '.' + (+m + 1) + '.0'\n    }\n\n    debug('tilde return', ret)\n    return ret\n  })\n}\n\n// ^ --> * (any, kinda silly)\n// ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0\n// ^2.0, ^2.0.x --> >=2.0.0 <3.0.0\n// ^1.2, ^1.2.x --> >=1.2.0 <2.0.0\n// ^1.2.3 --> >=1.2.3 <2.0.0\n// ^1.2.0 --> >=1.2.0 <2.0.0\nfunction replaceCarets (comp, options) {\n  return comp.trim().split(/\\s+/).map(function (comp) {\n    return replaceCaret(comp, options)\n  }).join(' ')\n}\n\nfunction replaceCaret (comp, options) {\n  debug('caret', comp, options)\n  var r = options.loose ? safeRe[t.CARETLOOSE] : safeRe[t.CARET]\n  return comp.replace(r, function (_, M, m, p, pr) {\n    debug('caret', comp, _, M, m, p, pr)\n    var ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (isX(p)) {\n      if (M === '0') {\n        ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n      } else {\n        ret = '>=' + M + '.' + m + '.0 <' + (+M + 1) + '.0.0'\n      }\n    } else if (pr) {\n      debug('replaceCaret pr', pr)\n      if (M === '0') {\n        if (m === '0') {\n          ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n                ' <' + M + '.' + m + '.' + (+p + 1)\n        } else {\n          ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n                ' <' + M + '.' + (+m + 1) + '.0'\n        }\n      } else {\n        ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n              ' <' + (+M + 1) + '.0.0'\n      }\n    } else {\n      debug('no pr')\n      if (M === '0') {\n        if (m === '0') {\n          ret = '>=' + M + '.' + m + '.' + p +\n                ' <' + M + '.' + m + '.' + (+p + 1)\n        } else {\n          ret = '>=' + M + '.' + m + '.' + p +\n                ' <' + M + '.' + (+m + 1) + '.0'\n        }\n      } else {\n        ret = '>=' + M + '.' + m + '.' + p +\n              ' <' + (+M + 1) + '.0.0'\n      }\n    }\n\n    debug('caret return', ret)\n    return ret\n  })\n}\n\nfunction replaceXRanges (comp, options) {\n  debug('replaceXRanges', comp, options)\n  return comp.split(/\\s+/).map(function (comp) {\n    return replaceXRange(comp, options)\n  }).join(' ')\n}\n\nfunction replaceXRange (comp, options) {\n  comp = comp.trim()\n  var r = options.loose ? safeRe[t.XRANGELOOSE] : safeRe[t.XRANGE]\n  return comp.replace(r, function (ret, gtlt, M, m, p, pr) {\n    debug('xRange', comp, ret, gtlt, M, m, p, pr)\n    var xM = isX(M)\n    var xm = xM || isX(m)\n    var xp = xm || isX(p)\n    var anyX = xp\n\n    if (gtlt === '=' && anyX) {\n      gtlt = ''\n    }\n\n    // if we're including prereleases in the match, then we need\n    // to fix this to -0, the lowest possible prerelease value\n    pr = options.includePrerelease ? '-0' : ''\n\n    if (xM) {\n      if (gtlt === '>' || gtlt === '<') {\n        // nothing is allowed\n        ret = '<0.0.0-0'\n      } else {\n        // nothing is forbidden\n        ret = '*'\n      }\n    } else if (gtlt && anyX) {\n      // we know patch is an x, because we have any x at all.\n      // replace X with 0\n      if (xm) {\n        m = 0\n      }\n      p = 0\n\n      if (gtlt === '>') {\n        // >1 => >=2.0.0\n        // >1.2 => >=1.3.0\n        // >1.2.3 => >= 1.2.4\n        gtlt = '>='\n        if (xm) {\n          M = +M + 1\n          m = 0\n          p = 0\n        } else {\n          m = +m + 1\n          p = 0\n        }\n      } else if (gtlt === '<=') {\n        // <=0.7.x is actually <0.8.0, since any 0.7.x should\n        // pass.  Similarly, <=7.x is actually <8.0.0, etc.\n        gtlt = '<'\n        if (xm) {\n          M = +M + 1\n        } else {\n          m = +m + 1\n        }\n      }\n\n      ret = gtlt + M + '.' + m + '.' + p + pr\n    } else if (xm) {\n      ret = '>=' + M + '.0.0' + pr + ' <' + (+M + 1) + '.0.0' + pr\n    } else if (xp) {\n      ret = '>=' + M + '.' + m + '.0' + pr +\n        ' <' + M + '.' + (+m + 1) + '.0' + pr\n    }\n\n    debug('xRange return', ret)\n\n    return ret\n  })\n}\n\n// Because * is AND-ed with everything else in the comparator,\n// and '' means \"any version\", just remove the *s entirely.\nfunction replaceStars (comp, options) {\n  debug('replaceStars', comp, options)\n  // Looseness is ignored here.  star is always as loose as it gets!\n  return comp.trim().replace(safeRe[t.STAR], '')\n}\n\n// This function is passed to string.replace(re[t.HYPHENRANGE])\n// M, m, patch, prerelease, build\n// 1.2 - 3.4.5 => >=1.2.0 <=3.4.5\n// 1.2.3 - 3.4 => >=1.2.0 <3.5.0 Any 3.4.x will do\n// 1.2 - 3.4 => >=1.2.0 <3.5.0\nfunction hyphenReplace ($0,\n  from, fM, fm, fp, fpr, fb,\n  to, tM, tm, tp, tpr, tb) {\n  if (isX(fM)) {\n    from = ''\n  } else if (isX(fm)) {\n    from = '>=' + fM + '.0.0'\n  } else if (isX(fp)) {\n    from = '>=' + fM + '.' + fm + '.0'\n  } else {\n    from = '>=' + from\n  }\n\n  if (isX(tM)) {\n    to = ''\n  } else if (isX(tm)) {\n    to = '<' + (+tM + 1) + '.0.0'\n  } else if (isX(tp)) {\n    to = '<' + tM + '.' + (+tm + 1) + '.0'\n  } else if (tpr) {\n    to = '<=' + tM + '.' + tm + '.' + tp + '-' + tpr\n  } else {\n    to = '<=' + to\n  }\n\n  return (from + ' ' + to).trim()\n}\n\n// if ANY of the sets match ALL of its comparators, then pass\nRange.prototype.test = function (version) {\n  if (!version) {\n    return false\n  }\n\n  if (typeof version === 'string') {\n    try {\n      version = new SemVer(version, this.options)\n    } catch (er) {\n      return false\n    }\n  }\n\n  for (var i = 0; i < this.set.length; i++) {\n    if (testSet(this.set[i], version, this.options)) {\n      return true\n    }\n  }\n  return false\n}\n\nfunction testSet (set, version, options) {\n  for (var i = 0; i < set.length; i++) {\n    if (!set[i].test(version)) {\n      return false\n    }\n  }\n\n  if (version.prerelease.length && !options.includePrerelease) {\n    // Find the set of versions that are allowed to have prereleases\n    // For example, ^1.2.3-pr.1 desugars to >=1.2.3-pr.1 <2.0.0\n    // That should allow `1.2.3-pr.2` to pass.\n    // However, `1.2.4-alpha.notready` should NOT be allowed,\n    // even though it's within the range set by the comparators.\n    for (i = 0; i < set.length; i++) {\n      debug(set[i].semver)\n      if (set[i].semver === ANY) {\n        continue\n      }\n\n      if (set[i].semver.prerelease.length > 0) {\n        var allowed = set[i].semver\n        if (allowed.major === version.major &&\n            allowed.minor === version.minor &&\n            allowed.patch === version.patch) {\n          return true\n        }\n      }\n    }\n\n    // Version has a -pre, but it's not one of the ones we like.\n    return false\n  }\n\n  return true\n}\n\nexports.satisfies = satisfies\nfunction satisfies (version, range, options) {\n  try {\n    range = new Range(range, options)\n  } catch (er) {\n    return false\n  }\n  return range.test(version)\n}\n\nexports.maxSatisfying = maxSatisfying\nfunction maxSatisfying (versions, range, options) {\n  var max = null\n  var maxSV = null\n  try {\n    var rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach(function (v) {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!max || maxSV.compare(v) === -1) {\n        // compare(max, v, true)\n        max = v\n        maxSV = new SemVer(max, options)\n      }\n    }\n  })\n  return max\n}\n\nexports.minSatisfying = minSatisfying\nfunction minSatisfying (versions, range, options) {\n  var min = null\n  var minSV = null\n  try {\n    var rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach(function (v) {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!min || minSV.compare(v) === 1) {\n        // compare(min, v, true)\n        min = v\n        minSV = new SemVer(min, options)\n      }\n    }\n  })\n  return min\n}\n\nexports.minVersion = minVersion\nfunction minVersion (range, loose) {\n  range = new Range(range, loose)\n\n  var minver = new SemVer('0.0.0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = new SemVer('0.0.0-0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = null\n  for (var i = 0; i < range.set.length; ++i) {\n    var comparators = range.set[i]\n\n    comparators.forEach(function (comparator) {\n      // Clone to avoid manipulating the comparator's semver object.\n      var compver = new SemVer(comparator.semver.version)\n      switch (comparator.operator) {\n        case '>':\n          if (compver.prerelease.length === 0) {\n            compver.patch++\n          } else {\n            compver.prerelease.push(0)\n          }\n          compver.raw = compver.format()\n          /* fallthrough */\n        case '':\n        case '>=':\n          if (!minver || gt(minver, compver)) {\n            minver = compver\n          }\n          break\n        case '<':\n        case '<=':\n          /* Ignore maximum versions */\n          break\n        /* istanbul ignore next */\n        default:\n          throw new Error('Unexpected operation: ' + comparator.operator)\n      }\n    })\n  }\n\n  if (minver && range.test(minver)) {\n    return minver\n  }\n\n  return null\n}\n\nexports.validRange = validRange\nfunction validRange (range, options) {\n  try {\n    // Return '*' instead of '' so that truthiness works.\n    // This will throw if it's invalid anyway\n    return new Range(range, options).range || '*'\n  } catch (er) {\n    return null\n  }\n}\n\n// Determine if version is less than all the versions possible in the range\nexports.ltr = ltr\nfunction ltr (version, range, options) {\n  return outside(version, range, '<', options)\n}\n\n// Determine if version is greater than all the versions possible in the range.\nexports.gtr = gtr\nfunction gtr (version, range, options) {\n  return outside(version, range, '>', options)\n}\n\nexports.outside = outside\nfunction outside (version, range, hilo, options) {\n  version = new SemVer(version, options)\n  range = new Range(range, options)\n\n  var gtfn, ltefn, ltfn, comp, ecomp\n  switch (hilo) {\n    case '>':\n      gtfn = gt\n      ltefn = lte\n      ltfn = lt\n      comp = '>'\n      ecomp = '>='\n      break\n    case '<':\n      gtfn = lt\n      ltefn = gte\n      ltfn = gt\n      comp = '<'\n      ecomp = '<='\n      break\n    default:\n      throw new TypeError('Must provide a hilo val of \"<\" or \">\"')\n  }\n\n  // If it satisifes the range it is not outside\n  if (satisfies(version, range, options)) {\n    return false\n  }\n\n  // From now on, variable terms are as if we're in \"gtr\" mode.\n  // but note that everything is flipped for the \"ltr\" function.\n\n  for (var i = 0; i < range.set.length; ++i) {\n    var comparators = range.set[i]\n\n    var high = null\n    var low = null\n\n    comparators.forEach(function (comparator) {\n      if (comparator.semver === ANY) {\n        comparator = new Comparator('>=0.0.0')\n      }\n      high = high || comparator\n      low = low || comparator\n      if (gtfn(comparator.semver, high.semver, options)) {\n        high = comparator\n      } else if (ltfn(comparator.semver, low.semver, options)) {\n        low = comparator\n      }\n    })\n\n    // If the edge version comparator has a operator then our version\n    // isn't outside it\n    if (high.operator === comp || high.operator === ecomp) {\n      return false\n    }\n\n    // If the lowest version comparator has an operator and our version\n    // is less than it then it isn't higher than the range\n    if ((!low.operator || low.operator === comp) &&\n        ltefn(version, low.semver)) {\n      return false\n    } else if (low.operator === ecomp && ltfn(version, low.semver)) {\n      return false\n    }\n  }\n  return true\n}\n\nexports.prerelease = prerelease\nfunction prerelease (version, options) {\n  var parsed = parse(version, options)\n  return (parsed && parsed.prerelease.length) ? parsed.prerelease : null\n}\n\nexports.intersects = intersects\nfunction intersects (r1, r2, options) {\n  r1 = new Range(r1, options)\n  r2 = new Range(r2, options)\n  return r1.intersects(r2)\n}\n\nexports.coerce = coerce\nfunction coerce (version, options) {\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version === 'number') {\n    version = String(version)\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  options = options || {}\n\n  var match = null\n  if (!options.rtl) {\n    match = version.match(safeRe[t.COERCE])\n  } else {\n    // Find the right-most coercible string that does not share\n    // a terminus with a more left-ward coercible string.\n    // Eg, '1.2.3.4' wants to coerce '2.3.4', not '3.4' or '4'\n    //\n    // Walk through the string checking with a /g regexp\n    // Manually set the index so as to pick up overlapping matches.\n    // Stop when we get a match that ends at the string end, since no\n    // coercible string can be more right-ward without the same terminus.\n    var next\n    while ((next = safeRe[t.COERCERTL].exec(version)) &&\n      (!match || match.index + match[0].length !== version.length)\n    ) {\n      if (!match ||\n          next.index + next[0].length !== match.index + match[0].length) {\n        match = next\n      }\n      safeRe[t.COERCERTL].lastIndex = next.index + next[1].length + next[2].length\n    }\n    // leave it in a clean state\n    safeRe[t.COERCERTL].lastIndex = -1\n  }\n\n  if (match === null) {\n    return null\n  }\n\n  return parse(match[2] +\n    '.' + (match[3] || '0') +\n    '.' + (match[4] || '0'), options)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,cAAU,OAAO,UAAU;AAE3B,QAAI;AAEJ,QAAI,OAAO,YAAY,YACnB,QAAQ,OACR,QAAQ,IAAI,cACZ,cAAc,KAAK,QAAQ,IAAI,UAAU,GAAG;AAC9C,cAAQ,WAAY;AAClB,YAAI,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAClD,aAAK,QAAQ,QAAQ;AACrB,gBAAQ,IAAI,MAAM,SAAS,IAAI;AAAA,MACjC;AAAA,IACF,OAAO;AACL,cAAQ,WAAY;AAAA,MAAC;AAAA,IACvB;AAIA,YAAQ,sBAAsB;AAE9B,QAAI,aAAa;AACjB,QAAI,mBAAmB,OAAO;AAAA,IACD;AAG7B,QAAI,4BAA4B;AAEhC,QAAI,wBAAwB,aAAa;AAGzC,QAAI,KAAK,QAAQ,KAAK,CAAC;AACvB,QAAI,SAAS,QAAQ,SAAS,CAAC;AAC/B,QAAI,MAAM,QAAQ,MAAM,CAAC;AACzB,QAAI,IAAI,QAAQ,SAAS,CAAC;AAC1B,QAAI,IAAI;AAER,aAAS,IAAK,GAAG;AACf,QAAE,CAAC,IAAI;AAAA,IACT;AAEA,QAAI,mBAAmB;AAQvB,QAAI,wBAAwB;AAAA,MAC1B,CAAC,OAAO,CAAC;AAAA,MACT,CAAC,OAAO,UAAU;AAAA,MAClB,CAAC,kBAAkB,qBAAqB;AAAA,IAC1C;AAEA,aAAS,WAAY,OAAO;AAC1B,eAASA,KAAI,GAAGA,KAAI,sBAAsB,QAAQA,MAAK;AACrD,YAAI,QAAQ,sBAAsBA,EAAC,EAAE,CAAC;AACtC,YAAI,MAAM,sBAAsBA,EAAC,EAAE,CAAC;AACpC,gBAAQ,MACL,MAAM,QAAQ,GAAG,EAAE,KAAK,QAAQ,QAAQ,MAAM,GAAG,EACjD,MAAM,QAAQ,GAAG,EAAE,KAAK,QAAQ,QAAQ,MAAM,GAAG;AAAA,MACtD;AACA,aAAO;AAAA,IACT;AAQA,QAAI,mBAAmB;AACvB,QAAI,EAAE,iBAAiB,IAAI;AAC3B,QAAI,wBAAwB;AAC5B,QAAI,EAAE,sBAAsB,IAAI;AAMhC,QAAI,sBAAsB;AAC1B,QAAI,EAAE,oBAAoB,IAAI,kBAAkB,mBAAmB;AAKnE,QAAI,aAAa;AACjB,QAAI,EAAE,WAAW,IAAI,MAAM,IAAI,EAAE,iBAAiB,IAAI,UAC7B,IAAI,EAAE,iBAAiB,IAAI,UAC3B,IAAI,EAAE,iBAAiB,IAAI;AAEpD,QAAI,kBAAkB;AACtB,QAAI,EAAE,gBAAgB,IAAI,MAAM,IAAI,EAAE,sBAAsB,IAAI,UAClC,IAAI,EAAE,sBAAsB,IAAI,UAChC,IAAI,EAAE,sBAAsB,IAAI;AAK9D,QAAI,sBAAsB;AAC1B,QAAI,EAAE,oBAAoB,IAAI,QAAQ,IAAI,EAAE,iBAAiB,IACjC,MAAM,IAAI,EAAE,oBAAoB,IAAI;AAEhE,QAAI,2BAA2B;AAC/B,QAAI,EAAE,yBAAyB,IAAI,QAAQ,IAAI,EAAE,sBAAsB,IACtC,MAAM,IAAI,EAAE,oBAAoB,IAAI;AAMrE,QAAI,YAAY;AAChB,QAAI,EAAE,UAAU,IAAI,UAAU,IAAI,EAAE,oBAAoB,IACtC,WAAW,IAAI,EAAE,oBAAoB,IAAI;AAE3D,QAAI,iBAAiB;AACrB,QAAI,EAAE,eAAe,IAAI,WAAW,IAAI,EAAE,yBAAyB,IAC5C,WAAW,IAAI,EAAE,yBAAyB,IAAI;AAKrE,QAAI,iBAAiB;AACrB,QAAI,EAAE,eAAe,IAAI,mBAAmB;AAM5C,QAAI,OAAO;AACX,QAAI,EAAE,KAAK,IAAI,YAAY,IAAI,EAAE,eAAe,IACnC,WAAW,IAAI,EAAE,eAAe,IAAI;AAWjD,QAAI,MAAM;AACV,QAAI,WAAW;AACf,QAAI,EAAE,SAAS,IAAI,OAAO,IAAI,EAAE,WAAW,IACzB,IAAI,EAAE,UAAU,IAAI,MACpB,IAAI,EAAE,KAAK,IAAI;AAEjC,QAAI,EAAE,IAAI,IAAI,MAAM,IAAI,EAAE,SAAS,IAAI;AAKvC,QAAI,YAAY;AAChB,QAAI,EAAE,UAAU,IAAI,aAAa,IAAI,EAAE,gBAAgB,IACrC,IAAI,EAAE,eAAe,IAAI,MACzB,IAAI,EAAE,KAAK,IAAI;AAEjC,QAAI,OAAO;AACX,QAAI,EAAE,KAAK,IAAI,MAAM,IAAI,EAAE,UAAU,IAAI;AAEzC,QAAI,MAAM;AACV,QAAI,EAAE,IAAI,IAAI;AAKd,QAAI,uBAAuB;AAC3B,QAAI,EAAE,qBAAqB,IAAI,IAAI,EAAE,sBAAsB,IAAI;AAC/D,QAAI,kBAAkB;AACtB,QAAI,EAAE,gBAAgB,IAAI,IAAI,EAAE,iBAAiB,IAAI;AAErD,QAAI,aAAa;AACjB,QAAI,EAAE,WAAW,IAAI,cAAc,IAAI,EAAE,gBAAgB,IAAI,aAC9B,IAAI,EAAE,gBAAgB,IAAI,aAC1B,IAAI,EAAE,gBAAgB,IAAI,SAC9B,IAAI,EAAE,UAAU,IAAI,OAC5B,IAAI,EAAE,KAAK,IAAI;AAGlC,QAAI,kBAAkB;AACtB,QAAI,EAAE,gBAAgB,IAAI,cAAc,IAAI,EAAE,qBAAqB,IAAI,aACnC,IAAI,EAAE,qBAAqB,IAAI,aAC/B,IAAI,EAAE,qBAAqB,IAAI,SACnC,IAAI,EAAE,eAAe,IAAI,OACjC,IAAI,EAAE,KAAK,IAAI;AAGvC,QAAI,QAAQ;AACZ,QAAI,EAAE,MAAM,IAAI,MAAM,IAAI,EAAE,IAAI,IAAI,SAAS,IAAI,EAAE,WAAW,IAAI;AAClE,QAAI,aAAa;AACjB,QAAI,EAAE,WAAW,IAAI,MAAM,IAAI,EAAE,IAAI,IAAI,SAAS,IAAI,EAAE,gBAAgB,IAAI;AAI5E,QAAI,QAAQ;AACZ,QAAI,EAAE,MAAM,IAAI,sBACU,4BAA4B,oBACtB,4BAA4B,sBAC5B,4BAA4B;AAE5D,QAAI,WAAW;AACf,OAAG,EAAE,SAAS,IAAI,IAAI,OAAO,IAAI,EAAE,MAAM,GAAG,GAAG;AAC/C,WAAO,EAAE,SAAS,IAAI,IAAI,OAAO,WAAW,IAAI,EAAE,MAAM,CAAC,GAAG,GAAG;AAI/D,QAAI,WAAW;AACf,QAAI,EAAE,SAAS,IAAI;AAEnB,QAAI,WAAW;AACf,QAAI,EAAE,SAAS,IAAI,WAAW,IAAI,EAAE,SAAS,IAAI;AACjD,OAAG,EAAE,SAAS,IAAI,IAAI,OAAO,IAAI,EAAE,SAAS,GAAG,GAAG;AAClD,WAAO,EAAE,SAAS,IAAI,IAAI,OAAO,WAAW,IAAI,EAAE,SAAS,CAAC,GAAG,GAAG;AAClE,QAAI,mBAAmB;AAEvB,QAAI,OAAO;AACX,QAAI,EAAE,KAAK,IAAI,MAAM,IAAI,EAAE,SAAS,IAAI,IAAI,EAAE,WAAW,IAAI;AAC7D,QAAI,YAAY;AAChB,QAAI,EAAE,UAAU,IAAI,MAAM,IAAI,EAAE,SAAS,IAAI,IAAI,EAAE,gBAAgB,IAAI;AAIvE,QAAI,WAAW;AACf,QAAI,EAAE,SAAS,IAAI;AAEnB,QAAI,WAAW;AACf,QAAI,EAAE,SAAS,IAAI,WAAW,IAAI,EAAE,SAAS,IAAI;AACjD,OAAG,EAAE,SAAS,IAAI,IAAI,OAAO,IAAI,EAAE,SAAS,GAAG,GAAG;AAClD,WAAO,EAAE,SAAS,IAAI,IAAI,OAAO,WAAW,IAAI,EAAE,SAAS,CAAC,GAAG,GAAG;AAClE,QAAI,mBAAmB;AAEvB,QAAI,OAAO;AACX,QAAI,EAAE,KAAK,IAAI,MAAM,IAAI,EAAE,SAAS,IAAI,IAAI,EAAE,WAAW,IAAI;AAC7D,QAAI,YAAY;AAChB,QAAI,EAAE,UAAU,IAAI,MAAM,IAAI,EAAE,SAAS,IAAI,IAAI,EAAE,gBAAgB,IAAI;AAGvE,QAAI,iBAAiB;AACrB,QAAI,EAAE,eAAe,IAAI,MAAM,IAAI,EAAE,IAAI,IAAI,UAAU,IAAI,EAAE,UAAU,IAAI;AAC3E,QAAI,YAAY;AAChB,QAAI,EAAE,UAAU,IAAI,MAAM,IAAI,EAAE,IAAI,IAAI,UAAU,IAAI,EAAE,SAAS,IAAI;AAIrE,QAAI,gBAAgB;AACpB,QAAI,EAAE,cAAc,IAAI,WAAW,IAAI,EAAE,IAAI,IACvB,UAAU,IAAI,EAAE,UAAU,IAAI,MAAM,IAAI,EAAE,WAAW,IAAI;AAG/E,OAAG,EAAE,cAAc,IAAI,IAAI,OAAO,IAAI,EAAE,cAAc,GAAG,GAAG;AAC5D,WAAO,EAAE,cAAc,IAAI,IAAI,OAAO,WAAW,IAAI,EAAE,cAAc,CAAC,GAAG,GAAG;AAC5E,QAAI,wBAAwB;AAM5B,QAAI,aAAa;AACjB,QAAI,EAAE,WAAW,IAAI,WAAW,IAAI,EAAE,WAAW,IAAI,gBAE5B,IAAI,EAAE,WAAW,IAAI;AAG9C,QAAI,kBAAkB;AACtB,QAAI,EAAE,gBAAgB,IAAI,WAAW,IAAI,EAAE,gBAAgB,IAAI,gBAEjC,IAAI,EAAE,gBAAgB,IAAI;AAIxD,QAAI,MAAM;AACV,QAAI,EAAE,IAAI,IAAI;AAId,SAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,GAAG,IAAI,CAAC,CAAC;AACf,UAAI,CAAC,GAAG,CAAC,GAAG;AACV,WAAG,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,CAAC;AAQzB,eAAO,CAAC,IAAI,IAAI,OAAO,WAAW,IAAI,CAAC,CAAC,CAAC;AAAA,MAC3C;AAAA,IACF;AAbS;AAeT,YAAQ,QAAQ;AAChB,aAAS,MAAO,SAAS,SAAS;AAChC,UAAI,CAAC,WAAW,OAAO,YAAY,UAAU;AAC3C,kBAAU;AAAA,UACR,OAAO,CAAC,CAAC;AAAA,UACT,mBAAmB;AAAA,QACrB;AAAA,MACF;AAEA,UAAI,mBAAmB,QAAQ;AAC7B,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,YAAY,UAAU;AAC/B,eAAO;AAAA,MACT;AAEA,UAAI,QAAQ,SAAS,YAAY;AAC/B,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,QAAQ,QAAQ,OAAO,EAAE,KAAK,IAAI,OAAO,EAAE,IAAI;AACvD,UAAI,CAAC,EAAE,KAAK,OAAO,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,UAAI;AACF,eAAO,IAAI,OAAO,SAAS,OAAO;AAAA,MACpC,SAAS,IAAI;AACX,eAAO;AAAA,MACT;AAAA,IACF;AAEA,YAAQ,QAAQ;AAChB,aAAS,MAAO,SAAS,SAAS;AAChC,UAAI,IAAI,MAAM,SAAS,OAAO;AAC9B,aAAO,IAAI,EAAE,UAAU;AAAA,IACzB;AAEA,YAAQ,QAAQ;AAChB,aAAS,MAAO,SAAS,SAAS;AAChC,UAAI,IAAI,MAAM,QAAQ,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,OAAO;AAC3D,aAAO,IAAI,EAAE,UAAU;AAAA,IACzB;AAEA,YAAQ,SAAS;AAEjB,aAAS,OAAQ,SAAS,SAAS;AACjC,UAAI,CAAC,WAAW,OAAO,YAAY,UAAU;AAC3C,kBAAU;AAAA,UACR,OAAO,CAAC,CAAC;AAAA,UACT,mBAAmB;AAAA,QACrB;AAAA,MACF;AACA,UAAI,mBAAmB,QAAQ;AAC7B,YAAI,QAAQ,UAAU,QAAQ,OAAO;AACnC,iBAAO;AAAA,QACT,OAAO;AACL,oBAAU,QAAQ;AAAA,QACpB;AAAA,MACF,WAAW,OAAO,YAAY,UAAU;AACtC,cAAM,IAAI,UAAU,sBAAsB,OAAO;AAAA,MACnD;AAEA,UAAI,QAAQ,SAAS,YAAY;AAC/B,cAAM,IAAI,UAAU,4BAA4B,aAAa,aAAa;AAAA,MAC5E;AAEA,UAAI,EAAE,gBAAgB,SAAS;AAC7B,eAAO,IAAI,OAAO,SAAS,OAAO;AAAA,MACpC;AAEA,YAAM,UAAU,SAAS,OAAO;AAChC,WAAK,UAAU;AACf,WAAK,QAAQ,CAAC,CAAC,QAAQ;AAEvB,UAAI,IAAI,QAAQ,KAAK,EAAE,MAAM,QAAQ,QAAQ,OAAO,EAAE,KAAK,IAAI,OAAO,EAAE,IAAI,CAAC;AAE7E,UAAI,CAAC,GAAG;AACN,cAAM,IAAI,UAAU,sBAAsB,OAAO;AAAA,MACnD;AAEA,WAAK,MAAM;AAGX,WAAK,QAAQ,CAAC,EAAE,CAAC;AACjB,WAAK,QAAQ,CAAC,EAAE,CAAC;AACjB,WAAK,QAAQ,CAAC,EAAE,CAAC;AAEjB,UAAI,KAAK,QAAQ,oBAAoB,KAAK,QAAQ,GAAG;AACnD,cAAM,IAAI,UAAU,uBAAuB;AAAA,MAC7C;AAEA,UAAI,KAAK,QAAQ,oBAAoB,KAAK,QAAQ,GAAG;AACnD,cAAM,IAAI,UAAU,uBAAuB;AAAA,MAC7C;AAEA,UAAI,KAAK,QAAQ,oBAAoB,KAAK,QAAQ,GAAG;AACnD,cAAM,IAAI,UAAU,uBAAuB;AAAA,MAC7C;AAGA,UAAI,CAAC,EAAE,CAAC,GAAG;AACT,aAAK,aAAa,CAAC;AAAA,MACrB,OAAO;AACL,aAAK,aAAa,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,SAAU,IAAI;AAClD,cAAI,WAAW,KAAK,EAAE,GAAG;AACvB,gBAAI,MAAM,CAAC;AACX,gBAAI,OAAO,KAAK,MAAM,kBAAkB;AACtC,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAEA,WAAK,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;AACvC,WAAK,OAAO;AAAA,IACd;AAEA,WAAO,UAAU,SAAS,WAAY;AACpC,WAAK,UAAU,KAAK,QAAQ,MAAM,KAAK,QAAQ,MAAM,KAAK;AAC1D,UAAI,KAAK,WAAW,QAAQ;AAC1B,aAAK,WAAW,MAAM,KAAK,WAAW,KAAK,GAAG;AAAA,MAChD;AACA,aAAO,KAAK;AAAA,IACd;AAEA,WAAO,UAAU,WAAW,WAAY;AACtC,aAAO,KAAK;AAAA,IACd;AAEA,WAAO,UAAU,UAAU,SAAU,OAAO;AAC1C,YAAM,kBAAkB,KAAK,SAAS,KAAK,SAAS,KAAK;AACzD,UAAI,EAAE,iBAAiB,SAAS;AAC9B,gBAAQ,IAAI,OAAO,OAAO,KAAK,OAAO;AAAA,MACxC;AAEA,aAAO,KAAK,YAAY,KAAK,KAAK,KAAK,WAAW,KAAK;AAAA,IACzD;AAEA,WAAO,UAAU,cAAc,SAAU,OAAO;AAC9C,UAAI,EAAE,iBAAiB,SAAS;AAC9B,gBAAQ,IAAI,OAAO,OAAO,KAAK,OAAO;AAAA,MACxC;AAEA,aAAO,mBAAmB,KAAK,OAAO,MAAM,KAAK,KAC1C,mBAAmB,KAAK,OAAO,MAAM,KAAK,KAC1C,mBAAmB,KAAK,OAAO,MAAM,KAAK;AAAA,IACnD;AAEA,WAAO,UAAU,aAAa,SAAU,OAAO;AAC7C,UAAI,EAAE,iBAAiB,SAAS;AAC9B,gBAAQ,IAAI,OAAO,OAAO,KAAK,OAAO;AAAA,MACxC;AAGA,UAAI,KAAK,WAAW,UAAU,CAAC,MAAM,WAAW,QAAQ;AACtD,eAAO;AAAA,MACT,WAAW,CAAC,KAAK,WAAW,UAAU,MAAM,WAAW,QAAQ;AAC7D,eAAO;AAAA,MACT,WAAW,CAAC,KAAK,WAAW,UAAU,CAAC,MAAM,WAAW,QAAQ;AAC9D,eAAO;AAAA,MACT;AAEA,UAAIA,KAAI;AACR,SAAG;AACD,YAAI,IAAI,KAAK,WAAWA,EAAC;AACzB,YAAI,IAAI,MAAM,WAAWA,EAAC;AAC1B,cAAM,sBAAsBA,IAAG,GAAG,CAAC;AACnC,YAAI,MAAM,UAAa,MAAM,QAAW;AACtC,iBAAO;AAAA,QACT,WAAW,MAAM,QAAW;AAC1B,iBAAO;AAAA,QACT,WAAW,MAAM,QAAW;AAC1B,iBAAO;AAAA,QACT,WAAW,MAAM,GAAG;AAClB;AAAA,QACF,OAAO;AACL,iBAAO,mBAAmB,GAAG,CAAC;AAAA,QAChC;AAAA,MACF,SAAS,EAAEA;AAAA,IACb;AAEA,WAAO,UAAU,eAAe,SAAU,OAAO;AAC/C,UAAI,EAAE,iBAAiB,SAAS;AAC9B,gBAAQ,IAAI,OAAO,OAAO,KAAK,OAAO;AAAA,MACxC;AAEA,UAAIA,KAAI;AACR,SAAG;AACD,YAAI,IAAI,KAAK,MAAMA,EAAC;AACpB,YAAI,IAAI,MAAM,MAAMA,EAAC;AACrB,cAAM,sBAAsBA,IAAG,GAAG,CAAC;AACnC,YAAI,MAAM,UAAa,MAAM,QAAW;AACtC,iBAAO;AAAA,QACT,WAAW,MAAM,QAAW;AAC1B,iBAAO;AAAA,QACT,WAAW,MAAM,QAAW;AAC1B,iBAAO;AAAA,QACT,WAAW,MAAM,GAAG;AAClB;AAAA,QACF,OAAO;AACL,iBAAO,mBAAmB,GAAG,CAAC;AAAA,QAChC;AAAA,MACF,SAAS,EAAEA;AAAA,IACb;AAIA,WAAO,UAAU,MAAM,SAAU,SAAS,YAAY;AACpD,cAAQ,SAAS;AAAA,QACf,KAAK;AACH,eAAK,WAAW,SAAS;AACzB,eAAK,QAAQ;AACb,eAAK,QAAQ;AACb,eAAK;AACL,eAAK,IAAI,OAAO,UAAU;AAC1B;AAAA,QACF,KAAK;AACH,eAAK,WAAW,SAAS;AACzB,eAAK,QAAQ;AACb,eAAK;AACL,eAAK,IAAI,OAAO,UAAU;AAC1B;AAAA,QACF,KAAK;AAIH,eAAK,WAAW,SAAS;AACzB,eAAK,IAAI,SAAS,UAAU;AAC5B,eAAK,IAAI,OAAO,UAAU;AAC1B;AAAA;AAAA;AAAA,QAGF,KAAK;AACH,cAAI,KAAK,WAAW,WAAW,GAAG;AAChC,iBAAK,IAAI,SAAS,UAAU;AAAA,UAC9B;AACA,eAAK,IAAI,OAAO,UAAU;AAC1B;AAAA,QAEF,KAAK;AAKH,cAAI,KAAK,UAAU,KACf,KAAK,UAAU,KACf,KAAK,WAAW,WAAW,GAAG;AAChC,iBAAK;AAAA,UACP;AACA,eAAK,QAAQ;AACb,eAAK,QAAQ;AACb,eAAK,aAAa,CAAC;AACnB;AAAA,QACF,KAAK;AAKH,cAAI,KAAK,UAAU,KAAK,KAAK,WAAW,WAAW,GAAG;AACpD,iBAAK;AAAA,UACP;AACA,eAAK,QAAQ;AACb,eAAK,aAAa,CAAC;AACnB;AAAA,QACF,KAAK;AAKH,cAAI,KAAK,WAAW,WAAW,GAAG;AAChC,iBAAK;AAAA,UACP;AACA,eAAK,aAAa,CAAC;AACnB;AAAA;AAAA;AAAA,QAGF,KAAK;AACH,cAAI,KAAK,WAAW,WAAW,GAAG;AAChC,iBAAK,aAAa,CAAC,CAAC;AAAA,UACtB,OAAO;AACL,gBAAIA,KAAI,KAAK,WAAW;AACxB,mBAAO,EAAEA,MAAK,GAAG;AACf,kBAAI,OAAO,KAAK,WAAWA,EAAC,MAAM,UAAU;AAC1C,qBAAK,WAAWA,EAAC;AACjB,gBAAAA,KAAI;AAAA,cACN;AAAA,YACF;AACA,gBAAIA,OAAM,IAAI;AAEZ,mBAAK,WAAW,KAAK,CAAC;AAAA,YACxB;AAAA,UACF;AACA,cAAI,YAAY;AAGd,gBAAI,KAAK,WAAW,CAAC,MAAM,YAAY;AACrC,kBAAI,MAAM,KAAK,WAAW,CAAC,CAAC,GAAG;AAC7B,qBAAK,aAAa,CAAC,YAAY,CAAC;AAAA,cAClC;AAAA,YACF,OAAO;AACL,mBAAK,aAAa,CAAC,YAAY,CAAC;AAAA,YAClC;AAAA,UACF;AACA;AAAA,QAEF;AACE,gBAAM,IAAI,MAAM,iCAAiC,OAAO;AAAA,MAC5D;AACA,WAAK,OAAO;AACZ,WAAK,MAAM,KAAK;AAChB,aAAO;AAAA,IACT;AAEA,YAAQ,MAAM;AACd,aAAS,IAAK,SAAS,SAAS,OAAO,YAAY;AACjD,UAAI,OAAQ,UAAW,UAAU;AAC/B,qBAAa;AACb,gBAAQ;AAAA,MACV;AAEA,UAAI;AACF,eAAO,IAAI,OAAO,SAAS,KAAK,EAAE,IAAI,SAAS,UAAU,EAAE;AAAA,MAC7D,SAAS,IAAI;AACX,eAAO;AAAA,MACT;AAAA,IACF;AAEA,YAAQ,OAAO;AACf,aAAS,KAAM,UAAU,UAAU;AACjC,UAAI,GAAG,UAAU,QAAQ,GAAG;AAC1B,eAAO;AAAA,MACT,OAAO;AACL,YAAI,KAAK,MAAM,QAAQ;AACvB,YAAI,KAAK,MAAM,QAAQ;AACvB,YAAI,SAAS;AACb,YAAI,GAAG,WAAW,UAAU,GAAG,WAAW,QAAQ;AAChD,mBAAS;AACT,cAAI,gBAAgB;AAAA,QACtB;AACA,iBAAS,OAAO,IAAI;AAClB,cAAI,QAAQ,WAAW,QAAQ,WAAW,QAAQ,SAAS;AACzD,gBAAI,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG;AACvB,qBAAO,SAAS;AAAA,YAClB;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,YAAQ,qBAAqB;AAE7B,QAAI,UAAU;AACd,aAAS,mBAAoB,GAAG,GAAG;AACjC,UAAI,OAAO,QAAQ,KAAK,CAAC;AACzB,UAAI,OAAO,QAAQ,KAAK,CAAC;AAEzB,UAAI,QAAQ,MAAM;AAChB,YAAI,CAAC;AACL,YAAI,CAAC;AAAA,MACP;AAEA,aAAO,MAAM,IAAI,IACZ,QAAQ,CAAC,OAAQ,KACjB,QAAQ,CAAC,OAAQ,IAClB,IAAI,IAAI,KACR;AAAA,IACN;AAEA,YAAQ,sBAAsB;AAC9B,aAAS,oBAAqB,GAAG,GAAG;AAClC,aAAO,mBAAmB,GAAG,CAAC;AAAA,IAChC;AAEA,YAAQ,QAAQ;AAChB,aAAS,MAAO,GAAG,OAAO;AACxB,aAAO,IAAI,OAAO,GAAG,KAAK,EAAE;AAAA,IAC9B;AAEA,YAAQ,QAAQ;AAChB,aAAS,MAAO,GAAG,OAAO;AACxB,aAAO,IAAI,OAAO,GAAG,KAAK,EAAE;AAAA,IAC9B;AAEA,YAAQ,QAAQ;AAChB,aAAS,MAAO,GAAG,OAAO;AACxB,aAAO,IAAI,OAAO,GAAG,KAAK,EAAE;AAAA,IAC9B;AAEA,YAAQ,UAAU;AAClB,aAAS,QAAS,GAAG,GAAG,OAAO;AAC7B,aAAO,IAAI,OAAO,GAAG,KAAK,EAAE,QAAQ,IAAI,OAAO,GAAG,KAAK,CAAC;AAAA,IAC1D;AAEA,YAAQ,eAAe;AACvB,aAAS,aAAc,GAAG,GAAG;AAC3B,aAAO,QAAQ,GAAG,GAAG,IAAI;AAAA,IAC3B;AAEA,YAAQ,eAAe;AACvB,aAAS,aAAc,GAAG,GAAG,OAAO;AAClC,UAAI,WAAW,IAAI,OAAO,GAAG,KAAK;AAClC,UAAI,WAAW,IAAI,OAAO,GAAG,KAAK;AAClC,aAAO,SAAS,QAAQ,QAAQ,KAAK,SAAS,aAAa,QAAQ;AAAA,IACrE;AAEA,YAAQ,WAAW;AACnB,aAAS,SAAU,GAAG,GAAG,OAAO;AAC9B,aAAO,QAAQ,GAAG,GAAG,KAAK;AAAA,IAC5B;AAEA,YAAQ,OAAO;AACf,aAAS,KAAM,MAAM,OAAO;AAC1B,aAAO,KAAK,KAAK,SAAU,GAAG,GAAG;AAC/B,eAAO,QAAQ,aAAa,GAAG,GAAG,KAAK;AAAA,MACzC,CAAC;AAAA,IACH;AAEA,YAAQ,QAAQ;AAChB,aAAS,MAAO,MAAM,OAAO;AAC3B,aAAO,KAAK,KAAK,SAAU,GAAG,GAAG;AAC/B,eAAO,QAAQ,aAAa,GAAG,GAAG,KAAK;AAAA,MACzC,CAAC;AAAA,IACH;AAEA,YAAQ,KAAK;AACb,aAAS,GAAI,GAAG,GAAG,OAAO;AACxB,aAAO,QAAQ,GAAG,GAAG,KAAK,IAAI;AAAA,IAChC;AAEA,YAAQ,KAAK;AACb,aAAS,GAAI,GAAG,GAAG,OAAO;AACxB,aAAO,QAAQ,GAAG,GAAG,KAAK,IAAI;AAAA,IAChC;AAEA,YAAQ,KAAK;AACb,aAAS,GAAI,GAAG,GAAG,OAAO;AACxB,aAAO,QAAQ,GAAG,GAAG,KAAK,MAAM;AAAA,IAClC;AAEA,YAAQ,MAAM;AACd,aAAS,IAAK,GAAG,GAAG,OAAO;AACzB,aAAO,QAAQ,GAAG,GAAG,KAAK,MAAM;AAAA,IAClC;AAEA,YAAQ,MAAM;AACd,aAAS,IAAK,GAAG,GAAG,OAAO;AACzB,aAAO,QAAQ,GAAG,GAAG,KAAK,KAAK;AAAA,IACjC;AAEA,YAAQ,MAAM;AACd,aAAS,IAAK,GAAG,GAAG,OAAO;AACzB,aAAO,QAAQ,GAAG,GAAG,KAAK,KAAK;AAAA,IACjC;AAEA,YAAQ,MAAM;AACd,aAAS,IAAK,GAAG,IAAI,GAAG,OAAO;AAC7B,cAAQ,IAAI;AAAA,QACV,KAAK;AACH,cAAI,OAAO,MAAM;AACf,gBAAI,EAAE;AACR,cAAI,OAAO,MAAM;AACf,gBAAI,EAAE;AACR,iBAAO,MAAM;AAAA,QAEf,KAAK;AACH,cAAI,OAAO,MAAM;AACf,gBAAI,EAAE;AACR,cAAI,OAAO,MAAM;AACf,gBAAI,EAAE;AACR,iBAAO,MAAM;AAAA,QAEf,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,GAAG,GAAG,GAAG,KAAK;AAAA,QAEvB,KAAK;AACH,iBAAO,IAAI,GAAG,GAAG,KAAK;AAAA,QAExB,KAAK;AACH,iBAAO,GAAG,GAAG,GAAG,KAAK;AAAA,QAEvB,KAAK;AACH,iBAAO,IAAI,GAAG,GAAG,KAAK;AAAA,QAExB,KAAK;AACH,iBAAO,GAAG,GAAG,GAAG,KAAK;AAAA,QAEvB,KAAK;AACH,iBAAO,IAAI,GAAG,GAAG,KAAK;AAAA,QAExB;AACE,gBAAM,IAAI,UAAU,uBAAuB,EAAE;AAAA,MACjD;AAAA,IACF;AAEA,YAAQ,aAAa;AACrB,aAAS,WAAY,MAAM,SAAS;AAClC,UAAI,CAAC,WAAW,OAAO,YAAY,UAAU;AAC3C,kBAAU;AAAA,UACR,OAAO,CAAC,CAAC;AAAA,UACT,mBAAmB;AAAA,QACrB;AAAA,MACF;AAEA,UAAI,gBAAgB,YAAY;AAC9B,YAAI,KAAK,UAAU,CAAC,CAAC,QAAQ,OAAO;AAClC,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,KAAK;AAAA,QACd;AAAA,MACF;AAEA,UAAI,EAAE,gBAAgB,aAAa;AACjC,eAAO,IAAI,WAAW,MAAM,OAAO;AAAA,MACrC;AAEA,aAAO,KAAK,KAAK,EAAE,MAAM,KAAK,EAAE,KAAK,GAAG;AACxC,YAAM,cAAc,MAAM,OAAO;AACjC,WAAK,UAAU;AACf,WAAK,QAAQ,CAAC,CAAC,QAAQ;AACvB,WAAK,MAAM,IAAI;AAEf,UAAI,KAAK,WAAW,KAAK;AACvB,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,aAAK,QAAQ,KAAK,WAAW,KAAK,OAAO;AAAA,MAC3C;AAEA,YAAM,QAAQ,IAAI;AAAA,IACpB;AAEA,QAAI,MAAM,CAAC;AACX,eAAW,UAAU,QAAQ,SAAU,MAAM;AAC3C,UAAI,IAAI,KAAK,QAAQ,QAAQ,OAAO,EAAE,eAAe,IAAI,OAAO,EAAE,UAAU;AAC5E,UAAI,IAAI,KAAK,MAAM,CAAC;AAEpB,UAAI,CAAC,GAAG;AACN,cAAM,IAAI,UAAU,yBAAyB,IAAI;AAAA,MACnD;AAEA,WAAK,WAAW,EAAE,CAAC,MAAM,SAAY,EAAE,CAAC,IAAI;AAC5C,UAAI,KAAK,aAAa,KAAK;AACzB,aAAK,WAAW;AAAA,MAClB;AAGA,UAAI,CAAC,EAAE,CAAC,GAAG;AACT,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,aAAK,SAAS,IAAI,OAAO,EAAE,CAAC,GAAG,KAAK,QAAQ,KAAK;AAAA,MACnD;AAAA,IACF;AAEA,eAAW,UAAU,WAAW,WAAY;AAC1C,aAAO,KAAK;AAAA,IACd;AAEA,eAAW,UAAU,OAAO,SAAU,SAAS;AAC7C,YAAM,mBAAmB,SAAS,KAAK,QAAQ,KAAK;AAEpD,UAAI,KAAK,WAAW,OAAO,YAAY,KAAK;AAC1C,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,YAAY,UAAU;AAC/B,YAAI;AACF,oBAAU,IAAI,OAAO,SAAS,KAAK,OAAO;AAAA,QAC5C,SAAS,IAAI;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO,IAAI,SAAS,KAAK,UAAU,KAAK,QAAQ,KAAK,OAAO;AAAA,IAC9D;AAEA,eAAW,UAAU,aAAa,SAAU,MAAM,SAAS;AACzD,UAAI,EAAE,gBAAgB,aAAa;AACjC,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AAEA,UAAI,CAAC,WAAW,OAAO,YAAY,UAAU;AAC3C,kBAAU;AAAA,UACR,OAAO,CAAC,CAAC;AAAA,UACT,mBAAmB;AAAA,QACrB;AAAA,MACF;AAEA,UAAI;AAEJ,UAAI,KAAK,aAAa,IAAI;AACxB,YAAI,KAAK,UAAU,IAAI;AACrB,iBAAO;AAAA,QACT;AACA,mBAAW,IAAI,MAAM,KAAK,OAAO,OAAO;AACxC,eAAO,UAAU,KAAK,OAAO,UAAU,OAAO;AAAA,MAChD,WAAW,KAAK,aAAa,IAAI;AAC/B,YAAI,KAAK,UAAU,IAAI;AACrB,iBAAO;AAAA,QACT;AACA,mBAAW,IAAI,MAAM,KAAK,OAAO,OAAO;AACxC,eAAO,UAAU,KAAK,QAAQ,UAAU,OAAO;AAAA,MACjD;AAEA,UAAI,2BACD,KAAK,aAAa,QAAQ,KAAK,aAAa,SAC5C,KAAK,aAAa,QAAQ,KAAK,aAAa;AAC/C,UAAI,2BACD,KAAK,aAAa,QAAQ,KAAK,aAAa,SAC5C,KAAK,aAAa,QAAQ,KAAK,aAAa;AAC/C,UAAI,aAAa,KAAK,OAAO,YAAY,KAAK,OAAO;AACrD,UAAI,gCACD,KAAK,aAAa,QAAQ,KAAK,aAAa,UAC5C,KAAK,aAAa,QAAQ,KAAK,aAAa;AAC/C,UAAI,6BACF,IAAI,KAAK,QAAQ,KAAK,KAAK,QAAQ,OAAO,OACxC,KAAK,aAAa,QAAQ,KAAK,aAAa,SAC7C,KAAK,aAAa,QAAQ,KAAK,aAAa;AAC/C,UAAI,gCACF,IAAI,KAAK,QAAQ,KAAK,KAAK,QAAQ,OAAO,OACxC,KAAK,aAAa,QAAQ,KAAK,aAAa,SAC7C,KAAK,aAAa,QAAQ,KAAK,aAAa;AAE/C,aAAO,2BAA2B,2BAC/B,cAAc,gCACf,8BAA8B;AAAA,IAClC;AAEA,YAAQ,QAAQ;AAChB,aAAS,MAAO,OAAO,SAAS;AAC9B,UAAI,CAAC,WAAW,OAAO,YAAY,UAAU;AAC3C,kBAAU;AAAA,UACR,OAAO,CAAC,CAAC;AAAA,UACT,mBAAmB;AAAA,QACrB;AAAA,MACF;AAEA,UAAI,iBAAiB,OAAO;AAC1B,YAAI,MAAM,UAAU,CAAC,CAAC,QAAQ,SAC1B,MAAM,sBAAsB,CAAC,CAAC,QAAQ,mBAAmB;AAC3D,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,IAAI,MAAM,MAAM,KAAK,OAAO;AAAA,QACrC;AAAA,MACF;AAEA,UAAI,iBAAiB,YAAY;AAC/B,eAAO,IAAI,MAAM,MAAM,OAAO,OAAO;AAAA,MACvC;AAEA,UAAI,EAAE,gBAAgB,QAAQ;AAC5B,eAAO,IAAI,MAAM,OAAO,OAAO;AAAA,MACjC;AAEA,WAAK,UAAU;AACf,WAAK,QAAQ,CAAC,CAAC,QAAQ;AACvB,WAAK,oBAAoB,CAAC,CAAC,QAAQ;AAKnC,WAAK,MAAM,MACR,KAAK,EACL,MAAM,KAAK,EACX,KAAK,GAAG;AAGX,WAAK,MAAM,KAAK,IAAI,MAAM,IAAI,EAAE,IAAI,SAAUC,QAAO;AACnD,eAAO,KAAK,WAAWA,OAAM,KAAK,CAAC;AAAA,MACrC,GAAG,IAAI,EAAE,OAAO,SAAU,GAAG;AAE3B,eAAO,EAAE;AAAA,MACX,CAAC;AAED,UAAI,CAAC,KAAK,IAAI,QAAQ;AACpB,cAAM,IAAI,UAAU,2BAA2B,KAAK,GAAG;AAAA,MACzD;AAEA,WAAK,OAAO;AAAA,IACd;AAEA,UAAM,UAAU,SAAS,WAAY;AACnC,WAAK,QAAQ,KAAK,IAAI,IAAI,SAAU,OAAO;AACzC,eAAO,MAAM,KAAK,GAAG,EAAE,KAAK;AAAA,MAC9B,CAAC,EAAE,KAAK,IAAI,EAAE,KAAK;AACnB,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,UAAU,WAAW,WAAY;AACrC,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,UAAU,aAAa,SAAU,OAAO;AAC5C,UAAI,QAAQ,KAAK,QAAQ;AAEzB,UAAI,KAAK,QAAQ,OAAO,EAAE,gBAAgB,IAAI,OAAO,EAAE,WAAW;AAClE,cAAQ,MAAM,QAAQ,IAAI,aAAa;AACvC,YAAM,kBAAkB,KAAK;AAE7B,cAAQ,MAAM,QAAQ,OAAO,EAAE,cAAc,GAAG,qBAAqB;AACrE,YAAM,mBAAmB,OAAO,OAAO,EAAE,cAAc,CAAC;AAGxD,cAAQ,MAAM,QAAQ,OAAO,EAAE,SAAS,GAAG,gBAAgB;AAG3D,cAAQ,MAAM,QAAQ,OAAO,EAAE,SAAS,GAAG,gBAAgB;AAG3D,cAAQ,MAAM,MAAM,KAAK,EAAE,KAAK,GAAG;AAKnC,UAAI,SAAS,QAAQ,OAAO,EAAE,eAAe,IAAI,OAAO,EAAE,UAAU;AACpE,UAAI,MAAM,MAAM,MAAM,GAAG,EAAE,IAAI,SAAU,MAAM;AAC7C,eAAO,gBAAgB,MAAM,KAAK,OAAO;AAAA,MAC3C,GAAG,IAAI,EAAE,KAAK,GAAG,EAAE,MAAM,KAAK;AAC9B,UAAI,KAAK,QAAQ,OAAO;AAEtB,cAAM,IAAI,OAAO,SAAU,MAAM;AAC/B,iBAAO,CAAC,CAAC,KAAK,MAAM,MAAM;AAAA,QAC5B,CAAC;AAAA,MACH;AACA,YAAM,IAAI,IAAI,SAAU,MAAM;AAC5B,eAAO,IAAI,WAAW,MAAM,KAAK,OAAO;AAAA,MAC1C,GAAG,IAAI;AAEP,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,aAAa,SAAU,OAAO,SAAS;AACrD,UAAI,EAAE,iBAAiB,QAAQ;AAC7B,cAAM,IAAI,UAAU,qBAAqB;AAAA,MAC3C;AAEA,aAAO,KAAK,IAAI,KAAK,SAAU,iBAAiB;AAC9C,eACE,cAAc,iBAAiB,OAAO,KACtC,MAAM,IAAI,KAAK,SAAU,kBAAkB;AACzC,iBACE,cAAc,kBAAkB,OAAO,KACvC,gBAAgB,MAAM,SAAU,gBAAgB;AAC9C,mBAAO,iBAAiB,MAAM,SAAU,iBAAiB;AACvD,qBAAO,eAAe,WAAW,iBAAiB,OAAO;AAAA,YAC3D,CAAC;AAAA,UACH,CAAC;AAAA,QAEL,CAAC;AAAA,MAEL,CAAC;AAAA,IACH;AAIA,aAAS,cAAe,aAAa,SAAS;AAC5C,UAAI,SAAS;AACb,UAAI,uBAAuB,YAAY,MAAM;AAC7C,UAAI,iBAAiB,qBAAqB,IAAI;AAE9C,aAAO,UAAU,qBAAqB,QAAQ;AAC5C,iBAAS,qBAAqB,MAAM,SAAU,iBAAiB;AAC7D,iBAAO,eAAe,WAAW,iBAAiB,OAAO;AAAA,QAC3D,CAAC;AAED,yBAAiB,qBAAqB,IAAI;AAAA,MAC5C;AAEA,aAAO;AAAA,IACT;AAGA,YAAQ,gBAAgB;AACxB,aAAS,cAAe,OAAO,SAAS;AACtC,aAAO,IAAI,MAAM,OAAO,OAAO,EAAE,IAAI,IAAI,SAAU,MAAM;AACvD,eAAO,KAAK,IAAI,SAAU,GAAG;AAC3B,iBAAO,EAAE;AAAA,QACX,CAAC,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE,MAAM,GAAG;AAAA,MAC/B,CAAC;AAAA,IACH;AAKA,aAAS,gBAAiB,MAAM,SAAS;AACvC,YAAM,QAAQ,MAAM,OAAO;AAC3B,aAAO,cAAc,MAAM,OAAO;AAClC,YAAM,SAAS,IAAI;AACnB,aAAO,cAAc,MAAM,OAAO;AAClC,YAAM,UAAU,IAAI;AACpB,aAAO,eAAe,MAAM,OAAO;AACnC,YAAM,UAAU,IAAI;AACpB,aAAO,aAAa,MAAM,OAAO;AACjC,YAAM,SAAS,IAAI;AACnB,aAAO;AAAA,IACT;AAEA,aAAS,IAAK,IAAI;AAChB,aAAO,CAAC,MAAM,GAAG,YAAY,MAAM,OAAO,OAAO;AAAA,IACnD;AAQA,aAAS,cAAe,MAAM,SAAS;AACrC,aAAO,KAAK,KAAK,EAAE,MAAM,KAAK,EAAE,IAAI,SAAUC,OAAM;AAClD,eAAO,aAAaA,OAAM,OAAO;AAAA,MACnC,CAAC,EAAE,KAAK,GAAG;AAAA,IACb;AAEA,aAAS,aAAc,MAAM,SAAS;AACpC,UAAI,IAAI,QAAQ,QAAQ,OAAO,EAAE,UAAU,IAAI,OAAO,EAAE,KAAK;AAC7D,aAAO,KAAK,QAAQ,GAAG,SAAU,GAAG,GAAG,GAAG,GAAG,IAAI;AAC/C,cAAM,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE;AACnC,YAAI;AAEJ,YAAI,IAAI,CAAC,GAAG;AACV,gBAAM;AAAA,QACR,WAAW,IAAI,CAAC,GAAG;AACjB,gBAAM,OAAO,IAAI,YAAY,CAAC,IAAI,KAAK;AAAA,QACzC,WAAW,IAAI,CAAC,GAAG;AAEjB,gBAAM,OAAO,IAAI,MAAM,IAAI,SAAS,IAAI,OAAO,CAAC,IAAI,KAAK;AAAA,QAC3D,WAAW,IAAI;AACb,gBAAM,mBAAmB,EAAE;AAC3B,gBAAM,OAAO,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,KACrC,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK;AAAA,QACpC,OAAO;AAEL,gBAAM,OAAO,IAAI,MAAM,IAAI,MAAM,IAC3B,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK;AAAA,QACpC;AAEA,cAAM,gBAAgB,GAAG;AACzB,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAQA,aAAS,cAAe,MAAM,SAAS;AACrC,aAAO,KAAK,KAAK,EAAE,MAAM,KAAK,EAAE,IAAI,SAAUA,OAAM;AAClD,eAAO,aAAaA,OAAM,OAAO;AAAA,MACnC,CAAC,EAAE,KAAK,GAAG;AAAA,IACb;AAEA,aAAS,aAAc,MAAM,SAAS;AACpC,YAAM,SAAS,MAAM,OAAO;AAC5B,UAAI,IAAI,QAAQ,QAAQ,OAAO,EAAE,UAAU,IAAI,OAAO,EAAE,KAAK;AAC7D,aAAO,KAAK,QAAQ,GAAG,SAAU,GAAG,GAAG,GAAG,GAAG,IAAI;AAC/C,cAAM,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE;AACnC,YAAI;AAEJ,YAAI,IAAI,CAAC,GAAG;AACV,gBAAM;AAAA,QACR,WAAW,IAAI,CAAC,GAAG;AACjB,gBAAM,OAAO,IAAI,YAAY,CAAC,IAAI,KAAK;AAAA,QACzC,WAAW,IAAI,CAAC,GAAG;AACjB,cAAI,MAAM,KAAK;AACb,kBAAM,OAAO,IAAI,MAAM,IAAI,SAAS,IAAI,OAAO,CAAC,IAAI,KAAK;AAAA,UAC3D,OAAO;AACL,kBAAM,OAAO,IAAI,MAAM,IAAI,UAAU,CAAC,IAAI,KAAK;AAAA,UACjD;AAAA,QACF,WAAW,IAAI;AACb,gBAAM,mBAAmB,EAAE;AAC3B,cAAI,MAAM,KAAK;AACb,gBAAI,MAAM,KAAK;AACb,oBAAM,OAAO,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,KACrC,OAAO,IAAI,MAAM,IAAI,OAAO,CAAC,IAAI;AAAA,YACzC,OAAO;AACL,oBAAM,OAAO,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,KACrC,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK;AAAA,YACpC;AAAA,UACF,OAAO;AACL,kBAAM,OAAO,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,KACrC,QAAQ,CAAC,IAAI,KAAK;AAAA,UAC1B;AAAA,QACF,OAAO;AACL,gBAAM,OAAO;AACb,cAAI,MAAM,KAAK;AACb,gBAAI,MAAM,KAAK;AACb,oBAAM,OAAO,IAAI,MAAM,IAAI,MAAM,IAC3B,OAAO,IAAI,MAAM,IAAI,OAAO,CAAC,IAAI;AAAA,YACzC,OAAO;AACL,oBAAM,OAAO,IAAI,MAAM,IAAI,MAAM,IAC3B,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK;AAAA,YACpC;AAAA,UACF,OAAO;AACL,kBAAM,OAAO,IAAI,MAAM,IAAI,MAAM,IAC3B,QAAQ,CAAC,IAAI,KAAK;AAAA,UAC1B;AAAA,QACF;AAEA,cAAM,gBAAgB,GAAG;AACzB,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,aAAS,eAAgB,MAAM,SAAS;AACtC,YAAM,kBAAkB,MAAM,OAAO;AACrC,aAAO,KAAK,MAAM,KAAK,EAAE,IAAI,SAAUA,OAAM;AAC3C,eAAO,cAAcA,OAAM,OAAO;AAAA,MACpC,CAAC,EAAE,KAAK,GAAG;AAAA,IACb;AAEA,aAAS,cAAe,MAAM,SAAS;AACrC,aAAO,KAAK,KAAK;AACjB,UAAI,IAAI,QAAQ,QAAQ,OAAO,EAAE,WAAW,IAAI,OAAO,EAAE,MAAM;AAC/D,aAAO,KAAK,QAAQ,GAAG,SAAU,KAAK,MAAM,GAAG,GAAG,GAAG,IAAI;AACvD,cAAM,UAAU,MAAM,KAAK,MAAM,GAAG,GAAG,GAAG,EAAE;AAC5C,YAAI,KAAK,IAAI,CAAC;AACd,YAAI,KAAK,MAAM,IAAI,CAAC;AACpB,YAAI,KAAK,MAAM,IAAI,CAAC;AACpB,YAAI,OAAO;AAEX,YAAI,SAAS,OAAO,MAAM;AACxB,iBAAO;AAAA,QACT;AAIA,aAAK,QAAQ,oBAAoB,OAAO;AAExC,YAAI,IAAI;AACN,cAAI,SAAS,OAAO,SAAS,KAAK;AAEhC,kBAAM;AAAA,UACR,OAAO;AAEL,kBAAM;AAAA,UACR;AAAA,QACF,WAAW,QAAQ,MAAM;AAGvB,cAAI,IAAI;AACN,gBAAI;AAAA,UACN;AACA,cAAI;AAEJ,cAAI,SAAS,KAAK;AAIhB,mBAAO;AACP,gBAAI,IAAI;AACN,kBAAI,CAAC,IAAI;AACT,kBAAI;AACJ,kBAAI;AAAA,YACN,OAAO;AACL,kBAAI,CAAC,IAAI;AACT,kBAAI;AAAA,YACN;AAAA,UACF,WAAW,SAAS,MAAM;AAGxB,mBAAO;AACP,gBAAI,IAAI;AACN,kBAAI,CAAC,IAAI;AAAA,YACX,OAAO;AACL,kBAAI,CAAC,IAAI;AAAA,YACX;AAAA,UACF;AAEA,gBAAM,OAAO,IAAI,MAAM,IAAI,MAAM,IAAI;AAAA,QACvC,WAAW,IAAI;AACb,gBAAM,OAAO,IAAI,SAAS,KAAK,QAAQ,CAAC,IAAI,KAAK,SAAS;AAAA,QAC5D,WAAW,IAAI;AACb,gBAAM,OAAO,IAAI,MAAM,IAAI,OAAO,KAChC,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO;AAAA,QACvC;AAEA,cAAM,iBAAiB,GAAG;AAE1B,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAIA,aAAS,aAAc,MAAM,SAAS;AACpC,YAAM,gBAAgB,MAAM,OAAO;AAEnC,aAAO,KAAK,KAAK,EAAE,QAAQ,OAAO,EAAE,IAAI,GAAG,EAAE;AAAA,IAC/C;AAOA,aAAS,cAAe,IACtB,MAAM,IAAI,IAAI,IAAI,KAAK,IACvB,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI;AACzB,UAAI,IAAI,EAAE,GAAG;AACX,eAAO;AAAA,MACT,WAAW,IAAI,EAAE,GAAG;AAClB,eAAO,OAAO,KAAK;AAAA,MACrB,WAAW,IAAI,EAAE,GAAG;AAClB,eAAO,OAAO,KAAK,MAAM,KAAK;AAAA,MAChC,OAAO;AACL,eAAO,OAAO;AAAA,MAChB;AAEA,UAAI,IAAI,EAAE,GAAG;AACX,aAAK;AAAA,MACP,WAAW,IAAI,EAAE,GAAG;AAClB,aAAK,OAAO,CAAC,KAAK,KAAK;AAAA,MACzB,WAAW,IAAI,EAAE,GAAG;AAClB,aAAK,MAAM,KAAK,OAAO,CAAC,KAAK,KAAK;AAAA,MACpC,WAAW,KAAK;AACd,aAAK,OAAO,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAAA,MAC/C,OAAO;AACL,aAAK,OAAO;AAAA,MACd;AAEA,cAAQ,OAAO,MAAM,IAAI,KAAK;AAAA,IAChC;AAGA,UAAM,UAAU,OAAO,SAAU,SAAS;AACxC,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,YAAY,UAAU;AAC/B,YAAI;AACF,oBAAU,IAAI,OAAO,SAAS,KAAK,OAAO;AAAA,QAC5C,SAAS,IAAI;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAASF,KAAI,GAAGA,KAAI,KAAK,IAAI,QAAQA,MAAK;AACxC,YAAI,QAAQ,KAAK,IAAIA,EAAC,GAAG,SAAS,KAAK,OAAO,GAAG;AAC/C,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,QAAS,KAAK,SAAS,SAAS;AACvC,eAASA,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACnC,YAAI,CAAC,IAAIA,EAAC,EAAE,KAAK,OAAO,GAAG;AACzB,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,QAAQ,WAAW,UAAU,CAAC,QAAQ,mBAAmB;AAM3D,aAAKA,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AAC/B,gBAAM,IAAIA,EAAC,EAAE,MAAM;AACnB,cAAI,IAAIA,EAAC,EAAE,WAAW,KAAK;AACzB;AAAA,UACF;AAEA,cAAI,IAAIA,EAAC,EAAE,OAAO,WAAW,SAAS,GAAG;AACvC,gBAAI,UAAU,IAAIA,EAAC,EAAE;AACrB,gBAAI,QAAQ,UAAU,QAAQ,SAC1B,QAAQ,UAAU,QAAQ,SAC1B,QAAQ,UAAU,QAAQ,OAAO;AACnC,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAGA,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,YAAY;AACpB,aAAS,UAAW,SAAS,OAAO,SAAS;AAC3C,UAAI;AACF,gBAAQ,IAAI,MAAM,OAAO,OAAO;AAAA,MAClC,SAAS,IAAI;AACX,eAAO;AAAA,MACT;AACA,aAAO,MAAM,KAAK,OAAO;AAAA,IAC3B;AAEA,YAAQ,gBAAgB;AACxB,aAAS,cAAe,UAAU,OAAO,SAAS;AAChD,UAAI,MAAM;AACV,UAAI,QAAQ;AACZ,UAAI;AACF,YAAI,WAAW,IAAI,MAAM,OAAO,OAAO;AAAA,MACzC,SAAS,IAAI;AACX,eAAO;AAAA,MACT;AACA,eAAS,QAAQ,SAAU,GAAG;AAC5B,YAAI,SAAS,KAAK,CAAC,GAAG;AAEpB,cAAI,CAAC,OAAO,MAAM,QAAQ,CAAC,MAAM,IAAI;AAEnC,kBAAM;AACN,oBAAQ,IAAI,OAAO,KAAK,OAAO;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAEA,YAAQ,gBAAgB;AACxB,aAAS,cAAe,UAAU,OAAO,SAAS;AAChD,UAAI,MAAM;AACV,UAAI,QAAQ;AACZ,UAAI;AACF,YAAI,WAAW,IAAI,MAAM,OAAO,OAAO;AAAA,MACzC,SAAS,IAAI;AACX,eAAO;AAAA,MACT;AACA,eAAS,QAAQ,SAAU,GAAG;AAC5B,YAAI,SAAS,KAAK,CAAC,GAAG;AAEpB,cAAI,CAAC,OAAO,MAAM,QAAQ,CAAC,MAAM,GAAG;AAElC,kBAAM;AACN,oBAAQ,IAAI,OAAO,KAAK,OAAO;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAEA,YAAQ,aAAa;AACrB,aAAS,WAAY,OAAO,OAAO;AACjC,cAAQ,IAAI,MAAM,OAAO,KAAK;AAE9B,UAAI,SAAS,IAAI,OAAO,OAAO;AAC/B,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,eAAO;AAAA,MACT;AAEA,eAAS,IAAI,OAAO,SAAS;AAC7B,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,eAAO;AAAA,MACT;AAEA,eAAS;AACT,eAASA,KAAI,GAAGA,KAAI,MAAM,IAAI,QAAQ,EAAEA,IAAG;AACzC,YAAI,cAAc,MAAM,IAAIA,EAAC;AAE7B,oBAAY,QAAQ,SAAU,YAAY;AAExC,cAAI,UAAU,IAAI,OAAO,WAAW,OAAO,OAAO;AAClD,kBAAQ,WAAW,UAAU;AAAA,YAC3B,KAAK;AACH,kBAAI,QAAQ,WAAW,WAAW,GAAG;AACnC,wBAAQ;AAAA,cACV,OAAO;AACL,wBAAQ,WAAW,KAAK,CAAC;AAAA,cAC3B;AACA,sBAAQ,MAAM,QAAQ,OAAO;AAAA;AAAA,YAE/B,KAAK;AAAA,YACL,KAAK;AACH,kBAAI,CAAC,UAAU,GAAG,QAAQ,OAAO,GAAG;AAClC,yBAAS;AAAA,cACX;AACA;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AAEH;AAAA;AAAA,YAEF;AACE,oBAAM,IAAI,MAAM,2BAA2B,WAAW,QAAQ;AAAA,UAClE;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI,UAAU,MAAM,KAAK,MAAM,GAAG;AAChC,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,aAAa;AACrB,aAAS,WAAY,OAAO,SAAS;AACnC,UAAI;AAGF,eAAO,IAAI,MAAM,OAAO,OAAO,EAAE,SAAS;AAAA,MAC5C,SAAS,IAAI;AACX,eAAO;AAAA,MACT;AAAA,IACF;AAGA,YAAQ,MAAM;AACd,aAAS,IAAK,SAAS,OAAO,SAAS;AACrC,aAAO,QAAQ,SAAS,OAAO,KAAK,OAAO;AAAA,IAC7C;AAGA,YAAQ,MAAM;AACd,aAAS,IAAK,SAAS,OAAO,SAAS;AACrC,aAAO,QAAQ,SAAS,OAAO,KAAK,OAAO;AAAA,IAC7C;AAEA,YAAQ,UAAU;AAClB,aAAS,QAAS,SAAS,OAAO,MAAM,SAAS;AAC/C,gBAAU,IAAI,OAAO,SAAS,OAAO;AACrC,cAAQ,IAAI,MAAM,OAAO,OAAO;AAEhC,UAAI,MAAM,OAAO,MAAM,MAAM;AAC7B,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AACP,kBAAQ;AACR,iBAAO;AACP,iBAAO;AACP,kBAAQ;AACR;AAAA,QACF,KAAK;AACH,iBAAO;AACP,kBAAQ;AACR,iBAAO;AACP,iBAAO;AACP,kBAAQ;AACR;AAAA,QACF;AACE,gBAAM,IAAI,UAAU,uCAAuC;AAAA,MAC/D;AAGA,UAAI,UAAU,SAAS,OAAO,OAAO,GAAG;AACtC,eAAO;AAAA,MACT;AAKA,eAASA,KAAI,GAAGA,KAAI,MAAM,IAAI,QAAQ,EAAEA,IAAG;AACzC,YAAI,cAAc,MAAM,IAAIA,EAAC;AAE7B,YAAI,OAAO;AACX,YAAI,MAAM;AAEV,oBAAY,QAAQ,SAAU,YAAY;AACxC,cAAI,WAAW,WAAW,KAAK;AAC7B,yBAAa,IAAI,WAAW,SAAS;AAAA,UACvC;AACA,iBAAO,QAAQ;AACf,gBAAM,OAAO;AACb,cAAI,KAAK,WAAW,QAAQ,KAAK,QAAQ,OAAO,GAAG;AACjD,mBAAO;AAAA,UACT,WAAW,KAAK,WAAW,QAAQ,IAAI,QAAQ,OAAO,GAAG;AACvD,kBAAM;AAAA,UACR;AAAA,QACF,CAAC;AAID,YAAI,KAAK,aAAa,QAAQ,KAAK,aAAa,OAAO;AACrD,iBAAO;AAAA,QACT;AAIA,aAAK,CAAC,IAAI,YAAY,IAAI,aAAa,SACnC,MAAM,SAAS,IAAI,MAAM,GAAG;AAC9B,iBAAO;AAAA,QACT,WAAW,IAAI,aAAa,SAAS,KAAK,SAAS,IAAI,MAAM,GAAG;AAC9D,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,aAAa;AACrB,aAAS,WAAY,SAAS,SAAS;AACrC,UAAI,SAAS,MAAM,SAAS,OAAO;AACnC,aAAQ,UAAU,OAAO,WAAW,SAAU,OAAO,aAAa;AAAA,IACpE;AAEA,YAAQ,aAAa;AACrB,aAAS,WAAY,IAAI,IAAI,SAAS;AACpC,WAAK,IAAI,MAAM,IAAI,OAAO;AAC1B,WAAK,IAAI,MAAM,IAAI,OAAO;AAC1B,aAAO,GAAG,WAAW,EAAE;AAAA,IACzB;AAEA,YAAQ,SAAS;AACjB,aAAS,OAAQ,SAAS,SAAS;AACjC,UAAI,mBAAmB,QAAQ;AAC7B,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,YAAY,UAAU;AAC/B,kBAAU,OAAO,OAAO;AAAA,MAC1B;AAEA,UAAI,OAAO,YAAY,UAAU;AAC/B,eAAO;AAAA,MACT;AAEA,gBAAU,WAAW,CAAC;AAEtB,UAAI,QAAQ;AACZ,UAAI,CAAC,QAAQ,KAAK;AAChB,gBAAQ,QAAQ,MAAM,OAAO,EAAE,MAAM,CAAC;AAAA,MACxC,OAAO;AASL,YAAI;AACJ,gBAAQ,OAAO,OAAO,EAAE,SAAS,EAAE,KAAK,OAAO,OAC5C,CAAC,SAAS,MAAM,QAAQ,MAAM,CAAC,EAAE,WAAW,QAAQ,SACrD;AACA,cAAI,CAAC,SACD,KAAK,QAAQ,KAAK,CAAC,EAAE,WAAW,MAAM,QAAQ,MAAM,CAAC,EAAE,QAAQ;AACjE,oBAAQ;AAAA,UACV;AACA,iBAAO,EAAE,SAAS,EAAE,YAAY,KAAK,QAAQ,KAAK,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE;AAAA,QACxE;AAEA,eAAO,EAAE,SAAS,EAAE,YAAY;AAAA,MAClC;AAEA,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA,MACT;AAEA,aAAO,MAAM,MAAM,CAAC,IAClB,OAAO,MAAM,CAAC,KAAK,OACnB,OAAO,MAAM,CAAC,KAAK,MAAM,OAAO;AAAA,IACpC;AAAA;AAAA;", "names": ["i", "range", "comp"]}