import {
  applyDecorators
} from "./chunk-3V3V5ILP.js";
import {
  Tn,
  pt
} from "./chunk-TTAPWIZ2.js";
import {
  require_client_logger
} from "./chunk-LHOSQLWN.js";
import {
  require_react
} from "./chunk-AZZHOME7.js";
import {
  require_preview_api
} from "./chunk-ZLWVYHQP.js";
import {
  __commonJS,
  __export,
  __toESM as __toESM2
} from "./chunk-DF7VAP3D.js";
import {
  __toESM
} from "./chunk-7D4SUZUM.js";

// node_modules/@storybook/react/dist/chunk-6BNVLEVL.mjs
var React__default = __toESM(require_react(), 1);
var import_react = __toESM(require_react(), 1);
var require_dist = __commonJS({ "../../node_modules/@base2/pretty-print-object/dist/index.js"(exports) {
  var __assign = exports && exports.__assign || function() {
    return __assign = Object.assign || function(t) {
      for (var s, i = 1, n = arguments.length; i < n; i++) {
        s = arguments[i];
        for (var p in s) Object.prototype.hasOwnProperty.call(s, p) && (t[p] = s[p]);
      }
      return t;
    }, __assign.apply(this, arguments);
  }, __spreadArrays = exports && exports.__spreadArrays || function() {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++) for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++) r[k] = a[j];
    return r;
  };
  Object.defineProperty(exports, "__esModule", { value: true });
  var seen = [];
  function isObj(value) {
    var type = typeof value;
    return value !== null && (type === "object" || type === "function");
  }
  function isRegexp(value) {
    return Object.prototype.toString.call(value) === "[object RegExp]";
  }
  function getOwnEnumPropSymbols(object) {
    return Object.getOwnPropertySymbols(object).filter(function(keySymbol) {
      return Object.prototype.propertyIsEnumerable.call(object, keySymbol);
    });
  }
  function prettyPrint2(input, options, pad) {
    pad === void 0 && (pad = "");
    var defaultOptions = { indent: "	", singleQuotes: true }, combinedOptions = __assign(__assign({}, defaultOptions), options), tokens;
    combinedOptions.inlineCharacterLimit === void 0 ? tokens = { newLine: `
`, newLineOrSpace: `
`, pad, indent: pad + combinedOptions.indent } : tokens = { newLine: "@@__PRETTY_PRINT_NEW_LINE__@@", newLineOrSpace: "@@__PRETTY_PRINT_NEW_LINE_OR_SPACE__@@", pad: "@@__PRETTY_PRINT_PAD__@@", indent: "@@__PRETTY_PRINT_INDENT__@@" };
    var expandWhiteSpace = function(string) {
      if (combinedOptions.inlineCharacterLimit === void 0) return string;
      var oneLined = string.replace(new RegExp(tokens.newLine, "g"), "").replace(new RegExp(tokens.newLineOrSpace, "g"), " ").replace(new RegExp(tokens.pad + "|" + tokens.indent, "g"), "");
      return oneLined.length <= combinedOptions.inlineCharacterLimit ? oneLined : string.replace(new RegExp(tokens.newLine + "|" + tokens.newLineOrSpace, "g"), `
`).replace(new RegExp(tokens.pad, "g"), pad).replace(new RegExp(tokens.indent, "g"), pad + combinedOptions.indent);
    };
    if (seen.indexOf(input) !== -1) return '"[Circular]"';
    if (input == null || typeof input == "number" || typeof input == "boolean" || typeof input == "function" || typeof input == "symbol" || isRegexp(input)) return String(input);
    if (input instanceof Date) return "new Date('" + input.toISOString() + "')";
    if (Array.isArray(input)) {
      if (input.length === 0) return "[]";
      seen.push(input);
      var ret = "[" + tokens.newLine + input.map(function(el, i) {
        var eol = input.length - 1 === i ? tokens.newLine : "," + tokens.newLineOrSpace, value = prettyPrint2(el, combinedOptions, pad + combinedOptions.indent);
        return combinedOptions.transform && (value = combinedOptions.transform(input, i, value)), tokens.indent + value + eol;
      }).join("") + tokens.pad + "]";
      return seen.pop(), expandWhiteSpace(ret);
    }
    if (isObj(input)) {
      var objKeys_1 = __spreadArrays(Object.keys(input), getOwnEnumPropSymbols(input));
      if (combinedOptions.filter && (objKeys_1 = objKeys_1.filter(function(el) {
        return combinedOptions.filter && combinedOptions.filter(input, el);
      })), objKeys_1.length === 0) return "{}";
      seen.push(input);
      var ret = "{" + tokens.newLine + objKeys_1.map(function(el, i) {
        var eol = objKeys_1.length - 1 === i ? tokens.newLine : "," + tokens.newLineOrSpace, isSymbol = typeof el == "symbol", isClassic = !isSymbol && /^[a-z$_][a-z$_0-9]*$/i.test(el.toString()), key = isSymbol || isClassic ? el : prettyPrint2(el, combinedOptions), value = prettyPrint2(input[el], combinedOptions, pad + combinedOptions.indent);
        return combinedOptions.transform && (value = combinedOptions.transform(input, el, value)), tokens.indent + String(key) + ": " + value + eol;
      }).join("") + tokens.pad + "}";
      return seen.pop(), expandWhiteSpace(ret);
    }
    return input = String(input).replace(/[\r\n]/g, function(x) {
      return x === `
` ? "\\n" : "\\r";
    }), combinedOptions.singleQuotes ? (input = input.replace(/\\?'/g, "\\'"), "'" + input + "'") : (input = input.replace(/"/g, '\\"'), '"' + input + '"');
  }
  exports.prettyPrint = prettyPrint2;
} });
var require_react_is_production_min = __commonJS({ "../../node_modules/react-element-to-jsx-string/node_modules/react-is/cjs/react-is.production.min.js"(exports) {
  var b = Symbol.for("react.element"), c = Symbol.for("react.portal"), d = Symbol.for("react.fragment"), e = Symbol.for("react.strict_mode"), f = Symbol.for("react.profiler"), g = Symbol.for("react.provider"), h = Symbol.for("react.context"), k = Symbol.for("react.server_context"), l = Symbol.for("react.forward_ref"), m = Symbol.for("react.suspense"), n = Symbol.for("react.suspense_list"), p = Symbol.for("react.memo"), q = Symbol.for("react.lazy"), t = Symbol.for("react.offscreen"), u;
  u = Symbol.for("react.module.reference");
  function v(a) {
    if (typeof a == "object" && a !== null) {
      var r = a.$$typeof;
      switch (r) {
        case b:
          switch (a = a.type, a) {
            case d:
            case f:
            case e:
            case m:
            case n:
              return a;
            default:
              switch (a = a && a.$$typeof, a) {
                case k:
                case h:
                case l:
                case q:
                case p:
                case g:
                  return a;
                default:
                  return r;
              }
          }
        case c:
          return r;
      }
    }
  }
  exports.ContextConsumer = h;
  exports.ContextProvider = g;
  exports.Element = b;
  exports.ForwardRef = l;
  exports.Fragment = d;
  exports.Lazy = q;
  exports.Memo = p;
  exports.Portal = c;
  exports.Profiler = f;
  exports.StrictMode = e;
  exports.Suspense = m;
  exports.SuspenseList = n;
  exports.isAsyncMode = function() {
    return false;
  };
  exports.isConcurrentMode = function() {
    return false;
  };
  exports.isContextConsumer = function(a) {
    return v(a) === h;
  };
  exports.isContextProvider = function(a) {
    return v(a) === g;
  };
  exports.isElement = function(a) {
    return typeof a == "object" && a !== null && a.$$typeof === b;
  };
  exports.isForwardRef = function(a) {
    return v(a) === l;
  };
  exports.isFragment = function(a) {
    return v(a) === d;
  };
  exports.isLazy = function(a) {
    return v(a) === q;
  };
  exports.isMemo = function(a) {
    return v(a) === p;
  };
  exports.isPortal = function(a) {
    return v(a) === c;
  };
  exports.isProfiler = function(a) {
    return v(a) === f;
  };
  exports.isStrictMode = function(a) {
    return v(a) === e;
  };
  exports.isSuspense = function(a) {
    return v(a) === m;
  };
  exports.isSuspenseList = function(a) {
    return v(a) === n;
  };
  exports.isValidElementType = function(a) {
    return typeof a == "string" || typeof a == "function" || a === d || a === f || a === e || a === m || a === n || a === t || typeof a == "object" && a !== null && (a.$$typeof === q || a.$$typeof === p || a.$$typeof === g || a.$$typeof === h || a.$$typeof === l || a.$$typeof === u || a.getModuleId !== void 0);
  };
  exports.typeOf = v;
} });
var require_react_is_development = __commonJS({ "../../node_modules/react-element-to-jsx-string/node_modules/react-is/cjs/react-is.development.js"(exports) {
  (function() {
    var enableScopeAPI = false, enableCacheElement = false, enableTransitionTracing = false, enableLegacyHidden = false, enableDebugTracing = false, REACT_ELEMENT_TYPE = Symbol.for("react.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler"), REACT_PROVIDER_TYPE = Symbol.for("react.provider"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_SERVER_CONTEXT_TYPE = Symbol.for("react.server_context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_OFFSCREEN_TYPE = Symbol.for("react.offscreen"), REACT_MODULE_REFERENCE;
    REACT_MODULE_REFERENCE = Symbol.for("react.module.reference");
    function isValidElementType(type) {
      return !!(typeof type == "string" || typeof type == "function" || type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden || type === REACT_OFFSCREEN_TYPE || enableScopeAPI || enableCacheElement || enableTransitionTracing || typeof type == "object" && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== void 0));
    }
    function typeOf(object) {
      if (typeof object == "object" && object !== null) {
        var $$typeof = object.$$typeof;
        switch ($$typeof) {
          case REACT_ELEMENT_TYPE:
            var type = object.type;
            switch (type) {
              case REACT_FRAGMENT_TYPE:
              case REACT_PROFILER_TYPE:
              case REACT_STRICT_MODE_TYPE:
              case REACT_SUSPENSE_TYPE:
              case REACT_SUSPENSE_LIST_TYPE:
                return type;
              default:
                var $$typeofType = type && type.$$typeof;
                switch ($$typeofType) {
                  case REACT_SERVER_CONTEXT_TYPE:
                  case REACT_CONTEXT_TYPE:
                  case REACT_FORWARD_REF_TYPE:
                  case REACT_LAZY_TYPE:
                  case REACT_MEMO_TYPE:
                  case REACT_PROVIDER_TYPE:
                    return $$typeofType;
                  default:
                    return $$typeof;
                }
            }
          case REACT_PORTAL_TYPE:
            return $$typeof;
        }
      }
    }
    var ContextConsumer = REACT_CONTEXT_TYPE, ContextProvider = REACT_PROVIDER_TYPE, Element = REACT_ELEMENT_TYPE, ForwardRef2 = REACT_FORWARD_REF_TYPE, Fragment2 = REACT_FRAGMENT_TYPE, Lazy = REACT_LAZY_TYPE, Memo2 = REACT_MEMO_TYPE, Portal = REACT_PORTAL_TYPE, Profiler = REACT_PROFILER_TYPE, StrictMode = REACT_STRICT_MODE_TYPE, Suspense = REACT_SUSPENSE_TYPE, SuspenseList = REACT_SUSPENSE_LIST_TYPE, hasWarnedAboutDeprecatedIsAsyncMode = false, hasWarnedAboutDeprecatedIsConcurrentMode = false;
    function isAsyncMode(object) {
      return hasWarnedAboutDeprecatedIsAsyncMode || (hasWarnedAboutDeprecatedIsAsyncMode = true, console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 18+.")), false;
    }
    function isConcurrentMode(object) {
      return hasWarnedAboutDeprecatedIsConcurrentMode || (hasWarnedAboutDeprecatedIsConcurrentMode = true, console.warn("The ReactIs.isConcurrentMode() alias has been deprecated, and will be removed in React 18+.")), false;
    }
    function isContextConsumer2(object) {
      return typeOf(object) === REACT_CONTEXT_TYPE;
    }
    function isContextProvider2(object) {
      return typeOf(object) === REACT_PROVIDER_TYPE;
    }
    function isElement(object) {
      return typeof object == "object" && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;
    }
    function isForwardRef3(object) {
      return typeOf(object) === REACT_FORWARD_REF_TYPE;
    }
    function isFragment(object) {
      return typeOf(object) === REACT_FRAGMENT_TYPE;
    }
    function isLazy2(object) {
      return typeOf(object) === REACT_LAZY_TYPE;
    }
    function isMemo3(object) {
      return typeOf(object) === REACT_MEMO_TYPE;
    }
    function isPortal(object) {
      return typeOf(object) === REACT_PORTAL_TYPE;
    }
    function isProfiler2(object) {
      return typeOf(object) === REACT_PROFILER_TYPE;
    }
    function isStrictMode2(object) {
      return typeOf(object) === REACT_STRICT_MODE_TYPE;
    }
    function isSuspense2(object) {
      return typeOf(object) === REACT_SUSPENSE_TYPE;
    }
    function isSuspenseList(object) {
      return typeOf(object) === REACT_SUSPENSE_LIST_TYPE;
    }
    exports.ContextConsumer = ContextConsumer, exports.ContextProvider = ContextProvider, exports.Element = Element, exports.ForwardRef = ForwardRef2, exports.Fragment = Fragment2, exports.Lazy = Lazy, exports.Memo = Memo2, exports.Portal = Portal, exports.Profiler = Profiler, exports.StrictMode = StrictMode, exports.Suspense = Suspense, exports.SuspenseList = SuspenseList, exports.isAsyncMode = isAsyncMode, exports.isConcurrentMode = isConcurrentMode, exports.isContextConsumer = isContextConsumer2, exports.isContextProvider = isContextProvider2, exports.isElement = isElement, exports.isForwardRef = isForwardRef3, exports.isFragment = isFragment, exports.isLazy = isLazy2, exports.isMemo = isMemo3, exports.isPortal = isPortal, exports.isProfiler = isProfiler2, exports.isStrictMode = isStrictMode2, exports.isSuspense = isSuspense2, exports.isSuspenseList = isSuspenseList, exports.isValidElementType = isValidElementType, exports.typeOf = typeOf;
  })();
} });
var require_react_is = __commonJS({ "../../node_modules/react-element-to-jsx-string/node_modules/react-is/index.js"(exports, module) {
  false ? module.exports = require_react_is_production_min() : module.exports = require_react_is_development();
} });
var isMemo = (component) => component.$$typeof === Symbol.for("react.memo");
var isForwardRef = (component) => component.$$typeof === Symbol.for("react.forward_ref");
function isObject(o) {
  return Object.prototype.toString.call(o) === "[object Object]";
}
function isPlainObject(o) {
  var ctor, prot;
  return isObject(o) === false ? false : (ctor = o.constructor, ctor === void 0 ? true : (prot = ctor.prototype, !(isObject(prot) === false || prot.hasOwnProperty("isPrototypeOf") === false)));
}
var import_pretty_print_object = __toESM2(require_dist());
var import_react_is = __toESM2(require_react_is());
var spacer = function(times, tabStop) {
  return times === 0 ? "" : new Array(times * tabStop).fill(" ").join("");
};
function _arrayLikeToArray(r, a) {
  (a == null || a > r.length) && (a = r.length);
  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];
  return n;
}
function _arrayWithoutHoles(r) {
  if (Array.isArray(r)) return _arrayLikeToArray(r);
}
function _defineProperty(e, r, t) {
  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e;
}
function _iterableToArray(r) {
  if (typeof Symbol < "u" && r[Symbol.iterator] != null || r["@@iterator"] != null) return Array.from(r);
}
function _nonIterableSpread() {
  throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);
}
function ownKeys(e, r) {
  var t = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    r && (o = o.filter(function(r2) {
      return Object.getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
function _objectSpread2(e) {
  for (var r = 1; r < arguments.length; r++) {
    var t = arguments[r] != null ? arguments[r] : {};
    r % 2 ? ownKeys(Object(t), true).forEach(function(r2) {
      _defineProperty(e, r2, t[r2]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r2) {
      Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
function _toConsumableArray(r) {
  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();
}
function _toPrimitive(t, r) {
  if (typeof t != "object" || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (e !== void 0) {
    var i = e.call(t, r || "default");
    if (typeof i != "object") return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (r === "string" ? String : Number)(t);
}
function _toPropertyKey(t) {
  var i = _toPrimitive(t, "string");
  return typeof i == "symbol" ? i : i + "";
}
function _typeof(o) {
  "@babel/helpers - typeof";
  return _typeof = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && typeof Symbol == "function" && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof(o);
}
function _unsupportedIterableToArray(r, a) {
  if (r) {
    if (typeof r == "string") return _arrayLikeToArray(r, a);
    var t = {}.toString.call(r).slice(8, -1);
    return t === "Object" && r.constructor && (t = r.constructor.name), t === "Map" || t === "Set" ? Array.from(r) : t === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;
  }
}
function safeSortObject(value, seen) {
  if (value === null || _typeof(value) !== "object" || value instanceof Date || value instanceof RegExp) return value;
  if (React__default.isValidElement(value)) {
    var copyObj = _objectSpread2({}, value);
    return delete copyObj._owner, copyObj;
  }
  return seen.add(value), Array.isArray(value) ? value.map(function(v) {
    return safeSortObject(v, seen);
  }) : Object.keys(value).sort().reduce(function(result, key) {
    return key === "current" || seen.has(value[key]) ? result[key] = "[Circular]" : result[key] = safeSortObject(value[key], seen), result;
  }, {});
}
function sortObject(value) {
  return safeSortObject(value, /* @__PURE__ */ new WeakSet());
}
var createStringTreeNode = function(value) {
  return { type: "string", value };
};
var createNumberTreeNode = function(value) {
  return { type: "number", value };
};
var createReactElementTreeNode = function(displayName, props, defaultProps, childrens) {
  return { type: "ReactElement", displayName, props, defaultProps, childrens };
};
var createReactFragmentTreeNode = function(key, childrens) {
  return { type: "ReactFragment", key, childrens };
};
var supportFragment = !!import_react.Fragment;
var getFunctionTypeName = function(functionType) {
  return !functionType.name || functionType.name === "_default" ? "No Display Name" : functionType.name;
};
var _getWrappedComponentDisplayName = function(Component) {
  switch (true) {
    case !!Component.displayName:
      return Component.displayName;
    case Component.$$typeof === import_react_is.Memo:
      return _getWrappedComponentDisplayName(Component.type);
    case Component.$$typeof === import_react_is.ForwardRef:
      return _getWrappedComponentDisplayName(Component.render);
    default:
      return getFunctionTypeName(Component);
  }
};
var getReactElementDisplayName = function(element) {
  switch (true) {
    case typeof element.type == "string":
      return element.type;
    case typeof element.type == "function":
      return element.type.displayName ? element.type.displayName : getFunctionTypeName(element.type);
    case (0, import_react_is.isForwardRef)(element):
    case (0, import_react_is.isMemo)(element):
      return _getWrappedComponentDisplayName(element.type);
    case (0, import_react_is.isContextConsumer)(element):
      return "".concat(element.type._context.displayName || "Context", ".Consumer");
    case (0, import_react_is.isContextProvider)(element):
      return "".concat(element.type._context.displayName || "Context", ".Provider");
    case (0, import_react_is.isLazy)(element):
      return "Lazy";
    case (0, import_react_is.isProfiler)(element):
      return "Profiler";
    case (0, import_react_is.isStrictMode)(element):
      return "StrictMode";
    case (0, import_react_is.isSuspense)(element):
      return "Suspense";
    default:
      return "UnknownElementType";
  }
};
var noChildren = function(propsValue, propName) {
  return propName !== "children";
};
var onlyMeaningfulChildren = function(children) {
  return children !== true && children !== false && children !== null && children !== "";
};
var filterProps = function(originalProps, cb) {
  var filteredProps = {};
  return Object.keys(originalProps).filter(function(key) {
    return cb(originalProps[key], key);
  }).forEach(function(key) {
    return filteredProps[key] = originalProps[key];
  }), filteredProps;
};
var _parseReactElement = function(element, options) {
  var _options$displayName = options.displayName, displayNameFn = _options$displayName === void 0 ? getReactElementDisplayName : _options$displayName;
  if (typeof element == "string") return createStringTreeNode(element);
  if (typeof element == "number") return createNumberTreeNode(element);
  if (!import_react.default.isValidElement(element)) throw new Error("react-element-to-jsx-string: Expected a React.Element, got `".concat(_typeof(element), "`"));
  var displayName = displayNameFn(element), props = filterProps(element.props, noChildren);
  element.ref !== null && (props.ref = element.ref);
  var key = element.key;
  typeof key == "string" && key.search(/^\./) && (props.key = key);
  var defaultProps = filterProps(element.type.defaultProps || {}, noChildren), childrens = import_react.default.Children.toArray(element.props.children).filter(onlyMeaningfulChildren).map(function(child) {
    return _parseReactElement(child, options);
  });
  return supportFragment && element.type === import_react.Fragment ? createReactFragmentTreeNode(key, childrens) : createReactElementTreeNode(displayName, props, defaultProps, childrens);
};
function noRefCheck() {
}
var inlineFunction = function(fn) {
  return fn.toString().split(`
`).map(function(line) {
    return line.trim();
  }).join("");
};
var defaultFunctionValue = inlineFunction;
var formatFunction = function(fn, options) {
  var _options$functionValu = options.functionValue, functionValue = _options$functionValu === void 0 ? defaultFunctionValue : _options$functionValu, showFunctions = options.showFunctions;
  return functionValue(!showFunctions && functionValue === defaultFunctionValue ? noRefCheck : fn);
};
var formatComplexDataStructure = function(value, inline, lvl, options) {
  var normalizedValue = sortObject(value), stringifiedValue = (0, import_pretty_print_object.prettyPrint)(normalizedValue, { transform: function(currentObj, prop, originalResult) {
    var currentValue = currentObj[prop];
    return currentValue && (0, import_react.isValidElement)(currentValue) ? formatTreeNode(_parseReactElement(currentValue, options), true, lvl, options) : typeof currentValue == "function" ? formatFunction(currentValue, options) : originalResult;
  } });
  return inline ? stringifiedValue.replace(/\s+/g, " ").replace(/{ /g, "{").replace(/ }/g, "}").replace(/\[ /g, "[").replace(/ ]/g, "]") : stringifiedValue.replace(/\t/g, spacer(1, options.tabStop)).replace(/\n([^$])/g, `
`.concat(spacer(lvl + 1, options.tabStop), "$1"));
};
var escape$1 = function(s) {
  return s.replace(/"/g, "&quot;");
};
var formatPropValue = function(propValue, inline, lvl, options) {
  if (typeof propValue == "number") return "{".concat(String(propValue), "}");
  if (typeof propValue == "string") return '"'.concat(escape$1(propValue), '"');
  if (_typeof(propValue) === "symbol") {
    var symbolDescription = propValue.valueOf().toString().replace(/Symbol\((.*)\)/, "$1");
    return symbolDescription ? "{Symbol('".concat(symbolDescription, "')}") : "{Symbol()}";
  }
  return typeof propValue == "function" ? "{".concat(formatFunction(propValue, options), "}") : (0, import_react.isValidElement)(propValue) ? "{".concat(formatTreeNode(_parseReactElement(propValue, options), true, lvl, options), "}") : propValue instanceof Date ? isNaN(propValue.valueOf()) ? "{new Date(NaN)}" : '{new Date("'.concat(propValue.toISOString(), '")}') : isPlainObject(propValue) || Array.isArray(propValue) ? "{".concat(formatComplexDataStructure(propValue, inline, lvl, options), "}") : "{".concat(String(propValue), "}");
};
var formatProp = function(name, hasValue, value, hasDefaultValue, defaultValue, inline, lvl, options) {
  if (!hasValue && !hasDefaultValue) throw new Error('The prop "'.concat(name, '" has no value and no default: could not be formatted'));
  var usedValue = hasValue ? value : defaultValue, useBooleanShorthandSyntax = options.useBooleanShorthandSyntax, tabStop = options.tabStop, formattedPropValue = formatPropValue(usedValue, inline, lvl, options), attributeFormattedInline = " ", attributeFormattedMultiline = `
`.concat(spacer(lvl + 1, tabStop)), isMultilineAttribute = formattedPropValue.includes(`
`);
  return useBooleanShorthandSyntax && formattedPropValue === "{false}" && !hasDefaultValue ? (attributeFormattedInline = "", attributeFormattedMultiline = "") : useBooleanShorthandSyntax && formattedPropValue === "{true}" ? (attributeFormattedInline += "".concat(name), attributeFormattedMultiline += "".concat(name)) : (attributeFormattedInline += "".concat(name, "=").concat(formattedPropValue), attributeFormattedMultiline += "".concat(name, "=").concat(formattedPropValue)), { attributeFormattedInline, attributeFormattedMultiline, isMultilineAttribute };
};
var mergeSiblingPlainStringChildrenReducer = function(previousNodes, currentNode) {
  var nodes = previousNodes.slice(0, previousNodes.length > 0 ? previousNodes.length - 1 : 0), previousNode = previousNodes[previousNodes.length - 1];
  return previousNode && (currentNode.type === "string" || currentNode.type === "number") && (previousNode.type === "string" || previousNode.type === "number") ? nodes.push(createStringTreeNode(String(previousNode.value) + String(currentNode.value))) : (previousNode && nodes.push(previousNode), nodes.push(currentNode)), nodes;
};
var isKeyOrRefProps = function(propName) {
  return ["key", "ref"].includes(propName);
};
var sortPropsByNames = function(shouldSortUserProps) {
  return function(props) {
    var haveKeyProp = props.includes("key"), haveRefProp = props.includes("ref"), userPropsOnly = props.filter(function(oneProp) {
      return !isKeyOrRefProps(oneProp);
    }), sortedProps = _toConsumableArray(shouldSortUserProps ? userPropsOnly.sort() : userPropsOnly);
    return haveRefProp && sortedProps.unshift("ref"), haveKeyProp && sortedProps.unshift("key"), sortedProps;
  };
};
function createPropFilter(props, filter) {
  return Array.isArray(filter) ? function(key) {
    return filter.indexOf(key) === -1;
  } : function(key) {
    return filter(props[key], key);
  };
}
var compensateMultilineStringElementIndentation = function(element, formattedElement, inline, lvl, options) {
  var tabStop = options.tabStop;
  return element.type === "string" ? formattedElement.split(`
`).map(function(line, offset) {
    return offset === 0 ? line : "".concat(spacer(lvl, tabStop)).concat(line);
  }).join(`
`) : formattedElement;
};
var formatOneChildren = function(inline, lvl, options) {
  return function(element) {
    return compensateMultilineStringElementIndentation(element, formatTreeNode(element, inline, lvl, options), inline, lvl, options);
  };
};
var onlyPropsWithOriginalValue = function(defaultProps, props) {
  return function(propName) {
    var haveDefaultValue = Object.keys(defaultProps).includes(propName);
    return !haveDefaultValue || haveDefaultValue && defaultProps[propName] !== props[propName];
  };
};
var isInlineAttributeTooLong = function(attributes, inlineAttributeString, lvl, tabStop, maxInlineAttributesLineLength) {
  return maxInlineAttributesLineLength ? spacer(lvl, tabStop).length + inlineAttributeString.length > maxInlineAttributesLineLength : attributes.length > 1;
};
var shouldRenderMultilineAttr = function(attributes, inlineAttributeString, containsMultilineAttr, inline, lvl, tabStop, maxInlineAttributesLineLength) {
  return (isInlineAttributeTooLong(attributes, inlineAttributeString, lvl, tabStop, maxInlineAttributesLineLength) || containsMultilineAttr) && !inline;
};
var formatReactElementNode = function(node, inline, lvl, options) {
  var type = node.type, _node$displayName = node.displayName, displayName = _node$displayName === void 0 ? "" : _node$displayName, childrens = node.childrens, _node$props = node.props, props = _node$props === void 0 ? {} : _node$props, _node$defaultProps = node.defaultProps, defaultProps = _node$defaultProps === void 0 ? {} : _node$defaultProps;
  if (type !== "ReactElement") throw new Error('The "formatReactElementNode" function could only format node of type "ReactElement". Given:  '.concat(type));
  var filterProps3 = options.filterProps, maxInlineAttributesLineLength = options.maxInlineAttributesLineLength, showDefaultProps = options.showDefaultProps, sortProps = options.sortProps, tabStop = options.tabStop, out = "<".concat(displayName), outInlineAttr = out, outMultilineAttr = out, containsMultilineAttr = false, visibleAttributeNames = [], propFilter = createPropFilter(props, filterProps3);
  Object.keys(props).filter(propFilter).filter(onlyPropsWithOriginalValue(defaultProps, props)).forEach(function(propName) {
    return visibleAttributeNames.push(propName);
  }), Object.keys(defaultProps).filter(propFilter).filter(function() {
    return showDefaultProps;
  }).filter(function(defaultPropName) {
    return !visibleAttributeNames.includes(defaultPropName);
  }).forEach(function(defaultPropName) {
    return visibleAttributeNames.push(defaultPropName);
  });
  var attributes = sortPropsByNames(sortProps)(visibleAttributeNames);
  if (attributes.forEach(function(attributeName) {
    var _formatProp = formatProp(attributeName, Object.keys(props).includes(attributeName), props[attributeName], Object.keys(defaultProps).includes(attributeName), defaultProps[attributeName], inline, lvl, options), attributeFormattedInline = _formatProp.attributeFormattedInline, attributeFormattedMultiline = _formatProp.attributeFormattedMultiline, isMultilineAttribute = _formatProp.isMultilineAttribute;
    isMultilineAttribute && (containsMultilineAttr = true), outInlineAttr += attributeFormattedInline, outMultilineAttr += attributeFormattedMultiline;
  }), outMultilineAttr += `
`.concat(spacer(lvl, tabStop)), shouldRenderMultilineAttr(attributes, outInlineAttr, containsMultilineAttr, inline, lvl, tabStop, maxInlineAttributesLineLength) ? out = outMultilineAttr : out = outInlineAttr, childrens && childrens.length > 0) {
    var newLvl = lvl + 1;
    out += ">", inline || (out += `
`, out += spacer(newLvl, tabStop)), out += childrens.reduce(mergeSiblingPlainStringChildrenReducer, []).map(formatOneChildren(inline, newLvl, options)).join(inline ? "" : `
`.concat(spacer(newLvl, tabStop))), inline || (out += `
`, out += spacer(newLvl - 1, tabStop)), out += "</".concat(displayName, ">");
  } else isInlineAttributeTooLong(attributes, outInlineAttr, lvl, tabStop, maxInlineAttributesLineLength) || (out += " "), out += "/>";
  return out;
};
var REACT_FRAGMENT_TAG_NAME_SHORT_SYNTAX = "";
var REACT_FRAGMENT_TAG_NAME_EXPLICIT_SYNTAX = "React.Fragment";
var toReactElementTreeNode = function(displayName, key, childrens) {
  var props = {};
  return key && (props = { key }), { type: "ReactElement", displayName, props, defaultProps: {}, childrens };
};
var isKeyedFragment = function(_ref) {
  var key = _ref.key;
  return !!key;
};
var hasNoChildren = function(_ref2) {
  var childrens = _ref2.childrens;
  return childrens.length === 0;
};
var formatReactFragmentNode = function(node, inline, lvl, options) {
  var type = node.type, key = node.key, childrens = node.childrens;
  if (type !== "ReactFragment") throw new Error('The "formatReactFragmentNode" function could only format node of type "ReactFragment". Given: '.concat(type));
  var useFragmentShortSyntax = options.useFragmentShortSyntax, displayName;
  return useFragmentShortSyntax ? hasNoChildren(node) || isKeyedFragment(node) ? displayName = REACT_FRAGMENT_TAG_NAME_EXPLICIT_SYNTAX : displayName = REACT_FRAGMENT_TAG_NAME_SHORT_SYNTAX : displayName = REACT_FRAGMENT_TAG_NAME_EXPLICIT_SYNTAX, formatReactElementNode(toReactElementTreeNode(displayName, key, childrens), inline, lvl, options);
};
var jsxStopChars = ["<", ">", "{", "}"];
var shouldBeEscaped = function(s) {
  return jsxStopChars.some(function(jsxStopChar) {
    return s.includes(jsxStopChar);
  });
};
var escape2 = function(s) {
  return shouldBeEscaped(s) ? "{`".concat(s, "`}") : s;
};
var preserveTrailingSpace = function(s) {
  var result = s;
  return result.endsWith(" ") && (result = result.replace(/^(.*?)(\s+)$/, "$1{'$2'}")), result.startsWith(" ") && (result = result.replace(/^(\s+)(.*)$/, "{'$1'}$2")), result;
};
var formatTreeNode = function(node, inline, lvl, options) {
  if (node.type === "number") return String(node.value);
  if (node.type === "string") return node.value ? "".concat(preserveTrailingSpace(escape2(String(node.value)))) : "";
  if (node.type === "ReactElement") return formatReactElementNode(node, inline, lvl, options);
  if (node.type === "ReactFragment") return formatReactFragmentNode(node, inline, lvl, options);
  throw new TypeError('Unknow format type "'.concat(node.type, '"'));
};
var formatTree = function(node, options) {
  return formatTreeNode(node, false, 0, options);
};
var reactElementToJsxString = function(element) {
  var _ref = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, _ref$filterProps = _ref.filterProps, filterProps3 = _ref$filterProps === void 0 ? [] : _ref$filterProps, _ref$showDefaultProps = _ref.showDefaultProps, showDefaultProps = _ref$showDefaultProps === void 0 ? true : _ref$showDefaultProps, _ref$showFunctions = _ref.showFunctions, showFunctions = _ref$showFunctions === void 0 ? false : _ref$showFunctions, functionValue = _ref.functionValue, _ref$tabStop = _ref.tabStop, tabStop = _ref$tabStop === void 0 ? 2 : _ref$tabStop, _ref$useBooleanShorth = _ref.useBooleanShorthandSyntax, useBooleanShorthandSyntax = _ref$useBooleanShorth === void 0 ? true : _ref$useBooleanShorth, _ref$useFragmentShort = _ref.useFragmentShortSyntax, useFragmentShortSyntax = _ref$useFragmentShort === void 0 ? true : _ref$useFragmentShort, _ref$sortProps = _ref.sortProps, sortProps = _ref$sortProps === void 0 ? true : _ref$sortProps, maxInlineAttributesLineLength = _ref.maxInlineAttributesLineLength, displayName = _ref.displayName;
  if (!element) throw new Error("react-element-to-jsx-string: Expected a ReactElement");
  var options = { filterProps: filterProps3, showDefaultProps, showFunctions, functionValue, tabStop, useBooleanShorthandSyntax, useFragmentShortSyntax, sortProps, maxInlineAttributesLineLength, displayName };
  return formatTree(_parseReactElement(element, options), options);
};

// node_modules/@storybook/react/dist/chunk-6PSAWJ36.mjs
var import_react2 = __toESM(require_react(), 1);
var import_client_logger = __toESM(require_client_logger(), 1);
var import_preview_api = __toESM(require_preview_api(), 1);
var entry_preview_docs_exports = {};
__export(entry_preview_docs_exports, { applyDecorators: () => applyDecorators2, decorators: () => decorators, parameters: () => parameters });
var reactElementToJSXString = reactElementToJsxString;
var toPascalCase = (str) => str.charAt(0).toUpperCase() + str.slice(1);
var getReactSymbolName = (elementType) => (elementType.$$typeof || elementType).toString().replace(/^Symbol\((.*)\)$/, "$1").split(".").map((segment) => segment.split("_").map(toPascalCase).join("")).join(".");
function simplifyNodeForStringify(node) {
  if ((0, import_react2.isValidElement)(node)) {
    let props = Object.keys(node.props).reduce((acc, cur) => (acc[cur] = simplifyNodeForStringify(node.props[cur]), acc), {});
    return { ...node, props, _owner: null };
  }
  return Array.isArray(node) ? node.map(simplifyNodeForStringify) : node;
}
var renderJsx = (code, options) => {
  if (typeof code > "u") return import_client_logger.logger.warn("Too many skip or undefined component"), null;
  let renderedJSX = code, Type = renderedJSX.type;
  for (let i = 0; i < options?.skip; i += 1) {
    if (typeof renderedJSX > "u") return import_client_logger.logger.warn("Cannot skip undefined element"), null;
    if (import_react2.default.Children.count(renderedJSX) > 1) return import_client_logger.logger.warn("Trying to skip an array of elements"), null;
    typeof renderedJSX.props.children > "u" ? (import_client_logger.logger.warn("Not enough children to skip elements."), typeof renderedJSX.type == "function" && renderedJSX.type.name === "" && (renderedJSX = import_react2.default.createElement(Type, { ...renderedJSX.props }))) : typeof renderedJSX.props.children == "function" ? renderedJSX = renderedJSX.props.children() : renderedJSX = renderedJSX.props.children;
  }
  let displayNameDefaults;
  typeof options?.displayName == "string" ? displayNameDefaults = { showFunctions: true, displayName: () => options.displayName } : displayNameDefaults = { displayName: (el) => el.type.displayName ? el.type.displayName : pt(el.type, "displayName") ? pt(el.type, "displayName") : el.type.render?.displayName ? el.type.render.displayName : typeof el.type == "symbol" || el.type.$$typeof && typeof el.type.$$typeof == "symbol" ? getReactSymbolName(el.type) : el.type.name && el.type.name !== "_default" ? el.type.name : typeof el.type == "function" ? "No Display Name" : isForwardRef(el.type) ? el.type.render.name : isMemo(el.type) ? el.type.type.name : el.type };
  let opts = { ...displayNameDefaults, ...{ filterProps: (value, key) => value !== void 0 }, ...options };
  return import_react2.default.Children.map(code, (c) => {
    let child = typeof c == "number" ? c.toString() : c, string = (typeof reactElementToJSXString == "function" ? reactElementToJSXString : reactElementToJSXString.default)(simplifyNodeForStringify(child), opts);
    if (string.indexOf("&quot;") > -1) {
      let matches = string.match(/\S+=\\"([^"]*)\\"/g);
      matches && matches.forEach((match) => {
        string = string.replace(match, match.replace(/&quot;/g, "'"));
      });
    }
    return string;
  }).join(`
`).replace(/function\s+noRefCheck\(\)\s*\{\}/g, "() => {}");
};
var defaultOpts = { skip: 0, showFunctions: false, enableBeautify: true, showDefaultProps: false };
var skipJsxRender = (context) => {
  let sourceParams = context?.parameters.docs?.source, isArgsStory = context?.parameters.__isArgsStory;
  return sourceParams?.type === Tn.DYNAMIC ? false : !isArgsStory || sourceParams?.code || sourceParams?.type === Tn.CODE;
};
var isMdx = (node) => node.type?.displayName === "MDXCreateElement" && !!node.props?.mdxType;
var mdxToJsx = (node) => {
  if (!isMdx(node)) return node;
  let { mdxType, originalType, children, ...rest } = node.props, jsxChildren = [];
  return children && (jsxChildren = (Array.isArray(children) ? children : [children]).map(mdxToJsx)), (0, import_react2.createElement)(originalType, rest, ...jsxChildren);
};
var jsxDecorator = (storyFn, context) => {
  let jsx = (0, import_preview_api.useRef)(void 0), story = storyFn(), skip = skipJsxRender(context), options = { ...defaultOpts, ...context?.parameters.jsx || {} }, storyJsx = context.originalStoryFn(context.args, context);
  return (0, import_preview_api.useEffect)(() => {
    if (skip) return;
    let sourceJsx = mdxToJsx(storyJsx), rendered = renderJsx(sourceJsx, options);
    rendered && jsx.current !== rendered && ((0, import_preview_api.emitTransformCode)(rendered, context), jsx.current = rendered);
  }), story;
};
var applyDecorators2 = (storyFn, decorators2) => {
  let jsxIndex = decorators2.findIndex((d) => d.originalFn === jsxDecorator), reorderedDecorators = jsxIndex === -1 ? decorators2 : [...decorators2.splice(jsxIndex, 1), ...decorators2];
  return applyDecorators(storyFn, reorderedDecorators);
};
var decorators = [jsxDecorator];
var parameters = { docs: { story: { inline: true } } };

export {
  isMemo,
  reactElementToJsxString,
  entry_preview_docs_exports,
  applyDecorators2,
  decorators,
  parameters
};
//# sourceMappingURL=chunk-NTVPHKIQ.js.map
