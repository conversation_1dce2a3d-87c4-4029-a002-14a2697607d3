import { Meta, StoryObj } from "@storybook/react";
import { DataTable } from "./DataTable";

const meta: Meta<typeof DataTable> = {
  title: "Components/DataTable",
  component: DataTable,
};
export default meta;

type Story = StoryObj<typeof DataTable>;

export const Default: Story = {
  args: {
    rows: [
      { id: 1, name: "<PERSON>", age: 25 },
      { id: 2, name: "<PERSON>", age: 30 },
    ],
    columns: ["id", "name", "age"],
  },
};
