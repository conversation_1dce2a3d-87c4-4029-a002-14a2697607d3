import type { <PERSON>a, StoryObj } from "@storybook/react";
import { DataTable } from "./DataTable";

interface User {
  id: number;
  name: string;
  age: number;
}

const meta: Meta<typeof DataTable<User>> = {
  title: "Components/DataTable",
  component: DataTable<User>,
};
export default meta;

type Story = StoryObj<typeof DataTable<User>>;

export const Default: Story = {
  args: {
    data: [
      { id: 1, name: "<PERSON>", age: 25 },
      { id: 2, name: "<PERSON>", age: 30 },
    ],
    columns: [
      { key: "id", title: "ID", dataIndex: "id", sortable: true },
      { key: "name", title: "Name", dataIndex: "name", sortable: true },
      { key: "age", title: "Age", dataIndex: "age", sortable: true },
    ],
    selectable: true,
  },
};
