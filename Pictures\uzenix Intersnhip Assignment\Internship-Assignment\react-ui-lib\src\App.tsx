import React, { useState } from "react";
import { InputField } from "./src/components/InputField/InputField.tsx";

function App() {
  const [text, setText] = useState("");

  return (
    <div className="p-6">
      <InputField
        label="Username"
        value={text}
        onChange={(e) => setText(e.target.value)}
        placeholder="Enter your username"
        helperText="This will be visible to others"
      />

      <InputField
        label="Password"
        type="password"
        placeholder="Enter your password"
        errorMessage="Password is required"
        invalid={true}
      />
    </div>
  );
}

export default App;
