{"version": 3, "sources": ["../../../../../storybook/dist/viewport/index.js"], "sourcesContent": ["// src/viewport/constants.ts\nvar e = \"storybook/viewport\", p = \"viewport\", i = `${e}/panel`, h = `${e}/tool`;\n\n// src/viewport/defaults.ts\nvar t = {\n  iphone5: {\n    name: \"iPhone 5\",\n    styles: {\n      height: \"568px\",\n      width: \"320px\"\n    },\n    type: \"mobile\"\n  },\n  iphone6: {\n    name: \"iPhone 6\",\n    styles: {\n      height: \"667px\",\n      width: \"375px\"\n    },\n    type: \"mobile\"\n  },\n  iphone6p: {\n    name: \"iPhone 6 Plus\",\n    styles: {\n      height: \"736px\",\n      width: \"414px\"\n    },\n    type: \"mobile\"\n  },\n  iphone8p: {\n    name: \"iPhone 8 Plus\",\n    styles: {\n      height: \"736px\",\n      width: \"414px\"\n    },\n    type: \"mobile\"\n  },\n  iphonex: {\n    name: \"iPhone X\",\n    styles: {\n      height: \"812px\",\n      width: \"375px\"\n    },\n    type: \"mobile\"\n  },\n  iphonexr: {\n    name: \"iPhone XR\",\n    styles: {\n      height: \"896px\",\n      width: \"414px\"\n    },\n    type: \"mobile\"\n  },\n  iphonexsmax: {\n    name: \"iPhone XS Max\",\n    styles: {\n      height: \"896px\",\n      width: \"414px\"\n    },\n    type: \"mobile\"\n  },\n  iphonese2: {\n    name: \"iPhone SE (2nd generation)\",\n    styles: {\n      height: \"667px\",\n      width: \"375px\"\n    },\n    type: \"mobile\"\n  },\n  iphone12mini: {\n    name: \"iPhone 12 mini\",\n    styles: {\n      height: \"812px\",\n      width: \"375px\"\n    },\n    type: \"mobile\"\n  },\n  iphone12: {\n    name: \"iPhone 12\",\n    styles: {\n      height: \"844px\",\n      width: \"390px\"\n    },\n    type: \"mobile\"\n  },\n  iphone12promax: {\n    name: \"iPhone 12 Pro Max\",\n    styles: {\n      height: \"926px\",\n      width: \"428px\"\n    },\n    type: \"mobile\"\n  },\n  iphoneSE3: {\n    name: \"iPhone SE 3rd generation\",\n    styles: {\n      height: \"667px\",\n      width: \"375px\"\n    },\n    type: \"mobile\"\n  },\n  iphone13: {\n    name: \"iPhone 13\",\n    styles: {\n      height: \"844px\",\n      width: \"390px\"\n    },\n    type: \"mobile\"\n  },\n  iphone13pro: {\n    name: \"iPhone 13 Pro\",\n    styles: {\n      height: \"844px\",\n      width: \"390px\"\n    },\n    type: \"mobile\"\n  },\n  iphone13promax: {\n    name: \"iPhone 13 Pro Max\",\n    styles: {\n      height: \"926px\",\n      width: \"428px\"\n    },\n    type: \"mobile\"\n  },\n  iphone14: {\n    name: \"iPhone 14\",\n    styles: {\n      height: \"844px\",\n      width: \"390px\"\n    },\n    type: \"mobile\"\n  },\n  iphone14pro: {\n    name: \"iPhone 14 Pro\",\n    styles: {\n      height: \"852px\",\n      width: \"393px\"\n    },\n    type: \"mobile\"\n  },\n  iphone14promax: {\n    name: \"iPhone 14 Pro Max\",\n    styles: {\n      height: \"932px\",\n      width: \"430px\"\n    },\n    type: \"mobile\"\n  },\n  ipad: {\n    name: \"iPad\",\n    styles: {\n      height: \"1024px\",\n      width: \"768px\"\n    },\n    type: \"tablet\"\n  },\n  ipad10p: {\n    name: \"iPad Pro 10.5-in\",\n    styles: {\n      height: \"1112px\",\n      width: \"834px\"\n    },\n    type: \"tablet\"\n  },\n  ipad11p: {\n    name: \"iPad Pro 11-in\",\n    styles: {\n      height: \"1194px\",\n      width: \"834px\"\n    },\n    type: \"tablet\"\n  },\n  ipad12p: {\n    name: \"iPad Pro 12.9-in\",\n    styles: {\n      height: \"1366px\",\n      width: \"1024px\"\n    },\n    type: \"tablet\"\n  },\n  galaxys5: {\n    name: \"Galaxy S5\",\n    styles: {\n      height: \"640px\",\n      width: \"360px\"\n    },\n    type: \"mobile\"\n  },\n  galaxys9: {\n    name: \"Galaxy S9\",\n    styles: {\n      height: \"740px\",\n      width: \"360px\"\n    },\n    type: \"mobile\"\n  },\n  nexus5x: {\n    name: \"Nexus 5X\",\n    styles: {\n      height: \"660px\",\n      width: \"412px\"\n    },\n    type: \"mobile\"\n  },\n  nexus6p: {\n    name: \"Nexus 6P\",\n    styles: {\n      height: \"732px\",\n      width: \"412px\"\n    },\n    type: \"mobile\"\n  },\n  pixel: {\n    name: \"Pixel\",\n    styles: {\n      height: \"960px\",\n      width: \"540px\"\n    },\n    type: \"mobile\"\n  },\n  pixelxl: {\n    name: \"Pixel XL\",\n    styles: {\n      height: \"1280px\",\n      width: \"720px\"\n    },\n    type: \"mobile\"\n  }\n}, x = t, s = \"responsive\", n = {\n  mobile1: {\n    name: \"Small mobile\",\n    styles: {\n      height: \"568px\",\n      width: \"320px\"\n    },\n    type: \"mobile\"\n  },\n  mobile2: {\n    name: \"Large mobile\",\n    styles: {\n      height: \"896px\",\n      width: \"414px\"\n    },\n    type: \"mobile\"\n  },\n  tablet: {\n    name: \"Tablet\",\n    styles: {\n      height: \"1112px\",\n      width: \"834px\"\n    },\n    type: \"tablet\"\n  },\n  desktop: {\n    name: \"Desktop\",\n    styles: {\n      height: \"1024px\",\n      width: \"1280px\"\n    },\n    type: \"desktop\"\n  }\n};\n\n// src/viewport/responsiveViewport.tsx\nvar y = {\n  name: \"Reset viewport\",\n  styles: {\n    height: \"100%\",\n    width: \"100%\"\n  },\n  type: \"desktop\"\n};\nexport {\n  e as ADDON_ID,\n  s as DEFAULT_VIEWPORT,\n  x as INITIAL_VIEWPORTS,\n  n as MINIMAL_VIEWPORTS,\n  i as PANEL_ID,\n  p as PARAM_KEY,\n  h as TOOL_ID,\n  y as responsiveViewport\n};\n"], "mappings": ";;;AACA,IAAI,IAAI;AAAR,IAA8B,IAAI;AAAlC,IAA8C,IAAI,GAAG,CAAC;AAAtD,IAAgE,IAAI,GAAG,CAAC;AAGxE,IAAI,IAAI;AAAA,EACN,SAAS;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AACF;AAjOA,IAiOG,IAAI;AAjOP,IAiOU,IAAI;AAjOd,IAiO4B,IAAI;AAAA,EAC9B,SAAS;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AACF;AAGA,IAAI,IAAI;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AACR;", "names": []}