import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// optional-peer-dep:__vite-optional-peer-dep:msw/core/http:@vitest/mocker:false
var require_mocker_false = __commonJS({
  "optional-peer-dep:__vite-optional-peer-dep:msw/core/http:@vitest/mocker:false"(exports, module) {
    module.exports = {};
    throw new Error(`Could not resolve "msw/core/http" imported by "@vitest/mocker". Is it installed?`);
  }
});
export default require_mocker_false();
//# sourceMappingURL=mocker_false-TNUXJKVI.js.map
