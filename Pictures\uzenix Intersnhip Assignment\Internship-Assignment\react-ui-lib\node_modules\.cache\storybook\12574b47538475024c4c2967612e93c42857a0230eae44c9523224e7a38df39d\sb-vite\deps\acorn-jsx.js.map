{"version": 3, "sources": ["../../../../../acorn-jsx/xhtml.js", "../../../../../acorn/dist/acorn.js", "../../../../../acorn-jsx/index.js"], "sourcesContent": ["module.exports = {\n  quot: '\\u0022',\n  amp: '&',\n  apos: '\\u0027',\n  lt: '<',\n  gt: '>',\n  nbsp: '\\u00A0',\n  iexcl: '\\u00A1',\n  cent: '\\u00A2',\n  pound: '\\u00A3',\n  curren: '\\u00A4',\n  yen: '\\u00A5',\n  brvbar: '\\u00A6',\n  sect: '\\u00A7',\n  uml: '\\u00A8',\n  copy: '\\u00A9',\n  ordf: '\\u00AA',\n  laquo: '\\u00AB',\n  not: '\\u00AC',\n  shy: '\\u00AD',\n  reg: '\\u00AE',\n  macr: '\\u00AF',\n  deg: '\\u00B0',\n  plusmn: '\\u00B1',\n  sup2: '\\u00B2',\n  sup3: '\\u00B3',\n  acute: '\\u00B4',\n  micro: '\\u00B5',\n  para: '\\u00B6',\n  middot: '\\u00B7',\n  cedil: '\\u00B8',\n  sup1: '\\u00B9',\n  ordm: '\\u00BA',\n  raquo: '\\u00BB',\n  frac14: '\\u00BC',\n  frac12: '\\u00BD',\n  frac34: '\\u00BE',\n  iquest: '\\u00BF',\n  Agrave: '\\u00C0',\n  Aacute: '\\u00C1',\n  Acirc: '\\u00C2',\n  Atilde: '\\u00C3',\n  Auml: '\\u00C4',\n  Aring: '\\u00C5',\n  AElig: '\\u00C6',\n  Ccedil: '\\u00C7',\n  Egrave: '\\u00C8',\n  Eacute: '\\u00C9',\n  Ecirc: '\\u00CA',\n  Euml: '\\u00CB',\n  Igrave: '\\u00CC',\n  Iacute: '\\u00CD',\n  Icirc: '\\u00CE',\n  Iuml: '\\u00CF',\n  ETH: '\\u00D0',\n  Ntilde: '\\u00D1',\n  Ograve: '\\u00D2',\n  Oacute: '\\u00D3',\n  Ocirc: '\\u00D4',\n  Otilde: '\\u00D5',\n  Ouml: '\\u00D6',\n  times: '\\u00D7',\n  Oslash: '\\u00D8',\n  Ugrave: '\\u00D9',\n  Uacute: '\\u00DA',\n  Ucirc: '\\u00DB',\n  Uuml: '\\u00DC',\n  Yacute: '\\u00DD',\n  THORN: '\\u00DE',\n  szlig: '\\u00DF',\n  agrave: '\\u00E0',\n  aacute: '\\u00E1',\n  acirc: '\\u00E2',\n  atilde: '\\u00E3',\n  auml: '\\u00E4',\n  aring: '\\u00E5',\n  aelig: '\\u00E6',\n  ccedil: '\\u00E7',\n  egrave: '\\u00E8',\n  eacute: '\\u00E9',\n  ecirc: '\\u00EA',\n  euml: '\\u00EB',\n  igrave: '\\u00EC',\n  iacute: '\\u00ED',\n  icirc: '\\u00EE',\n  iuml: '\\u00EF',\n  eth: '\\u00F0',\n  ntilde: '\\u00F1',\n  ograve: '\\u00F2',\n  oacute: '\\u00F3',\n  ocirc: '\\u00F4',\n  otilde: '\\u00F5',\n  ouml: '\\u00F6',\n  divide: '\\u00F7',\n  oslash: '\\u00F8',\n  ugrave: '\\u00F9',\n  uacute: '\\u00FA',\n  ucirc: '\\u00FB',\n  uuml: '\\u00FC',\n  yacute: '\\u00FD',\n  thorn: '\\u00FE',\n  yuml: '\\u00FF',\n  OElig: '\\u0152',\n  oelig: '\\u0153',\n  Scaron: '\\u0160',\n  scaron: '\\u0161',\n  Yuml: '\\u0178',\n  fnof: '\\u0192',\n  circ: '\\u02C6',\n  tilde: '\\u02DC',\n  Alpha: '\\u0391',\n  Beta: '\\u0392',\n  Gamma: '\\u0393',\n  Delta: '\\u0394',\n  Epsilon: '\\u0395',\n  Zeta: '\\u0396',\n  Eta: '\\u0397',\n  Theta: '\\u0398',\n  Iota: '\\u0399',\n  Kappa: '\\u039A',\n  Lambda: '\\u039B',\n  Mu: '\\u039C',\n  Nu: '\\u039D',\n  Xi: '\\u039E',\n  Omicron: '\\u039F',\n  Pi: '\\u03A0',\n  Rho: '\\u03A1',\n  Sigma: '\\u03A3',\n  Tau: '\\u03A4',\n  Upsilon: '\\u03A5',\n  Phi: '\\u03A6',\n  Chi: '\\u03A7',\n  Psi: '\\u03A8',\n  Omega: '\\u03A9',\n  alpha: '\\u03B1',\n  beta: '\\u03B2',\n  gamma: '\\u03B3',\n  delta: '\\u03B4',\n  epsilon: '\\u03B5',\n  zeta: '\\u03B6',\n  eta: '\\u03B7',\n  theta: '\\u03B8',\n  iota: '\\u03B9',\n  kappa: '\\u03BA',\n  lambda: '\\u03BB',\n  mu: '\\u03BC',\n  nu: '\\u03BD',\n  xi: '\\u03BE',\n  omicron: '\\u03BF',\n  pi: '\\u03C0',\n  rho: '\\u03C1',\n  sigmaf: '\\u03C2',\n  sigma: '\\u03C3',\n  tau: '\\u03C4',\n  upsilon: '\\u03C5',\n  phi: '\\u03C6',\n  chi: '\\u03C7',\n  psi: '\\u03C8',\n  omega: '\\u03C9',\n  thetasym: '\\u03D1',\n  upsih: '\\u03D2',\n  piv: '\\u03D6',\n  ensp: '\\u2002',\n  emsp: '\\u2003',\n  thinsp: '\\u2009',\n  zwnj: '\\u200C',\n  zwj: '\\u200D',\n  lrm: '\\u200E',\n  rlm: '\\u200F',\n  ndash: '\\u2013',\n  mdash: '\\u2014',\n  lsquo: '\\u2018',\n  rsquo: '\\u2019',\n  sbquo: '\\u201A',\n  ldquo: '\\u201C',\n  rdquo: '\\u201D',\n  bdquo: '\\u201E',\n  dagger: '\\u2020',\n  Dagger: '\\u2021',\n  bull: '\\u2022',\n  hellip: '\\u2026',\n  permil: '\\u2030',\n  prime: '\\u2032',\n  Prime: '\\u2033',\n  lsaquo: '\\u2039',\n  rsaquo: '\\u203A',\n  oline: '\\u203E',\n  frasl: '\\u2044',\n  euro: '\\u20AC',\n  image: '\\u2111',\n  weierp: '\\u2118',\n  real: '\\u211C',\n  trade: '\\u2122',\n  alefsym: '\\u2135',\n  larr: '\\u2190',\n  uarr: '\\u2191',\n  rarr: '\\u2192',\n  darr: '\\u2193',\n  harr: '\\u2194',\n  crarr: '\\u21B5',\n  lArr: '\\u21D0',\n  uArr: '\\u21D1',\n  rArr: '\\u21D2',\n  dArr: '\\u21D3',\n  hArr: '\\u21D4',\n  forall: '\\u2200',\n  part: '\\u2202',\n  exist: '\\u2203',\n  empty: '\\u2205',\n  nabla: '\\u2207',\n  isin: '\\u2208',\n  notin: '\\u2209',\n  ni: '\\u220B',\n  prod: '\\u220F',\n  sum: '\\u2211',\n  minus: '\\u2212',\n  lowast: '\\u2217',\n  radic: '\\u221A',\n  prop: '\\u221D',\n  infin: '\\u221E',\n  ang: '\\u2220',\n  and: '\\u2227',\n  or: '\\u2228',\n  cap: '\\u2229',\n  cup: '\\u222A',\n  'int': '\\u222B',\n  there4: '\\u2234',\n  sim: '\\u223C',\n  cong: '\\u2245',\n  asymp: '\\u2248',\n  ne: '\\u2260',\n  equiv: '\\u2261',\n  le: '\\u2264',\n  ge: '\\u2265',\n  sub: '\\u2282',\n  sup: '\\u2283',\n  nsub: '\\u2284',\n  sube: '\\u2286',\n  supe: '\\u2287',\n  oplus: '\\u2295',\n  otimes: '\\u2297',\n  perp: '\\u22A5',\n  sdot: '\\u22C5',\n  lceil: '\\u2308',\n  rceil: '\\u2309',\n  lfloor: '\\u230A',\n  rfloor: '\\u230B',\n  lang: '\\u2329',\n  rang: '\\u232A',\n  loz: '\\u25CA',\n  spades: '\\u2660',\n  clubs: '\\u2663',\n  hearts: '\\u2665',\n  diams: '\\u2666'\n};\n", "(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\n  typeof define === 'function' && define.amd ? define(['exports'], factory) :\n  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.acorn = {}));\n})(this, (function (exports) { 'use strict';\n\n  // This file was generated. Do not modify manually!\n  var astralIdentifierCodes = [509, 0, 227, 0, 150, 4, 294, 9, 1368, 2, 2, 1, 6, 3, 41, 2, 5, 0, 166, 1, 574, 3, 9, 9, 7, 9, 32, 4, 318, 1, 80, 3, 71, 10, 50, 3, 123, 2, 54, 14, 32, 10, 3, 1, 11, 3, 46, 10, 8, 0, 46, 9, 7, 2, 37, 13, 2, 9, 6, 1, 45, 0, 13, 2, 49, 13, 9, 3, 2, 11, 83, 11, 7, 0, 3, 0, 158, 11, 6, 9, 7, 3, 56, 1, 2, 6, 3, 1, 3, 2, 10, 0, 11, 1, 3, 6, 4, 4, 68, 8, 2, 0, 3, 0, 2, 3, 2, 4, 2, 0, 15, 1, 83, 17, 10, 9, 5, 0, 82, 19, 13, 9, 214, 6, 3, 8, 28, 1, 83, 16, 16, 9, 82, 12, 9, 9, 7, 19, 58, 14, 5, 9, 243, 14, 166, 9, 71, 5, 2, 1, 3, 3, 2, 0, 2, 1, 13, 9, 120, 6, 3, 6, 4, 0, 29, 9, 41, 6, 2, 3, 9, 0, 10, 10, 47, 15, 343, 9, 54, 7, 2, 7, 17, 9, 57, 21, 2, 13, 123, 5, 4, 0, 2, 1, 2, 6, 2, 0, 9, 9, 49, 4, 2, 1, 2, 4, 9, 9, 330, 3, 10, 1, 2, 0, 49, 6, 4, 4, 14, 10, 5350, 0, 7, 14, 11465, 27, 2343, 9, 87, 9, 39, 4, 60, 6, 26, 9, 535, 9, 470, 0, 2, 54, 8, 3, 82, 0, 12, 1, 19628, 1, 4178, 9, 519, 45, 3, 22, 543, 4, 4, 5, 9, 7, 3, 6, 31, 3, 149, 2, 1418, 49, 513, 54, 5, 49, 9, 0, 15, 0, 23, 4, 2, 14, 1361, 6, 2, 16, 3, 6, 2, 1, 2, 4, 101, 0, 161, 6, 10, 9, 357, 0, 62, 13, 499, 13, 245, 1, 2, 9, 726, 6, 110, 6, 6, 9, 4759, 9, 787719, 239];\n\n  // This file was generated. Do not modify manually!\n  var astralIdentifierStartCodes = [0, 11, 2, 25, 2, 18, 2, 1, 2, 14, 3, 13, 35, 122, 70, 52, 268, 28, 4, 48, 48, 31, 14, 29, 6, 37, 11, 29, 3, 35, 5, 7, 2, 4, 43, 157, 19, 35, 5, 35, 5, 39, 9, 51, 13, 10, 2, 14, 2, 6, 2, 1, 2, 10, 2, 14, 2, 6, 2, 1, 4, 51, 13, 310, 10, 21, 11, 7, 25, 5, 2, 41, 2, 8, 70, 5, 3, 0, 2, 43, 2, 1, 4, 0, 3, 22, 11, 22, 10, 30, 66, 18, 2, 1, 11, 21, 11, 25, 71, 55, 7, 1, 65, 0, 16, 3, 2, 2, 2, 28, 43, 28, 4, 28, 36, 7, 2, 27, 28, 53, 11, 21, 11, 18, 14, 17, 111, 72, 56, 50, 14, 50, 14, 35, 39, 27, 10, 22, 251, 41, 7, 1, 17, 2, 60, 28, 11, 0, 9, 21, 43, 17, 47, 20, 28, 22, 13, 52, 58, 1, 3, 0, 14, 44, 33, 24, 27, 35, 30, 0, 3, 0, 9, 34, 4, 0, 13, 47, 15, 3, 22, 0, 2, 0, 36, 17, 2, 24, 20, 1, 64, 6, 2, 0, 2, 3, 2, 14, 2, 9, 8, 46, 39, 7, 3, 1, 3, 21, 2, 6, 2, 1, 2, 4, 4, 0, 19, 0, 13, 4, 31, 9, 2, 0, 3, 0, 2, 37, 2, 0, 26, 0, 2, 0, 45, 52, 19, 3, 21, 2, 31, 47, 21, 1, 2, 0, 185, 46, 42, 3, 37, 47, 21, 0, 60, 42, 14, 0, 72, 26, 38, 6, 186, 43, 117, 63, 32, 7, 3, 0, 3, 7, 2, 1, 2, 23, 16, 0, 2, 0, 95, 7, 3, 38, 17, 0, 2, 0, 29, 0, 11, 39, 8, 0, 22, 0, 12, 45, 20, 0, 19, 72, 200, 32, 32, 8, 2, 36, 18, 0, 50, 29, 113, 6, 2, 1, 2, 37, 22, 0, 26, 5, 2, 1, 2, 31, 15, 0, 328, 18, 16, 0, 2, 12, 2, 33, 125, 0, 80, 921, 103, 110, 18, 195, 2637, 96, 16, 1071, 18, 5, 26, 3994, 6, 582, 6842, 29, 1763, 568, 8, 30, 18, 78, 18, 29, 19, 47, 17, 3, 32, 20, 6, 18, 433, 44, 212, 63, 129, 74, 6, 0, 67, 12, 65, 1, 2, 0, 29, 6135, 9, 1237, 42, 9, 8936, 3, 2, 6, 2, 1, 2, 290, 16, 0, 30, 2, 3, 0, 15, 3, 9, 395, 2309, 106, 6, 12, 4, 8, 8, 9, 5991, 84, 2, 70, 2, 1, 3, 0, 3, 1, 3, 3, 2, 11, 2, 0, 2, 6, 2, 64, 2, 3, 3, 7, 2, 6, 2, 27, 2, 3, 2, 4, 2, 0, 4, 6, 2, 339, 3, 24, 2, 24, 2, 30, 2, 24, 2, 30, 2, 24, 2, 30, 2, 24, 2, 30, 2, 24, 2, 7, 1845, 30, 7, 5, 262, 61, 147, 44, 11, 6, 17, 0, 322, 29, 19, 43, 485, 27, 229, 29, 3, 0, 496, 6, 2, 3, 2, 1, 2, 14, 2, 196, 60, 67, 8, 0, 1205, 3, 2, 26, 2, 1, 2, 0, 3, 0, 2, 9, 2, 3, 2, 0, 2, 0, 7, 0, 5, 0, 2, 0, 2, 0, 2, 2, 2, 1, 2, 0, 3, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 1, 2, 0, 3, 3, 2, 6, 2, 3, 2, 3, 2, 0, 2, 9, 2, 16, 6, 2, 2, 4, 2, 16, 4421, 42719, 33, 4153, 7, 221, 3, 5761, 15, 7472, 16, 621, 2467, 541, 1507, 4938, 6, 4191];\n\n  // This file was generated. Do not modify manually!\n  var nonASCIIidentifierChars = \"\\u200c\\u200d\\xb7\\u0300-\\u036f\\u0387\\u0483-\\u0487\\u0591-\\u05bd\\u05bf\\u05c1\\u05c2\\u05c4\\u05c5\\u05c7\\u0610-\\u061a\\u064b-\\u0669\\u0670\\u06d6-\\u06dc\\u06df-\\u06e4\\u06e7\\u06e8\\u06ea-\\u06ed\\u06f0-\\u06f9\\u0711\\u0730-\\u074a\\u07a6-\\u07b0\\u07c0-\\u07c9\\u07eb-\\u07f3\\u07fd\\u0816-\\u0819\\u081b-\\u0823\\u0825-\\u0827\\u0829-\\u082d\\u0859-\\u085b\\u0897-\\u089f\\u08ca-\\u08e1\\u08e3-\\u0903\\u093a-\\u093c\\u093e-\\u094f\\u0951-\\u0957\\u0962\\u0963\\u0966-\\u096f\\u0981-\\u0983\\u09bc\\u09be-\\u09c4\\u09c7\\u09c8\\u09cb-\\u09cd\\u09d7\\u09e2\\u09e3\\u09e6-\\u09ef\\u09fe\\u0a01-\\u0a03\\u0a3c\\u0a3e-\\u0a42\\u0a47\\u0a48\\u0a4b-\\u0a4d\\u0a51\\u0a66-\\u0a71\\u0a75\\u0a81-\\u0a83\\u0abc\\u0abe-\\u0ac5\\u0ac7-\\u0ac9\\u0acb-\\u0acd\\u0ae2\\u0ae3\\u0ae6-\\u0aef\\u0afa-\\u0aff\\u0b01-\\u0b03\\u0b3c\\u0b3e-\\u0b44\\u0b47\\u0b48\\u0b4b-\\u0b4d\\u0b55-\\u0b57\\u0b62\\u0b63\\u0b66-\\u0b6f\\u0b82\\u0bbe-\\u0bc2\\u0bc6-\\u0bc8\\u0bca-\\u0bcd\\u0bd7\\u0be6-\\u0bef\\u0c00-\\u0c04\\u0c3c\\u0c3e-\\u0c44\\u0c46-\\u0c48\\u0c4a-\\u0c4d\\u0c55\\u0c56\\u0c62\\u0c63\\u0c66-\\u0c6f\\u0c81-\\u0c83\\u0cbc\\u0cbe-\\u0cc4\\u0cc6-\\u0cc8\\u0cca-\\u0ccd\\u0cd5\\u0cd6\\u0ce2\\u0ce3\\u0ce6-\\u0cef\\u0cf3\\u0d00-\\u0d03\\u0d3b\\u0d3c\\u0d3e-\\u0d44\\u0d46-\\u0d48\\u0d4a-\\u0d4d\\u0d57\\u0d62\\u0d63\\u0d66-\\u0d6f\\u0d81-\\u0d83\\u0dca\\u0dcf-\\u0dd4\\u0dd6\\u0dd8-\\u0ddf\\u0de6-\\u0def\\u0df2\\u0df3\\u0e31\\u0e34-\\u0e3a\\u0e47-\\u0e4e\\u0e50-\\u0e59\\u0eb1\\u0eb4-\\u0ebc\\u0ec8-\\u0ece\\u0ed0-\\u0ed9\\u0f18\\u0f19\\u0f20-\\u0f29\\u0f35\\u0f37\\u0f39\\u0f3e\\u0f3f\\u0f71-\\u0f84\\u0f86\\u0f87\\u0f8d-\\u0f97\\u0f99-\\u0fbc\\u0fc6\\u102b-\\u103e\\u1040-\\u1049\\u1056-\\u1059\\u105e-\\u1060\\u1062-\\u1064\\u1067-\\u106d\\u1071-\\u1074\\u1082-\\u108d\\u108f-\\u109d\\u135d-\\u135f\\u1369-\\u1371\\u1712-\\u1715\\u1732-\\u1734\\u1752\\u1753\\u1772\\u1773\\u17b4-\\u17d3\\u17dd\\u17e0-\\u17e9\\u180b-\\u180d\\u180f-\\u1819\\u18a9\\u1920-\\u192b\\u1930-\\u193b\\u1946-\\u194f\\u19d0-\\u19da\\u1a17-\\u1a1b\\u1a55-\\u1a5e\\u1a60-\\u1a7c\\u1a7f-\\u1a89\\u1a90-\\u1a99\\u1ab0-\\u1abd\\u1abf-\\u1ace\\u1b00-\\u1b04\\u1b34-\\u1b44\\u1b50-\\u1b59\\u1b6b-\\u1b73\\u1b80-\\u1b82\\u1ba1-\\u1bad\\u1bb0-\\u1bb9\\u1be6-\\u1bf3\\u1c24-\\u1c37\\u1c40-\\u1c49\\u1c50-\\u1c59\\u1cd0-\\u1cd2\\u1cd4-\\u1ce8\\u1ced\\u1cf4\\u1cf7-\\u1cf9\\u1dc0-\\u1dff\\u200c\\u200d\\u203f\\u2040\\u2054\\u20d0-\\u20dc\\u20e1\\u20e5-\\u20f0\\u2cef-\\u2cf1\\u2d7f\\u2de0-\\u2dff\\u302a-\\u302f\\u3099\\u309a\\u30fb\\ua620-\\ua629\\ua66f\\ua674-\\ua67d\\ua69e\\ua69f\\ua6f0\\ua6f1\\ua802\\ua806\\ua80b\\ua823-\\ua827\\ua82c\\ua880\\ua881\\ua8b4-\\ua8c5\\ua8d0-\\ua8d9\\ua8e0-\\ua8f1\\ua8ff-\\ua909\\ua926-\\ua92d\\ua947-\\ua953\\ua980-\\ua983\\ua9b3-\\ua9c0\\ua9d0-\\ua9d9\\ua9e5\\ua9f0-\\ua9f9\\uaa29-\\uaa36\\uaa43\\uaa4c\\uaa4d\\uaa50-\\uaa59\\uaa7b-\\uaa7d\\uaab0\\uaab2-\\uaab4\\uaab7\\uaab8\\uaabe\\uaabf\\uaac1\\uaaeb-\\uaaef\\uaaf5\\uaaf6\\uabe3-\\uabea\\uabec\\uabed\\uabf0-\\uabf9\\ufb1e\\ufe00-\\ufe0f\\ufe20-\\ufe2f\\ufe33\\ufe34\\ufe4d-\\ufe4f\\uff10-\\uff19\\uff3f\\uff65\";\n\n  // This file was generated. Do not modify manually!\n  var nonASCIIidentifierStartChars = \"\\xaa\\xb5\\xba\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0370-\\u0374\\u0376\\u0377\\u037a-\\u037d\\u037f\\u0386\\u0388-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u048a-\\u052f\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05d0-\\u05ea\\u05ef-\\u05f2\\u0620-\\u064a\\u066e\\u066f\\u0671-\\u06d3\\u06d5\\u06e5\\u06e6\\u06ee\\u06ef\\u06fa-\\u06fc\\u06ff\\u0710\\u0712-\\u072f\\u074d-\\u07a5\\u07b1\\u07ca-\\u07ea\\u07f4\\u07f5\\u07fa\\u0800-\\u0815\\u081a\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086a\\u0870-\\u0887\\u0889-\\u088e\\u08a0-\\u08c9\\u0904-\\u0939\\u093d\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098c\\u098f\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bd\\u09ce\\u09dc\\u09dd\\u09df-\\u09e1\\u09f0\\u09f1\\u09fc\\u0a05-\\u0a0a\\u0a0f\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32\\u0a33\\u0a35\\u0a36\\u0a38\\u0a39\\u0a59-\\u0a5c\\u0a5e\\u0a72-\\u0a74\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2\\u0ab3\\u0ab5-\\u0ab9\\u0abd\\u0ad0\\u0ae0\\u0ae1\\u0af9\\u0b05-\\u0b0c\\u0b0f\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32\\u0b33\\u0b35-\\u0b39\\u0b3d\\u0b5c\\u0b5d\\u0b5f-\\u0b61\\u0b71\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99\\u0b9a\\u0b9c\\u0b9e\\u0b9f\\u0ba3\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bd0\\u0c05-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c39\\u0c3d\\u0c58-\\u0c5a\\u0c5d\\u0c60\\u0c61\\u0c80\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbd\\u0cdd\\u0cde\\u0ce0\\u0ce1\\u0cf1\\u0cf2\\u0d04-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d3a\\u0d3d\\u0d4e\\u0d54-\\u0d56\\u0d5f-\\u0d61\\u0d7a-\\u0d7f\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0e01-\\u0e30\\u0e32\\u0e33\\u0e40-\\u0e46\\u0e81\\u0e82\\u0e84\\u0e86-\\u0e8a\\u0e8c-\\u0ea3\\u0ea5\\u0ea7-\\u0eb0\\u0eb2\\u0eb3\\u0ebd\\u0ec0-\\u0ec4\\u0ec6\\u0edc-\\u0edf\\u0f00\\u0f40-\\u0f47\\u0f49-\\u0f6c\\u0f88-\\u0f8c\\u1000-\\u102a\\u103f\\u1050-\\u1055\\u105a-\\u105d\\u1061\\u1065\\u1066\\u106e-\\u1070\\u1075-\\u1081\\u108e\\u10a0-\\u10c5\\u10c7\\u10cd\\u10d0-\\u10fa\\u10fc-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be\\u12c0\\u12c2-\\u12c5\\u12c8-\\u12d6\\u12d8-\\u1310\\u1312-\\u1315\\u1318-\\u135a\\u1380-\\u138f\\u13a0-\\u13f5\\u13f8-\\u13fd\\u1401-\\u166c\\u166f-\\u167f\\u1681-\\u169a\\u16a0-\\u16ea\\u16ee-\\u16f8\\u1700-\\u1711\\u171f-\\u1731\\u1740-\\u1751\\u1760-\\u176c\\u176e-\\u1770\\u1780-\\u17b3\\u17d7\\u17dc\\u1820-\\u1878\\u1880-\\u18a8\\u18aa\\u18b0-\\u18f5\\u1900-\\u191e\\u1950-\\u196d\\u1970-\\u1974\\u1980-\\u19ab\\u19b0-\\u19c9\\u1a00-\\u1a16\\u1a20-\\u1a54\\u1aa7\\u1b05-\\u1b33\\u1b45-\\u1b4c\\u1b83-\\u1ba0\\u1bae\\u1baf\\u1bba-\\u1be5\\u1c00-\\u1c23\\u1c4d-\\u1c4f\\u1c5a-\\u1c7d\\u1c80-\\u1c8a\\u1c90-\\u1cba\\u1cbd-\\u1cbf\\u1ce9-\\u1cec\\u1cee-\\u1cf3\\u1cf5\\u1cf6\\u1cfa\\u1d00-\\u1dbf\\u1e00-\\u1f15\\u1f18-\\u1f1d\\u1f20-\\u1f45\\u1f48-\\u1f4d\\u1f50-\\u1f57\\u1f59\\u1f5b\\u1f5d\\u1f5f-\\u1f7d\\u1f80-\\u1fb4\\u1fb6-\\u1fbc\\u1fbe\\u1fc2-\\u1fc4\\u1fc6-\\u1fcc\\u1fd0-\\u1fd3\\u1fd6-\\u1fdb\\u1fe0-\\u1fec\\u1ff2-\\u1ff4\\u1ff6-\\u1ffc\\u2071\\u207f\\u2090-\\u209c\\u2102\\u2107\\u210a-\\u2113\\u2115\\u2118-\\u211d\\u2124\\u2126\\u2128\\u212a-\\u2139\\u213c-\\u213f\\u2145-\\u2149\\u214e\\u2160-\\u2188\\u2c00-\\u2ce4\\u2ceb-\\u2cee\\u2cf2\\u2cf3\\u2d00-\\u2d25\\u2d27\\u2d2d\\u2d30-\\u2d67\\u2d6f\\u2d80-\\u2d96\\u2da0-\\u2da6\\u2da8-\\u2dae\\u2db0-\\u2db6\\u2db8-\\u2dbe\\u2dc0-\\u2dc6\\u2dc8-\\u2dce\\u2dd0-\\u2dd6\\u2dd8-\\u2dde\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303c\\u3041-\\u3096\\u309b-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312f\\u3131-\\u318e\\u31a0-\\u31bf\\u31f0-\\u31ff\\u3400-\\u4dbf\\u4e00-\\ua48c\\ua4d0-\\ua4fd\\ua500-\\ua60c\\ua610-\\ua61f\\ua62a\\ua62b\\ua640-\\ua66e\\ua67f-\\ua69d\\ua6a0-\\ua6ef\\ua717-\\ua71f\\ua722-\\ua788\\ua78b-\\ua7cd\\ua7d0\\ua7d1\\ua7d3\\ua7d5-\\ua7dc\\ua7f2-\\ua801\\ua803-\\ua805\\ua807-\\ua80a\\ua80c-\\ua822\\ua840-\\ua873\\ua882-\\ua8b3\\ua8f2-\\ua8f7\\ua8fb\\ua8fd\\ua8fe\\ua90a-\\ua925\\ua930-\\ua946\\ua960-\\ua97c\\ua984-\\ua9b2\\ua9cf\\ua9e0-\\ua9e4\\ua9e6-\\ua9ef\\ua9fa-\\ua9fe\\uaa00-\\uaa28\\uaa40-\\uaa42\\uaa44-\\uaa4b\\uaa60-\\uaa76\\uaa7a\\uaa7e-\\uaaaf\\uaab1\\uaab5\\uaab6\\uaab9-\\uaabd\\uaac0\\uaac2\\uaadb-\\uaadd\\uaae0-\\uaaea\\uaaf2-\\uaaf4\\uab01-\\uab06\\uab09-\\uab0e\\uab11-\\uab16\\uab20-\\uab26\\uab28-\\uab2e\\uab30-\\uab5a\\uab5c-\\uab69\\uab70-\\uabe2\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufb00-\\ufb06\\ufb13-\\ufb17\\ufb1d\\ufb1f-\\ufb28\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40\\ufb41\\ufb43\\ufb44\\ufb46-\\ufbb1\\ufbd3-\\ufd3d\\ufd50-\\ufd8f\\ufd92-\\ufdc7\\ufdf0-\\ufdfb\\ufe70-\\ufe74\\ufe76-\\ufefc\\uff21-\\uff3a\\uff41-\\uff5a\\uff66-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc\";\n\n  // These are a run-length and offset encoded representation of the\n  // >0xffff code points that are a valid part of identifiers. The\n  // offset starts at 0x10000, and each pair of numbers represents an\n  // offset to the next range, and then a size of the range.\n\n  // Reserved word lists for various dialects of the language\n\n  var reservedWords = {\n    3: \"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile\",\n    5: \"class enum extends super const export import\",\n    6: \"enum\",\n    strict: \"implements interface let package private protected public static yield\",\n    strictBind: \"eval arguments\"\n  };\n\n  // And the keywords\n\n  var ecma5AndLessKeywords = \"break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this\";\n\n  var keywords$1 = {\n    5: ecma5AndLessKeywords,\n    \"5module\": ecma5AndLessKeywords + \" export import\",\n    6: ecma5AndLessKeywords + \" const class extends export import super\"\n  };\n\n  var keywordRelationalOperator = /^in(stanceof)?$/;\n\n  // ## Character categories\n\n  var nonASCIIidentifierStart = new RegExp(\"[\" + nonASCIIidentifierStartChars + \"]\");\n  var nonASCIIidentifier = new RegExp(\"[\" + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"]\");\n\n  // This has a complexity linear to the value of the code. The\n  // assumption is that looking up astral identifier characters is\n  // rare.\n  function isInAstralSet(code, set) {\n    var pos = 0x10000;\n    for (var i = 0; i < set.length; i += 2) {\n      pos += set[i];\n      if (pos > code) { return false }\n      pos += set[i + 1];\n      if (pos >= code) { return true }\n    }\n    return false\n  }\n\n  // Test whether a given character code starts an identifier.\n\n  function isIdentifierStart(code, astral) {\n    if (code < 65) { return code === 36 }\n    if (code < 91) { return true }\n    if (code < 97) { return code === 95 }\n    if (code < 123) { return true }\n    if (code <= 0xffff) { return code >= 0xaa && nonASCIIidentifierStart.test(String.fromCharCode(code)) }\n    if (astral === false) { return false }\n    return isInAstralSet(code, astralIdentifierStartCodes)\n  }\n\n  // Test whether a given character is part of an identifier.\n\n  function isIdentifierChar(code, astral) {\n    if (code < 48) { return code === 36 }\n    if (code < 58) { return true }\n    if (code < 65) { return false }\n    if (code < 91) { return true }\n    if (code < 97) { return code === 95 }\n    if (code < 123) { return true }\n    if (code <= 0xffff) { return code >= 0xaa && nonASCIIidentifier.test(String.fromCharCode(code)) }\n    if (astral === false) { return false }\n    return isInAstralSet(code, astralIdentifierStartCodes) || isInAstralSet(code, astralIdentifierCodes)\n  }\n\n  // ## Token types\n\n  // The assignment of fine-grained, information-carrying type objects\n  // allows the tokenizer to store the information it has about a\n  // token in a way that is very cheap for the parser to look up.\n\n  // All token type variables start with an underscore, to make them\n  // easy to recognize.\n\n  // The `beforeExpr` property is used to disambiguate between regular\n  // expressions and divisions. It is set on all token types that can\n  // be followed by an expression (thus, a slash after them would be a\n  // regular expression).\n  //\n  // The `startsExpr` property is used to check if the token ends a\n  // `yield` expression. It is set on all token types that either can\n  // directly start an expression (like a quotation mark) or can\n  // continue an expression (like the body of a string).\n  //\n  // `isLoop` marks a keyword as starting a loop, which is important\n  // to know when parsing a label, in order to allow or disallow\n  // continue jumps to that label.\n\n  var TokenType = function TokenType(label, conf) {\n    if ( conf === void 0 ) conf = {};\n\n    this.label = label;\n    this.keyword = conf.keyword;\n    this.beforeExpr = !!conf.beforeExpr;\n    this.startsExpr = !!conf.startsExpr;\n    this.isLoop = !!conf.isLoop;\n    this.isAssign = !!conf.isAssign;\n    this.prefix = !!conf.prefix;\n    this.postfix = !!conf.postfix;\n    this.binop = conf.binop || null;\n    this.updateContext = null;\n  };\n\n  function binop(name, prec) {\n    return new TokenType(name, {beforeExpr: true, binop: prec})\n  }\n  var beforeExpr = {beforeExpr: true}, startsExpr = {startsExpr: true};\n\n  // Map keyword names to token types.\n\n  var keywords = {};\n\n  // Succinct definitions of keyword token types\n  function kw(name, options) {\n    if ( options === void 0 ) options = {};\n\n    options.keyword = name;\n    return keywords[name] = new TokenType(name, options)\n  }\n\n  var types$1 = {\n    num: new TokenType(\"num\", startsExpr),\n    regexp: new TokenType(\"regexp\", startsExpr),\n    string: new TokenType(\"string\", startsExpr),\n    name: new TokenType(\"name\", startsExpr),\n    privateId: new TokenType(\"privateId\", startsExpr),\n    eof: new TokenType(\"eof\"),\n\n    // Punctuation token types.\n    bracketL: new TokenType(\"[\", {beforeExpr: true, startsExpr: true}),\n    bracketR: new TokenType(\"]\"),\n    braceL: new TokenType(\"{\", {beforeExpr: true, startsExpr: true}),\n    braceR: new TokenType(\"}\"),\n    parenL: new TokenType(\"(\", {beforeExpr: true, startsExpr: true}),\n    parenR: new TokenType(\")\"),\n    comma: new TokenType(\",\", beforeExpr),\n    semi: new TokenType(\";\", beforeExpr),\n    colon: new TokenType(\":\", beforeExpr),\n    dot: new TokenType(\".\"),\n    question: new TokenType(\"?\", beforeExpr),\n    questionDot: new TokenType(\"?.\"),\n    arrow: new TokenType(\"=>\", beforeExpr),\n    template: new TokenType(\"template\"),\n    invalidTemplate: new TokenType(\"invalidTemplate\"),\n    ellipsis: new TokenType(\"...\", beforeExpr),\n    backQuote: new TokenType(\"`\", startsExpr),\n    dollarBraceL: new TokenType(\"${\", {beforeExpr: true, startsExpr: true}),\n\n    // Operators. These carry several kinds of properties to help the\n    // parser use them properly (the presence of these properties is\n    // what categorizes them as operators).\n    //\n    // `binop`, when present, specifies that this operator is a binary\n    // operator, and will refer to its precedence.\n    //\n    // `prefix` and `postfix` mark the operator as a prefix or postfix\n    // unary operator.\n    //\n    // `isAssign` marks all of `=`, `+=`, `-=` etcetera, which act as\n    // binary operators with a very low precedence, that should result\n    // in AssignmentExpression nodes.\n\n    eq: new TokenType(\"=\", {beforeExpr: true, isAssign: true}),\n    assign: new TokenType(\"_=\", {beforeExpr: true, isAssign: true}),\n    incDec: new TokenType(\"++/--\", {prefix: true, postfix: true, startsExpr: true}),\n    prefix: new TokenType(\"!/~\", {beforeExpr: true, prefix: true, startsExpr: true}),\n    logicalOR: binop(\"||\", 1),\n    logicalAND: binop(\"&&\", 2),\n    bitwiseOR: binop(\"|\", 3),\n    bitwiseXOR: binop(\"^\", 4),\n    bitwiseAND: binop(\"&\", 5),\n    equality: binop(\"==/!=/===/!==\", 6),\n    relational: binop(\"</>/<=/>=\", 7),\n    bitShift: binop(\"<</>>/>>>\", 8),\n    plusMin: new TokenType(\"+/-\", {beforeExpr: true, binop: 9, prefix: true, startsExpr: true}),\n    modulo: binop(\"%\", 10),\n    star: binop(\"*\", 10),\n    slash: binop(\"/\", 10),\n    starstar: new TokenType(\"**\", {beforeExpr: true}),\n    coalesce: binop(\"??\", 1),\n\n    // Keyword token types.\n    _break: kw(\"break\"),\n    _case: kw(\"case\", beforeExpr),\n    _catch: kw(\"catch\"),\n    _continue: kw(\"continue\"),\n    _debugger: kw(\"debugger\"),\n    _default: kw(\"default\", beforeExpr),\n    _do: kw(\"do\", {isLoop: true, beforeExpr: true}),\n    _else: kw(\"else\", beforeExpr),\n    _finally: kw(\"finally\"),\n    _for: kw(\"for\", {isLoop: true}),\n    _function: kw(\"function\", startsExpr),\n    _if: kw(\"if\"),\n    _return: kw(\"return\", beforeExpr),\n    _switch: kw(\"switch\"),\n    _throw: kw(\"throw\", beforeExpr),\n    _try: kw(\"try\"),\n    _var: kw(\"var\"),\n    _const: kw(\"const\"),\n    _while: kw(\"while\", {isLoop: true}),\n    _with: kw(\"with\"),\n    _new: kw(\"new\", {beforeExpr: true, startsExpr: true}),\n    _this: kw(\"this\", startsExpr),\n    _super: kw(\"super\", startsExpr),\n    _class: kw(\"class\", startsExpr),\n    _extends: kw(\"extends\", beforeExpr),\n    _export: kw(\"export\"),\n    _import: kw(\"import\", startsExpr),\n    _null: kw(\"null\", startsExpr),\n    _true: kw(\"true\", startsExpr),\n    _false: kw(\"false\", startsExpr),\n    _in: kw(\"in\", {beforeExpr: true, binop: 7}),\n    _instanceof: kw(\"instanceof\", {beforeExpr: true, binop: 7}),\n    _typeof: kw(\"typeof\", {beforeExpr: true, prefix: true, startsExpr: true}),\n    _void: kw(\"void\", {beforeExpr: true, prefix: true, startsExpr: true}),\n    _delete: kw(\"delete\", {beforeExpr: true, prefix: true, startsExpr: true})\n  };\n\n  // Matches a whole line break (where CRLF is considered a single\n  // line break). Used to count lines.\n\n  var lineBreak = /\\r\\n?|\\n|\\u2028|\\u2029/;\n  var lineBreakG = new RegExp(lineBreak.source, \"g\");\n\n  function isNewLine(code) {\n    return code === 10 || code === 13 || code === 0x2028 || code === 0x2029\n  }\n\n  function nextLineBreak(code, from, end) {\n    if ( end === void 0 ) end = code.length;\n\n    for (var i = from; i < end; i++) {\n      var next = code.charCodeAt(i);\n      if (isNewLine(next))\n        { return i < end - 1 && next === 13 && code.charCodeAt(i + 1) === 10 ? i + 2 : i + 1 }\n    }\n    return -1\n  }\n\n  var nonASCIIwhitespace = /[\\u1680\\u2000-\\u200a\\u202f\\u205f\\u3000\\ufeff]/;\n\n  var skipWhiteSpace = /(?:\\s|\\/\\/.*|\\/\\*[^]*?\\*\\/)*/g;\n\n  var ref = Object.prototype;\n  var hasOwnProperty = ref.hasOwnProperty;\n  var toString = ref.toString;\n\n  var hasOwn = Object.hasOwn || (function (obj, propName) { return (\n    hasOwnProperty.call(obj, propName)\n  ); });\n\n  var isArray = Array.isArray || (function (obj) { return (\n    toString.call(obj) === \"[object Array]\"\n  ); });\n\n  var regexpCache = Object.create(null);\n\n  function wordsRegexp(words) {\n    return regexpCache[words] || (regexpCache[words] = new RegExp(\"^(?:\" + words.replace(/ /g, \"|\") + \")$\"))\n  }\n\n  function codePointToString(code) {\n    // UTF-16 Decoding\n    if (code <= 0xFFFF) { return String.fromCharCode(code) }\n    code -= 0x10000;\n    return String.fromCharCode((code >> 10) + 0xD800, (code & 1023) + 0xDC00)\n  }\n\n  var loneSurrogate = /(?:[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])/;\n\n  // These are used when `options.locations` is on, for the\n  // `startLoc` and `endLoc` properties.\n\n  var Position = function Position(line, col) {\n    this.line = line;\n    this.column = col;\n  };\n\n  Position.prototype.offset = function offset (n) {\n    return new Position(this.line, this.column + n)\n  };\n\n  var SourceLocation = function SourceLocation(p, start, end) {\n    this.start = start;\n    this.end = end;\n    if (p.sourceFile !== null) { this.source = p.sourceFile; }\n  };\n\n  // The `getLineInfo` function is mostly useful when the\n  // `locations` option is off (for performance reasons) and you\n  // want to find the line/column position for a given character\n  // offset. `input` should be the code string that the offset refers\n  // into.\n\n  function getLineInfo(input, offset) {\n    for (var line = 1, cur = 0;;) {\n      var nextBreak = nextLineBreak(input, cur, offset);\n      if (nextBreak < 0) { return new Position(line, offset - cur) }\n      ++line;\n      cur = nextBreak;\n    }\n  }\n\n  // A second argument must be given to configure the parser process.\n  // These options are recognized (only `ecmaVersion` is required):\n\n  var defaultOptions = {\n    // `ecmaVersion` indicates the ECMAScript version to parse. Must be\n    // either 3, 5, 6 (or 2015), 7 (2016), 8 (2017), 9 (2018), 10\n    // (2019), 11 (2020), 12 (2021), 13 (2022), 14 (2023), or `\"latest\"`\n    // (the latest version the library supports). This influences\n    // support for strict mode, the set of reserved words, and support\n    // for new syntax features.\n    ecmaVersion: null,\n    // `sourceType` indicates the mode the code should be parsed in.\n    // Can be either `\"script\"` or `\"module\"`. This influences global\n    // strict mode and parsing of `import` and `export` declarations.\n    sourceType: \"script\",\n    // `onInsertedSemicolon` can be a callback that will be called when\n    // a semicolon is automatically inserted. It will be passed the\n    // position of the inserted semicolon as an offset, and if\n    // `locations` is enabled, it is given the location as a `{line,\n    // column}` object as second argument.\n    onInsertedSemicolon: null,\n    // `onTrailingComma` is similar to `onInsertedSemicolon`, but for\n    // trailing commas.\n    onTrailingComma: null,\n    // By default, reserved words are only enforced if ecmaVersion >= 5.\n    // Set `allowReserved` to a boolean value to explicitly turn this on\n    // an off. When this option has the value \"never\", reserved words\n    // and keywords can also not be used as property names.\n    allowReserved: null,\n    // When enabled, a return at the top level is not considered an\n    // error.\n    allowReturnOutsideFunction: false,\n    // When enabled, import/export statements are not constrained to\n    // appearing at the top of the program, and an import.meta expression\n    // in a script isn't considered an error.\n    allowImportExportEverywhere: false,\n    // By default, await identifiers are allowed to appear at the top-level scope only if ecmaVersion >= 2022.\n    // When enabled, await identifiers are allowed to appear at the top-level scope,\n    // but they are still not allowed in non-async functions.\n    allowAwaitOutsideFunction: null,\n    // When enabled, super identifiers are not constrained to\n    // appearing in methods and do not raise an error when they appear elsewhere.\n    allowSuperOutsideMethod: null,\n    // When enabled, hashbang directive in the beginning of file is\n    // allowed and treated as a line comment. Enabled by default when\n    // `ecmaVersion` >= 2023.\n    allowHashBang: false,\n    // By default, the parser will verify that private properties are\n    // only used in places where they are valid and have been declared.\n    // Set this to false to turn such checks off.\n    checkPrivateFields: true,\n    // When `locations` is on, `loc` properties holding objects with\n    // `start` and `end` properties in `{line, column}` form (with\n    // line being 1-based and column 0-based) will be attached to the\n    // nodes.\n    locations: false,\n    // A function can be passed as `onToken` option, which will\n    // cause Acorn to call that function with object in the same\n    // format as tokens returned from `tokenizer().getToken()`. Note\n    // that you are not allowed to call the parser from the\n    // callback—that will corrupt its internal state.\n    onToken: null,\n    // A function can be passed as `onComment` option, which will\n    // cause Acorn to call that function with `(block, text, start,\n    // end)` parameters whenever a comment is skipped. `block` is a\n    // boolean indicating whether this is a block (`/* */`) comment,\n    // `text` is the content of the comment, and `start` and `end` are\n    // character offsets that denote the start and end of the comment.\n    // When the `locations` option is on, two more parameters are\n    // passed, the full `{line, column}` locations of the start and\n    // end of the comments. Note that you are not allowed to call the\n    // parser from the callback—that will corrupt its internal state.\n    // When this option has an array as value, objects representing the\n    // comments are pushed to it.\n    onComment: null,\n    // Nodes have their start and end characters offsets recorded in\n    // `start` and `end` properties (directly on the node, rather than\n    // the `loc` object, which holds line/column data. To also add a\n    // [semi-standardized][range] `range` property holding a `[start,\n    // end]` array with the same numbers, set the `ranges` option to\n    // `true`.\n    //\n    // [range]: https://bugzilla.mozilla.org/show_bug.cgi?id=745678\n    ranges: false,\n    // It is possible to parse multiple files into a single AST by\n    // passing the tree produced by parsing the first file as\n    // `program` option in subsequent parses. This will add the\n    // toplevel forms of the parsed file to the `Program` (top) node\n    // of an existing parse tree.\n    program: null,\n    // When `locations` is on, you can pass this to record the source\n    // file in every node's `loc` object.\n    sourceFile: null,\n    // This value, if given, is stored in every node, whether\n    // `locations` is on or off.\n    directSourceFile: null,\n    // When enabled, parenthesized expressions are represented by\n    // (non-standard) ParenthesizedExpression nodes\n    preserveParens: false\n  };\n\n  // Interpret and default an options object\n\n  var warnedAboutEcmaVersion = false;\n\n  function getOptions(opts) {\n    var options = {};\n\n    for (var opt in defaultOptions)\n      { options[opt] = opts && hasOwn(opts, opt) ? opts[opt] : defaultOptions[opt]; }\n\n    if (options.ecmaVersion === \"latest\") {\n      options.ecmaVersion = 1e8;\n    } else if (options.ecmaVersion == null) {\n      if (!warnedAboutEcmaVersion && typeof console === \"object\" && console.warn) {\n        warnedAboutEcmaVersion = true;\n        console.warn(\"Since Acorn 8.0.0, options.ecmaVersion is required.\\nDefaulting to 2020, but this will stop working in the future.\");\n      }\n      options.ecmaVersion = 11;\n    } else if (options.ecmaVersion >= 2015) {\n      options.ecmaVersion -= 2009;\n    }\n\n    if (options.allowReserved == null)\n      { options.allowReserved = options.ecmaVersion < 5; }\n\n    if (!opts || opts.allowHashBang == null)\n      { options.allowHashBang = options.ecmaVersion >= 14; }\n\n    if (isArray(options.onToken)) {\n      var tokens = options.onToken;\n      options.onToken = function (token) { return tokens.push(token); };\n    }\n    if (isArray(options.onComment))\n      { options.onComment = pushComment(options, options.onComment); }\n\n    return options\n  }\n\n  function pushComment(options, array) {\n    return function(block, text, start, end, startLoc, endLoc) {\n      var comment = {\n        type: block ? \"Block\" : \"Line\",\n        value: text,\n        start: start,\n        end: end\n      };\n      if (options.locations)\n        { comment.loc = new SourceLocation(this, startLoc, endLoc); }\n      if (options.ranges)\n        { comment.range = [start, end]; }\n      array.push(comment);\n    }\n  }\n\n  // Each scope gets a bitset that may contain these flags\n  var\n      SCOPE_TOP = 1,\n      SCOPE_FUNCTION = 2,\n      SCOPE_ASYNC = 4,\n      SCOPE_GENERATOR = 8,\n      SCOPE_ARROW = 16,\n      SCOPE_SIMPLE_CATCH = 32,\n      SCOPE_SUPER = 64,\n      SCOPE_DIRECT_SUPER = 128,\n      SCOPE_CLASS_STATIC_BLOCK = 256,\n      SCOPE_CLASS_FIELD_INIT = 512,\n      SCOPE_VAR = SCOPE_TOP | SCOPE_FUNCTION | SCOPE_CLASS_STATIC_BLOCK;\n\n  function functionFlags(async, generator) {\n    return SCOPE_FUNCTION | (async ? SCOPE_ASYNC : 0) | (generator ? SCOPE_GENERATOR : 0)\n  }\n\n  // Used in checkLVal* and declareName to determine the type of a binding\n  var\n      BIND_NONE = 0, // Not a binding\n      BIND_VAR = 1, // Var-style binding\n      BIND_LEXICAL = 2, // Let- or const-style binding\n      BIND_FUNCTION = 3, // Function declaration\n      BIND_SIMPLE_CATCH = 4, // Simple (identifier pattern) catch binding\n      BIND_OUTSIDE = 5; // Special case for function names as bound inside the function\n\n  var Parser = function Parser(options, input, startPos) {\n    this.options = options = getOptions(options);\n    this.sourceFile = options.sourceFile;\n    this.keywords = wordsRegexp(keywords$1[options.ecmaVersion >= 6 ? 6 : options.sourceType === \"module\" ? \"5module\" : 5]);\n    var reserved = \"\";\n    if (options.allowReserved !== true) {\n      reserved = reservedWords[options.ecmaVersion >= 6 ? 6 : options.ecmaVersion === 5 ? 5 : 3];\n      if (options.sourceType === \"module\") { reserved += \" await\"; }\n    }\n    this.reservedWords = wordsRegexp(reserved);\n    var reservedStrict = (reserved ? reserved + \" \" : \"\") + reservedWords.strict;\n    this.reservedWordsStrict = wordsRegexp(reservedStrict);\n    this.reservedWordsStrictBind = wordsRegexp(reservedStrict + \" \" + reservedWords.strictBind);\n    this.input = String(input);\n\n    // Used to signal to callers of `readWord1` whether the word\n    // contained any escape sequences. This is needed because words with\n    // escape sequences must not be interpreted as keywords.\n    this.containsEsc = false;\n\n    // Set up token state\n\n    // The current position of the tokenizer in the input.\n    if (startPos) {\n      this.pos = startPos;\n      this.lineStart = this.input.lastIndexOf(\"\\n\", startPos - 1) + 1;\n      this.curLine = this.input.slice(0, this.lineStart).split(lineBreak).length;\n    } else {\n      this.pos = this.lineStart = 0;\n      this.curLine = 1;\n    }\n\n    // Properties of the current token:\n    // Its type\n    this.type = types$1.eof;\n    // For tokens that include more information than their type, the value\n    this.value = null;\n    // Its start and end offset\n    this.start = this.end = this.pos;\n    // And, if locations are used, the {line, column} object\n    // corresponding to those offsets\n    this.startLoc = this.endLoc = this.curPosition();\n\n    // Position information for the previous token\n    this.lastTokEndLoc = this.lastTokStartLoc = null;\n    this.lastTokStart = this.lastTokEnd = this.pos;\n\n    // The context stack is used to superficially track syntactic\n    // context to predict whether a regular expression is allowed in a\n    // given position.\n    this.context = this.initialContext();\n    this.exprAllowed = true;\n\n    // Figure out if it's a module code.\n    this.inModule = options.sourceType === \"module\";\n    this.strict = this.inModule || this.strictDirective(this.pos);\n\n    // Used to signify the start of a potential arrow function\n    this.potentialArrowAt = -1;\n    this.potentialArrowInForAwait = false;\n\n    // Positions to delayed-check that yield/await does not exist in default parameters.\n    this.yieldPos = this.awaitPos = this.awaitIdentPos = 0;\n    // Labels in scope.\n    this.labels = [];\n    // Thus-far undefined exports.\n    this.undefinedExports = Object.create(null);\n\n    // If enabled, skip leading hashbang line.\n    if (this.pos === 0 && options.allowHashBang && this.input.slice(0, 2) === \"#!\")\n      { this.skipLineComment(2); }\n\n    // Scope tracking for duplicate variable names (see scope.js)\n    this.scopeStack = [];\n    this.enterScope(SCOPE_TOP);\n\n    // For RegExp validation\n    this.regexpState = null;\n\n    // The stack of private names.\n    // Each element has two properties: 'declared' and 'used'.\n    // When it exited from the outermost class definition, all used private names must be declared.\n    this.privateNameStack = [];\n  };\n\n  var prototypeAccessors = { inFunction: { configurable: true },inGenerator: { configurable: true },inAsync: { configurable: true },canAwait: { configurable: true },allowSuper: { configurable: true },allowDirectSuper: { configurable: true },treatFunctionsAsVar: { configurable: true },allowNewDotTarget: { configurable: true },inClassStaticBlock: { configurable: true } };\n\n  Parser.prototype.parse = function parse () {\n    var node = this.options.program || this.startNode();\n    this.nextToken();\n    return this.parseTopLevel(node)\n  };\n\n  prototypeAccessors.inFunction.get = function () { return (this.currentVarScope().flags & SCOPE_FUNCTION) > 0 };\n\n  prototypeAccessors.inGenerator.get = function () { return (this.currentVarScope().flags & SCOPE_GENERATOR) > 0 };\n\n  prototypeAccessors.inAsync.get = function () { return (this.currentVarScope().flags & SCOPE_ASYNC) > 0 };\n\n  prototypeAccessors.canAwait.get = function () {\n    for (var i = this.scopeStack.length - 1; i >= 0; i--) {\n      var ref = this.scopeStack[i];\n        var flags = ref.flags;\n      if (flags & (SCOPE_CLASS_STATIC_BLOCK | SCOPE_CLASS_FIELD_INIT)) { return false }\n      if (flags & SCOPE_FUNCTION) { return (flags & SCOPE_ASYNC) > 0 }\n    }\n    return (this.inModule && this.options.ecmaVersion >= 13) || this.options.allowAwaitOutsideFunction\n  };\n\n  prototypeAccessors.allowSuper.get = function () {\n    var ref = this.currentThisScope();\n      var flags = ref.flags;\n    return (flags & SCOPE_SUPER) > 0 || this.options.allowSuperOutsideMethod\n  };\n\n  prototypeAccessors.allowDirectSuper.get = function () { return (this.currentThisScope().flags & SCOPE_DIRECT_SUPER) > 0 };\n\n  prototypeAccessors.treatFunctionsAsVar.get = function () { return this.treatFunctionsAsVarInScope(this.currentScope()) };\n\n  prototypeAccessors.allowNewDotTarget.get = function () {\n    for (var i = this.scopeStack.length - 1; i >= 0; i--) {\n      var ref = this.scopeStack[i];\n        var flags = ref.flags;\n      if (flags & (SCOPE_CLASS_STATIC_BLOCK | SCOPE_CLASS_FIELD_INIT) ||\n          ((flags & SCOPE_FUNCTION) && !(flags & SCOPE_ARROW))) { return true }\n    }\n    return false\n  };\n\n  prototypeAccessors.inClassStaticBlock.get = function () {\n    return (this.currentVarScope().flags & SCOPE_CLASS_STATIC_BLOCK) > 0\n  };\n\n  Parser.extend = function extend () {\n      var plugins = [], len = arguments.length;\n      while ( len-- ) plugins[ len ] = arguments[ len ];\n\n    var cls = this;\n    for (var i = 0; i < plugins.length; i++) { cls = plugins[i](cls); }\n    return cls\n  };\n\n  Parser.parse = function parse (input, options) {\n    return new this(options, input).parse()\n  };\n\n  Parser.parseExpressionAt = function parseExpressionAt (input, pos, options) {\n    var parser = new this(options, input, pos);\n    parser.nextToken();\n    return parser.parseExpression()\n  };\n\n  Parser.tokenizer = function tokenizer (input, options) {\n    return new this(options, input)\n  };\n\n  Object.defineProperties( Parser.prototype, prototypeAccessors );\n\n  var pp$9 = Parser.prototype;\n\n  // ## Parser utilities\n\n  var literal = /^(?:'((?:\\\\[^]|[^'\\\\])*?)'|\"((?:\\\\[^]|[^\"\\\\])*?)\")/;\n  pp$9.strictDirective = function(start) {\n    if (this.options.ecmaVersion < 5) { return false }\n    for (;;) {\n      // Try to find string literal.\n      skipWhiteSpace.lastIndex = start;\n      start += skipWhiteSpace.exec(this.input)[0].length;\n      var match = literal.exec(this.input.slice(start));\n      if (!match) { return false }\n      if ((match[1] || match[2]) === \"use strict\") {\n        skipWhiteSpace.lastIndex = start + match[0].length;\n        var spaceAfter = skipWhiteSpace.exec(this.input), end = spaceAfter.index + spaceAfter[0].length;\n        var next = this.input.charAt(end);\n        return next === \";\" || next === \"}\" ||\n          (lineBreak.test(spaceAfter[0]) &&\n           !(/[(`.[+\\-/*%<>=,?^&]/.test(next) || next === \"!\" && this.input.charAt(end + 1) === \"=\"))\n      }\n      start += match[0].length;\n\n      // Skip semicolon, if any.\n      skipWhiteSpace.lastIndex = start;\n      start += skipWhiteSpace.exec(this.input)[0].length;\n      if (this.input[start] === \";\")\n        { start++; }\n    }\n  };\n\n  // Predicate that tests whether the next token is of the given\n  // type, and if yes, consumes it as a side effect.\n\n  pp$9.eat = function(type) {\n    if (this.type === type) {\n      this.next();\n      return true\n    } else {\n      return false\n    }\n  };\n\n  // Tests whether parsed token is a contextual keyword.\n\n  pp$9.isContextual = function(name) {\n    return this.type === types$1.name && this.value === name && !this.containsEsc\n  };\n\n  // Consumes contextual keyword if possible.\n\n  pp$9.eatContextual = function(name) {\n    if (!this.isContextual(name)) { return false }\n    this.next();\n    return true\n  };\n\n  // Asserts that following token is given contextual keyword.\n\n  pp$9.expectContextual = function(name) {\n    if (!this.eatContextual(name)) { this.unexpected(); }\n  };\n\n  // Test whether a semicolon can be inserted at the current position.\n\n  pp$9.canInsertSemicolon = function() {\n    return this.type === types$1.eof ||\n      this.type === types$1.braceR ||\n      lineBreak.test(this.input.slice(this.lastTokEnd, this.start))\n  };\n\n  pp$9.insertSemicolon = function() {\n    if (this.canInsertSemicolon()) {\n      if (this.options.onInsertedSemicolon)\n        { this.options.onInsertedSemicolon(this.lastTokEnd, this.lastTokEndLoc); }\n      return true\n    }\n  };\n\n  // Consume a semicolon, or, failing that, see if we are allowed to\n  // pretend that there is a semicolon at this position.\n\n  pp$9.semicolon = function() {\n    if (!this.eat(types$1.semi) && !this.insertSemicolon()) { this.unexpected(); }\n  };\n\n  pp$9.afterTrailingComma = function(tokType, notNext) {\n    if (this.type === tokType) {\n      if (this.options.onTrailingComma)\n        { this.options.onTrailingComma(this.lastTokStart, this.lastTokStartLoc); }\n      if (!notNext)\n        { this.next(); }\n      return true\n    }\n  };\n\n  // Expect a token of a given type. If found, consume it, otherwise,\n  // raise an unexpected token error.\n\n  pp$9.expect = function(type) {\n    this.eat(type) || this.unexpected();\n  };\n\n  // Raise an unexpected token error.\n\n  pp$9.unexpected = function(pos) {\n    this.raise(pos != null ? pos : this.start, \"Unexpected token\");\n  };\n\n  var DestructuringErrors = function DestructuringErrors() {\n    this.shorthandAssign =\n    this.trailingComma =\n    this.parenthesizedAssign =\n    this.parenthesizedBind =\n    this.doubleProto =\n      -1;\n  };\n\n  pp$9.checkPatternErrors = function(refDestructuringErrors, isAssign) {\n    if (!refDestructuringErrors) { return }\n    if (refDestructuringErrors.trailingComma > -1)\n      { this.raiseRecoverable(refDestructuringErrors.trailingComma, \"Comma is not permitted after the rest element\"); }\n    var parens = isAssign ? refDestructuringErrors.parenthesizedAssign : refDestructuringErrors.parenthesizedBind;\n    if (parens > -1) { this.raiseRecoverable(parens, isAssign ? \"Assigning to rvalue\" : \"Parenthesized pattern\"); }\n  };\n\n  pp$9.checkExpressionErrors = function(refDestructuringErrors, andThrow) {\n    if (!refDestructuringErrors) { return false }\n    var shorthandAssign = refDestructuringErrors.shorthandAssign;\n    var doubleProto = refDestructuringErrors.doubleProto;\n    if (!andThrow) { return shorthandAssign >= 0 || doubleProto >= 0 }\n    if (shorthandAssign >= 0)\n      { this.raise(shorthandAssign, \"Shorthand property assignments are valid only in destructuring patterns\"); }\n    if (doubleProto >= 0)\n      { this.raiseRecoverable(doubleProto, \"Redefinition of __proto__ property\"); }\n  };\n\n  pp$9.checkYieldAwaitInDefaultParams = function() {\n    if (this.yieldPos && (!this.awaitPos || this.yieldPos < this.awaitPos))\n      { this.raise(this.yieldPos, \"Yield expression cannot be a default value\"); }\n    if (this.awaitPos)\n      { this.raise(this.awaitPos, \"Await expression cannot be a default value\"); }\n  };\n\n  pp$9.isSimpleAssignTarget = function(expr) {\n    if (expr.type === \"ParenthesizedExpression\")\n      { return this.isSimpleAssignTarget(expr.expression) }\n    return expr.type === \"Identifier\" || expr.type === \"MemberExpression\"\n  };\n\n  var pp$8 = Parser.prototype;\n\n  // ### Statement parsing\n\n  // Parse a program. Initializes the parser, reads any number of\n  // statements, and wraps them in a Program node.  Optionally takes a\n  // `program` argument.  If present, the statements will be appended\n  // to its body instead of creating a new node.\n\n  pp$8.parseTopLevel = function(node) {\n    var exports = Object.create(null);\n    if (!node.body) { node.body = []; }\n    while (this.type !== types$1.eof) {\n      var stmt = this.parseStatement(null, true, exports);\n      node.body.push(stmt);\n    }\n    if (this.inModule)\n      { for (var i = 0, list = Object.keys(this.undefinedExports); i < list.length; i += 1)\n        {\n          var name = list[i];\n\n          this.raiseRecoverable(this.undefinedExports[name].start, (\"Export '\" + name + \"' is not defined\"));\n        } }\n    this.adaptDirectivePrologue(node.body);\n    this.next();\n    node.sourceType = this.options.sourceType;\n    return this.finishNode(node, \"Program\")\n  };\n\n  var loopLabel = {kind: \"loop\"}, switchLabel = {kind: \"switch\"};\n\n  pp$8.isLet = function(context) {\n    if (this.options.ecmaVersion < 6 || !this.isContextual(\"let\")) { return false }\n    skipWhiteSpace.lastIndex = this.pos;\n    var skip = skipWhiteSpace.exec(this.input);\n    var next = this.pos + skip[0].length, nextCh = this.input.charCodeAt(next);\n    // For ambiguous cases, determine if a LexicalDeclaration (or only a\n    // Statement) is allowed here. If context is not empty then only a Statement\n    // is allowed. However, `let [` is an explicit negative lookahead for\n    // ExpressionStatement, so special-case it first.\n    if (nextCh === 91 || nextCh === 92) { return true } // '[', '\\'\n    if (context) { return false }\n\n    if (nextCh === 123 || nextCh > 0xd7ff && nextCh < 0xdc00) { return true } // '{', astral\n    if (isIdentifierStart(nextCh, true)) {\n      var pos = next + 1;\n      while (isIdentifierChar(nextCh = this.input.charCodeAt(pos), true)) { ++pos; }\n      if (nextCh === 92 || nextCh > 0xd7ff && nextCh < 0xdc00) { return true }\n      var ident = this.input.slice(next, pos);\n      if (!keywordRelationalOperator.test(ident)) { return true }\n    }\n    return false\n  };\n\n  // check 'async [no LineTerminator here] function'\n  // - 'async /*foo*/ function' is OK.\n  // - 'async /*\\n*/ function' is invalid.\n  pp$8.isAsyncFunction = function() {\n    if (this.options.ecmaVersion < 8 || !this.isContextual(\"async\"))\n      { return false }\n\n    skipWhiteSpace.lastIndex = this.pos;\n    var skip = skipWhiteSpace.exec(this.input);\n    var next = this.pos + skip[0].length, after;\n    return !lineBreak.test(this.input.slice(this.pos, next)) &&\n      this.input.slice(next, next + 8) === \"function\" &&\n      (next + 8 === this.input.length ||\n       !(isIdentifierChar(after = this.input.charCodeAt(next + 8)) || after > 0xd7ff && after < 0xdc00))\n  };\n\n  pp$8.isUsingKeyword = function(isAwaitUsing, isFor) {\n    if (this.options.ecmaVersion < 17 || !this.isContextual(isAwaitUsing ? \"await\" : \"using\"))\n      { return false }\n\n    skipWhiteSpace.lastIndex = this.pos;\n    var skip = skipWhiteSpace.exec(this.input);\n    var next = this.pos + skip[0].length;\n\n    if (lineBreak.test(this.input.slice(this.pos, next))) { return false }\n\n    if (isAwaitUsing) {\n      var awaitEndPos = next + 5 /* await */, after;\n      if (this.input.slice(next, awaitEndPos) !== \"using\" ||\n        awaitEndPos === this.input.length ||\n        isIdentifierChar(after = this.input.charCodeAt(awaitEndPos)) ||\n        (after > 0xd7ff && after < 0xdc00)\n      ) { return false }\n\n      skipWhiteSpace.lastIndex = awaitEndPos;\n      var skipAfterUsing = skipWhiteSpace.exec(this.input);\n      if (skipAfterUsing && lineBreak.test(this.input.slice(awaitEndPos, awaitEndPos + skipAfterUsing[0].length))) { return false }\n    }\n\n    if (isFor) {\n      var ofEndPos = next + 2 /* of */, after$1;\n      if (this.input.slice(next, ofEndPos) === \"of\") {\n        if (ofEndPos === this.input.length ||\n          (!isIdentifierChar(after$1 = this.input.charCodeAt(ofEndPos)) && !(after$1 > 0xd7ff && after$1 < 0xdc00))) { return false }\n      }\n    }\n\n    var ch = this.input.charCodeAt(next);\n    return isIdentifierStart(ch, true) || ch === 92 // '\\'\n  };\n\n  pp$8.isAwaitUsing = function(isFor) {\n    return this.isUsingKeyword(true, isFor)\n  };\n\n  pp$8.isUsing = function(isFor) {\n    return this.isUsingKeyword(false, isFor)\n  };\n\n  // Parse a single statement.\n  //\n  // If expecting a statement and finding a slash operator, parse a\n  // regular expression literal. This is to handle cases like\n  // `if (foo) /blah/.exec(foo)`, where looking at the previous token\n  // does not help.\n\n  pp$8.parseStatement = function(context, topLevel, exports) {\n    var starttype = this.type, node = this.startNode(), kind;\n\n    if (this.isLet(context)) {\n      starttype = types$1._var;\n      kind = \"let\";\n    }\n\n    // Most types of statements are recognized by the keyword they\n    // start with. Many are trivial to parse, some require a bit of\n    // complexity.\n\n    switch (starttype) {\n    case types$1._break: case types$1._continue: return this.parseBreakContinueStatement(node, starttype.keyword)\n    case types$1._debugger: return this.parseDebuggerStatement(node)\n    case types$1._do: return this.parseDoStatement(node)\n    case types$1._for: return this.parseForStatement(node)\n    case types$1._function:\n      // Function as sole body of either an if statement or a labeled statement\n      // works, but not when it is part of a labeled statement that is the sole\n      // body of an if statement.\n      if ((context && (this.strict || context !== \"if\" && context !== \"label\")) && this.options.ecmaVersion >= 6) { this.unexpected(); }\n      return this.parseFunctionStatement(node, false, !context)\n    case types$1._class:\n      if (context) { this.unexpected(); }\n      return this.parseClass(node, true)\n    case types$1._if: return this.parseIfStatement(node)\n    case types$1._return: return this.parseReturnStatement(node)\n    case types$1._switch: return this.parseSwitchStatement(node)\n    case types$1._throw: return this.parseThrowStatement(node)\n    case types$1._try: return this.parseTryStatement(node)\n    case types$1._const: case types$1._var:\n      kind = kind || this.value;\n      if (context && kind !== \"var\") { this.unexpected(); }\n      return this.parseVarStatement(node, kind)\n    case types$1._while: return this.parseWhileStatement(node)\n    case types$1._with: return this.parseWithStatement(node)\n    case types$1.braceL: return this.parseBlock(true, node)\n    case types$1.semi: return this.parseEmptyStatement(node)\n    case types$1._export:\n    case types$1._import:\n      if (this.options.ecmaVersion > 10 && starttype === types$1._import) {\n        skipWhiteSpace.lastIndex = this.pos;\n        var skip = skipWhiteSpace.exec(this.input);\n        var next = this.pos + skip[0].length, nextCh = this.input.charCodeAt(next);\n        if (nextCh === 40 || nextCh === 46) // '(' or '.'\n          { return this.parseExpressionStatement(node, this.parseExpression()) }\n      }\n\n      if (!this.options.allowImportExportEverywhere) {\n        if (!topLevel)\n          { this.raise(this.start, \"'import' and 'export' may only appear at the top level\"); }\n        if (!this.inModule)\n          { this.raise(this.start, \"'import' and 'export' may appear only with 'sourceType: module'\"); }\n      }\n      return starttype === types$1._import ? this.parseImport(node) : this.parseExport(node, exports)\n\n      // If the statement does not start with a statement keyword or a\n      // brace, it's an ExpressionStatement or LabeledStatement. We\n      // simply start parsing an expression, and afterwards, if the\n      // next token is a colon and the expression was a simple\n      // Identifier node, we switch to interpreting it as a label.\n    default:\n      if (this.isAsyncFunction()) {\n        if (context) { this.unexpected(); }\n        this.next();\n        return this.parseFunctionStatement(node, true, !context)\n      }\n\n      var usingKind = this.isAwaitUsing(false) ? \"await using\" : this.isUsing(false) ? \"using\" : null;\n      if (usingKind) {\n        if (topLevel && this.options.sourceType === \"script\") {\n          this.raise(this.start, \"Using declaration cannot appear in the top level when source type is `script`\");\n        }\n        if (usingKind === \"await using\") {\n          if (!this.canAwait) {\n            this.raise(this.start, \"Await using cannot appear outside of async function\");\n          }\n          this.next();\n        }\n        this.next();\n        this.parseVar(node, false, usingKind);\n        this.semicolon();\n        return this.finishNode(node, \"VariableDeclaration\")\n      }\n\n      var maybeName = this.value, expr = this.parseExpression();\n      if (starttype === types$1.name && expr.type === \"Identifier\" && this.eat(types$1.colon))\n        { return this.parseLabeledStatement(node, maybeName, expr, context) }\n      else { return this.parseExpressionStatement(node, expr) }\n    }\n  };\n\n  pp$8.parseBreakContinueStatement = function(node, keyword) {\n    var isBreak = keyword === \"break\";\n    this.next();\n    if (this.eat(types$1.semi) || this.insertSemicolon()) { node.label = null; }\n    else if (this.type !== types$1.name) { this.unexpected(); }\n    else {\n      node.label = this.parseIdent();\n      this.semicolon();\n    }\n\n    // Verify that there is an actual destination to break or\n    // continue to.\n    var i = 0;\n    for (; i < this.labels.length; ++i) {\n      var lab = this.labels[i];\n      if (node.label == null || lab.name === node.label.name) {\n        if (lab.kind != null && (isBreak || lab.kind === \"loop\")) { break }\n        if (node.label && isBreak) { break }\n      }\n    }\n    if (i === this.labels.length) { this.raise(node.start, \"Unsyntactic \" + keyword); }\n    return this.finishNode(node, isBreak ? \"BreakStatement\" : \"ContinueStatement\")\n  };\n\n  pp$8.parseDebuggerStatement = function(node) {\n    this.next();\n    this.semicolon();\n    return this.finishNode(node, \"DebuggerStatement\")\n  };\n\n  pp$8.parseDoStatement = function(node) {\n    this.next();\n    this.labels.push(loopLabel);\n    node.body = this.parseStatement(\"do\");\n    this.labels.pop();\n    this.expect(types$1._while);\n    node.test = this.parseParenExpression();\n    if (this.options.ecmaVersion >= 6)\n      { this.eat(types$1.semi); }\n    else\n      { this.semicolon(); }\n    return this.finishNode(node, \"DoWhileStatement\")\n  };\n\n  // Disambiguating between a `for` and a `for`/`in` or `for`/`of`\n  // loop is non-trivial. Basically, we have to parse the init `var`\n  // statement or expression, disallowing the `in` operator (see\n  // the second parameter to `parseExpression`), and then check\n  // whether the next token is `in` or `of`. When there is no init\n  // part (semicolon immediately after the opening parenthesis), it\n  // is a regular `for` loop.\n\n  pp$8.parseForStatement = function(node) {\n    this.next();\n    var awaitAt = (this.options.ecmaVersion >= 9 && this.canAwait && this.eatContextual(\"await\")) ? this.lastTokStart : -1;\n    this.labels.push(loopLabel);\n    this.enterScope(0);\n    this.expect(types$1.parenL);\n    if (this.type === types$1.semi) {\n      if (awaitAt > -1) { this.unexpected(awaitAt); }\n      return this.parseFor(node, null)\n    }\n    var isLet = this.isLet();\n    if (this.type === types$1._var || this.type === types$1._const || isLet) {\n      var init$1 = this.startNode(), kind = isLet ? \"let\" : this.value;\n      this.next();\n      this.parseVar(init$1, true, kind);\n      this.finishNode(init$1, \"VariableDeclaration\");\n      return this.parseForAfterInit(node, init$1, awaitAt)\n    }\n    var startsWithLet = this.isContextual(\"let\"), isForOf = false;\n\n    var usingKind = this.isUsing(true) ? \"using\" : this.isAwaitUsing(true) ? \"await using\" : null;\n    if (usingKind) {\n      var init$2 = this.startNode();\n      this.next();\n      if (usingKind === \"await using\") { this.next(); }\n      this.parseVar(init$2, true, usingKind);\n      this.finishNode(init$2, \"VariableDeclaration\");\n      return this.parseForAfterInit(node, init$2, awaitAt)\n    }\n    var containsEsc = this.containsEsc;\n    var refDestructuringErrors = new DestructuringErrors;\n    var initPos = this.start;\n    var init = awaitAt > -1\n      ? this.parseExprSubscripts(refDestructuringErrors, \"await\")\n      : this.parseExpression(true, refDestructuringErrors);\n    if (this.type === types$1._in || (isForOf = this.options.ecmaVersion >= 6 && this.isContextual(\"of\"))) {\n      if (awaitAt > -1) { // implies `ecmaVersion >= 9` (see declaration of awaitAt)\n        if (this.type === types$1._in) { this.unexpected(awaitAt); }\n        node.await = true;\n      } else if (isForOf && this.options.ecmaVersion >= 8) {\n        if (init.start === initPos && !containsEsc && init.type === \"Identifier\" && init.name === \"async\") { this.unexpected(); }\n        else if (this.options.ecmaVersion >= 9) { node.await = false; }\n      }\n      if (startsWithLet && isForOf) { this.raise(init.start, \"The left-hand side of a for-of loop may not start with 'let'.\"); }\n      this.toAssignable(init, false, refDestructuringErrors);\n      this.checkLValPattern(init);\n      return this.parseForIn(node, init)\n    } else {\n      this.checkExpressionErrors(refDestructuringErrors, true);\n    }\n    if (awaitAt > -1) { this.unexpected(awaitAt); }\n    return this.parseFor(node, init)\n  };\n\n  // Helper method to parse for loop after variable initialization\n  pp$8.parseForAfterInit = function(node, init, awaitAt) {\n    if ((this.type === types$1._in || (this.options.ecmaVersion >= 6 && this.isContextual(\"of\"))) && init.declarations.length === 1) {\n      if (this.options.ecmaVersion >= 9) {\n        if (this.type === types$1._in) {\n          if (awaitAt > -1) { this.unexpected(awaitAt); }\n        } else { node.await = awaitAt > -1; }\n      }\n      return this.parseForIn(node, init)\n    }\n    if (awaitAt > -1) { this.unexpected(awaitAt); }\n    return this.parseFor(node, init)\n  };\n\n  pp$8.parseFunctionStatement = function(node, isAsync, declarationPosition) {\n    this.next();\n    return this.parseFunction(node, FUNC_STATEMENT | (declarationPosition ? 0 : FUNC_HANGING_STATEMENT), false, isAsync)\n  };\n\n  pp$8.parseIfStatement = function(node) {\n    this.next();\n    node.test = this.parseParenExpression();\n    // allow function declarations in branches, but only in non-strict mode\n    node.consequent = this.parseStatement(\"if\");\n    node.alternate = this.eat(types$1._else) ? this.parseStatement(\"if\") : null;\n    return this.finishNode(node, \"IfStatement\")\n  };\n\n  pp$8.parseReturnStatement = function(node) {\n    if (!this.inFunction && !this.options.allowReturnOutsideFunction)\n      { this.raise(this.start, \"'return' outside of function\"); }\n    this.next();\n\n    // In `return` (and `break`/`continue`), the keywords with\n    // optional arguments, we eagerly look for a semicolon or the\n    // possibility to insert one.\n\n    if (this.eat(types$1.semi) || this.insertSemicolon()) { node.argument = null; }\n    else { node.argument = this.parseExpression(); this.semicolon(); }\n    return this.finishNode(node, \"ReturnStatement\")\n  };\n\n  pp$8.parseSwitchStatement = function(node) {\n    this.next();\n    node.discriminant = this.parseParenExpression();\n    node.cases = [];\n    this.expect(types$1.braceL);\n    this.labels.push(switchLabel);\n    this.enterScope(0);\n\n    // Statements under must be grouped (by label) in SwitchCase\n    // nodes. `cur` is used to keep the node that we are currently\n    // adding statements to.\n\n    var cur;\n    for (var sawDefault = false; this.type !== types$1.braceR;) {\n      if (this.type === types$1._case || this.type === types$1._default) {\n        var isCase = this.type === types$1._case;\n        if (cur) { this.finishNode(cur, \"SwitchCase\"); }\n        node.cases.push(cur = this.startNode());\n        cur.consequent = [];\n        this.next();\n        if (isCase) {\n          cur.test = this.parseExpression();\n        } else {\n          if (sawDefault) { this.raiseRecoverable(this.lastTokStart, \"Multiple default clauses\"); }\n          sawDefault = true;\n          cur.test = null;\n        }\n        this.expect(types$1.colon);\n      } else {\n        if (!cur) { this.unexpected(); }\n        cur.consequent.push(this.parseStatement(null));\n      }\n    }\n    this.exitScope();\n    if (cur) { this.finishNode(cur, \"SwitchCase\"); }\n    this.next(); // Closing brace\n    this.labels.pop();\n    return this.finishNode(node, \"SwitchStatement\")\n  };\n\n  pp$8.parseThrowStatement = function(node) {\n    this.next();\n    if (lineBreak.test(this.input.slice(this.lastTokEnd, this.start)))\n      { this.raise(this.lastTokEnd, \"Illegal newline after throw\"); }\n    node.argument = this.parseExpression();\n    this.semicolon();\n    return this.finishNode(node, \"ThrowStatement\")\n  };\n\n  // Reused empty array added for node fields that are always empty.\n\n  var empty$1 = [];\n\n  pp$8.parseCatchClauseParam = function() {\n    var param = this.parseBindingAtom();\n    var simple = param.type === \"Identifier\";\n    this.enterScope(simple ? SCOPE_SIMPLE_CATCH : 0);\n    this.checkLValPattern(param, simple ? BIND_SIMPLE_CATCH : BIND_LEXICAL);\n    this.expect(types$1.parenR);\n\n    return param\n  };\n\n  pp$8.parseTryStatement = function(node) {\n    this.next();\n    node.block = this.parseBlock();\n    node.handler = null;\n    if (this.type === types$1._catch) {\n      var clause = this.startNode();\n      this.next();\n      if (this.eat(types$1.parenL)) {\n        clause.param = this.parseCatchClauseParam();\n      } else {\n        if (this.options.ecmaVersion < 10) { this.unexpected(); }\n        clause.param = null;\n        this.enterScope(0);\n      }\n      clause.body = this.parseBlock(false);\n      this.exitScope();\n      node.handler = this.finishNode(clause, \"CatchClause\");\n    }\n    node.finalizer = this.eat(types$1._finally) ? this.parseBlock() : null;\n    if (!node.handler && !node.finalizer)\n      { this.raise(node.start, \"Missing catch or finally clause\"); }\n    return this.finishNode(node, \"TryStatement\")\n  };\n\n  pp$8.parseVarStatement = function(node, kind, allowMissingInitializer) {\n    this.next();\n    this.parseVar(node, false, kind, allowMissingInitializer);\n    this.semicolon();\n    return this.finishNode(node, \"VariableDeclaration\")\n  };\n\n  pp$8.parseWhileStatement = function(node) {\n    this.next();\n    node.test = this.parseParenExpression();\n    this.labels.push(loopLabel);\n    node.body = this.parseStatement(\"while\");\n    this.labels.pop();\n    return this.finishNode(node, \"WhileStatement\")\n  };\n\n  pp$8.parseWithStatement = function(node) {\n    if (this.strict) { this.raise(this.start, \"'with' in strict mode\"); }\n    this.next();\n    node.object = this.parseParenExpression();\n    node.body = this.parseStatement(\"with\");\n    return this.finishNode(node, \"WithStatement\")\n  };\n\n  pp$8.parseEmptyStatement = function(node) {\n    this.next();\n    return this.finishNode(node, \"EmptyStatement\")\n  };\n\n  pp$8.parseLabeledStatement = function(node, maybeName, expr, context) {\n    for (var i$1 = 0, list = this.labels; i$1 < list.length; i$1 += 1)\n      {\n      var label = list[i$1];\n\n      if (label.name === maybeName)\n        { this.raise(expr.start, \"Label '\" + maybeName + \"' is already declared\");\n    } }\n    var kind = this.type.isLoop ? \"loop\" : this.type === types$1._switch ? \"switch\" : null;\n    for (var i = this.labels.length - 1; i >= 0; i--) {\n      var label$1 = this.labels[i];\n      if (label$1.statementStart === node.start) {\n        // Update information about previous labels on this node\n        label$1.statementStart = this.start;\n        label$1.kind = kind;\n      } else { break }\n    }\n    this.labels.push({name: maybeName, kind: kind, statementStart: this.start});\n    node.body = this.parseStatement(context ? context.indexOf(\"label\") === -1 ? context + \"label\" : context : \"label\");\n    this.labels.pop();\n    node.label = expr;\n    return this.finishNode(node, \"LabeledStatement\")\n  };\n\n  pp$8.parseExpressionStatement = function(node, expr) {\n    node.expression = expr;\n    this.semicolon();\n    return this.finishNode(node, \"ExpressionStatement\")\n  };\n\n  // Parse a semicolon-enclosed block of statements, handling `\"use\n  // strict\"` declarations when `allowStrict` is true (used for\n  // function bodies).\n\n  pp$8.parseBlock = function(createNewLexicalScope, node, exitStrict) {\n    if ( createNewLexicalScope === void 0 ) createNewLexicalScope = true;\n    if ( node === void 0 ) node = this.startNode();\n\n    node.body = [];\n    this.expect(types$1.braceL);\n    if (createNewLexicalScope) { this.enterScope(0); }\n    while (this.type !== types$1.braceR) {\n      var stmt = this.parseStatement(null);\n      node.body.push(stmt);\n    }\n    if (exitStrict) { this.strict = false; }\n    this.next();\n    if (createNewLexicalScope) { this.exitScope(); }\n    return this.finishNode(node, \"BlockStatement\")\n  };\n\n  // Parse a regular `for` loop. The disambiguation code in\n  // `parseStatement` will already have parsed the init statement or\n  // expression.\n\n  pp$8.parseFor = function(node, init) {\n    node.init = init;\n    this.expect(types$1.semi);\n    node.test = this.type === types$1.semi ? null : this.parseExpression();\n    this.expect(types$1.semi);\n    node.update = this.type === types$1.parenR ? null : this.parseExpression();\n    this.expect(types$1.parenR);\n    node.body = this.parseStatement(\"for\");\n    this.exitScope();\n    this.labels.pop();\n    return this.finishNode(node, \"ForStatement\")\n  };\n\n  // Parse a `for`/`in` and `for`/`of` loop, which are almost\n  // same from parser's perspective.\n\n  pp$8.parseForIn = function(node, init) {\n    var isForIn = this.type === types$1._in;\n    this.next();\n\n    if (\n      init.type === \"VariableDeclaration\" &&\n      init.declarations[0].init != null &&\n      (\n        !isForIn ||\n        this.options.ecmaVersion < 8 ||\n        this.strict ||\n        init.kind !== \"var\" ||\n        init.declarations[0].id.type !== \"Identifier\"\n      )\n    ) {\n      this.raise(\n        init.start,\n        ((isForIn ? \"for-in\" : \"for-of\") + \" loop variable declaration may not have an initializer\")\n      );\n    }\n    node.left = init;\n    node.right = isForIn ? this.parseExpression() : this.parseMaybeAssign();\n    this.expect(types$1.parenR);\n    node.body = this.parseStatement(\"for\");\n    this.exitScope();\n    this.labels.pop();\n    return this.finishNode(node, isForIn ? \"ForInStatement\" : \"ForOfStatement\")\n  };\n\n  // Parse a list of variable declarations.\n\n  pp$8.parseVar = function(node, isFor, kind, allowMissingInitializer) {\n    node.declarations = [];\n    node.kind = kind;\n    for (;;) {\n      var decl = this.startNode();\n      this.parseVarId(decl, kind);\n      if (this.eat(types$1.eq)) {\n        decl.init = this.parseMaybeAssign(isFor);\n      } else if (!allowMissingInitializer && kind === \"const\" && !(this.type === types$1._in || (this.options.ecmaVersion >= 6 && this.isContextual(\"of\")))) {\n        this.unexpected();\n      } else if (!allowMissingInitializer && (kind === \"using\" || kind === \"await using\") && this.options.ecmaVersion >= 17 && this.type !== types$1._in && !this.isContextual(\"of\")) {\n        this.raise(this.lastTokEnd, (\"Missing initializer in \" + kind + \" declaration\"));\n      } else if (!allowMissingInitializer && decl.id.type !== \"Identifier\" && !(isFor && (this.type === types$1._in || this.isContextual(\"of\")))) {\n        this.raise(this.lastTokEnd, \"Complex binding patterns require an initialization value\");\n      } else {\n        decl.init = null;\n      }\n      node.declarations.push(this.finishNode(decl, \"VariableDeclarator\"));\n      if (!this.eat(types$1.comma)) { break }\n    }\n    return node\n  };\n\n  pp$8.parseVarId = function(decl, kind) {\n    decl.id = kind === \"using\" || kind === \"await using\"\n      ? this.parseIdent()\n      : this.parseBindingAtom();\n\n    this.checkLValPattern(decl.id, kind === \"var\" ? BIND_VAR : BIND_LEXICAL, false);\n  };\n\n  var FUNC_STATEMENT = 1, FUNC_HANGING_STATEMENT = 2, FUNC_NULLABLE_ID = 4;\n\n  // Parse a function declaration or literal (depending on the\n  // `statement & FUNC_STATEMENT`).\n\n  // Remove `allowExpressionBody` for 7.0.0, as it is only called with false\n  pp$8.parseFunction = function(node, statement, allowExpressionBody, isAsync, forInit) {\n    this.initFunction(node);\n    if (this.options.ecmaVersion >= 9 || this.options.ecmaVersion >= 6 && !isAsync) {\n      if (this.type === types$1.star && (statement & FUNC_HANGING_STATEMENT))\n        { this.unexpected(); }\n      node.generator = this.eat(types$1.star);\n    }\n    if (this.options.ecmaVersion >= 8)\n      { node.async = !!isAsync; }\n\n    if (statement & FUNC_STATEMENT) {\n      node.id = (statement & FUNC_NULLABLE_ID) && this.type !== types$1.name ? null : this.parseIdent();\n      if (node.id && !(statement & FUNC_HANGING_STATEMENT))\n        // If it is a regular function declaration in sloppy mode, then it is\n        // subject to Annex B semantics (BIND_FUNCTION). Otherwise, the binding\n        // mode depends on properties of the current scope (see\n        // treatFunctionsAsVar).\n        { this.checkLValSimple(node.id, (this.strict || node.generator || node.async) ? this.treatFunctionsAsVar ? BIND_VAR : BIND_LEXICAL : BIND_FUNCTION); }\n    }\n\n    var oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, oldAwaitIdentPos = this.awaitIdentPos;\n    this.yieldPos = 0;\n    this.awaitPos = 0;\n    this.awaitIdentPos = 0;\n    this.enterScope(functionFlags(node.async, node.generator));\n\n    if (!(statement & FUNC_STATEMENT))\n      { node.id = this.type === types$1.name ? this.parseIdent() : null; }\n\n    this.parseFunctionParams(node);\n    this.parseFunctionBody(node, allowExpressionBody, false, forInit);\n\n    this.yieldPos = oldYieldPos;\n    this.awaitPos = oldAwaitPos;\n    this.awaitIdentPos = oldAwaitIdentPos;\n    return this.finishNode(node, (statement & FUNC_STATEMENT) ? \"FunctionDeclaration\" : \"FunctionExpression\")\n  };\n\n  pp$8.parseFunctionParams = function(node) {\n    this.expect(types$1.parenL);\n    node.params = this.parseBindingList(types$1.parenR, false, this.options.ecmaVersion >= 8);\n    this.checkYieldAwaitInDefaultParams();\n  };\n\n  // Parse a class declaration or literal (depending on the\n  // `isStatement` parameter).\n\n  pp$8.parseClass = function(node, isStatement) {\n    this.next();\n\n    // ecma-262 14.6 Class Definitions\n    // A class definition is always strict mode code.\n    var oldStrict = this.strict;\n    this.strict = true;\n\n    this.parseClassId(node, isStatement);\n    this.parseClassSuper(node);\n    var privateNameMap = this.enterClassBody();\n    var classBody = this.startNode();\n    var hadConstructor = false;\n    classBody.body = [];\n    this.expect(types$1.braceL);\n    while (this.type !== types$1.braceR) {\n      var element = this.parseClassElement(node.superClass !== null);\n      if (element) {\n        classBody.body.push(element);\n        if (element.type === \"MethodDefinition\" && element.kind === \"constructor\") {\n          if (hadConstructor) { this.raiseRecoverable(element.start, \"Duplicate constructor in the same class\"); }\n          hadConstructor = true;\n        } else if (element.key && element.key.type === \"PrivateIdentifier\" && isPrivateNameConflicted(privateNameMap, element)) {\n          this.raiseRecoverable(element.key.start, (\"Identifier '#\" + (element.key.name) + \"' has already been declared\"));\n        }\n      }\n    }\n    this.strict = oldStrict;\n    this.next();\n    node.body = this.finishNode(classBody, \"ClassBody\");\n    this.exitClassBody();\n    return this.finishNode(node, isStatement ? \"ClassDeclaration\" : \"ClassExpression\")\n  };\n\n  pp$8.parseClassElement = function(constructorAllowsSuper) {\n    if (this.eat(types$1.semi)) { return null }\n\n    var ecmaVersion = this.options.ecmaVersion;\n    var node = this.startNode();\n    var keyName = \"\";\n    var isGenerator = false;\n    var isAsync = false;\n    var kind = \"method\";\n    var isStatic = false;\n\n    if (this.eatContextual(\"static\")) {\n      // Parse static init block\n      if (ecmaVersion >= 13 && this.eat(types$1.braceL)) {\n        this.parseClassStaticBlock(node);\n        return node\n      }\n      if (this.isClassElementNameStart() || this.type === types$1.star) {\n        isStatic = true;\n      } else {\n        keyName = \"static\";\n      }\n    }\n    node.static = isStatic;\n    if (!keyName && ecmaVersion >= 8 && this.eatContextual(\"async\")) {\n      if ((this.isClassElementNameStart() || this.type === types$1.star) && !this.canInsertSemicolon()) {\n        isAsync = true;\n      } else {\n        keyName = \"async\";\n      }\n    }\n    if (!keyName && (ecmaVersion >= 9 || !isAsync) && this.eat(types$1.star)) {\n      isGenerator = true;\n    }\n    if (!keyName && !isAsync && !isGenerator) {\n      var lastValue = this.value;\n      if (this.eatContextual(\"get\") || this.eatContextual(\"set\")) {\n        if (this.isClassElementNameStart()) {\n          kind = lastValue;\n        } else {\n          keyName = lastValue;\n        }\n      }\n    }\n\n    // Parse element name\n    if (keyName) {\n      // 'async', 'get', 'set', or 'static' were not a keyword contextually.\n      // The last token is any of those. Make it the element name.\n      node.computed = false;\n      node.key = this.startNodeAt(this.lastTokStart, this.lastTokStartLoc);\n      node.key.name = keyName;\n      this.finishNode(node.key, \"Identifier\");\n    } else {\n      this.parseClassElementName(node);\n    }\n\n    // Parse element value\n    if (ecmaVersion < 13 || this.type === types$1.parenL || kind !== \"method\" || isGenerator || isAsync) {\n      var isConstructor = !node.static && checkKeyName(node, \"constructor\");\n      var allowsDirectSuper = isConstructor && constructorAllowsSuper;\n      // Couldn't move this check into the 'parseClassMethod' method for backward compatibility.\n      if (isConstructor && kind !== \"method\") { this.raise(node.key.start, \"Constructor can't have get/set modifier\"); }\n      node.kind = isConstructor ? \"constructor\" : kind;\n      this.parseClassMethod(node, isGenerator, isAsync, allowsDirectSuper);\n    } else {\n      this.parseClassField(node);\n    }\n\n    return node\n  };\n\n  pp$8.isClassElementNameStart = function() {\n    return (\n      this.type === types$1.name ||\n      this.type === types$1.privateId ||\n      this.type === types$1.num ||\n      this.type === types$1.string ||\n      this.type === types$1.bracketL ||\n      this.type.keyword\n    )\n  };\n\n  pp$8.parseClassElementName = function(element) {\n    if (this.type === types$1.privateId) {\n      if (this.value === \"constructor\") {\n        this.raise(this.start, \"Classes can't have an element named '#constructor'\");\n      }\n      element.computed = false;\n      element.key = this.parsePrivateIdent();\n    } else {\n      this.parsePropertyName(element);\n    }\n  };\n\n  pp$8.parseClassMethod = function(method, isGenerator, isAsync, allowsDirectSuper) {\n    // Check key and flags\n    var key = method.key;\n    if (method.kind === \"constructor\") {\n      if (isGenerator) { this.raise(key.start, \"Constructor can't be a generator\"); }\n      if (isAsync) { this.raise(key.start, \"Constructor can't be an async method\"); }\n    } else if (method.static && checkKeyName(method, \"prototype\")) {\n      this.raise(key.start, \"Classes may not have a static property named prototype\");\n    }\n\n    // Parse value\n    var value = method.value = this.parseMethod(isGenerator, isAsync, allowsDirectSuper);\n\n    // Check value\n    if (method.kind === \"get\" && value.params.length !== 0)\n      { this.raiseRecoverable(value.start, \"getter should have no params\"); }\n    if (method.kind === \"set\" && value.params.length !== 1)\n      { this.raiseRecoverable(value.start, \"setter should have exactly one param\"); }\n    if (method.kind === \"set\" && value.params[0].type === \"RestElement\")\n      { this.raiseRecoverable(value.params[0].start, \"Setter cannot use rest params\"); }\n\n    return this.finishNode(method, \"MethodDefinition\")\n  };\n\n  pp$8.parseClassField = function(field) {\n    if (checkKeyName(field, \"constructor\")) {\n      this.raise(field.key.start, \"Classes can't have a field named 'constructor'\");\n    } else if (field.static && checkKeyName(field, \"prototype\")) {\n      this.raise(field.key.start, \"Classes can't have a static field named 'prototype'\");\n    }\n\n    if (this.eat(types$1.eq)) {\n      // To raise SyntaxError if 'arguments' exists in the initializer.\n      this.enterScope(SCOPE_CLASS_FIELD_INIT | SCOPE_SUPER);\n      field.value = this.parseMaybeAssign();\n      this.exitScope();\n    } else {\n      field.value = null;\n    }\n    this.semicolon();\n\n    return this.finishNode(field, \"PropertyDefinition\")\n  };\n\n  pp$8.parseClassStaticBlock = function(node) {\n    node.body = [];\n\n    var oldLabels = this.labels;\n    this.labels = [];\n    this.enterScope(SCOPE_CLASS_STATIC_BLOCK | SCOPE_SUPER);\n    while (this.type !== types$1.braceR) {\n      var stmt = this.parseStatement(null);\n      node.body.push(stmt);\n    }\n    this.next();\n    this.exitScope();\n    this.labels = oldLabels;\n\n    return this.finishNode(node, \"StaticBlock\")\n  };\n\n  pp$8.parseClassId = function(node, isStatement) {\n    if (this.type === types$1.name) {\n      node.id = this.parseIdent();\n      if (isStatement)\n        { this.checkLValSimple(node.id, BIND_LEXICAL, false); }\n    } else {\n      if (isStatement === true)\n        { this.unexpected(); }\n      node.id = null;\n    }\n  };\n\n  pp$8.parseClassSuper = function(node) {\n    node.superClass = this.eat(types$1._extends) ? this.parseExprSubscripts(null, false) : null;\n  };\n\n  pp$8.enterClassBody = function() {\n    var element = {declared: Object.create(null), used: []};\n    this.privateNameStack.push(element);\n    return element.declared\n  };\n\n  pp$8.exitClassBody = function() {\n    var ref = this.privateNameStack.pop();\n    var declared = ref.declared;\n    var used = ref.used;\n    if (!this.options.checkPrivateFields) { return }\n    var len = this.privateNameStack.length;\n    var parent = len === 0 ? null : this.privateNameStack[len - 1];\n    for (var i = 0; i < used.length; ++i) {\n      var id = used[i];\n      if (!hasOwn(declared, id.name)) {\n        if (parent) {\n          parent.used.push(id);\n        } else {\n          this.raiseRecoverable(id.start, (\"Private field '#\" + (id.name) + \"' must be declared in an enclosing class\"));\n        }\n      }\n    }\n  };\n\n  function isPrivateNameConflicted(privateNameMap, element) {\n    var name = element.key.name;\n    var curr = privateNameMap[name];\n\n    var next = \"true\";\n    if (element.type === \"MethodDefinition\" && (element.kind === \"get\" || element.kind === \"set\")) {\n      next = (element.static ? \"s\" : \"i\") + element.kind;\n    }\n\n    // `class { get #a(){}; static set #a(_){} }` is also conflict.\n    if (\n      curr === \"iget\" && next === \"iset\" ||\n      curr === \"iset\" && next === \"iget\" ||\n      curr === \"sget\" && next === \"sset\" ||\n      curr === \"sset\" && next === \"sget\"\n    ) {\n      privateNameMap[name] = \"true\";\n      return false\n    } else if (!curr) {\n      privateNameMap[name] = next;\n      return false\n    } else {\n      return true\n    }\n  }\n\n  function checkKeyName(node, name) {\n    var computed = node.computed;\n    var key = node.key;\n    return !computed && (\n      key.type === \"Identifier\" && key.name === name ||\n      key.type === \"Literal\" && key.value === name\n    )\n  }\n\n  // Parses module export declaration.\n\n  pp$8.parseExportAllDeclaration = function(node, exports) {\n    if (this.options.ecmaVersion >= 11) {\n      if (this.eatContextual(\"as\")) {\n        node.exported = this.parseModuleExportName();\n        this.checkExport(exports, node.exported, this.lastTokStart);\n      } else {\n        node.exported = null;\n      }\n    }\n    this.expectContextual(\"from\");\n    if (this.type !== types$1.string) { this.unexpected(); }\n    node.source = this.parseExprAtom();\n    if (this.options.ecmaVersion >= 16)\n      { node.attributes = this.parseWithClause(); }\n    this.semicolon();\n    return this.finishNode(node, \"ExportAllDeclaration\")\n  };\n\n  pp$8.parseExport = function(node, exports) {\n    this.next();\n    // export * from '...'\n    if (this.eat(types$1.star)) {\n      return this.parseExportAllDeclaration(node, exports)\n    }\n    if (this.eat(types$1._default)) { // export default ...\n      this.checkExport(exports, \"default\", this.lastTokStart);\n      node.declaration = this.parseExportDefaultDeclaration();\n      return this.finishNode(node, \"ExportDefaultDeclaration\")\n    }\n    // export var|const|let|function|class ...\n    if (this.shouldParseExportStatement()) {\n      node.declaration = this.parseExportDeclaration(node);\n      if (node.declaration.type === \"VariableDeclaration\")\n        { this.checkVariableExport(exports, node.declaration.declarations); }\n      else\n        { this.checkExport(exports, node.declaration.id, node.declaration.id.start); }\n      node.specifiers = [];\n      node.source = null;\n      if (this.options.ecmaVersion >= 16)\n        { node.attributes = []; }\n    } else { // export { x, y as z } [from '...']\n      node.declaration = null;\n      node.specifiers = this.parseExportSpecifiers(exports);\n      if (this.eatContextual(\"from\")) {\n        if (this.type !== types$1.string) { this.unexpected(); }\n        node.source = this.parseExprAtom();\n        if (this.options.ecmaVersion >= 16)\n          { node.attributes = this.parseWithClause(); }\n      } else {\n        for (var i = 0, list = node.specifiers; i < list.length; i += 1) {\n          // check for keywords used as local names\n          var spec = list[i];\n\n          this.checkUnreserved(spec.local);\n          // check if export is defined\n          this.checkLocalExport(spec.local);\n\n          if (spec.local.type === \"Literal\") {\n            this.raise(spec.local.start, \"A string literal cannot be used as an exported binding without `from`.\");\n          }\n        }\n\n        node.source = null;\n        if (this.options.ecmaVersion >= 16)\n          { node.attributes = []; }\n      }\n      this.semicolon();\n    }\n    return this.finishNode(node, \"ExportNamedDeclaration\")\n  };\n\n  pp$8.parseExportDeclaration = function(node) {\n    return this.parseStatement(null)\n  };\n\n  pp$8.parseExportDefaultDeclaration = function() {\n    var isAsync;\n    if (this.type === types$1._function || (isAsync = this.isAsyncFunction())) {\n      var fNode = this.startNode();\n      this.next();\n      if (isAsync) { this.next(); }\n      return this.parseFunction(fNode, FUNC_STATEMENT | FUNC_NULLABLE_ID, false, isAsync)\n    } else if (this.type === types$1._class) {\n      var cNode = this.startNode();\n      return this.parseClass(cNode, \"nullableID\")\n    } else {\n      var declaration = this.parseMaybeAssign();\n      this.semicolon();\n      return declaration\n    }\n  };\n\n  pp$8.checkExport = function(exports, name, pos) {\n    if (!exports) { return }\n    if (typeof name !== \"string\")\n      { name = name.type === \"Identifier\" ? name.name : name.value; }\n    if (hasOwn(exports, name))\n      { this.raiseRecoverable(pos, \"Duplicate export '\" + name + \"'\"); }\n    exports[name] = true;\n  };\n\n  pp$8.checkPatternExport = function(exports, pat) {\n    var type = pat.type;\n    if (type === \"Identifier\")\n      { this.checkExport(exports, pat, pat.start); }\n    else if (type === \"ObjectPattern\")\n      { for (var i = 0, list = pat.properties; i < list.length; i += 1)\n        {\n          var prop = list[i];\n\n          this.checkPatternExport(exports, prop);\n        } }\n    else if (type === \"ArrayPattern\")\n      { for (var i$1 = 0, list$1 = pat.elements; i$1 < list$1.length; i$1 += 1) {\n        var elt = list$1[i$1];\n\n          if (elt) { this.checkPatternExport(exports, elt); }\n      } }\n    else if (type === \"Property\")\n      { this.checkPatternExport(exports, pat.value); }\n    else if (type === \"AssignmentPattern\")\n      { this.checkPatternExport(exports, pat.left); }\n    else if (type === \"RestElement\")\n      { this.checkPatternExport(exports, pat.argument); }\n  };\n\n  pp$8.checkVariableExport = function(exports, decls) {\n    if (!exports) { return }\n    for (var i = 0, list = decls; i < list.length; i += 1)\n      {\n      var decl = list[i];\n\n      this.checkPatternExport(exports, decl.id);\n    }\n  };\n\n  pp$8.shouldParseExportStatement = function() {\n    return this.type.keyword === \"var\" ||\n      this.type.keyword === \"const\" ||\n      this.type.keyword === \"class\" ||\n      this.type.keyword === \"function\" ||\n      this.isLet() ||\n      this.isAsyncFunction()\n  };\n\n  // Parses a comma-separated list of module exports.\n\n  pp$8.parseExportSpecifier = function(exports) {\n    var node = this.startNode();\n    node.local = this.parseModuleExportName();\n\n    node.exported = this.eatContextual(\"as\") ? this.parseModuleExportName() : node.local;\n    this.checkExport(\n      exports,\n      node.exported,\n      node.exported.start\n    );\n\n    return this.finishNode(node, \"ExportSpecifier\")\n  };\n\n  pp$8.parseExportSpecifiers = function(exports) {\n    var nodes = [], first = true;\n    // export { x, y as z } [from '...']\n    this.expect(types$1.braceL);\n    while (!this.eat(types$1.braceR)) {\n      if (!first) {\n        this.expect(types$1.comma);\n        if (this.afterTrailingComma(types$1.braceR)) { break }\n      } else { first = false; }\n\n      nodes.push(this.parseExportSpecifier(exports));\n    }\n    return nodes\n  };\n\n  // Parses import declaration.\n\n  pp$8.parseImport = function(node) {\n    this.next();\n\n    // import '...'\n    if (this.type === types$1.string) {\n      node.specifiers = empty$1;\n      node.source = this.parseExprAtom();\n    } else {\n      node.specifiers = this.parseImportSpecifiers();\n      this.expectContextual(\"from\");\n      node.source = this.type === types$1.string ? this.parseExprAtom() : this.unexpected();\n    }\n    if (this.options.ecmaVersion >= 16)\n      { node.attributes = this.parseWithClause(); }\n    this.semicolon();\n    return this.finishNode(node, \"ImportDeclaration\")\n  };\n\n  // Parses a comma-separated list of module imports.\n\n  pp$8.parseImportSpecifier = function() {\n    var node = this.startNode();\n    node.imported = this.parseModuleExportName();\n\n    if (this.eatContextual(\"as\")) {\n      node.local = this.parseIdent();\n    } else {\n      this.checkUnreserved(node.imported);\n      node.local = node.imported;\n    }\n    this.checkLValSimple(node.local, BIND_LEXICAL);\n\n    return this.finishNode(node, \"ImportSpecifier\")\n  };\n\n  pp$8.parseImportDefaultSpecifier = function() {\n    // import defaultObj, { x, y as z } from '...'\n    var node = this.startNode();\n    node.local = this.parseIdent();\n    this.checkLValSimple(node.local, BIND_LEXICAL);\n    return this.finishNode(node, \"ImportDefaultSpecifier\")\n  };\n\n  pp$8.parseImportNamespaceSpecifier = function() {\n    var node = this.startNode();\n    this.next();\n    this.expectContextual(\"as\");\n    node.local = this.parseIdent();\n    this.checkLValSimple(node.local, BIND_LEXICAL);\n    return this.finishNode(node, \"ImportNamespaceSpecifier\")\n  };\n\n  pp$8.parseImportSpecifiers = function() {\n    var nodes = [], first = true;\n    if (this.type === types$1.name) {\n      nodes.push(this.parseImportDefaultSpecifier());\n      if (!this.eat(types$1.comma)) { return nodes }\n    }\n    if (this.type === types$1.star) {\n      nodes.push(this.parseImportNamespaceSpecifier());\n      return nodes\n    }\n    this.expect(types$1.braceL);\n    while (!this.eat(types$1.braceR)) {\n      if (!first) {\n        this.expect(types$1.comma);\n        if (this.afterTrailingComma(types$1.braceR)) { break }\n      } else { first = false; }\n\n      nodes.push(this.parseImportSpecifier());\n    }\n    return nodes\n  };\n\n  pp$8.parseWithClause = function() {\n    var nodes = [];\n    if (!this.eat(types$1._with)) {\n      return nodes\n    }\n    this.expect(types$1.braceL);\n    var attributeKeys = {};\n    var first = true;\n    while (!this.eat(types$1.braceR)) {\n      if (!first) {\n        this.expect(types$1.comma);\n        if (this.afterTrailingComma(types$1.braceR)) { break }\n      } else { first = false; }\n\n      var attr = this.parseImportAttribute();\n      var keyName = attr.key.type === \"Identifier\" ? attr.key.name : attr.key.value;\n      if (hasOwn(attributeKeys, keyName))\n        { this.raiseRecoverable(attr.key.start, \"Duplicate attribute key '\" + keyName + \"'\"); }\n      attributeKeys[keyName] = true;\n      nodes.push(attr);\n    }\n    return nodes\n  };\n\n  pp$8.parseImportAttribute = function() {\n    var node = this.startNode();\n    node.key = this.type === types$1.string ? this.parseExprAtom() : this.parseIdent(this.options.allowReserved !== \"never\");\n    this.expect(types$1.colon);\n    if (this.type !== types$1.string) {\n      this.unexpected();\n    }\n    node.value = this.parseExprAtom();\n    return this.finishNode(node, \"ImportAttribute\")\n  };\n\n  pp$8.parseModuleExportName = function() {\n    if (this.options.ecmaVersion >= 13 && this.type === types$1.string) {\n      var stringLiteral = this.parseLiteral(this.value);\n      if (loneSurrogate.test(stringLiteral.value)) {\n        this.raise(stringLiteral.start, \"An export name cannot include a lone surrogate.\");\n      }\n      return stringLiteral\n    }\n    return this.parseIdent(true)\n  };\n\n  // Set `ExpressionStatement#directive` property for directive prologues.\n  pp$8.adaptDirectivePrologue = function(statements) {\n    for (var i = 0; i < statements.length && this.isDirectiveCandidate(statements[i]); ++i) {\n      statements[i].directive = statements[i].expression.raw.slice(1, -1);\n    }\n  };\n  pp$8.isDirectiveCandidate = function(statement) {\n    return (\n      this.options.ecmaVersion >= 5 &&\n      statement.type === \"ExpressionStatement\" &&\n      statement.expression.type === \"Literal\" &&\n      typeof statement.expression.value === \"string\" &&\n      // Reject parenthesized strings.\n      (this.input[statement.start] === \"\\\"\" || this.input[statement.start] === \"'\")\n    )\n  };\n\n  var pp$7 = Parser.prototype;\n\n  // Convert existing expression atom to assignable pattern\n  // if possible.\n\n  pp$7.toAssignable = function(node, isBinding, refDestructuringErrors) {\n    if (this.options.ecmaVersion >= 6 && node) {\n      switch (node.type) {\n      case \"Identifier\":\n        if (this.inAsync && node.name === \"await\")\n          { this.raise(node.start, \"Cannot use 'await' as identifier inside an async function\"); }\n        break\n\n      case \"ObjectPattern\":\n      case \"ArrayPattern\":\n      case \"AssignmentPattern\":\n      case \"RestElement\":\n        break\n\n      case \"ObjectExpression\":\n        node.type = \"ObjectPattern\";\n        if (refDestructuringErrors) { this.checkPatternErrors(refDestructuringErrors, true); }\n        for (var i = 0, list = node.properties; i < list.length; i += 1) {\n          var prop = list[i];\n\n        this.toAssignable(prop, isBinding);\n          // Early error:\n          //   AssignmentRestProperty[Yield, Await] :\n          //     `...` DestructuringAssignmentTarget[Yield, Await]\n          //\n          //   It is a Syntax Error if |DestructuringAssignmentTarget| is an |ArrayLiteral| or an |ObjectLiteral|.\n          if (\n            prop.type === \"RestElement\" &&\n            (prop.argument.type === \"ArrayPattern\" || prop.argument.type === \"ObjectPattern\")\n          ) {\n            this.raise(prop.argument.start, \"Unexpected token\");\n          }\n        }\n        break\n\n      case \"Property\":\n        // AssignmentProperty has type === \"Property\"\n        if (node.kind !== \"init\") { this.raise(node.key.start, \"Object pattern can't contain getter or setter\"); }\n        this.toAssignable(node.value, isBinding);\n        break\n\n      case \"ArrayExpression\":\n        node.type = \"ArrayPattern\";\n        if (refDestructuringErrors) { this.checkPatternErrors(refDestructuringErrors, true); }\n        this.toAssignableList(node.elements, isBinding);\n        break\n\n      case \"SpreadElement\":\n        node.type = \"RestElement\";\n        this.toAssignable(node.argument, isBinding);\n        if (node.argument.type === \"AssignmentPattern\")\n          { this.raise(node.argument.start, \"Rest elements cannot have a default value\"); }\n        break\n\n      case \"AssignmentExpression\":\n        if (node.operator !== \"=\") { this.raise(node.left.end, \"Only '=' operator can be used for specifying default value.\"); }\n        node.type = \"AssignmentPattern\";\n        delete node.operator;\n        this.toAssignable(node.left, isBinding);\n        break\n\n      case \"ParenthesizedExpression\":\n        this.toAssignable(node.expression, isBinding, refDestructuringErrors);\n        break\n\n      case \"ChainExpression\":\n        this.raiseRecoverable(node.start, \"Optional chaining cannot appear in left-hand side\");\n        break\n\n      case \"MemberExpression\":\n        if (!isBinding) { break }\n\n      default:\n        this.raise(node.start, \"Assigning to rvalue\");\n      }\n    } else if (refDestructuringErrors) { this.checkPatternErrors(refDestructuringErrors, true); }\n    return node\n  };\n\n  // Convert list of expression atoms to binding list.\n\n  pp$7.toAssignableList = function(exprList, isBinding) {\n    var end = exprList.length;\n    for (var i = 0; i < end; i++) {\n      var elt = exprList[i];\n      if (elt) { this.toAssignable(elt, isBinding); }\n    }\n    if (end) {\n      var last = exprList[end - 1];\n      if (this.options.ecmaVersion === 6 && isBinding && last && last.type === \"RestElement\" && last.argument.type !== \"Identifier\")\n        { this.unexpected(last.argument.start); }\n    }\n    return exprList\n  };\n\n  // Parses spread element.\n\n  pp$7.parseSpread = function(refDestructuringErrors) {\n    var node = this.startNode();\n    this.next();\n    node.argument = this.parseMaybeAssign(false, refDestructuringErrors);\n    return this.finishNode(node, \"SpreadElement\")\n  };\n\n  pp$7.parseRestBinding = function() {\n    var node = this.startNode();\n    this.next();\n\n    // RestElement inside of a function parameter must be an identifier\n    if (this.options.ecmaVersion === 6 && this.type !== types$1.name)\n      { this.unexpected(); }\n\n    node.argument = this.parseBindingAtom();\n\n    return this.finishNode(node, \"RestElement\")\n  };\n\n  // Parses lvalue (assignable) atom.\n\n  pp$7.parseBindingAtom = function() {\n    if (this.options.ecmaVersion >= 6) {\n      switch (this.type) {\n      case types$1.bracketL:\n        var node = this.startNode();\n        this.next();\n        node.elements = this.parseBindingList(types$1.bracketR, true, true);\n        return this.finishNode(node, \"ArrayPattern\")\n\n      case types$1.braceL:\n        return this.parseObj(true)\n      }\n    }\n    return this.parseIdent()\n  };\n\n  pp$7.parseBindingList = function(close, allowEmpty, allowTrailingComma, allowModifiers) {\n    var elts = [], first = true;\n    while (!this.eat(close)) {\n      if (first) { first = false; }\n      else { this.expect(types$1.comma); }\n      if (allowEmpty && this.type === types$1.comma) {\n        elts.push(null);\n      } else if (allowTrailingComma && this.afterTrailingComma(close)) {\n        break\n      } else if (this.type === types$1.ellipsis) {\n        var rest = this.parseRestBinding();\n        this.parseBindingListItem(rest);\n        elts.push(rest);\n        if (this.type === types$1.comma) { this.raiseRecoverable(this.start, \"Comma is not permitted after the rest element\"); }\n        this.expect(close);\n        break\n      } else {\n        elts.push(this.parseAssignableListItem(allowModifiers));\n      }\n    }\n    return elts\n  };\n\n  pp$7.parseAssignableListItem = function(allowModifiers) {\n    var elem = this.parseMaybeDefault(this.start, this.startLoc);\n    this.parseBindingListItem(elem);\n    return elem\n  };\n\n  pp$7.parseBindingListItem = function(param) {\n    return param\n  };\n\n  // Parses assignment pattern around given atom if possible.\n\n  pp$7.parseMaybeDefault = function(startPos, startLoc, left) {\n    left = left || this.parseBindingAtom();\n    if (this.options.ecmaVersion < 6 || !this.eat(types$1.eq)) { return left }\n    var node = this.startNodeAt(startPos, startLoc);\n    node.left = left;\n    node.right = this.parseMaybeAssign();\n    return this.finishNode(node, \"AssignmentPattern\")\n  };\n\n  // The following three functions all verify that a node is an lvalue —\n  // something that can be bound, or assigned to. In order to do so, they perform\n  // a variety of checks:\n  //\n  // - Check that none of the bound/assigned-to identifiers are reserved words.\n  // - Record name declarations for bindings in the appropriate scope.\n  // - Check duplicate argument names, if checkClashes is set.\n  //\n  // If a complex binding pattern is encountered (e.g., object and array\n  // destructuring), the entire pattern is recursively checked.\n  //\n  // There are three versions of checkLVal*() appropriate for different\n  // circumstances:\n  //\n  // - checkLValSimple() shall be used if the syntactic construct supports\n  //   nothing other than identifiers and member expressions. Parenthesized\n  //   expressions are also correctly handled. This is generally appropriate for\n  //   constructs for which the spec says\n  //\n  //   > It is a Syntax Error if AssignmentTargetType of [the production] is not\n  //   > simple.\n  //\n  //   It is also appropriate for checking if an identifier is valid and not\n  //   defined elsewhere, like import declarations or function/class identifiers.\n  //\n  //   Examples where this is used include:\n  //     a += …;\n  //     import a from '…';\n  //   where a is the node to be checked.\n  //\n  // - checkLValPattern() shall be used if the syntactic construct supports\n  //   anything checkLValSimple() supports, as well as object and array\n  //   destructuring patterns. This is generally appropriate for constructs for\n  //   which the spec says\n  //\n  //   > It is a Syntax Error if [the production] is neither an ObjectLiteral nor\n  //   > an ArrayLiteral and AssignmentTargetType of [the production] is not\n  //   > simple.\n  //\n  //   Examples where this is used include:\n  //     (a = …);\n  //     const a = …;\n  //     try { … } catch (a) { … }\n  //   where a is the node to be checked.\n  //\n  // - checkLValInnerPattern() shall be used if the syntactic construct supports\n  //   anything checkLValPattern() supports, as well as default assignment\n  //   patterns, rest elements, and other constructs that may appear within an\n  //   object or array destructuring pattern.\n  //\n  //   As a special case, function parameters also use checkLValInnerPattern(),\n  //   as they also support defaults and rest constructs.\n  //\n  // These functions deliberately support both assignment and binding constructs,\n  // as the logic for both is exceedingly similar. If the node is the target of\n  // an assignment, then bindingType should be set to BIND_NONE. Otherwise, it\n  // should be set to the appropriate BIND_* constant, like BIND_VAR or\n  // BIND_LEXICAL.\n  //\n  // If the function is called with a non-BIND_NONE bindingType, then\n  // additionally a checkClashes object may be specified to allow checking for\n  // duplicate argument names. checkClashes is ignored if the provided construct\n  // is an assignment (i.e., bindingType is BIND_NONE).\n\n  pp$7.checkLValSimple = function(expr, bindingType, checkClashes) {\n    if ( bindingType === void 0 ) bindingType = BIND_NONE;\n\n    var isBind = bindingType !== BIND_NONE;\n\n    switch (expr.type) {\n    case \"Identifier\":\n      if (this.strict && this.reservedWordsStrictBind.test(expr.name))\n        { this.raiseRecoverable(expr.start, (isBind ? \"Binding \" : \"Assigning to \") + expr.name + \" in strict mode\"); }\n      if (isBind) {\n        if (bindingType === BIND_LEXICAL && expr.name === \"let\")\n          { this.raiseRecoverable(expr.start, \"let is disallowed as a lexically bound name\"); }\n        if (checkClashes) {\n          if (hasOwn(checkClashes, expr.name))\n            { this.raiseRecoverable(expr.start, \"Argument name clash\"); }\n          checkClashes[expr.name] = true;\n        }\n        if (bindingType !== BIND_OUTSIDE) { this.declareName(expr.name, bindingType, expr.start); }\n      }\n      break\n\n    case \"ChainExpression\":\n      this.raiseRecoverable(expr.start, \"Optional chaining cannot appear in left-hand side\");\n      break\n\n    case \"MemberExpression\":\n      if (isBind) { this.raiseRecoverable(expr.start, \"Binding member expression\"); }\n      break\n\n    case \"ParenthesizedExpression\":\n      if (isBind) { this.raiseRecoverable(expr.start, \"Binding parenthesized expression\"); }\n      return this.checkLValSimple(expr.expression, bindingType, checkClashes)\n\n    default:\n      this.raise(expr.start, (isBind ? \"Binding\" : \"Assigning to\") + \" rvalue\");\n    }\n  };\n\n  pp$7.checkLValPattern = function(expr, bindingType, checkClashes) {\n    if ( bindingType === void 0 ) bindingType = BIND_NONE;\n\n    switch (expr.type) {\n    case \"ObjectPattern\":\n      for (var i = 0, list = expr.properties; i < list.length; i += 1) {\n        var prop = list[i];\n\n      this.checkLValInnerPattern(prop, bindingType, checkClashes);\n      }\n      break\n\n    case \"ArrayPattern\":\n      for (var i$1 = 0, list$1 = expr.elements; i$1 < list$1.length; i$1 += 1) {\n        var elem = list$1[i$1];\n\n      if (elem) { this.checkLValInnerPattern(elem, bindingType, checkClashes); }\n      }\n      break\n\n    default:\n      this.checkLValSimple(expr, bindingType, checkClashes);\n    }\n  };\n\n  pp$7.checkLValInnerPattern = function(expr, bindingType, checkClashes) {\n    if ( bindingType === void 0 ) bindingType = BIND_NONE;\n\n    switch (expr.type) {\n    case \"Property\":\n      // AssignmentProperty has type === \"Property\"\n      this.checkLValInnerPattern(expr.value, bindingType, checkClashes);\n      break\n\n    case \"AssignmentPattern\":\n      this.checkLValPattern(expr.left, bindingType, checkClashes);\n      break\n\n    case \"RestElement\":\n      this.checkLValPattern(expr.argument, bindingType, checkClashes);\n      break\n\n    default:\n      this.checkLValPattern(expr, bindingType, checkClashes);\n    }\n  };\n\n  // The algorithm used to determine whether a regexp can appear at a\n  // given point in the program is loosely based on sweet.js' approach.\n  // See https://github.com/mozilla/sweet.js/wiki/design\n\n\n  var TokContext = function TokContext(token, isExpr, preserveSpace, override, generator) {\n    this.token = token;\n    this.isExpr = !!isExpr;\n    this.preserveSpace = !!preserveSpace;\n    this.override = override;\n    this.generator = !!generator;\n  };\n\n  var types = {\n    b_stat: new TokContext(\"{\", false),\n    b_expr: new TokContext(\"{\", true),\n    b_tmpl: new TokContext(\"${\", false),\n    p_stat: new TokContext(\"(\", false),\n    p_expr: new TokContext(\"(\", true),\n    q_tmpl: new TokContext(\"`\", true, true, function (p) { return p.tryReadTemplateToken(); }),\n    f_stat: new TokContext(\"function\", false),\n    f_expr: new TokContext(\"function\", true),\n    f_expr_gen: new TokContext(\"function\", true, false, null, true),\n    f_gen: new TokContext(\"function\", false, false, null, true)\n  };\n\n  var pp$6 = Parser.prototype;\n\n  pp$6.initialContext = function() {\n    return [types.b_stat]\n  };\n\n  pp$6.curContext = function() {\n    return this.context[this.context.length - 1]\n  };\n\n  pp$6.braceIsBlock = function(prevType) {\n    var parent = this.curContext();\n    if (parent === types.f_expr || parent === types.f_stat)\n      { return true }\n    if (prevType === types$1.colon && (parent === types.b_stat || parent === types.b_expr))\n      { return !parent.isExpr }\n\n    // The check for `tt.name && exprAllowed` detects whether we are\n    // after a `yield` or `of` construct. See the `updateContext` for\n    // `tt.name`.\n    if (prevType === types$1._return || prevType === types$1.name && this.exprAllowed)\n      { return lineBreak.test(this.input.slice(this.lastTokEnd, this.start)) }\n    if (prevType === types$1._else || prevType === types$1.semi || prevType === types$1.eof || prevType === types$1.parenR || prevType === types$1.arrow)\n      { return true }\n    if (prevType === types$1.braceL)\n      { return parent === types.b_stat }\n    if (prevType === types$1._var || prevType === types$1._const || prevType === types$1.name)\n      { return false }\n    return !this.exprAllowed\n  };\n\n  pp$6.inGeneratorContext = function() {\n    for (var i = this.context.length - 1; i >= 1; i--) {\n      var context = this.context[i];\n      if (context.token === \"function\")\n        { return context.generator }\n    }\n    return false\n  };\n\n  pp$6.updateContext = function(prevType) {\n    var update, type = this.type;\n    if (type.keyword && prevType === types$1.dot)\n      { this.exprAllowed = false; }\n    else if (update = type.updateContext)\n      { update.call(this, prevType); }\n    else\n      { this.exprAllowed = type.beforeExpr; }\n  };\n\n  // Used to handle edge cases when token context could not be inferred correctly during tokenization phase\n\n  pp$6.overrideContext = function(tokenCtx) {\n    if (this.curContext() !== tokenCtx) {\n      this.context[this.context.length - 1] = tokenCtx;\n    }\n  };\n\n  // Token-specific context update code\n\n  types$1.parenR.updateContext = types$1.braceR.updateContext = function() {\n    if (this.context.length === 1) {\n      this.exprAllowed = true;\n      return\n    }\n    var out = this.context.pop();\n    if (out === types.b_stat && this.curContext().token === \"function\") {\n      out = this.context.pop();\n    }\n    this.exprAllowed = !out.isExpr;\n  };\n\n  types$1.braceL.updateContext = function(prevType) {\n    this.context.push(this.braceIsBlock(prevType) ? types.b_stat : types.b_expr);\n    this.exprAllowed = true;\n  };\n\n  types$1.dollarBraceL.updateContext = function() {\n    this.context.push(types.b_tmpl);\n    this.exprAllowed = true;\n  };\n\n  types$1.parenL.updateContext = function(prevType) {\n    var statementParens = prevType === types$1._if || prevType === types$1._for || prevType === types$1._with || prevType === types$1._while;\n    this.context.push(statementParens ? types.p_stat : types.p_expr);\n    this.exprAllowed = true;\n  };\n\n  types$1.incDec.updateContext = function() {\n    // tokExprAllowed stays unchanged\n  };\n\n  types$1._function.updateContext = types$1._class.updateContext = function(prevType) {\n    if (prevType.beforeExpr && prevType !== types$1._else &&\n        !(prevType === types$1.semi && this.curContext() !== types.p_stat) &&\n        !(prevType === types$1._return && lineBreak.test(this.input.slice(this.lastTokEnd, this.start))) &&\n        !((prevType === types$1.colon || prevType === types$1.braceL) && this.curContext() === types.b_stat))\n      { this.context.push(types.f_expr); }\n    else\n      { this.context.push(types.f_stat); }\n    this.exprAllowed = false;\n  };\n\n  types$1.colon.updateContext = function() {\n    if (this.curContext().token === \"function\") { this.context.pop(); }\n    this.exprAllowed = true;\n  };\n\n  types$1.backQuote.updateContext = function() {\n    if (this.curContext() === types.q_tmpl)\n      { this.context.pop(); }\n    else\n      { this.context.push(types.q_tmpl); }\n    this.exprAllowed = false;\n  };\n\n  types$1.star.updateContext = function(prevType) {\n    if (prevType === types$1._function) {\n      var index = this.context.length - 1;\n      if (this.context[index] === types.f_expr)\n        { this.context[index] = types.f_expr_gen; }\n      else\n        { this.context[index] = types.f_gen; }\n    }\n    this.exprAllowed = true;\n  };\n\n  types$1.name.updateContext = function(prevType) {\n    var allowed = false;\n    if (this.options.ecmaVersion >= 6 && prevType !== types$1.dot) {\n      if (this.value === \"of\" && !this.exprAllowed ||\n          this.value === \"yield\" && this.inGeneratorContext())\n        { allowed = true; }\n    }\n    this.exprAllowed = allowed;\n  };\n\n  // A recursive descent parser operates by defining functions for all\n  // syntactic elements, and recursively calling those, each function\n  // advancing the input stream and returning an AST node. Precedence\n  // of constructs (for example, the fact that `!x[1]` means `!(x[1])`\n  // instead of `(!x)[1]` is handled by the fact that the parser\n  // function that parses unary prefix operators is called first, and\n  // in turn calls the function that parses `[]` subscripts — that\n  // way, it'll receive the node for `x[1]` already parsed, and wraps\n  // *that* in the unary operator node.\n  //\n  // Acorn uses an [operator precedence parser][opp] to handle binary\n  // operator precedence, because it is much more compact than using\n  // the technique outlined above, which uses different, nesting\n  // functions to specify precedence, for all of the ten binary\n  // precedence levels that JavaScript defines.\n  //\n  // [opp]: http://en.wikipedia.org/wiki/Operator-precedence_parser\n\n\n  var pp$5 = Parser.prototype;\n\n  // Check if property name clashes with already added.\n  // Object/class getters and setters are not allowed to clash —\n  // either with each other or with an init property — and in\n  // strict mode, init properties are also not allowed to be repeated.\n\n  pp$5.checkPropClash = function(prop, propHash, refDestructuringErrors) {\n    if (this.options.ecmaVersion >= 9 && prop.type === \"SpreadElement\")\n      { return }\n    if (this.options.ecmaVersion >= 6 && (prop.computed || prop.method || prop.shorthand))\n      { return }\n    var key = prop.key;\n    var name;\n    switch (key.type) {\n    case \"Identifier\": name = key.name; break\n    case \"Literal\": name = String(key.value); break\n    default: return\n    }\n    var kind = prop.kind;\n    if (this.options.ecmaVersion >= 6) {\n      if (name === \"__proto__\" && kind === \"init\") {\n        if (propHash.proto) {\n          if (refDestructuringErrors) {\n            if (refDestructuringErrors.doubleProto < 0) {\n              refDestructuringErrors.doubleProto = key.start;\n            }\n          } else {\n            this.raiseRecoverable(key.start, \"Redefinition of __proto__ property\");\n          }\n        }\n        propHash.proto = true;\n      }\n      return\n    }\n    name = \"$\" + name;\n    var other = propHash[name];\n    if (other) {\n      var redefinition;\n      if (kind === \"init\") {\n        redefinition = this.strict && other.init || other.get || other.set;\n      } else {\n        redefinition = other.init || other[kind];\n      }\n      if (redefinition)\n        { this.raiseRecoverable(key.start, \"Redefinition of property\"); }\n    } else {\n      other = propHash[name] = {\n        init: false,\n        get: false,\n        set: false\n      };\n    }\n    other[kind] = true;\n  };\n\n  // ### Expression parsing\n\n  // These nest, from the most general expression type at the top to\n  // 'atomic', nondivisible expression types at the bottom. Most of\n  // the functions will simply let the function(s) below them parse,\n  // and, *if* the syntactic construct they handle is present, wrap\n  // the AST node that the inner parser gave them in another node.\n\n  // Parse a full expression. The optional arguments are used to\n  // forbid the `in` operator (in for loops initalization expressions)\n  // and provide reference for storing '=' operator inside shorthand\n  // property assignment in contexts where both object expression\n  // and object pattern might appear (so it's possible to raise\n  // delayed syntax error at correct position).\n\n  pp$5.parseExpression = function(forInit, refDestructuringErrors) {\n    var startPos = this.start, startLoc = this.startLoc;\n    var expr = this.parseMaybeAssign(forInit, refDestructuringErrors);\n    if (this.type === types$1.comma) {\n      var node = this.startNodeAt(startPos, startLoc);\n      node.expressions = [expr];\n      while (this.eat(types$1.comma)) { node.expressions.push(this.parseMaybeAssign(forInit, refDestructuringErrors)); }\n      return this.finishNode(node, \"SequenceExpression\")\n    }\n    return expr\n  };\n\n  // Parse an assignment expression. This includes applications of\n  // operators like `+=`.\n\n  pp$5.parseMaybeAssign = function(forInit, refDestructuringErrors, afterLeftParse) {\n    if (this.isContextual(\"yield\")) {\n      if (this.inGenerator) { return this.parseYield(forInit) }\n      // The tokenizer will assume an expression is allowed after\n      // `yield`, but this isn't that kind of yield\n      else { this.exprAllowed = false; }\n    }\n\n    var ownDestructuringErrors = false, oldParenAssign = -1, oldTrailingComma = -1, oldDoubleProto = -1;\n    if (refDestructuringErrors) {\n      oldParenAssign = refDestructuringErrors.parenthesizedAssign;\n      oldTrailingComma = refDestructuringErrors.trailingComma;\n      oldDoubleProto = refDestructuringErrors.doubleProto;\n      refDestructuringErrors.parenthesizedAssign = refDestructuringErrors.trailingComma = -1;\n    } else {\n      refDestructuringErrors = new DestructuringErrors;\n      ownDestructuringErrors = true;\n    }\n\n    var startPos = this.start, startLoc = this.startLoc;\n    if (this.type === types$1.parenL || this.type === types$1.name) {\n      this.potentialArrowAt = this.start;\n      this.potentialArrowInForAwait = forInit === \"await\";\n    }\n    var left = this.parseMaybeConditional(forInit, refDestructuringErrors);\n    if (afterLeftParse) { left = afterLeftParse.call(this, left, startPos, startLoc); }\n    if (this.type.isAssign) {\n      var node = this.startNodeAt(startPos, startLoc);\n      node.operator = this.value;\n      if (this.type === types$1.eq)\n        { left = this.toAssignable(left, false, refDestructuringErrors); }\n      if (!ownDestructuringErrors) {\n        refDestructuringErrors.parenthesizedAssign = refDestructuringErrors.trailingComma = refDestructuringErrors.doubleProto = -1;\n      }\n      if (refDestructuringErrors.shorthandAssign >= left.start)\n        { refDestructuringErrors.shorthandAssign = -1; } // reset because shorthand default was used correctly\n      if (this.type === types$1.eq)\n        { this.checkLValPattern(left); }\n      else\n        { this.checkLValSimple(left); }\n      node.left = left;\n      this.next();\n      node.right = this.parseMaybeAssign(forInit);\n      if (oldDoubleProto > -1) { refDestructuringErrors.doubleProto = oldDoubleProto; }\n      return this.finishNode(node, \"AssignmentExpression\")\n    } else {\n      if (ownDestructuringErrors) { this.checkExpressionErrors(refDestructuringErrors, true); }\n    }\n    if (oldParenAssign > -1) { refDestructuringErrors.parenthesizedAssign = oldParenAssign; }\n    if (oldTrailingComma > -1) { refDestructuringErrors.trailingComma = oldTrailingComma; }\n    return left\n  };\n\n  // Parse a ternary conditional (`?:`) operator.\n\n  pp$5.parseMaybeConditional = function(forInit, refDestructuringErrors) {\n    var startPos = this.start, startLoc = this.startLoc;\n    var expr = this.parseExprOps(forInit, refDestructuringErrors);\n    if (this.checkExpressionErrors(refDestructuringErrors)) { return expr }\n    if (this.eat(types$1.question)) {\n      var node = this.startNodeAt(startPos, startLoc);\n      node.test = expr;\n      node.consequent = this.parseMaybeAssign();\n      this.expect(types$1.colon);\n      node.alternate = this.parseMaybeAssign(forInit);\n      return this.finishNode(node, \"ConditionalExpression\")\n    }\n    return expr\n  };\n\n  // Start the precedence parser.\n\n  pp$5.parseExprOps = function(forInit, refDestructuringErrors) {\n    var startPos = this.start, startLoc = this.startLoc;\n    var expr = this.parseMaybeUnary(refDestructuringErrors, false, false, forInit);\n    if (this.checkExpressionErrors(refDestructuringErrors)) { return expr }\n    return expr.start === startPos && expr.type === \"ArrowFunctionExpression\" ? expr : this.parseExprOp(expr, startPos, startLoc, -1, forInit)\n  };\n\n  // Parse binary operators with the operator precedence parsing\n  // algorithm. `left` is the left-hand side of the operator.\n  // `minPrec` provides context that allows the function to stop and\n  // defer further parser to one of its callers when it encounters an\n  // operator that has a lower precedence than the set it is parsing.\n\n  pp$5.parseExprOp = function(left, leftStartPos, leftStartLoc, minPrec, forInit) {\n    var prec = this.type.binop;\n    if (prec != null && (!forInit || this.type !== types$1._in)) {\n      if (prec > minPrec) {\n        var logical = this.type === types$1.logicalOR || this.type === types$1.logicalAND;\n        var coalesce = this.type === types$1.coalesce;\n        if (coalesce) {\n          // Handle the precedence of `tt.coalesce` as equal to the range of logical expressions.\n          // In other words, `node.right` shouldn't contain logical expressions in order to check the mixed error.\n          prec = types$1.logicalAND.binop;\n        }\n        var op = this.value;\n        this.next();\n        var startPos = this.start, startLoc = this.startLoc;\n        var right = this.parseExprOp(this.parseMaybeUnary(null, false, false, forInit), startPos, startLoc, prec, forInit);\n        var node = this.buildBinary(leftStartPos, leftStartLoc, left, right, op, logical || coalesce);\n        if ((logical && this.type === types$1.coalesce) || (coalesce && (this.type === types$1.logicalOR || this.type === types$1.logicalAND))) {\n          this.raiseRecoverable(this.start, \"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses\");\n        }\n        return this.parseExprOp(node, leftStartPos, leftStartLoc, minPrec, forInit)\n      }\n    }\n    return left\n  };\n\n  pp$5.buildBinary = function(startPos, startLoc, left, right, op, logical) {\n    if (right.type === \"PrivateIdentifier\") { this.raise(right.start, \"Private identifier can only be left side of binary expression\"); }\n    var node = this.startNodeAt(startPos, startLoc);\n    node.left = left;\n    node.operator = op;\n    node.right = right;\n    return this.finishNode(node, logical ? \"LogicalExpression\" : \"BinaryExpression\")\n  };\n\n  // Parse unary operators, both prefix and postfix.\n\n  pp$5.parseMaybeUnary = function(refDestructuringErrors, sawUnary, incDec, forInit) {\n    var startPos = this.start, startLoc = this.startLoc, expr;\n    if (this.isContextual(\"await\") && this.canAwait) {\n      expr = this.parseAwait(forInit);\n      sawUnary = true;\n    } else if (this.type.prefix) {\n      var node = this.startNode(), update = this.type === types$1.incDec;\n      node.operator = this.value;\n      node.prefix = true;\n      this.next();\n      node.argument = this.parseMaybeUnary(null, true, update, forInit);\n      this.checkExpressionErrors(refDestructuringErrors, true);\n      if (update) { this.checkLValSimple(node.argument); }\n      else if (this.strict && node.operator === \"delete\" && isLocalVariableAccess(node.argument))\n        { this.raiseRecoverable(node.start, \"Deleting local variable in strict mode\"); }\n      else if (node.operator === \"delete\" && isPrivateFieldAccess(node.argument))\n        { this.raiseRecoverable(node.start, \"Private fields can not be deleted\"); }\n      else { sawUnary = true; }\n      expr = this.finishNode(node, update ? \"UpdateExpression\" : \"UnaryExpression\");\n    } else if (!sawUnary && this.type === types$1.privateId) {\n      if ((forInit || this.privateNameStack.length === 0) && this.options.checkPrivateFields) { this.unexpected(); }\n      expr = this.parsePrivateIdent();\n      // only could be private fields in 'in', such as #x in obj\n      if (this.type !== types$1._in) { this.unexpected(); }\n    } else {\n      expr = this.parseExprSubscripts(refDestructuringErrors, forInit);\n      if (this.checkExpressionErrors(refDestructuringErrors)) { return expr }\n      while (this.type.postfix && !this.canInsertSemicolon()) {\n        var node$1 = this.startNodeAt(startPos, startLoc);\n        node$1.operator = this.value;\n        node$1.prefix = false;\n        node$1.argument = expr;\n        this.checkLValSimple(expr);\n        this.next();\n        expr = this.finishNode(node$1, \"UpdateExpression\");\n      }\n    }\n\n    if (!incDec && this.eat(types$1.starstar)) {\n      if (sawUnary)\n        { this.unexpected(this.lastTokStart); }\n      else\n        { return this.buildBinary(startPos, startLoc, expr, this.parseMaybeUnary(null, false, false, forInit), \"**\", false) }\n    } else {\n      return expr\n    }\n  };\n\n  function isLocalVariableAccess(node) {\n    return (\n      node.type === \"Identifier\" ||\n      node.type === \"ParenthesizedExpression\" && isLocalVariableAccess(node.expression)\n    )\n  }\n\n  function isPrivateFieldAccess(node) {\n    return (\n      node.type === \"MemberExpression\" && node.property.type === \"PrivateIdentifier\" ||\n      node.type === \"ChainExpression\" && isPrivateFieldAccess(node.expression) ||\n      node.type === \"ParenthesizedExpression\" && isPrivateFieldAccess(node.expression)\n    )\n  }\n\n  // Parse call, dot, and `[]`-subscript expressions.\n\n  pp$5.parseExprSubscripts = function(refDestructuringErrors, forInit) {\n    var startPos = this.start, startLoc = this.startLoc;\n    var expr = this.parseExprAtom(refDestructuringErrors, forInit);\n    if (expr.type === \"ArrowFunctionExpression\" && this.input.slice(this.lastTokStart, this.lastTokEnd) !== \")\")\n      { return expr }\n    var result = this.parseSubscripts(expr, startPos, startLoc, false, forInit);\n    if (refDestructuringErrors && result.type === \"MemberExpression\") {\n      if (refDestructuringErrors.parenthesizedAssign >= result.start) { refDestructuringErrors.parenthesizedAssign = -1; }\n      if (refDestructuringErrors.parenthesizedBind >= result.start) { refDestructuringErrors.parenthesizedBind = -1; }\n      if (refDestructuringErrors.trailingComma >= result.start) { refDestructuringErrors.trailingComma = -1; }\n    }\n    return result\n  };\n\n  pp$5.parseSubscripts = function(base, startPos, startLoc, noCalls, forInit) {\n    var maybeAsyncArrow = this.options.ecmaVersion >= 8 && base.type === \"Identifier\" && base.name === \"async\" &&\n        this.lastTokEnd === base.end && !this.canInsertSemicolon() && base.end - base.start === 5 &&\n        this.potentialArrowAt === base.start;\n    var optionalChained = false;\n\n    while (true) {\n      var element = this.parseSubscript(base, startPos, startLoc, noCalls, maybeAsyncArrow, optionalChained, forInit);\n\n      if (element.optional) { optionalChained = true; }\n      if (element === base || element.type === \"ArrowFunctionExpression\") {\n        if (optionalChained) {\n          var chainNode = this.startNodeAt(startPos, startLoc);\n          chainNode.expression = element;\n          element = this.finishNode(chainNode, \"ChainExpression\");\n        }\n        return element\n      }\n\n      base = element;\n    }\n  };\n\n  pp$5.shouldParseAsyncArrow = function() {\n    return !this.canInsertSemicolon() && this.eat(types$1.arrow)\n  };\n\n  pp$5.parseSubscriptAsyncArrow = function(startPos, startLoc, exprList, forInit) {\n    return this.parseArrowExpression(this.startNodeAt(startPos, startLoc), exprList, true, forInit)\n  };\n\n  pp$5.parseSubscript = function(base, startPos, startLoc, noCalls, maybeAsyncArrow, optionalChained, forInit) {\n    var optionalSupported = this.options.ecmaVersion >= 11;\n    var optional = optionalSupported && this.eat(types$1.questionDot);\n    if (noCalls && optional) { this.raise(this.lastTokStart, \"Optional chaining cannot appear in the callee of new expressions\"); }\n\n    var computed = this.eat(types$1.bracketL);\n    if (computed || (optional && this.type !== types$1.parenL && this.type !== types$1.backQuote) || this.eat(types$1.dot)) {\n      var node = this.startNodeAt(startPos, startLoc);\n      node.object = base;\n      if (computed) {\n        node.property = this.parseExpression();\n        this.expect(types$1.bracketR);\n      } else if (this.type === types$1.privateId && base.type !== \"Super\") {\n        node.property = this.parsePrivateIdent();\n      } else {\n        node.property = this.parseIdent(this.options.allowReserved !== \"never\");\n      }\n      node.computed = !!computed;\n      if (optionalSupported) {\n        node.optional = optional;\n      }\n      base = this.finishNode(node, \"MemberExpression\");\n    } else if (!noCalls && this.eat(types$1.parenL)) {\n      var refDestructuringErrors = new DestructuringErrors, oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, oldAwaitIdentPos = this.awaitIdentPos;\n      this.yieldPos = 0;\n      this.awaitPos = 0;\n      this.awaitIdentPos = 0;\n      var exprList = this.parseExprList(types$1.parenR, this.options.ecmaVersion >= 8, false, refDestructuringErrors);\n      if (maybeAsyncArrow && !optional && this.shouldParseAsyncArrow()) {\n        this.checkPatternErrors(refDestructuringErrors, false);\n        this.checkYieldAwaitInDefaultParams();\n        if (this.awaitIdentPos > 0)\n          { this.raise(this.awaitIdentPos, \"Cannot use 'await' as identifier inside an async function\"); }\n        this.yieldPos = oldYieldPos;\n        this.awaitPos = oldAwaitPos;\n        this.awaitIdentPos = oldAwaitIdentPos;\n        return this.parseSubscriptAsyncArrow(startPos, startLoc, exprList, forInit)\n      }\n      this.checkExpressionErrors(refDestructuringErrors, true);\n      this.yieldPos = oldYieldPos || this.yieldPos;\n      this.awaitPos = oldAwaitPos || this.awaitPos;\n      this.awaitIdentPos = oldAwaitIdentPos || this.awaitIdentPos;\n      var node$1 = this.startNodeAt(startPos, startLoc);\n      node$1.callee = base;\n      node$1.arguments = exprList;\n      if (optionalSupported) {\n        node$1.optional = optional;\n      }\n      base = this.finishNode(node$1, \"CallExpression\");\n    } else if (this.type === types$1.backQuote) {\n      if (optional || optionalChained) {\n        this.raise(this.start, \"Optional chaining cannot appear in the tag of tagged template expressions\");\n      }\n      var node$2 = this.startNodeAt(startPos, startLoc);\n      node$2.tag = base;\n      node$2.quasi = this.parseTemplate({isTagged: true});\n      base = this.finishNode(node$2, \"TaggedTemplateExpression\");\n    }\n    return base\n  };\n\n  // Parse an atomic expression — either a single token that is an\n  // expression, an expression started by a keyword like `function` or\n  // `new`, or an expression wrapped in punctuation like `()`, `[]`,\n  // or `{}`.\n\n  pp$5.parseExprAtom = function(refDestructuringErrors, forInit, forNew) {\n    // If a division operator appears in an expression position, the\n    // tokenizer got confused, and we force it to read a regexp instead.\n    if (this.type === types$1.slash) { this.readRegexp(); }\n\n    var node, canBeArrow = this.potentialArrowAt === this.start;\n    switch (this.type) {\n    case types$1._super:\n      if (!this.allowSuper)\n        { this.raise(this.start, \"'super' keyword outside a method\"); }\n      node = this.startNode();\n      this.next();\n      if (this.type === types$1.parenL && !this.allowDirectSuper)\n        { this.raise(node.start, \"super() call outside constructor of a subclass\"); }\n      // The `super` keyword can appear at below:\n      // SuperProperty:\n      //     super [ Expression ]\n      //     super . IdentifierName\n      // SuperCall:\n      //     super ( Arguments )\n      if (this.type !== types$1.dot && this.type !== types$1.bracketL && this.type !== types$1.parenL)\n        { this.unexpected(); }\n      return this.finishNode(node, \"Super\")\n\n    case types$1._this:\n      node = this.startNode();\n      this.next();\n      return this.finishNode(node, \"ThisExpression\")\n\n    case types$1.name:\n      var startPos = this.start, startLoc = this.startLoc, containsEsc = this.containsEsc;\n      var id = this.parseIdent(false);\n      if (this.options.ecmaVersion >= 8 && !containsEsc && id.name === \"async\" && !this.canInsertSemicolon() && this.eat(types$1._function)) {\n        this.overrideContext(types.f_expr);\n        return this.parseFunction(this.startNodeAt(startPos, startLoc), 0, false, true, forInit)\n      }\n      if (canBeArrow && !this.canInsertSemicolon()) {\n        if (this.eat(types$1.arrow))\n          { return this.parseArrowExpression(this.startNodeAt(startPos, startLoc), [id], false, forInit) }\n        if (this.options.ecmaVersion >= 8 && id.name === \"async\" && this.type === types$1.name && !containsEsc &&\n            (!this.potentialArrowInForAwait || this.value !== \"of\" || this.containsEsc)) {\n          id = this.parseIdent(false);\n          if (this.canInsertSemicolon() || !this.eat(types$1.arrow))\n            { this.unexpected(); }\n          return this.parseArrowExpression(this.startNodeAt(startPos, startLoc), [id], true, forInit)\n        }\n      }\n      return id\n\n    case types$1.regexp:\n      var value = this.value;\n      node = this.parseLiteral(value.value);\n      node.regex = {pattern: value.pattern, flags: value.flags};\n      return node\n\n    case types$1.num: case types$1.string:\n      return this.parseLiteral(this.value)\n\n    case types$1._null: case types$1._true: case types$1._false:\n      node = this.startNode();\n      node.value = this.type === types$1._null ? null : this.type === types$1._true;\n      node.raw = this.type.keyword;\n      this.next();\n      return this.finishNode(node, \"Literal\")\n\n    case types$1.parenL:\n      var start = this.start, expr = this.parseParenAndDistinguishExpression(canBeArrow, forInit);\n      if (refDestructuringErrors) {\n        if (refDestructuringErrors.parenthesizedAssign < 0 && !this.isSimpleAssignTarget(expr))\n          { refDestructuringErrors.parenthesizedAssign = start; }\n        if (refDestructuringErrors.parenthesizedBind < 0)\n          { refDestructuringErrors.parenthesizedBind = start; }\n      }\n      return expr\n\n    case types$1.bracketL:\n      node = this.startNode();\n      this.next();\n      node.elements = this.parseExprList(types$1.bracketR, true, true, refDestructuringErrors);\n      return this.finishNode(node, \"ArrayExpression\")\n\n    case types$1.braceL:\n      this.overrideContext(types.b_expr);\n      return this.parseObj(false, refDestructuringErrors)\n\n    case types$1._function:\n      node = this.startNode();\n      this.next();\n      return this.parseFunction(node, 0)\n\n    case types$1._class:\n      return this.parseClass(this.startNode(), false)\n\n    case types$1._new:\n      return this.parseNew()\n\n    case types$1.backQuote:\n      return this.parseTemplate()\n\n    case types$1._import:\n      if (this.options.ecmaVersion >= 11) {\n        return this.parseExprImport(forNew)\n      } else {\n        return this.unexpected()\n      }\n\n    default:\n      return this.parseExprAtomDefault()\n    }\n  };\n\n  pp$5.parseExprAtomDefault = function() {\n    this.unexpected();\n  };\n\n  pp$5.parseExprImport = function(forNew) {\n    var node = this.startNode();\n\n    // Consume `import` as an identifier for `import.meta`.\n    // Because `this.parseIdent(true)` doesn't check escape sequences, it needs the check of `this.containsEsc`.\n    if (this.containsEsc) { this.raiseRecoverable(this.start, \"Escape sequence in keyword import\"); }\n    this.next();\n\n    if (this.type === types$1.parenL && !forNew) {\n      return this.parseDynamicImport(node)\n    } else if (this.type === types$1.dot) {\n      var meta = this.startNodeAt(node.start, node.loc && node.loc.start);\n      meta.name = \"import\";\n      node.meta = this.finishNode(meta, \"Identifier\");\n      return this.parseImportMeta(node)\n    } else {\n      this.unexpected();\n    }\n  };\n\n  pp$5.parseDynamicImport = function(node) {\n    this.next(); // skip `(`\n\n    // Parse node.source.\n    node.source = this.parseMaybeAssign();\n\n    if (this.options.ecmaVersion >= 16) {\n      if (!this.eat(types$1.parenR)) {\n        this.expect(types$1.comma);\n        if (!this.afterTrailingComma(types$1.parenR)) {\n          node.options = this.parseMaybeAssign();\n          if (!this.eat(types$1.parenR)) {\n            this.expect(types$1.comma);\n            if (!this.afterTrailingComma(types$1.parenR)) {\n              this.unexpected();\n            }\n          }\n        } else {\n          node.options = null;\n        }\n      } else {\n        node.options = null;\n      }\n    } else {\n      // Verify ending.\n      if (!this.eat(types$1.parenR)) {\n        var errorPos = this.start;\n        if (this.eat(types$1.comma) && this.eat(types$1.parenR)) {\n          this.raiseRecoverable(errorPos, \"Trailing comma is not allowed in import()\");\n        } else {\n          this.unexpected(errorPos);\n        }\n      }\n    }\n\n    return this.finishNode(node, \"ImportExpression\")\n  };\n\n  pp$5.parseImportMeta = function(node) {\n    this.next(); // skip `.`\n\n    var containsEsc = this.containsEsc;\n    node.property = this.parseIdent(true);\n\n    if (node.property.name !== \"meta\")\n      { this.raiseRecoverable(node.property.start, \"The only valid meta property for import is 'import.meta'\"); }\n    if (containsEsc)\n      { this.raiseRecoverable(node.start, \"'import.meta' must not contain escaped characters\"); }\n    if (this.options.sourceType !== \"module\" && !this.options.allowImportExportEverywhere)\n      { this.raiseRecoverable(node.start, \"Cannot use 'import.meta' outside a module\"); }\n\n    return this.finishNode(node, \"MetaProperty\")\n  };\n\n  pp$5.parseLiteral = function(value) {\n    var node = this.startNode();\n    node.value = value;\n    node.raw = this.input.slice(this.start, this.end);\n    if (node.raw.charCodeAt(node.raw.length - 1) === 110)\n      { node.bigint = node.value != null ? node.value.toString() : node.raw.slice(0, -1).replace(/_/g, \"\"); }\n    this.next();\n    return this.finishNode(node, \"Literal\")\n  };\n\n  pp$5.parseParenExpression = function() {\n    this.expect(types$1.parenL);\n    var val = this.parseExpression();\n    this.expect(types$1.parenR);\n    return val\n  };\n\n  pp$5.shouldParseArrow = function(exprList) {\n    return !this.canInsertSemicolon()\n  };\n\n  pp$5.parseParenAndDistinguishExpression = function(canBeArrow, forInit) {\n    var startPos = this.start, startLoc = this.startLoc, val, allowTrailingComma = this.options.ecmaVersion >= 8;\n    if (this.options.ecmaVersion >= 6) {\n      this.next();\n\n      var innerStartPos = this.start, innerStartLoc = this.startLoc;\n      var exprList = [], first = true, lastIsComma = false;\n      var refDestructuringErrors = new DestructuringErrors, oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, spreadStart;\n      this.yieldPos = 0;\n      this.awaitPos = 0;\n      // Do not save awaitIdentPos to allow checking awaits nested in parameters\n      while (this.type !== types$1.parenR) {\n        first ? first = false : this.expect(types$1.comma);\n        if (allowTrailingComma && this.afterTrailingComma(types$1.parenR, true)) {\n          lastIsComma = true;\n          break\n        } else if (this.type === types$1.ellipsis) {\n          spreadStart = this.start;\n          exprList.push(this.parseParenItem(this.parseRestBinding()));\n          if (this.type === types$1.comma) {\n            this.raiseRecoverable(\n              this.start,\n              \"Comma is not permitted after the rest element\"\n            );\n          }\n          break\n        } else {\n          exprList.push(this.parseMaybeAssign(false, refDestructuringErrors, this.parseParenItem));\n        }\n      }\n      var innerEndPos = this.lastTokEnd, innerEndLoc = this.lastTokEndLoc;\n      this.expect(types$1.parenR);\n\n      if (canBeArrow && this.shouldParseArrow(exprList) && this.eat(types$1.arrow)) {\n        this.checkPatternErrors(refDestructuringErrors, false);\n        this.checkYieldAwaitInDefaultParams();\n        this.yieldPos = oldYieldPos;\n        this.awaitPos = oldAwaitPos;\n        return this.parseParenArrowList(startPos, startLoc, exprList, forInit)\n      }\n\n      if (!exprList.length || lastIsComma) { this.unexpected(this.lastTokStart); }\n      if (spreadStart) { this.unexpected(spreadStart); }\n      this.checkExpressionErrors(refDestructuringErrors, true);\n      this.yieldPos = oldYieldPos || this.yieldPos;\n      this.awaitPos = oldAwaitPos || this.awaitPos;\n\n      if (exprList.length > 1) {\n        val = this.startNodeAt(innerStartPos, innerStartLoc);\n        val.expressions = exprList;\n        this.finishNodeAt(val, \"SequenceExpression\", innerEndPos, innerEndLoc);\n      } else {\n        val = exprList[0];\n      }\n    } else {\n      val = this.parseParenExpression();\n    }\n\n    if (this.options.preserveParens) {\n      var par = this.startNodeAt(startPos, startLoc);\n      par.expression = val;\n      return this.finishNode(par, \"ParenthesizedExpression\")\n    } else {\n      return val\n    }\n  };\n\n  pp$5.parseParenItem = function(item) {\n    return item\n  };\n\n  pp$5.parseParenArrowList = function(startPos, startLoc, exprList, forInit) {\n    return this.parseArrowExpression(this.startNodeAt(startPos, startLoc), exprList, false, forInit)\n  };\n\n  // New's precedence is slightly tricky. It must allow its argument to\n  // be a `[]` or dot subscript expression, but not a call — at least,\n  // not without wrapping it in parentheses. Thus, it uses the noCalls\n  // argument to parseSubscripts to prevent it from consuming the\n  // argument list.\n\n  var empty = [];\n\n  pp$5.parseNew = function() {\n    if (this.containsEsc) { this.raiseRecoverable(this.start, \"Escape sequence in keyword new\"); }\n    var node = this.startNode();\n    this.next();\n    if (this.options.ecmaVersion >= 6 && this.type === types$1.dot) {\n      var meta = this.startNodeAt(node.start, node.loc && node.loc.start);\n      meta.name = \"new\";\n      node.meta = this.finishNode(meta, \"Identifier\");\n      this.next();\n      var containsEsc = this.containsEsc;\n      node.property = this.parseIdent(true);\n      if (node.property.name !== \"target\")\n        { this.raiseRecoverable(node.property.start, \"The only valid meta property for new is 'new.target'\"); }\n      if (containsEsc)\n        { this.raiseRecoverable(node.start, \"'new.target' must not contain escaped characters\"); }\n      if (!this.allowNewDotTarget)\n        { this.raiseRecoverable(node.start, \"'new.target' can only be used in functions and class static block\"); }\n      return this.finishNode(node, \"MetaProperty\")\n    }\n    var startPos = this.start, startLoc = this.startLoc;\n    node.callee = this.parseSubscripts(this.parseExprAtom(null, false, true), startPos, startLoc, true, false);\n    if (this.eat(types$1.parenL)) { node.arguments = this.parseExprList(types$1.parenR, this.options.ecmaVersion >= 8, false); }\n    else { node.arguments = empty; }\n    return this.finishNode(node, \"NewExpression\")\n  };\n\n  // Parse template expression.\n\n  pp$5.parseTemplateElement = function(ref) {\n    var isTagged = ref.isTagged;\n\n    var elem = this.startNode();\n    if (this.type === types$1.invalidTemplate) {\n      if (!isTagged) {\n        this.raiseRecoverable(this.start, \"Bad escape sequence in untagged template literal\");\n      }\n      elem.value = {\n        raw: this.value.replace(/\\r\\n?/g, \"\\n\"),\n        cooked: null\n      };\n    } else {\n      elem.value = {\n        raw: this.input.slice(this.start, this.end).replace(/\\r\\n?/g, \"\\n\"),\n        cooked: this.value\n      };\n    }\n    this.next();\n    elem.tail = this.type === types$1.backQuote;\n    return this.finishNode(elem, \"TemplateElement\")\n  };\n\n  pp$5.parseTemplate = function(ref) {\n    if ( ref === void 0 ) ref = {};\n    var isTagged = ref.isTagged; if ( isTagged === void 0 ) isTagged = false;\n\n    var node = this.startNode();\n    this.next();\n    node.expressions = [];\n    var curElt = this.parseTemplateElement({isTagged: isTagged});\n    node.quasis = [curElt];\n    while (!curElt.tail) {\n      if (this.type === types$1.eof) { this.raise(this.pos, \"Unterminated template literal\"); }\n      this.expect(types$1.dollarBraceL);\n      node.expressions.push(this.parseExpression());\n      this.expect(types$1.braceR);\n      node.quasis.push(curElt = this.parseTemplateElement({isTagged: isTagged}));\n    }\n    this.next();\n    return this.finishNode(node, \"TemplateLiteral\")\n  };\n\n  pp$5.isAsyncProp = function(prop) {\n    return !prop.computed && prop.key.type === \"Identifier\" && prop.key.name === \"async\" &&\n      (this.type === types$1.name || this.type === types$1.num || this.type === types$1.string || this.type === types$1.bracketL || this.type.keyword || (this.options.ecmaVersion >= 9 && this.type === types$1.star)) &&\n      !lineBreak.test(this.input.slice(this.lastTokEnd, this.start))\n  };\n\n  // Parse an object literal or binding pattern.\n\n  pp$5.parseObj = function(isPattern, refDestructuringErrors) {\n    var node = this.startNode(), first = true, propHash = {};\n    node.properties = [];\n    this.next();\n    while (!this.eat(types$1.braceR)) {\n      if (!first) {\n        this.expect(types$1.comma);\n        if (this.options.ecmaVersion >= 5 && this.afterTrailingComma(types$1.braceR)) { break }\n      } else { first = false; }\n\n      var prop = this.parseProperty(isPattern, refDestructuringErrors);\n      if (!isPattern) { this.checkPropClash(prop, propHash, refDestructuringErrors); }\n      node.properties.push(prop);\n    }\n    return this.finishNode(node, isPattern ? \"ObjectPattern\" : \"ObjectExpression\")\n  };\n\n  pp$5.parseProperty = function(isPattern, refDestructuringErrors) {\n    var prop = this.startNode(), isGenerator, isAsync, startPos, startLoc;\n    if (this.options.ecmaVersion >= 9 && this.eat(types$1.ellipsis)) {\n      if (isPattern) {\n        prop.argument = this.parseIdent(false);\n        if (this.type === types$1.comma) {\n          this.raiseRecoverable(this.start, \"Comma is not permitted after the rest element\");\n        }\n        return this.finishNode(prop, \"RestElement\")\n      }\n      // Parse argument.\n      prop.argument = this.parseMaybeAssign(false, refDestructuringErrors);\n      // To disallow trailing comma via `this.toAssignable()`.\n      if (this.type === types$1.comma && refDestructuringErrors && refDestructuringErrors.trailingComma < 0) {\n        refDestructuringErrors.trailingComma = this.start;\n      }\n      // Finish\n      return this.finishNode(prop, \"SpreadElement\")\n    }\n    if (this.options.ecmaVersion >= 6) {\n      prop.method = false;\n      prop.shorthand = false;\n      if (isPattern || refDestructuringErrors) {\n        startPos = this.start;\n        startLoc = this.startLoc;\n      }\n      if (!isPattern)\n        { isGenerator = this.eat(types$1.star); }\n    }\n    var containsEsc = this.containsEsc;\n    this.parsePropertyName(prop);\n    if (!isPattern && !containsEsc && this.options.ecmaVersion >= 8 && !isGenerator && this.isAsyncProp(prop)) {\n      isAsync = true;\n      isGenerator = this.options.ecmaVersion >= 9 && this.eat(types$1.star);\n      this.parsePropertyName(prop);\n    } else {\n      isAsync = false;\n    }\n    this.parsePropertyValue(prop, isPattern, isGenerator, isAsync, startPos, startLoc, refDestructuringErrors, containsEsc);\n    return this.finishNode(prop, \"Property\")\n  };\n\n  pp$5.parseGetterSetter = function(prop) {\n    var kind = prop.key.name;\n    this.parsePropertyName(prop);\n    prop.value = this.parseMethod(false);\n    prop.kind = kind;\n    var paramCount = prop.kind === \"get\" ? 0 : 1;\n    if (prop.value.params.length !== paramCount) {\n      var start = prop.value.start;\n      if (prop.kind === \"get\")\n        { this.raiseRecoverable(start, \"getter should have no params\"); }\n      else\n        { this.raiseRecoverable(start, \"setter should have exactly one param\"); }\n    } else {\n      if (prop.kind === \"set\" && prop.value.params[0].type === \"RestElement\")\n        { this.raiseRecoverable(prop.value.params[0].start, \"Setter cannot use rest params\"); }\n    }\n  };\n\n  pp$5.parsePropertyValue = function(prop, isPattern, isGenerator, isAsync, startPos, startLoc, refDestructuringErrors, containsEsc) {\n    if ((isGenerator || isAsync) && this.type === types$1.colon)\n      { this.unexpected(); }\n\n    if (this.eat(types$1.colon)) {\n      prop.value = isPattern ? this.parseMaybeDefault(this.start, this.startLoc) : this.parseMaybeAssign(false, refDestructuringErrors);\n      prop.kind = \"init\";\n    } else if (this.options.ecmaVersion >= 6 && this.type === types$1.parenL) {\n      if (isPattern) { this.unexpected(); }\n      prop.method = true;\n      prop.value = this.parseMethod(isGenerator, isAsync);\n      prop.kind = \"init\";\n    } else if (!isPattern && !containsEsc &&\n               this.options.ecmaVersion >= 5 && !prop.computed && prop.key.type === \"Identifier\" &&\n               (prop.key.name === \"get\" || prop.key.name === \"set\") &&\n               (this.type !== types$1.comma && this.type !== types$1.braceR && this.type !== types$1.eq)) {\n      if (isGenerator || isAsync) { this.unexpected(); }\n      this.parseGetterSetter(prop);\n    } else if (this.options.ecmaVersion >= 6 && !prop.computed && prop.key.type === \"Identifier\") {\n      if (isGenerator || isAsync) { this.unexpected(); }\n      this.checkUnreserved(prop.key);\n      if (prop.key.name === \"await\" && !this.awaitIdentPos)\n        { this.awaitIdentPos = startPos; }\n      if (isPattern) {\n        prop.value = this.parseMaybeDefault(startPos, startLoc, this.copyNode(prop.key));\n      } else if (this.type === types$1.eq && refDestructuringErrors) {\n        if (refDestructuringErrors.shorthandAssign < 0)\n          { refDestructuringErrors.shorthandAssign = this.start; }\n        prop.value = this.parseMaybeDefault(startPos, startLoc, this.copyNode(prop.key));\n      } else {\n        prop.value = this.copyNode(prop.key);\n      }\n      prop.kind = \"init\";\n      prop.shorthand = true;\n    } else { this.unexpected(); }\n  };\n\n  pp$5.parsePropertyName = function(prop) {\n    if (this.options.ecmaVersion >= 6) {\n      if (this.eat(types$1.bracketL)) {\n        prop.computed = true;\n        prop.key = this.parseMaybeAssign();\n        this.expect(types$1.bracketR);\n        return prop.key\n      } else {\n        prop.computed = false;\n      }\n    }\n    return prop.key = this.type === types$1.num || this.type === types$1.string ? this.parseExprAtom() : this.parseIdent(this.options.allowReserved !== \"never\")\n  };\n\n  // Initialize empty function node.\n\n  pp$5.initFunction = function(node) {\n    node.id = null;\n    if (this.options.ecmaVersion >= 6) { node.generator = node.expression = false; }\n    if (this.options.ecmaVersion >= 8) { node.async = false; }\n  };\n\n  // Parse object or class method.\n\n  pp$5.parseMethod = function(isGenerator, isAsync, allowDirectSuper) {\n    var node = this.startNode(), oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, oldAwaitIdentPos = this.awaitIdentPos;\n\n    this.initFunction(node);\n    if (this.options.ecmaVersion >= 6)\n      { node.generator = isGenerator; }\n    if (this.options.ecmaVersion >= 8)\n      { node.async = !!isAsync; }\n\n    this.yieldPos = 0;\n    this.awaitPos = 0;\n    this.awaitIdentPos = 0;\n    this.enterScope(functionFlags(isAsync, node.generator) | SCOPE_SUPER | (allowDirectSuper ? SCOPE_DIRECT_SUPER : 0));\n\n    this.expect(types$1.parenL);\n    node.params = this.parseBindingList(types$1.parenR, false, this.options.ecmaVersion >= 8);\n    this.checkYieldAwaitInDefaultParams();\n    this.parseFunctionBody(node, false, true, false);\n\n    this.yieldPos = oldYieldPos;\n    this.awaitPos = oldAwaitPos;\n    this.awaitIdentPos = oldAwaitIdentPos;\n    return this.finishNode(node, \"FunctionExpression\")\n  };\n\n  // Parse arrow function expression with given parameters.\n\n  pp$5.parseArrowExpression = function(node, params, isAsync, forInit) {\n    var oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, oldAwaitIdentPos = this.awaitIdentPos;\n\n    this.enterScope(functionFlags(isAsync, false) | SCOPE_ARROW);\n    this.initFunction(node);\n    if (this.options.ecmaVersion >= 8) { node.async = !!isAsync; }\n\n    this.yieldPos = 0;\n    this.awaitPos = 0;\n    this.awaitIdentPos = 0;\n\n    node.params = this.toAssignableList(params, true);\n    this.parseFunctionBody(node, true, false, forInit);\n\n    this.yieldPos = oldYieldPos;\n    this.awaitPos = oldAwaitPos;\n    this.awaitIdentPos = oldAwaitIdentPos;\n    return this.finishNode(node, \"ArrowFunctionExpression\")\n  };\n\n  // Parse function body and check parameters.\n\n  pp$5.parseFunctionBody = function(node, isArrowFunction, isMethod, forInit) {\n    var isExpression = isArrowFunction && this.type !== types$1.braceL;\n    var oldStrict = this.strict, useStrict = false;\n\n    if (isExpression) {\n      node.body = this.parseMaybeAssign(forInit);\n      node.expression = true;\n      this.checkParams(node, false);\n    } else {\n      var nonSimple = this.options.ecmaVersion >= 7 && !this.isSimpleParamList(node.params);\n      if (!oldStrict || nonSimple) {\n        useStrict = this.strictDirective(this.end);\n        // If this is a strict mode function, verify that argument names\n        // are not repeated, and it does not try to bind the words `eval`\n        // or `arguments`.\n        if (useStrict && nonSimple)\n          { this.raiseRecoverable(node.start, \"Illegal 'use strict' directive in function with non-simple parameter list\"); }\n      }\n      // Start a new scope with regard to labels and the `inFunction`\n      // flag (restore them to their old value afterwards).\n      var oldLabels = this.labels;\n      this.labels = [];\n      if (useStrict) { this.strict = true; }\n\n      // Add the params to varDeclaredNames to ensure that an error is thrown\n      // if a let/const declaration in the function clashes with one of the params.\n      this.checkParams(node, !oldStrict && !useStrict && !isArrowFunction && !isMethod && this.isSimpleParamList(node.params));\n      // Ensure the function name isn't a forbidden identifier in strict mode, e.g. 'eval'\n      if (this.strict && node.id) { this.checkLValSimple(node.id, BIND_OUTSIDE); }\n      node.body = this.parseBlock(false, undefined, useStrict && !oldStrict);\n      node.expression = false;\n      this.adaptDirectivePrologue(node.body.body);\n      this.labels = oldLabels;\n    }\n    this.exitScope();\n  };\n\n  pp$5.isSimpleParamList = function(params) {\n    for (var i = 0, list = params; i < list.length; i += 1)\n      {\n      var param = list[i];\n\n      if (param.type !== \"Identifier\") { return false\n    } }\n    return true\n  };\n\n  // Checks function params for various disallowed patterns such as using \"eval\"\n  // or \"arguments\" and duplicate parameters.\n\n  pp$5.checkParams = function(node, allowDuplicates) {\n    var nameHash = Object.create(null);\n    for (var i = 0, list = node.params; i < list.length; i += 1)\n      {\n      var param = list[i];\n\n      this.checkLValInnerPattern(param, BIND_VAR, allowDuplicates ? null : nameHash);\n    }\n  };\n\n  // Parses a comma-separated list of expressions, and returns them as\n  // an array. `close` is the token type that ends the list, and\n  // `allowEmpty` can be turned on to allow subsequent commas with\n  // nothing in between them to be parsed as `null` (which is needed\n  // for array literals).\n\n  pp$5.parseExprList = function(close, allowTrailingComma, allowEmpty, refDestructuringErrors) {\n    var elts = [], first = true;\n    while (!this.eat(close)) {\n      if (!first) {\n        this.expect(types$1.comma);\n        if (allowTrailingComma && this.afterTrailingComma(close)) { break }\n      } else { first = false; }\n\n      var elt = (void 0);\n      if (allowEmpty && this.type === types$1.comma)\n        { elt = null; }\n      else if (this.type === types$1.ellipsis) {\n        elt = this.parseSpread(refDestructuringErrors);\n        if (refDestructuringErrors && this.type === types$1.comma && refDestructuringErrors.trailingComma < 0)\n          { refDestructuringErrors.trailingComma = this.start; }\n      } else {\n        elt = this.parseMaybeAssign(false, refDestructuringErrors);\n      }\n      elts.push(elt);\n    }\n    return elts\n  };\n\n  pp$5.checkUnreserved = function(ref) {\n    var start = ref.start;\n    var end = ref.end;\n    var name = ref.name;\n\n    if (this.inGenerator && name === \"yield\")\n      { this.raiseRecoverable(start, \"Cannot use 'yield' as identifier inside a generator\"); }\n    if (this.inAsync && name === \"await\")\n      { this.raiseRecoverable(start, \"Cannot use 'await' as identifier inside an async function\"); }\n    if (!(this.currentThisScope().flags & SCOPE_VAR) && name === \"arguments\")\n      { this.raiseRecoverable(start, \"Cannot use 'arguments' in class field initializer\"); }\n    if (this.inClassStaticBlock && (name === \"arguments\" || name === \"await\"))\n      { this.raise(start, (\"Cannot use \" + name + \" in class static initialization block\")); }\n    if (this.keywords.test(name))\n      { this.raise(start, (\"Unexpected keyword '\" + name + \"'\")); }\n    if (this.options.ecmaVersion < 6 &&\n      this.input.slice(start, end).indexOf(\"\\\\\") !== -1) { return }\n    var re = this.strict ? this.reservedWordsStrict : this.reservedWords;\n    if (re.test(name)) {\n      if (!this.inAsync && name === \"await\")\n        { this.raiseRecoverable(start, \"Cannot use keyword 'await' outside an async function\"); }\n      this.raiseRecoverable(start, (\"The keyword '\" + name + \"' is reserved\"));\n    }\n  };\n\n  // Parse the next token as an identifier. If `liberal` is true (used\n  // when parsing properties), it will also convert keywords into\n  // identifiers.\n\n  pp$5.parseIdent = function(liberal) {\n    var node = this.parseIdentNode();\n    this.next(!!liberal);\n    this.finishNode(node, \"Identifier\");\n    if (!liberal) {\n      this.checkUnreserved(node);\n      if (node.name === \"await\" && !this.awaitIdentPos)\n        { this.awaitIdentPos = node.start; }\n    }\n    return node\n  };\n\n  pp$5.parseIdentNode = function() {\n    var node = this.startNode();\n    if (this.type === types$1.name) {\n      node.name = this.value;\n    } else if (this.type.keyword) {\n      node.name = this.type.keyword;\n\n      // To fix https://github.com/acornjs/acorn/issues/575\n      // `class` and `function` keywords push new context into this.context.\n      // But there is no chance to pop the context if the keyword is consumed as an identifier such as a property name.\n      // If the previous token is a dot, this does not apply because the context-managing code already ignored the keyword\n      if ((node.name === \"class\" || node.name === \"function\") &&\n        (this.lastTokEnd !== this.lastTokStart + 1 || this.input.charCodeAt(this.lastTokStart) !== 46)) {\n        this.context.pop();\n      }\n      this.type = types$1.name;\n    } else {\n      this.unexpected();\n    }\n    return node\n  };\n\n  pp$5.parsePrivateIdent = function() {\n    var node = this.startNode();\n    if (this.type === types$1.privateId) {\n      node.name = this.value;\n    } else {\n      this.unexpected();\n    }\n    this.next();\n    this.finishNode(node, \"PrivateIdentifier\");\n\n    // For validating existence\n    if (this.options.checkPrivateFields) {\n      if (this.privateNameStack.length === 0) {\n        this.raise(node.start, (\"Private field '#\" + (node.name) + \"' must be declared in an enclosing class\"));\n      } else {\n        this.privateNameStack[this.privateNameStack.length - 1].used.push(node);\n      }\n    }\n\n    return node\n  };\n\n  // Parses yield expression inside generator.\n\n  pp$5.parseYield = function(forInit) {\n    if (!this.yieldPos) { this.yieldPos = this.start; }\n\n    var node = this.startNode();\n    this.next();\n    if (this.type === types$1.semi || this.canInsertSemicolon() || (this.type !== types$1.star && !this.type.startsExpr)) {\n      node.delegate = false;\n      node.argument = null;\n    } else {\n      node.delegate = this.eat(types$1.star);\n      node.argument = this.parseMaybeAssign(forInit);\n    }\n    return this.finishNode(node, \"YieldExpression\")\n  };\n\n  pp$5.parseAwait = function(forInit) {\n    if (!this.awaitPos) { this.awaitPos = this.start; }\n\n    var node = this.startNode();\n    this.next();\n    node.argument = this.parseMaybeUnary(null, true, false, forInit);\n    return this.finishNode(node, \"AwaitExpression\")\n  };\n\n  var pp$4 = Parser.prototype;\n\n  // This function is used to raise exceptions on parse errors. It\n  // takes an offset integer (into the current `input`) to indicate\n  // the location of the error, attaches the position to the end\n  // of the error message, and then raises a `SyntaxError` with that\n  // message.\n\n  pp$4.raise = function(pos, message) {\n    var loc = getLineInfo(this.input, pos);\n    message += \" (\" + loc.line + \":\" + loc.column + \")\";\n    if (this.sourceFile) {\n      message += \" in \" + this.sourceFile;\n    }\n    var err = new SyntaxError(message);\n    err.pos = pos; err.loc = loc; err.raisedAt = this.pos;\n    throw err\n  };\n\n  pp$4.raiseRecoverable = pp$4.raise;\n\n  pp$4.curPosition = function() {\n    if (this.options.locations) {\n      return new Position(this.curLine, this.pos - this.lineStart)\n    }\n  };\n\n  var pp$3 = Parser.prototype;\n\n  var Scope = function Scope(flags) {\n    this.flags = flags;\n    // A list of var-declared names in the current lexical scope\n    this.var = [];\n    // A list of lexically-declared names in the current lexical scope\n    this.lexical = [];\n    // A list of lexically-declared FunctionDeclaration names in the current lexical scope\n    this.functions = [];\n  };\n\n  // The functions in this module keep track of declared variables in the current scope in order to detect duplicate variable names.\n\n  pp$3.enterScope = function(flags) {\n    this.scopeStack.push(new Scope(flags));\n  };\n\n  pp$3.exitScope = function() {\n    this.scopeStack.pop();\n  };\n\n  // The spec says:\n  // > At the top level of a function, or script, function declarations are\n  // > treated like var declarations rather than like lexical declarations.\n  pp$3.treatFunctionsAsVarInScope = function(scope) {\n    return (scope.flags & SCOPE_FUNCTION) || !this.inModule && (scope.flags & SCOPE_TOP)\n  };\n\n  pp$3.declareName = function(name, bindingType, pos) {\n    var redeclared = false;\n    if (bindingType === BIND_LEXICAL) {\n      var scope = this.currentScope();\n      redeclared = scope.lexical.indexOf(name) > -1 || scope.functions.indexOf(name) > -1 || scope.var.indexOf(name) > -1;\n      scope.lexical.push(name);\n      if (this.inModule && (scope.flags & SCOPE_TOP))\n        { delete this.undefinedExports[name]; }\n    } else if (bindingType === BIND_SIMPLE_CATCH) {\n      var scope$1 = this.currentScope();\n      scope$1.lexical.push(name);\n    } else if (bindingType === BIND_FUNCTION) {\n      var scope$2 = this.currentScope();\n      if (this.treatFunctionsAsVar)\n        { redeclared = scope$2.lexical.indexOf(name) > -1; }\n      else\n        { redeclared = scope$2.lexical.indexOf(name) > -1 || scope$2.var.indexOf(name) > -1; }\n      scope$2.functions.push(name);\n    } else {\n      for (var i = this.scopeStack.length - 1; i >= 0; --i) {\n        var scope$3 = this.scopeStack[i];\n        if (scope$3.lexical.indexOf(name) > -1 && !((scope$3.flags & SCOPE_SIMPLE_CATCH) && scope$3.lexical[0] === name) ||\n            !this.treatFunctionsAsVarInScope(scope$3) && scope$3.functions.indexOf(name) > -1) {\n          redeclared = true;\n          break\n        }\n        scope$3.var.push(name);\n        if (this.inModule && (scope$3.flags & SCOPE_TOP))\n          { delete this.undefinedExports[name]; }\n        if (scope$3.flags & SCOPE_VAR) { break }\n      }\n    }\n    if (redeclared) { this.raiseRecoverable(pos, (\"Identifier '\" + name + \"' has already been declared\")); }\n  };\n\n  pp$3.checkLocalExport = function(id) {\n    // scope.functions must be empty as Module code is always strict.\n    if (this.scopeStack[0].lexical.indexOf(id.name) === -1 &&\n        this.scopeStack[0].var.indexOf(id.name) === -1) {\n      this.undefinedExports[id.name] = id;\n    }\n  };\n\n  pp$3.currentScope = function() {\n    return this.scopeStack[this.scopeStack.length - 1]\n  };\n\n  pp$3.currentVarScope = function() {\n    for (var i = this.scopeStack.length - 1;; i--) {\n      var scope = this.scopeStack[i];\n      if (scope.flags & (SCOPE_VAR | SCOPE_CLASS_FIELD_INIT | SCOPE_CLASS_STATIC_BLOCK)) { return scope }\n    }\n  };\n\n  // Could be useful for `this`, `new.target`, `super()`, `super.property`, and `super[property]`.\n  pp$3.currentThisScope = function() {\n    for (var i = this.scopeStack.length - 1;; i--) {\n      var scope = this.scopeStack[i];\n      if (scope.flags & (SCOPE_VAR | SCOPE_CLASS_FIELD_INIT | SCOPE_CLASS_STATIC_BLOCK) &&\n          !(scope.flags & SCOPE_ARROW)) { return scope }\n    }\n  };\n\n  var Node = function Node(parser, pos, loc) {\n    this.type = \"\";\n    this.start = pos;\n    this.end = 0;\n    if (parser.options.locations)\n      { this.loc = new SourceLocation(parser, loc); }\n    if (parser.options.directSourceFile)\n      { this.sourceFile = parser.options.directSourceFile; }\n    if (parser.options.ranges)\n      { this.range = [pos, 0]; }\n  };\n\n  // Start an AST node, attaching a start offset.\n\n  var pp$2 = Parser.prototype;\n\n  pp$2.startNode = function() {\n    return new Node(this, this.start, this.startLoc)\n  };\n\n  pp$2.startNodeAt = function(pos, loc) {\n    return new Node(this, pos, loc)\n  };\n\n  // Finish an AST node, adding `type` and `end` properties.\n\n  function finishNodeAt(node, type, pos, loc) {\n    node.type = type;\n    node.end = pos;\n    if (this.options.locations)\n      { node.loc.end = loc; }\n    if (this.options.ranges)\n      { node.range[1] = pos; }\n    return node\n  }\n\n  pp$2.finishNode = function(node, type) {\n    return finishNodeAt.call(this, node, type, this.lastTokEnd, this.lastTokEndLoc)\n  };\n\n  // Finish node at given position\n\n  pp$2.finishNodeAt = function(node, type, pos, loc) {\n    return finishNodeAt.call(this, node, type, pos, loc)\n  };\n\n  pp$2.copyNode = function(node) {\n    var newNode = new Node(this, node.start, this.startLoc);\n    for (var prop in node) { newNode[prop] = node[prop]; }\n    return newNode\n  };\n\n  // This file was generated by \"bin/generate-unicode-script-values.js\". Do not modify manually!\n  var scriptValuesAddedInUnicode = \"Gara Garay Gukh Gurung_Khema Hrkt Katakana_Or_Hiragana Kawi Kirat_Rai Krai Nag_Mundari Nagm Ol_Onal Onao Sunu Sunuwar Todhri Todr Tulu_Tigalari Tutg Unknown Zzzz\";\n\n  // This file contains Unicode properties extracted from the ECMAScript specification.\n  // The lists are extracted like so:\n  // $$('#table-binary-unicode-properties > figure > table > tbody > tr > td:nth-child(1) code').map(el => el.innerText)\n\n  // #table-binary-unicode-properties\n  var ecma9BinaryProperties = \"ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS\";\n  var ecma10BinaryProperties = ecma9BinaryProperties + \" Extended_Pictographic\";\n  var ecma11BinaryProperties = ecma10BinaryProperties;\n  var ecma12BinaryProperties = ecma11BinaryProperties + \" EBase EComp EMod EPres ExtPict\";\n  var ecma13BinaryProperties = ecma12BinaryProperties;\n  var ecma14BinaryProperties = ecma13BinaryProperties;\n\n  var unicodeBinaryProperties = {\n    9: ecma9BinaryProperties,\n    10: ecma10BinaryProperties,\n    11: ecma11BinaryProperties,\n    12: ecma12BinaryProperties,\n    13: ecma13BinaryProperties,\n    14: ecma14BinaryProperties\n  };\n\n  // #table-binary-unicode-properties-of-strings\n  var ecma14BinaryPropertiesOfStrings = \"Basic_Emoji Emoji_Keycap_Sequence RGI_Emoji_Modifier_Sequence RGI_Emoji_Flag_Sequence RGI_Emoji_Tag_Sequence RGI_Emoji_ZWJ_Sequence RGI_Emoji\";\n\n  var unicodeBinaryPropertiesOfStrings = {\n    9: \"\",\n    10: \"\",\n    11: \"\",\n    12: \"\",\n    13: \"\",\n    14: ecma14BinaryPropertiesOfStrings\n  };\n\n  // #table-unicode-general-category-values\n  var unicodeGeneralCategoryValues = \"Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu\";\n\n  // #table-unicode-script-values\n  var ecma9ScriptValues = \"Adlam Adlm Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb\";\n  var ecma10ScriptValues = ecma9ScriptValues + \" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd\";\n  var ecma11ScriptValues = ecma10ScriptValues + \" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho\";\n  var ecma12ScriptValues = ecma11ScriptValues + \" Chorasmian Chrs Diak Dives_Akuru Khitan_Small_Script Kits Yezi Yezidi\";\n  var ecma13ScriptValues = ecma12ScriptValues + \" Cypro_Minoan Cpmn Old_Uyghur Ougr Tangsa Tnsa Toto Vithkuqi Vith\";\n  var ecma14ScriptValues = ecma13ScriptValues + \" \" + scriptValuesAddedInUnicode;\n\n  var unicodeScriptValues = {\n    9: ecma9ScriptValues,\n    10: ecma10ScriptValues,\n    11: ecma11ScriptValues,\n    12: ecma12ScriptValues,\n    13: ecma13ScriptValues,\n    14: ecma14ScriptValues\n  };\n\n  var data = {};\n  function buildUnicodeData(ecmaVersion) {\n    var d = data[ecmaVersion] = {\n      binary: wordsRegexp(unicodeBinaryProperties[ecmaVersion] + \" \" + unicodeGeneralCategoryValues),\n      binaryOfStrings: wordsRegexp(unicodeBinaryPropertiesOfStrings[ecmaVersion]),\n      nonBinary: {\n        General_Category: wordsRegexp(unicodeGeneralCategoryValues),\n        Script: wordsRegexp(unicodeScriptValues[ecmaVersion])\n      }\n    };\n    d.nonBinary.Script_Extensions = d.nonBinary.Script;\n\n    d.nonBinary.gc = d.nonBinary.General_Category;\n    d.nonBinary.sc = d.nonBinary.Script;\n    d.nonBinary.scx = d.nonBinary.Script_Extensions;\n  }\n\n  for (var i = 0, list = [9, 10, 11, 12, 13, 14]; i < list.length; i += 1) {\n    var ecmaVersion = list[i];\n\n    buildUnicodeData(ecmaVersion);\n  }\n\n  var pp$1 = Parser.prototype;\n\n  // Track disjunction structure to determine whether a duplicate\n  // capture group name is allowed because it is in a separate branch.\n  var BranchID = function BranchID(parent, base) {\n    // Parent disjunction branch\n    this.parent = parent;\n    // Identifies this set of sibling branches\n    this.base = base || this;\n  };\n\n  BranchID.prototype.separatedFrom = function separatedFrom (alt) {\n    // A branch is separate from another branch if they or any of\n    // their parents are siblings in a given disjunction\n    for (var self = this; self; self = self.parent) {\n      for (var other = alt; other; other = other.parent) {\n        if (self.base === other.base && self !== other) { return true }\n      }\n    }\n    return false\n  };\n\n  BranchID.prototype.sibling = function sibling () {\n    return new BranchID(this.parent, this.base)\n  };\n\n  var RegExpValidationState = function RegExpValidationState(parser) {\n    this.parser = parser;\n    this.validFlags = \"gim\" + (parser.options.ecmaVersion >= 6 ? \"uy\" : \"\") + (parser.options.ecmaVersion >= 9 ? \"s\" : \"\") + (parser.options.ecmaVersion >= 13 ? \"d\" : \"\") + (parser.options.ecmaVersion >= 15 ? \"v\" : \"\");\n    this.unicodeProperties = data[parser.options.ecmaVersion >= 14 ? 14 : parser.options.ecmaVersion];\n    this.source = \"\";\n    this.flags = \"\";\n    this.start = 0;\n    this.switchU = false;\n    this.switchV = false;\n    this.switchN = false;\n    this.pos = 0;\n    this.lastIntValue = 0;\n    this.lastStringValue = \"\";\n    this.lastAssertionIsQuantifiable = false;\n    this.numCapturingParens = 0;\n    this.maxBackReference = 0;\n    this.groupNames = Object.create(null);\n    this.backReferenceNames = [];\n    this.branchID = null;\n  };\n\n  RegExpValidationState.prototype.reset = function reset (start, pattern, flags) {\n    var unicodeSets = flags.indexOf(\"v\") !== -1;\n    var unicode = flags.indexOf(\"u\") !== -1;\n    this.start = start | 0;\n    this.source = pattern + \"\";\n    this.flags = flags;\n    if (unicodeSets && this.parser.options.ecmaVersion >= 15) {\n      this.switchU = true;\n      this.switchV = true;\n      this.switchN = true;\n    } else {\n      this.switchU = unicode && this.parser.options.ecmaVersion >= 6;\n      this.switchV = false;\n      this.switchN = unicode && this.parser.options.ecmaVersion >= 9;\n    }\n  };\n\n  RegExpValidationState.prototype.raise = function raise (message) {\n    this.parser.raiseRecoverable(this.start, (\"Invalid regular expression: /\" + (this.source) + \"/: \" + message));\n  };\n\n  // If u flag is given, this returns the code point at the index (it combines a surrogate pair).\n  // Otherwise, this returns the code unit of the index (can be a part of a surrogate pair).\n  RegExpValidationState.prototype.at = function at (i, forceU) {\n      if ( forceU === void 0 ) forceU = false;\n\n    var s = this.source;\n    var l = s.length;\n    if (i >= l) {\n      return -1\n    }\n    var c = s.charCodeAt(i);\n    if (!(forceU || this.switchU) || c <= 0xD7FF || c >= 0xE000 || i + 1 >= l) {\n      return c\n    }\n    var next = s.charCodeAt(i + 1);\n    return next >= 0xDC00 && next <= 0xDFFF ? (c << 10) + next - 0x35FDC00 : c\n  };\n\n  RegExpValidationState.prototype.nextIndex = function nextIndex (i, forceU) {\n      if ( forceU === void 0 ) forceU = false;\n\n    var s = this.source;\n    var l = s.length;\n    if (i >= l) {\n      return l\n    }\n    var c = s.charCodeAt(i), next;\n    if (!(forceU || this.switchU) || c <= 0xD7FF || c >= 0xE000 || i + 1 >= l ||\n        (next = s.charCodeAt(i + 1)) < 0xDC00 || next > 0xDFFF) {\n      return i + 1\n    }\n    return i + 2\n  };\n\n  RegExpValidationState.prototype.current = function current (forceU) {\n      if ( forceU === void 0 ) forceU = false;\n\n    return this.at(this.pos, forceU)\n  };\n\n  RegExpValidationState.prototype.lookahead = function lookahead (forceU) {\n      if ( forceU === void 0 ) forceU = false;\n\n    return this.at(this.nextIndex(this.pos, forceU), forceU)\n  };\n\n  RegExpValidationState.prototype.advance = function advance (forceU) {\n      if ( forceU === void 0 ) forceU = false;\n\n    this.pos = this.nextIndex(this.pos, forceU);\n  };\n\n  RegExpValidationState.prototype.eat = function eat (ch, forceU) {\n      if ( forceU === void 0 ) forceU = false;\n\n    if (this.current(forceU) === ch) {\n      this.advance(forceU);\n      return true\n    }\n    return false\n  };\n\n  RegExpValidationState.prototype.eatChars = function eatChars (chs, forceU) {\n      if ( forceU === void 0 ) forceU = false;\n\n    var pos = this.pos;\n    for (var i = 0, list = chs; i < list.length; i += 1) {\n      var ch = list[i];\n\n        var current = this.at(pos, forceU);\n      if (current === -1 || current !== ch) {\n        return false\n      }\n      pos = this.nextIndex(pos, forceU);\n    }\n    this.pos = pos;\n    return true\n  };\n\n  /**\n   * Validate the flags part of a given RegExpLiteral.\n   *\n   * @param {RegExpValidationState} state The state to validate RegExp.\n   * @returns {void}\n   */\n  pp$1.validateRegExpFlags = function(state) {\n    var validFlags = state.validFlags;\n    var flags = state.flags;\n\n    var u = false;\n    var v = false;\n\n    for (var i = 0; i < flags.length; i++) {\n      var flag = flags.charAt(i);\n      if (validFlags.indexOf(flag) === -1) {\n        this.raise(state.start, \"Invalid regular expression flag\");\n      }\n      if (flags.indexOf(flag, i + 1) > -1) {\n        this.raise(state.start, \"Duplicate regular expression flag\");\n      }\n      if (flag === \"u\") { u = true; }\n      if (flag === \"v\") { v = true; }\n    }\n    if (this.options.ecmaVersion >= 15 && u && v) {\n      this.raise(state.start, \"Invalid regular expression flag\");\n    }\n  };\n\n  function hasProp(obj) {\n    for (var _ in obj) { return true }\n    return false\n  }\n\n  /**\n   * Validate the pattern part of a given RegExpLiteral.\n   *\n   * @param {RegExpValidationState} state The state to validate RegExp.\n   * @returns {void}\n   */\n  pp$1.validateRegExpPattern = function(state) {\n    this.regexp_pattern(state);\n\n    // The goal symbol for the parse is |Pattern[~U, ~N]|. If the result of\n    // parsing contains a |GroupName|, reparse with the goal symbol\n    // |Pattern[~U, +N]| and use this result instead. Throw a *SyntaxError*\n    // exception if _P_ did not conform to the grammar, if any elements of _P_\n    // were not matched by the parse, or if any Early Error conditions exist.\n    if (!state.switchN && this.options.ecmaVersion >= 9 && hasProp(state.groupNames)) {\n      state.switchN = true;\n      this.regexp_pattern(state);\n    }\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-Pattern\n  pp$1.regexp_pattern = function(state) {\n    state.pos = 0;\n    state.lastIntValue = 0;\n    state.lastStringValue = \"\";\n    state.lastAssertionIsQuantifiable = false;\n    state.numCapturingParens = 0;\n    state.maxBackReference = 0;\n    state.groupNames = Object.create(null);\n    state.backReferenceNames.length = 0;\n    state.branchID = null;\n\n    this.regexp_disjunction(state);\n\n    if (state.pos !== state.source.length) {\n      // Make the same messages as V8.\n      if (state.eat(0x29 /* ) */)) {\n        state.raise(\"Unmatched ')'\");\n      }\n      if (state.eat(0x5D /* ] */) || state.eat(0x7D /* } */)) {\n        state.raise(\"Lone quantifier brackets\");\n      }\n    }\n    if (state.maxBackReference > state.numCapturingParens) {\n      state.raise(\"Invalid escape\");\n    }\n    for (var i = 0, list = state.backReferenceNames; i < list.length; i += 1) {\n      var name = list[i];\n\n      if (!state.groupNames[name]) {\n        state.raise(\"Invalid named capture referenced\");\n      }\n    }\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-Disjunction\n  pp$1.regexp_disjunction = function(state) {\n    var trackDisjunction = this.options.ecmaVersion >= 16;\n    if (trackDisjunction) { state.branchID = new BranchID(state.branchID, null); }\n    this.regexp_alternative(state);\n    while (state.eat(0x7C /* | */)) {\n      if (trackDisjunction) { state.branchID = state.branchID.sibling(); }\n      this.regexp_alternative(state);\n    }\n    if (trackDisjunction) { state.branchID = state.branchID.parent; }\n\n    // Make the same message as V8.\n    if (this.regexp_eatQuantifier(state, true)) {\n      state.raise(\"Nothing to repeat\");\n    }\n    if (state.eat(0x7B /* { */)) {\n      state.raise(\"Lone quantifier brackets\");\n    }\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-Alternative\n  pp$1.regexp_alternative = function(state) {\n    while (state.pos < state.source.length && this.regexp_eatTerm(state)) {}\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-Term\n  pp$1.regexp_eatTerm = function(state) {\n    if (this.regexp_eatAssertion(state)) {\n      // Handle `QuantifiableAssertion Quantifier` alternative.\n      // `state.lastAssertionIsQuantifiable` is true if the last eaten Assertion\n      // is a QuantifiableAssertion.\n      if (state.lastAssertionIsQuantifiable && this.regexp_eatQuantifier(state)) {\n        // Make the same message as V8.\n        if (state.switchU) {\n          state.raise(\"Invalid quantifier\");\n        }\n      }\n      return true\n    }\n\n    if (state.switchU ? this.regexp_eatAtom(state) : this.regexp_eatExtendedAtom(state)) {\n      this.regexp_eatQuantifier(state);\n      return true\n    }\n\n    return false\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-Assertion\n  pp$1.regexp_eatAssertion = function(state) {\n    var start = state.pos;\n    state.lastAssertionIsQuantifiable = false;\n\n    // ^, $\n    if (state.eat(0x5E /* ^ */) || state.eat(0x24 /* $ */)) {\n      return true\n    }\n\n    // \\b \\B\n    if (state.eat(0x5C /* \\ */)) {\n      if (state.eat(0x42 /* B */) || state.eat(0x62 /* b */)) {\n        return true\n      }\n      state.pos = start;\n    }\n\n    // Lookahead / Lookbehind\n    if (state.eat(0x28 /* ( */) && state.eat(0x3F /* ? */)) {\n      var lookbehind = false;\n      if (this.options.ecmaVersion >= 9) {\n        lookbehind = state.eat(0x3C /* < */);\n      }\n      if (state.eat(0x3D /* = */) || state.eat(0x21 /* ! */)) {\n        this.regexp_disjunction(state);\n        if (!state.eat(0x29 /* ) */)) {\n          state.raise(\"Unterminated group\");\n        }\n        state.lastAssertionIsQuantifiable = !lookbehind;\n        return true\n      }\n    }\n\n    state.pos = start;\n    return false\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-Quantifier\n  pp$1.regexp_eatQuantifier = function(state, noError) {\n    if ( noError === void 0 ) noError = false;\n\n    if (this.regexp_eatQuantifierPrefix(state, noError)) {\n      state.eat(0x3F /* ? */);\n      return true\n    }\n    return false\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-QuantifierPrefix\n  pp$1.regexp_eatQuantifierPrefix = function(state, noError) {\n    return (\n      state.eat(0x2A /* * */) ||\n      state.eat(0x2B /* + */) ||\n      state.eat(0x3F /* ? */) ||\n      this.regexp_eatBracedQuantifier(state, noError)\n    )\n  };\n  pp$1.regexp_eatBracedQuantifier = function(state, noError) {\n    var start = state.pos;\n    if (state.eat(0x7B /* { */)) {\n      var min = 0, max = -1;\n      if (this.regexp_eatDecimalDigits(state)) {\n        min = state.lastIntValue;\n        if (state.eat(0x2C /* , */) && this.regexp_eatDecimalDigits(state)) {\n          max = state.lastIntValue;\n        }\n        if (state.eat(0x7D /* } */)) {\n          // SyntaxError in https://www.ecma-international.org/ecma-262/8.0/#sec-term\n          if (max !== -1 && max < min && !noError) {\n            state.raise(\"numbers out of order in {} quantifier\");\n          }\n          return true\n        }\n      }\n      if (state.switchU && !noError) {\n        state.raise(\"Incomplete quantifier\");\n      }\n      state.pos = start;\n    }\n    return false\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-Atom\n  pp$1.regexp_eatAtom = function(state) {\n    return (\n      this.regexp_eatPatternCharacters(state) ||\n      state.eat(0x2E /* . */) ||\n      this.regexp_eatReverseSolidusAtomEscape(state) ||\n      this.regexp_eatCharacterClass(state) ||\n      this.regexp_eatUncapturingGroup(state) ||\n      this.regexp_eatCapturingGroup(state)\n    )\n  };\n  pp$1.regexp_eatReverseSolidusAtomEscape = function(state) {\n    var start = state.pos;\n    if (state.eat(0x5C /* \\ */)) {\n      if (this.regexp_eatAtomEscape(state)) {\n        return true\n      }\n      state.pos = start;\n    }\n    return false\n  };\n  pp$1.regexp_eatUncapturingGroup = function(state) {\n    var start = state.pos;\n    if (state.eat(0x28 /* ( */)) {\n      if (state.eat(0x3F /* ? */)) {\n        if (this.options.ecmaVersion >= 16) {\n          var addModifiers = this.regexp_eatModifiers(state);\n          var hasHyphen = state.eat(0x2D /* - */);\n          if (addModifiers || hasHyphen) {\n            for (var i = 0; i < addModifiers.length; i++) {\n              var modifier = addModifiers.charAt(i);\n              if (addModifiers.indexOf(modifier, i + 1) > -1) {\n                state.raise(\"Duplicate regular expression modifiers\");\n              }\n            }\n            if (hasHyphen) {\n              var removeModifiers = this.regexp_eatModifiers(state);\n              if (!addModifiers && !removeModifiers && state.current() === 0x3A /* : */) {\n                state.raise(\"Invalid regular expression modifiers\");\n              }\n              for (var i$1 = 0; i$1 < removeModifiers.length; i$1++) {\n                var modifier$1 = removeModifiers.charAt(i$1);\n                if (\n                  removeModifiers.indexOf(modifier$1, i$1 + 1) > -1 ||\n                  addModifiers.indexOf(modifier$1) > -1\n                ) {\n                  state.raise(\"Duplicate regular expression modifiers\");\n                }\n              }\n            }\n          }\n        }\n        if (state.eat(0x3A /* : */)) {\n          this.regexp_disjunction(state);\n          if (state.eat(0x29 /* ) */)) {\n            return true\n          }\n          state.raise(\"Unterminated group\");\n        }\n      }\n      state.pos = start;\n    }\n    return false\n  };\n  pp$1.regexp_eatCapturingGroup = function(state) {\n    if (state.eat(0x28 /* ( */)) {\n      if (this.options.ecmaVersion >= 9) {\n        this.regexp_groupSpecifier(state);\n      } else if (state.current() === 0x3F /* ? */) {\n        state.raise(\"Invalid group\");\n      }\n      this.regexp_disjunction(state);\n      if (state.eat(0x29 /* ) */)) {\n        state.numCapturingParens += 1;\n        return true\n      }\n      state.raise(\"Unterminated group\");\n    }\n    return false\n  };\n  // RegularExpressionModifiers ::\n  //   [empty]\n  //   RegularExpressionModifiers RegularExpressionModifier\n  pp$1.regexp_eatModifiers = function(state) {\n    var modifiers = \"\";\n    var ch = 0;\n    while ((ch = state.current()) !== -1 && isRegularExpressionModifier(ch)) {\n      modifiers += codePointToString(ch);\n      state.advance();\n    }\n    return modifiers\n  };\n  // RegularExpressionModifier :: one of\n  //   `i` `m` `s`\n  function isRegularExpressionModifier(ch) {\n    return ch === 0x69 /* i */ || ch === 0x6d /* m */ || ch === 0x73 /* s */\n  }\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-ExtendedAtom\n  pp$1.regexp_eatExtendedAtom = function(state) {\n    return (\n      state.eat(0x2E /* . */) ||\n      this.regexp_eatReverseSolidusAtomEscape(state) ||\n      this.regexp_eatCharacterClass(state) ||\n      this.regexp_eatUncapturingGroup(state) ||\n      this.regexp_eatCapturingGroup(state) ||\n      this.regexp_eatInvalidBracedQuantifier(state) ||\n      this.regexp_eatExtendedPatternCharacter(state)\n    )\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-InvalidBracedQuantifier\n  pp$1.regexp_eatInvalidBracedQuantifier = function(state) {\n    if (this.regexp_eatBracedQuantifier(state, true)) {\n      state.raise(\"Nothing to repeat\");\n    }\n    return false\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-SyntaxCharacter\n  pp$1.regexp_eatSyntaxCharacter = function(state) {\n    var ch = state.current();\n    if (isSyntaxCharacter(ch)) {\n      state.lastIntValue = ch;\n      state.advance();\n      return true\n    }\n    return false\n  };\n  function isSyntaxCharacter(ch) {\n    return (\n      ch === 0x24 /* $ */ ||\n      ch >= 0x28 /* ( */ && ch <= 0x2B /* + */ ||\n      ch === 0x2E /* . */ ||\n      ch === 0x3F /* ? */ ||\n      ch >= 0x5B /* [ */ && ch <= 0x5E /* ^ */ ||\n      ch >= 0x7B /* { */ && ch <= 0x7D /* } */\n    )\n  }\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-PatternCharacter\n  // But eat eager.\n  pp$1.regexp_eatPatternCharacters = function(state) {\n    var start = state.pos;\n    var ch = 0;\n    while ((ch = state.current()) !== -1 && !isSyntaxCharacter(ch)) {\n      state.advance();\n    }\n    return state.pos !== start\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-ExtendedPatternCharacter\n  pp$1.regexp_eatExtendedPatternCharacter = function(state) {\n    var ch = state.current();\n    if (\n      ch !== -1 &&\n      ch !== 0x24 /* $ */ &&\n      !(ch >= 0x28 /* ( */ && ch <= 0x2B /* + */) &&\n      ch !== 0x2E /* . */ &&\n      ch !== 0x3F /* ? */ &&\n      ch !== 0x5B /* [ */ &&\n      ch !== 0x5E /* ^ */ &&\n      ch !== 0x7C /* | */\n    ) {\n      state.advance();\n      return true\n    }\n    return false\n  };\n\n  // GroupSpecifier ::\n  //   [empty]\n  //   `?` GroupName\n  pp$1.regexp_groupSpecifier = function(state) {\n    if (state.eat(0x3F /* ? */)) {\n      if (!this.regexp_eatGroupName(state)) { state.raise(\"Invalid group\"); }\n      var trackDisjunction = this.options.ecmaVersion >= 16;\n      var known = state.groupNames[state.lastStringValue];\n      if (known) {\n        if (trackDisjunction) {\n          for (var i = 0, list = known; i < list.length; i += 1) {\n            var altID = list[i];\n\n            if (!altID.separatedFrom(state.branchID))\n              { state.raise(\"Duplicate capture group name\"); }\n          }\n        } else {\n          state.raise(\"Duplicate capture group name\");\n        }\n      }\n      if (trackDisjunction) {\n        (known || (state.groupNames[state.lastStringValue] = [])).push(state.branchID);\n      } else {\n        state.groupNames[state.lastStringValue] = true;\n      }\n    }\n  };\n\n  // GroupName ::\n  //   `<` RegExpIdentifierName `>`\n  // Note: this updates `state.lastStringValue` property with the eaten name.\n  pp$1.regexp_eatGroupName = function(state) {\n    state.lastStringValue = \"\";\n    if (state.eat(0x3C /* < */)) {\n      if (this.regexp_eatRegExpIdentifierName(state) && state.eat(0x3E /* > */)) {\n        return true\n      }\n      state.raise(\"Invalid capture group name\");\n    }\n    return false\n  };\n\n  // RegExpIdentifierName ::\n  //   RegExpIdentifierStart\n  //   RegExpIdentifierName RegExpIdentifierPart\n  // Note: this updates `state.lastStringValue` property with the eaten name.\n  pp$1.regexp_eatRegExpIdentifierName = function(state) {\n    state.lastStringValue = \"\";\n    if (this.regexp_eatRegExpIdentifierStart(state)) {\n      state.lastStringValue += codePointToString(state.lastIntValue);\n      while (this.regexp_eatRegExpIdentifierPart(state)) {\n        state.lastStringValue += codePointToString(state.lastIntValue);\n      }\n      return true\n    }\n    return false\n  };\n\n  // RegExpIdentifierStart ::\n  //   UnicodeIDStart\n  //   `$`\n  //   `_`\n  //   `\\` RegExpUnicodeEscapeSequence[+U]\n  pp$1.regexp_eatRegExpIdentifierStart = function(state) {\n    var start = state.pos;\n    var forceU = this.options.ecmaVersion >= 11;\n    var ch = state.current(forceU);\n    state.advance(forceU);\n\n    if (ch === 0x5C /* \\ */ && this.regexp_eatRegExpUnicodeEscapeSequence(state, forceU)) {\n      ch = state.lastIntValue;\n    }\n    if (isRegExpIdentifierStart(ch)) {\n      state.lastIntValue = ch;\n      return true\n    }\n\n    state.pos = start;\n    return false\n  };\n  function isRegExpIdentifierStart(ch) {\n    return isIdentifierStart(ch, true) || ch === 0x24 /* $ */ || ch === 0x5F /* _ */\n  }\n\n  // RegExpIdentifierPart ::\n  //   UnicodeIDContinue\n  //   `$`\n  //   `_`\n  //   `\\` RegExpUnicodeEscapeSequence[+U]\n  //   <ZWNJ>\n  //   <ZWJ>\n  pp$1.regexp_eatRegExpIdentifierPart = function(state) {\n    var start = state.pos;\n    var forceU = this.options.ecmaVersion >= 11;\n    var ch = state.current(forceU);\n    state.advance(forceU);\n\n    if (ch === 0x5C /* \\ */ && this.regexp_eatRegExpUnicodeEscapeSequence(state, forceU)) {\n      ch = state.lastIntValue;\n    }\n    if (isRegExpIdentifierPart(ch)) {\n      state.lastIntValue = ch;\n      return true\n    }\n\n    state.pos = start;\n    return false\n  };\n  function isRegExpIdentifierPart(ch) {\n    return isIdentifierChar(ch, true) || ch === 0x24 /* $ */ || ch === 0x5F /* _ */ || ch === 0x200C /* <ZWNJ> */ || ch === 0x200D /* <ZWJ> */\n  }\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-AtomEscape\n  pp$1.regexp_eatAtomEscape = function(state) {\n    if (\n      this.regexp_eatBackReference(state) ||\n      this.regexp_eatCharacterClassEscape(state) ||\n      this.regexp_eatCharacterEscape(state) ||\n      (state.switchN && this.regexp_eatKGroupName(state))\n    ) {\n      return true\n    }\n    if (state.switchU) {\n      // Make the same message as V8.\n      if (state.current() === 0x63 /* c */) {\n        state.raise(\"Invalid unicode escape\");\n      }\n      state.raise(\"Invalid escape\");\n    }\n    return false\n  };\n  pp$1.regexp_eatBackReference = function(state) {\n    var start = state.pos;\n    if (this.regexp_eatDecimalEscape(state)) {\n      var n = state.lastIntValue;\n      if (state.switchU) {\n        // For SyntaxError in https://www.ecma-international.org/ecma-262/8.0/#sec-atomescape\n        if (n > state.maxBackReference) {\n          state.maxBackReference = n;\n        }\n        return true\n      }\n      if (n <= state.numCapturingParens) {\n        return true\n      }\n      state.pos = start;\n    }\n    return false\n  };\n  pp$1.regexp_eatKGroupName = function(state) {\n    if (state.eat(0x6B /* k */)) {\n      if (this.regexp_eatGroupName(state)) {\n        state.backReferenceNames.push(state.lastStringValue);\n        return true\n      }\n      state.raise(\"Invalid named reference\");\n    }\n    return false\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-CharacterEscape\n  pp$1.regexp_eatCharacterEscape = function(state) {\n    return (\n      this.regexp_eatControlEscape(state) ||\n      this.regexp_eatCControlLetter(state) ||\n      this.regexp_eatZero(state) ||\n      this.regexp_eatHexEscapeSequence(state) ||\n      this.regexp_eatRegExpUnicodeEscapeSequence(state, false) ||\n      (!state.switchU && this.regexp_eatLegacyOctalEscapeSequence(state)) ||\n      this.regexp_eatIdentityEscape(state)\n    )\n  };\n  pp$1.regexp_eatCControlLetter = function(state) {\n    var start = state.pos;\n    if (state.eat(0x63 /* c */)) {\n      if (this.regexp_eatControlLetter(state)) {\n        return true\n      }\n      state.pos = start;\n    }\n    return false\n  };\n  pp$1.regexp_eatZero = function(state) {\n    if (state.current() === 0x30 /* 0 */ && !isDecimalDigit(state.lookahead())) {\n      state.lastIntValue = 0;\n      state.advance();\n      return true\n    }\n    return false\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-ControlEscape\n  pp$1.regexp_eatControlEscape = function(state) {\n    var ch = state.current();\n    if (ch === 0x74 /* t */) {\n      state.lastIntValue = 0x09; /* \\t */\n      state.advance();\n      return true\n    }\n    if (ch === 0x6E /* n */) {\n      state.lastIntValue = 0x0A; /* \\n */\n      state.advance();\n      return true\n    }\n    if (ch === 0x76 /* v */) {\n      state.lastIntValue = 0x0B; /* \\v */\n      state.advance();\n      return true\n    }\n    if (ch === 0x66 /* f */) {\n      state.lastIntValue = 0x0C; /* \\f */\n      state.advance();\n      return true\n    }\n    if (ch === 0x72 /* r */) {\n      state.lastIntValue = 0x0D; /* \\r */\n      state.advance();\n      return true\n    }\n    return false\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-ControlLetter\n  pp$1.regexp_eatControlLetter = function(state) {\n    var ch = state.current();\n    if (isControlLetter(ch)) {\n      state.lastIntValue = ch % 0x20;\n      state.advance();\n      return true\n    }\n    return false\n  };\n  function isControlLetter(ch) {\n    return (\n      (ch >= 0x41 /* A */ && ch <= 0x5A /* Z */) ||\n      (ch >= 0x61 /* a */ && ch <= 0x7A /* z */)\n    )\n  }\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-RegExpUnicodeEscapeSequence\n  pp$1.regexp_eatRegExpUnicodeEscapeSequence = function(state, forceU) {\n    if ( forceU === void 0 ) forceU = false;\n\n    var start = state.pos;\n    var switchU = forceU || state.switchU;\n\n    if (state.eat(0x75 /* u */)) {\n      if (this.regexp_eatFixedHexDigits(state, 4)) {\n        var lead = state.lastIntValue;\n        if (switchU && lead >= 0xD800 && lead <= 0xDBFF) {\n          var leadSurrogateEnd = state.pos;\n          if (state.eat(0x5C /* \\ */) && state.eat(0x75 /* u */) && this.regexp_eatFixedHexDigits(state, 4)) {\n            var trail = state.lastIntValue;\n            if (trail >= 0xDC00 && trail <= 0xDFFF) {\n              state.lastIntValue = (lead - 0xD800) * 0x400 + (trail - 0xDC00) + 0x10000;\n              return true\n            }\n          }\n          state.pos = leadSurrogateEnd;\n          state.lastIntValue = lead;\n        }\n        return true\n      }\n      if (\n        switchU &&\n        state.eat(0x7B /* { */) &&\n        this.regexp_eatHexDigits(state) &&\n        state.eat(0x7D /* } */) &&\n        isValidUnicode(state.lastIntValue)\n      ) {\n        return true\n      }\n      if (switchU) {\n        state.raise(\"Invalid unicode escape\");\n      }\n      state.pos = start;\n    }\n\n    return false\n  };\n  function isValidUnicode(ch) {\n    return ch >= 0 && ch <= 0x10FFFF\n  }\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-IdentityEscape\n  pp$1.regexp_eatIdentityEscape = function(state) {\n    if (state.switchU) {\n      if (this.regexp_eatSyntaxCharacter(state)) {\n        return true\n      }\n      if (state.eat(0x2F /* / */)) {\n        state.lastIntValue = 0x2F; /* / */\n        return true\n      }\n      return false\n    }\n\n    var ch = state.current();\n    if (ch !== 0x63 /* c */ && (!state.switchN || ch !== 0x6B /* k */)) {\n      state.lastIntValue = ch;\n      state.advance();\n      return true\n    }\n\n    return false\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-DecimalEscape\n  pp$1.regexp_eatDecimalEscape = function(state) {\n    state.lastIntValue = 0;\n    var ch = state.current();\n    if (ch >= 0x31 /* 1 */ && ch <= 0x39 /* 9 */) {\n      do {\n        state.lastIntValue = 10 * state.lastIntValue + (ch - 0x30 /* 0 */);\n        state.advance();\n      } while ((ch = state.current()) >= 0x30 /* 0 */ && ch <= 0x39 /* 9 */)\n      return true\n    }\n    return false\n  };\n\n  // Return values used by character set parsing methods, needed to\n  // forbid negation of sets that can match strings.\n  var CharSetNone = 0; // Nothing parsed\n  var CharSetOk = 1; // Construct parsed, cannot contain strings\n  var CharSetString = 2; // Construct parsed, can contain strings\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-CharacterClassEscape\n  pp$1.regexp_eatCharacterClassEscape = function(state) {\n    var ch = state.current();\n\n    if (isCharacterClassEscape(ch)) {\n      state.lastIntValue = -1;\n      state.advance();\n      return CharSetOk\n    }\n\n    var negate = false;\n    if (\n      state.switchU &&\n      this.options.ecmaVersion >= 9 &&\n      ((negate = ch === 0x50 /* P */) || ch === 0x70 /* p */)\n    ) {\n      state.lastIntValue = -1;\n      state.advance();\n      var result;\n      if (\n        state.eat(0x7B /* { */) &&\n        (result = this.regexp_eatUnicodePropertyValueExpression(state)) &&\n        state.eat(0x7D /* } */)\n      ) {\n        if (negate && result === CharSetString) { state.raise(\"Invalid property name\"); }\n        return result\n      }\n      state.raise(\"Invalid property name\");\n    }\n\n    return CharSetNone\n  };\n\n  function isCharacterClassEscape(ch) {\n    return (\n      ch === 0x64 /* d */ ||\n      ch === 0x44 /* D */ ||\n      ch === 0x73 /* s */ ||\n      ch === 0x53 /* S */ ||\n      ch === 0x77 /* w */ ||\n      ch === 0x57 /* W */\n    )\n  }\n\n  // UnicodePropertyValueExpression ::\n  //   UnicodePropertyName `=` UnicodePropertyValue\n  //   LoneUnicodePropertyNameOrValue\n  pp$1.regexp_eatUnicodePropertyValueExpression = function(state) {\n    var start = state.pos;\n\n    // UnicodePropertyName `=` UnicodePropertyValue\n    if (this.regexp_eatUnicodePropertyName(state) && state.eat(0x3D /* = */)) {\n      var name = state.lastStringValue;\n      if (this.regexp_eatUnicodePropertyValue(state)) {\n        var value = state.lastStringValue;\n        this.regexp_validateUnicodePropertyNameAndValue(state, name, value);\n        return CharSetOk\n      }\n    }\n    state.pos = start;\n\n    // LoneUnicodePropertyNameOrValue\n    if (this.regexp_eatLoneUnicodePropertyNameOrValue(state)) {\n      var nameOrValue = state.lastStringValue;\n      return this.regexp_validateUnicodePropertyNameOrValue(state, nameOrValue)\n    }\n    return CharSetNone\n  };\n\n  pp$1.regexp_validateUnicodePropertyNameAndValue = function(state, name, value) {\n    if (!hasOwn(state.unicodeProperties.nonBinary, name))\n      { state.raise(\"Invalid property name\"); }\n    if (!state.unicodeProperties.nonBinary[name].test(value))\n      { state.raise(\"Invalid property value\"); }\n  };\n\n  pp$1.regexp_validateUnicodePropertyNameOrValue = function(state, nameOrValue) {\n    if (state.unicodeProperties.binary.test(nameOrValue)) { return CharSetOk }\n    if (state.switchV && state.unicodeProperties.binaryOfStrings.test(nameOrValue)) { return CharSetString }\n    state.raise(\"Invalid property name\");\n  };\n\n  // UnicodePropertyName ::\n  //   UnicodePropertyNameCharacters\n  pp$1.regexp_eatUnicodePropertyName = function(state) {\n    var ch = 0;\n    state.lastStringValue = \"\";\n    while (isUnicodePropertyNameCharacter(ch = state.current())) {\n      state.lastStringValue += codePointToString(ch);\n      state.advance();\n    }\n    return state.lastStringValue !== \"\"\n  };\n\n  function isUnicodePropertyNameCharacter(ch) {\n    return isControlLetter(ch) || ch === 0x5F /* _ */\n  }\n\n  // UnicodePropertyValue ::\n  //   UnicodePropertyValueCharacters\n  pp$1.regexp_eatUnicodePropertyValue = function(state) {\n    var ch = 0;\n    state.lastStringValue = \"\";\n    while (isUnicodePropertyValueCharacter(ch = state.current())) {\n      state.lastStringValue += codePointToString(ch);\n      state.advance();\n    }\n    return state.lastStringValue !== \"\"\n  };\n  function isUnicodePropertyValueCharacter(ch) {\n    return isUnicodePropertyNameCharacter(ch) || isDecimalDigit(ch)\n  }\n\n  // LoneUnicodePropertyNameOrValue ::\n  //   UnicodePropertyValueCharacters\n  pp$1.regexp_eatLoneUnicodePropertyNameOrValue = function(state) {\n    return this.regexp_eatUnicodePropertyValue(state)\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-CharacterClass\n  pp$1.regexp_eatCharacterClass = function(state) {\n    if (state.eat(0x5B /* [ */)) {\n      var negate = state.eat(0x5E /* ^ */);\n      var result = this.regexp_classContents(state);\n      if (!state.eat(0x5D /* ] */))\n        { state.raise(\"Unterminated character class\"); }\n      if (negate && result === CharSetString)\n        { state.raise(\"Negated character class may contain strings\"); }\n      return true\n    }\n    return false\n  };\n\n  // https://tc39.es/ecma262/#prod-ClassContents\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-ClassRanges\n  pp$1.regexp_classContents = function(state) {\n    if (state.current() === 0x5D /* ] */) { return CharSetOk }\n    if (state.switchV) { return this.regexp_classSetExpression(state) }\n    this.regexp_nonEmptyClassRanges(state);\n    return CharSetOk\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-NonemptyClassRanges\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-NonemptyClassRangesNoDash\n  pp$1.regexp_nonEmptyClassRanges = function(state) {\n    while (this.regexp_eatClassAtom(state)) {\n      var left = state.lastIntValue;\n      if (state.eat(0x2D /* - */) && this.regexp_eatClassAtom(state)) {\n        var right = state.lastIntValue;\n        if (state.switchU && (left === -1 || right === -1)) {\n          state.raise(\"Invalid character class\");\n        }\n        if (left !== -1 && right !== -1 && left > right) {\n          state.raise(\"Range out of order in character class\");\n        }\n      }\n    }\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-ClassAtom\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-ClassAtomNoDash\n  pp$1.regexp_eatClassAtom = function(state) {\n    var start = state.pos;\n\n    if (state.eat(0x5C /* \\ */)) {\n      if (this.regexp_eatClassEscape(state)) {\n        return true\n      }\n      if (state.switchU) {\n        // Make the same message as V8.\n        var ch$1 = state.current();\n        if (ch$1 === 0x63 /* c */ || isOctalDigit(ch$1)) {\n          state.raise(\"Invalid class escape\");\n        }\n        state.raise(\"Invalid escape\");\n      }\n      state.pos = start;\n    }\n\n    var ch = state.current();\n    if (ch !== 0x5D /* ] */) {\n      state.lastIntValue = ch;\n      state.advance();\n      return true\n    }\n\n    return false\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-ClassEscape\n  pp$1.regexp_eatClassEscape = function(state) {\n    var start = state.pos;\n\n    if (state.eat(0x62 /* b */)) {\n      state.lastIntValue = 0x08; /* <BS> */\n      return true\n    }\n\n    if (state.switchU && state.eat(0x2D /* - */)) {\n      state.lastIntValue = 0x2D; /* - */\n      return true\n    }\n\n    if (!state.switchU && state.eat(0x63 /* c */)) {\n      if (this.regexp_eatClassControlLetter(state)) {\n        return true\n      }\n      state.pos = start;\n    }\n\n    return (\n      this.regexp_eatCharacterClassEscape(state) ||\n      this.regexp_eatCharacterEscape(state)\n    )\n  };\n\n  // https://tc39.es/ecma262/#prod-ClassSetExpression\n  // https://tc39.es/ecma262/#prod-ClassUnion\n  // https://tc39.es/ecma262/#prod-ClassIntersection\n  // https://tc39.es/ecma262/#prod-ClassSubtraction\n  pp$1.regexp_classSetExpression = function(state) {\n    var result = CharSetOk, subResult;\n    if (this.regexp_eatClassSetRange(state)) ; else if (subResult = this.regexp_eatClassSetOperand(state)) {\n      if (subResult === CharSetString) { result = CharSetString; }\n      // https://tc39.es/ecma262/#prod-ClassIntersection\n      var start = state.pos;\n      while (state.eatChars([0x26, 0x26] /* && */)) {\n        if (\n          state.current() !== 0x26 /* & */ &&\n          (subResult = this.regexp_eatClassSetOperand(state))\n        ) {\n          if (subResult !== CharSetString) { result = CharSetOk; }\n          continue\n        }\n        state.raise(\"Invalid character in character class\");\n      }\n      if (start !== state.pos) { return result }\n      // https://tc39.es/ecma262/#prod-ClassSubtraction\n      while (state.eatChars([0x2D, 0x2D] /* -- */)) {\n        if (this.regexp_eatClassSetOperand(state)) { continue }\n        state.raise(\"Invalid character in character class\");\n      }\n      if (start !== state.pos) { return result }\n    } else {\n      state.raise(\"Invalid character in character class\");\n    }\n    // https://tc39.es/ecma262/#prod-ClassUnion\n    for (;;) {\n      if (this.regexp_eatClassSetRange(state)) { continue }\n      subResult = this.regexp_eatClassSetOperand(state);\n      if (!subResult) { return result }\n      if (subResult === CharSetString) { result = CharSetString; }\n    }\n  };\n\n  // https://tc39.es/ecma262/#prod-ClassSetRange\n  pp$1.regexp_eatClassSetRange = function(state) {\n    var start = state.pos;\n    if (this.regexp_eatClassSetCharacter(state)) {\n      var left = state.lastIntValue;\n      if (state.eat(0x2D /* - */) && this.regexp_eatClassSetCharacter(state)) {\n        var right = state.lastIntValue;\n        if (left !== -1 && right !== -1 && left > right) {\n          state.raise(\"Range out of order in character class\");\n        }\n        return true\n      }\n      state.pos = start;\n    }\n    return false\n  };\n\n  // https://tc39.es/ecma262/#prod-ClassSetOperand\n  pp$1.regexp_eatClassSetOperand = function(state) {\n    if (this.regexp_eatClassSetCharacter(state)) { return CharSetOk }\n    return this.regexp_eatClassStringDisjunction(state) || this.regexp_eatNestedClass(state)\n  };\n\n  // https://tc39.es/ecma262/#prod-NestedClass\n  pp$1.regexp_eatNestedClass = function(state) {\n    var start = state.pos;\n    if (state.eat(0x5B /* [ */)) {\n      var negate = state.eat(0x5E /* ^ */);\n      var result = this.regexp_classContents(state);\n      if (state.eat(0x5D /* ] */)) {\n        if (negate && result === CharSetString) {\n          state.raise(\"Negated character class may contain strings\");\n        }\n        return result\n      }\n      state.pos = start;\n    }\n    if (state.eat(0x5C /* \\ */)) {\n      var result$1 = this.regexp_eatCharacterClassEscape(state);\n      if (result$1) {\n        return result$1\n      }\n      state.pos = start;\n    }\n    return null\n  };\n\n  // https://tc39.es/ecma262/#prod-ClassStringDisjunction\n  pp$1.regexp_eatClassStringDisjunction = function(state) {\n    var start = state.pos;\n    if (state.eatChars([0x5C, 0x71] /* \\q */)) {\n      if (state.eat(0x7B /* { */)) {\n        var result = this.regexp_classStringDisjunctionContents(state);\n        if (state.eat(0x7D /* } */)) {\n          return result\n        }\n      } else {\n        // Make the same message as V8.\n        state.raise(\"Invalid escape\");\n      }\n      state.pos = start;\n    }\n    return null\n  };\n\n  // https://tc39.es/ecma262/#prod-ClassStringDisjunctionContents\n  pp$1.regexp_classStringDisjunctionContents = function(state) {\n    var result = this.regexp_classString(state);\n    while (state.eat(0x7C /* | */)) {\n      if (this.regexp_classString(state) === CharSetString) { result = CharSetString; }\n    }\n    return result\n  };\n\n  // https://tc39.es/ecma262/#prod-ClassString\n  // https://tc39.es/ecma262/#prod-NonEmptyClassString\n  pp$1.regexp_classString = function(state) {\n    var count = 0;\n    while (this.regexp_eatClassSetCharacter(state)) { count++; }\n    return count === 1 ? CharSetOk : CharSetString\n  };\n\n  // https://tc39.es/ecma262/#prod-ClassSetCharacter\n  pp$1.regexp_eatClassSetCharacter = function(state) {\n    var start = state.pos;\n    if (state.eat(0x5C /* \\ */)) {\n      if (\n        this.regexp_eatCharacterEscape(state) ||\n        this.regexp_eatClassSetReservedPunctuator(state)\n      ) {\n        return true\n      }\n      if (state.eat(0x62 /* b */)) {\n        state.lastIntValue = 0x08; /* <BS> */\n        return true\n      }\n      state.pos = start;\n      return false\n    }\n    var ch = state.current();\n    if (ch < 0 || ch === state.lookahead() && isClassSetReservedDoublePunctuatorCharacter(ch)) { return false }\n    if (isClassSetSyntaxCharacter(ch)) { return false }\n    state.advance();\n    state.lastIntValue = ch;\n    return true\n  };\n\n  // https://tc39.es/ecma262/#prod-ClassSetReservedDoublePunctuator\n  function isClassSetReservedDoublePunctuatorCharacter(ch) {\n    return (\n      ch === 0x21 /* ! */ ||\n      ch >= 0x23 /* # */ && ch <= 0x26 /* & */ ||\n      ch >= 0x2A /* * */ && ch <= 0x2C /* , */ ||\n      ch === 0x2E /* . */ ||\n      ch >= 0x3A /* : */ && ch <= 0x40 /* @ */ ||\n      ch === 0x5E /* ^ */ ||\n      ch === 0x60 /* ` */ ||\n      ch === 0x7E /* ~ */\n    )\n  }\n\n  // https://tc39.es/ecma262/#prod-ClassSetSyntaxCharacter\n  function isClassSetSyntaxCharacter(ch) {\n    return (\n      ch === 0x28 /* ( */ ||\n      ch === 0x29 /* ) */ ||\n      ch === 0x2D /* - */ ||\n      ch === 0x2F /* / */ ||\n      ch >= 0x5B /* [ */ && ch <= 0x5D /* ] */ ||\n      ch >= 0x7B /* { */ && ch <= 0x7D /* } */\n    )\n  }\n\n  // https://tc39.es/ecma262/#prod-ClassSetReservedPunctuator\n  pp$1.regexp_eatClassSetReservedPunctuator = function(state) {\n    var ch = state.current();\n    if (isClassSetReservedPunctuator(ch)) {\n      state.lastIntValue = ch;\n      state.advance();\n      return true\n    }\n    return false\n  };\n\n  // https://tc39.es/ecma262/#prod-ClassSetReservedPunctuator\n  function isClassSetReservedPunctuator(ch) {\n    return (\n      ch === 0x21 /* ! */ ||\n      ch === 0x23 /* # */ ||\n      ch === 0x25 /* % */ ||\n      ch === 0x26 /* & */ ||\n      ch === 0x2C /* , */ ||\n      ch === 0x2D /* - */ ||\n      ch >= 0x3A /* : */ && ch <= 0x3E /* > */ ||\n      ch === 0x40 /* @ */ ||\n      ch === 0x60 /* ` */ ||\n      ch === 0x7E /* ~ */\n    )\n  }\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-ClassControlLetter\n  pp$1.regexp_eatClassControlLetter = function(state) {\n    var ch = state.current();\n    if (isDecimalDigit(ch) || ch === 0x5F /* _ */) {\n      state.lastIntValue = ch % 0x20;\n      state.advance();\n      return true\n    }\n    return false\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-HexEscapeSequence\n  pp$1.regexp_eatHexEscapeSequence = function(state) {\n    var start = state.pos;\n    if (state.eat(0x78 /* x */)) {\n      if (this.regexp_eatFixedHexDigits(state, 2)) {\n        return true\n      }\n      if (state.switchU) {\n        state.raise(\"Invalid escape\");\n      }\n      state.pos = start;\n    }\n    return false\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-DecimalDigits\n  pp$1.regexp_eatDecimalDigits = function(state) {\n    var start = state.pos;\n    var ch = 0;\n    state.lastIntValue = 0;\n    while (isDecimalDigit(ch = state.current())) {\n      state.lastIntValue = 10 * state.lastIntValue + (ch - 0x30 /* 0 */);\n      state.advance();\n    }\n    return state.pos !== start\n  };\n  function isDecimalDigit(ch) {\n    return ch >= 0x30 /* 0 */ && ch <= 0x39 /* 9 */\n  }\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-HexDigits\n  pp$1.regexp_eatHexDigits = function(state) {\n    var start = state.pos;\n    var ch = 0;\n    state.lastIntValue = 0;\n    while (isHexDigit(ch = state.current())) {\n      state.lastIntValue = 16 * state.lastIntValue + hexToInt(ch);\n      state.advance();\n    }\n    return state.pos !== start\n  };\n  function isHexDigit(ch) {\n    return (\n      (ch >= 0x30 /* 0 */ && ch <= 0x39 /* 9 */) ||\n      (ch >= 0x41 /* A */ && ch <= 0x46 /* F */) ||\n      (ch >= 0x61 /* a */ && ch <= 0x66 /* f */)\n    )\n  }\n  function hexToInt(ch) {\n    if (ch >= 0x41 /* A */ && ch <= 0x46 /* F */) {\n      return 10 + (ch - 0x41 /* A */)\n    }\n    if (ch >= 0x61 /* a */ && ch <= 0x66 /* f */) {\n      return 10 + (ch - 0x61 /* a */)\n    }\n    return ch - 0x30 /* 0 */\n  }\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-LegacyOctalEscapeSequence\n  // Allows only 0-377(octal) i.e. 0-255(decimal).\n  pp$1.regexp_eatLegacyOctalEscapeSequence = function(state) {\n    if (this.regexp_eatOctalDigit(state)) {\n      var n1 = state.lastIntValue;\n      if (this.regexp_eatOctalDigit(state)) {\n        var n2 = state.lastIntValue;\n        if (n1 <= 3 && this.regexp_eatOctalDigit(state)) {\n          state.lastIntValue = n1 * 64 + n2 * 8 + state.lastIntValue;\n        } else {\n          state.lastIntValue = n1 * 8 + n2;\n        }\n      } else {\n        state.lastIntValue = n1;\n      }\n      return true\n    }\n    return false\n  };\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-OctalDigit\n  pp$1.regexp_eatOctalDigit = function(state) {\n    var ch = state.current();\n    if (isOctalDigit(ch)) {\n      state.lastIntValue = ch - 0x30; /* 0 */\n      state.advance();\n      return true\n    }\n    state.lastIntValue = 0;\n    return false\n  };\n  function isOctalDigit(ch) {\n    return ch >= 0x30 /* 0 */ && ch <= 0x37 /* 7 */\n  }\n\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-Hex4Digits\n  // https://www.ecma-international.org/ecma-262/8.0/#prod-HexDigit\n  // And HexDigit HexDigit in https://www.ecma-international.org/ecma-262/8.0/#prod-HexEscapeSequence\n  pp$1.regexp_eatFixedHexDigits = function(state, length) {\n    var start = state.pos;\n    state.lastIntValue = 0;\n    for (var i = 0; i < length; ++i) {\n      var ch = state.current();\n      if (!isHexDigit(ch)) {\n        state.pos = start;\n        return false\n      }\n      state.lastIntValue = 16 * state.lastIntValue + hexToInt(ch);\n      state.advance();\n    }\n    return true\n  };\n\n  // Object type used to represent tokens. Note that normally, tokens\n  // simply exist as properties on the parser object. This is only\n  // used for the onToken callback and the external tokenizer.\n\n  var Token = function Token(p) {\n    this.type = p.type;\n    this.value = p.value;\n    this.start = p.start;\n    this.end = p.end;\n    if (p.options.locations)\n      { this.loc = new SourceLocation(p, p.startLoc, p.endLoc); }\n    if (p.options.ranges)\n      { this.range = [p.start, p.end]; }\n  };\n\n  // ## Tokenizer\n\n  var pp = Parser.prototype;\n\n  // Move to the next token\n\n  pp.next = function(ignoreEscapeSequenceInKeyword) {\n    if (!ignoreEscapeSequenceInKeyword && this.type.keyword && this.containsEsc)\n      { this.raiseRecoverable(this.start, \"Escape sequence in keyword \" + this.type.keyword); }\n    if (this.options.onToken)\n      { this.options.onToken(new Token(this)); }\n\n    this.lastTokEnd = this.end;\n    this.lastTokStart = this.start;\n    this.lastTokEndLoc = this.endLoc;\n    this.lastTokStartLoc = this.startLoc;\n    this.nextToken();\n  };\n\n  pp.getToken = function() {\n    this.next();\n    return new Token(this)\n  };\n\n  // If we're in an ES6 environment, make parsers iterable\n  if (typeof Symbol !== \"undefined\")\n    { pp[Symbol.iterator] = function() {\n      var this$1$1 = this;\n\n      return {\n        next: function () {\n          var token = this$1$1.getToken();\n          return {\n            done: token.type === types$1.eof,\n            value: token\n          }\n        }\n      }\n    }; }\n\n  // Toggle strict mode. Re-reads the next number or string to please\n  // pedantic tests (`\"use strict\"; 010;` should fail).\n\n  // Read a single token, updating the parser object's token-related\n  // properties.\n\n  pp.nextToken = function() {\n    var curContext = this.curContext();\n    if (!curContext || !curContext.preserveSpace) { this.skipSpace(); }\n\n    this.start = this.pos;\n    if (this.options.locations) { this.startLoc = this.curPosition(); }\n    if (this.pos >= this.input.length) { return this.finishToken(types$1.eof) }\n\n    if (curContext.override) { return curContext.override(this) }\n    else { this.readToken(this.fullCharCodeAtPos()); }\n  };\n\n  pp.readToken = function(code) {\n    // Identifier or keyword. '\\uXXXX' sequences are allowed in\n    // identifiers, so '\\' also dispatches to that.\n    if (isIdentifierStart(code, this.options.ecmaVersion >= 6) || code === 92 /* '\\' */)\n      { return this.readWord() }\n\n    return this.getTokenFromCode(code)\n  };\n\n  pp.fullCharCodeAtPos = function() {\n    var code = this.input.charCodeAt(this.pos);\n    if (code <= 0xd7ff || code >= 0xdc00) { return code }\n    var next = this.input.charCodeAt(this.pos + 1);\n    return next <= 0xdbff || next >= 0xe000 ? code : (code << 10) + next - 0x35fdc00\n  };\n\n  pp.skipBlockComment = function() {\n    var startLoc = this.options.onComment && this.curPosition();\n    var start = this.pos, end = this.input.indexOf(\"*/\", this.pos += 2);\n    if (end === -1) { this.raise(this.pos - 2, \"Unterminated comment\"); }\n    this.pos = end + 2;\n    if (this.options.locations) {\n      for (var nextBreak = (void 0), pos = start; (nextBreak = nextLineBreak(this.input, pos, this.pos)) > -1;) {\n        ++this.curLine;\n        pos = this.lineStart = nextBreak;\n      }\n    }\n    if (this.options.onComment)\n      { this.options.onComment(true, this.input.slice(start + 2, end), start, this.pos,\n                             startLoc, this.curPosition()); }\n  };\n\n  pp.skipLineComment = function(startSkip) {\n    var start = this.pos;\n    var startLoc = this.options.onComment && this.curPosition();\n    var ch = this.input.charCodeAt(this.pos += startSkip);\n    while (this.pos < this.input.length && !isNewLine(ch)) {\n      ch = this.input.charCodeAt(++this.pos);\n    }\n    if (this.options.onComment)\n      { this.options.onComment(false, this.input.slice(start + startSkip, this.pos), start, this.pos,\n                             startLoc, this.curPosition()); }\n  };\n\n  // Called at the start of the parse and after every token. Skips\n  // whitespace and comments, and.\n\n  pp.skipSpace = function() {\n    loop: while (this.pos < this.input.length) {\n      var ch = this.input.charCodeAt(this.pos);\n      switch (ch) {\n      case 32: case 160: // ' '\n        ++this.pos;\n        break\n      case 13:\n        if (this.input.charCodeAt(this.pos + 1) === 10) {\n          ++this.pos;\n        }\n      case 10: case 8232: case 8233:\n        ++this.pos;\n        if (this.options.locations) {\n          ++this.curLine;\n          this.lineStart = this.pos;\n        }\n        break\n      case 47: // '/'\n        switch (this.input.charCodeAt(this.pos + 1)) {\n        case 42: // '*'\n          this.skipBlockComment();\n          break\n        case 47:\n          this.skipLineComment(2);\n          break\n        default:\n          break loop\n        }\n        break\n      default:\n        if (ch > 8 && ch < 14 || ch >= 5760 && nonASCIIwhitespace.test(String.fromCharCode(ch))) {\n          ++this.pos;\n        } else {\n          break loop\n        }\n      }\n    }\n  };\n\n  // Called at the end of every token. Sets `end`, `val`, and\n  // maintains `context` and `exprAllowed`, and skips the space after\n  // the token, so that the next one's `start` will point at the\n  // right position.\n\n  pp.finishToken = function(type, val) {\n    this.end = this.pos;\n    if (this.options.locations) { this.endLoc = this.curPosition(); }\n    var prevType = this.type;\n    this.type = type;\n    this.value = val;\n\n    this.updateContext(prevType);\n  };\n\n  // ### Token reading\n\n  // This is the function that is called to fetch the next token. It\n  // is somewhat obscure, because it works in character codes rather\n  // than characters, and because operator parsing has been inlined\n  // into it.\n  //\n  // All in the name of speed.\n  //\n  pp.readToken_dot = function() {\n    var next = this.input.charCodeAt(this.pos + 1);\n    if (next >= 48 && next <= 57) { return this.readNumber(true) }\n    var next2 = this.input.charCodeAt(this.pos + 2);\n    if (this.options.ecmaVersion >= 6 && next === 46 && next2 === 46) { // 46 = dot '.'\n      this.pos += 3;\n      return this.finishToken(types$1.ellipsis)\n    } else {\n      ++this.pos;\n      return this.finishToken(types$1.dot)\n    }\n  };\n\n  pp.readToken_slash = function() { // '/'\n    var next = this.input.charCodeAt(this.pos + 1);\n    if (this.exprAllowed) { ++this.pos; return this.readRegexp() }\n    if (next === 61) { return this.finishOp(types$1.assign, 2) }\n    return this.finishOp(types$1.slash, 1)\n  };\n\n  pp.readToken_mult_modulo_exp = function(code) { // '%*'\n    var next = this.input.charCodeAt(this.pos + 1);\n    var size = 1;\n    var tokentype = code === 42 ? types$1.star : types$1.modulo;\n\n    // exponentiation operator ** and **=\n    if (this.options.ecmaVersion >= 7 && code === 42 && next === 42) {\n      ++size;\n      tokentype = types$1.starstar;\n      next = this.input.charCodeAt(this.pos + 2);\n    }\n\n    if (next === 61) { return this.finishOp(types$1.assign, size + 1) }\n    return this.finishOp(tokentype, size)\n  };\n\n  pp.readToken_pipe_amp = function(code) { // '|&'\n    var next = this.input.charCodeAt(this.pos + 1);\n    if (next === code) {\n      if (this.options.ecmaVersion >= 12) {\n        var next2 = this.input.charCodeAt(this.pos + 2);\n        if (next2 === 61) { return this.finishOp(types$1.assign, 3) }\n      }\n      return this.finishOp(code === 124 ? types$1.logicalOR : types$1.logicalAND, 2)\n    }\n    if (next === 61) { return this.finishOp(types$1.assign, 2) }\n    return this.finishOp(code === 124 ? types$1.bitwiseOR : types$1.bitwiseAND, 1)\n  };\n\n  pp.readToken_caret = function() { // '^'\n    var next = this.input.charCodeAt(this.pos + 1);\n    if (next === 61) { return this.finishOp(types$1.assign, 2) }\n    return this.finishOp(types$1.bitwiseXOR, 1)\n  };\n\n  pp.readToken_plus_min = function(code) { // '+-'\n    var next = this.input.charCodeAt(this.pos + 1);\n    if (next === code) {\n      if (next === 45 && !this.inModule && this.input.charCodeAt(this.pos + 2) === 62 &&\n          (this.lastTokEnd === 0 || lineBreak.test(this.input.slice(this.lastTokEnd, this.pos)))) {\n        // A `-->` line comment\n        this.skipLineComment(3);\n        this.skipSpace();\n        return this.nextToken()\n      }\n      return this.finishOp(types$1.incDec, 2)\n    }\n    if (next === 61) { return this.finishOp(types$1.assign, 2) }\n    return this.finishOp(types$1.plusMin, 1)\n  };\n\n  pp.readToken_lt_gt = function(code) { // '<>'\n    var next = this.input.charCodeAt(this.pos + 1);\n    var size = 1;\n    if (next === code) {\n      size = code === 62 && this.input.charCodeAt(this.pos + 2) === 62 ? 3 : 2;\n      if (this.input.charCodeAt(this.pos + size) === 61) { return this.finishOp(types$1.assign, size + 1) }\n      return this.finishOp(types$1.bitShift, size)\n    }\n    if (next === 33 && code === 60 && !this.inModule && this.input.charCodeAt(this.pos + 2) === 45 &&\n        this.input.charCodeAt(this.pos + 3) === 45) {\n      // `<!--`, an XML-style comment that should be interpreted as a line comment\n      this.skipLineComment(4);\n      this.skipSpace();\n      return this.nextToken()\n    }\n    if (next === 61) { size = 2; }\n    return this.finishOp(types$1.relational, size)\n  };\n\n  pp.readToken_eq_excl = function(code) { // '=!'\n    var next = this.input.charCodeAt(this.pos + 1);\n    if (next === 61) { return this.finishOp(types$1.equality, this.input.charCodeAt(this.pos + 2) === 61 ? 3 : 2) }\n    if (code === 61 && next === 62 && this.options.ecmaVersion >= 6) { // '=>'\n      this.pos += 2;\n      return this.finishToken(types$1.arrow)\n    }\n    return this.finishOp(code === 61 ? types$1.eq : types$1.prefix, 1)\n  };\n\n  pp.readToken_question = function() { // '?'\n    var ecmaVersion = this.options.ecmaVersion;\n    if (ecmaVersion >= 11) {\n      var next = this.input.charCodeAt(this.pos + 1);\n      if (next === 46) {\n        var next2 = this.input.charCodeAt(this.pos + 2);\n        if (next2 < 48 || next2 > 57) { return this.finishOp(types$1.questionDot, 2) }\n      }\n      if (next === 63) {\n        if (ecmaVersion >= 12) {\n          var next2$1 = this.input.charCodeAt(this.pos + 2);\n          if (next2$1 === 61) { return this.finishOp(types$1.assign, 3) }\n        }\n        return this.finishOp(types$1.coalesce, 2)\n      }\n    }\n    return this.finishOp(types$1.question, 1)\n  };\n\n  pp.readToken_numberSign = function() { // '#'\n    var ecmaVersion = this.options.ecmaVersion;\n    var code = 35; // '#'\n    if (ecmaVersion >= 13) {\n      ++this.pos;\n      code = this.fullCharCodeAtPos();\n      if (isIdentifierStart(code, true) || code === 92 /* '\\' */) {\n        return this.finishToken(types$1.privateId, this.readWord1())\n      }\n    }\n\n    this.raise(this.pos, \"Unexpected character '\" + codePointToString(code) + \"'\");\n  };\n\n  pp.getTokenFromCode = function(code) {\n    switch (code) {\n    // The interpretation of a dot depends on whether it is followed\n    // by a digit or another two dots.\n    case 46: // '.'\n      return this.readToken_dot()\n\n    // Punctuation tokens.\n    case 40: ++this.pos; return this.finishToken(types$1.parenL)\n    case 41: ++this.pos; return this.finishToken(types$1.parenR)\n    case 59: ++this.pos; return this.finishToken(types$1.semi)\n    case 44: ++this.pos; return this.finishToken(types$1.comma)\n    case 91: ++this.pos; return this.finishToken(types$1.bracketL)\n    case 93: ++this.pos; return this.finishToken(types$1.bracketR)\n    case 123: ++this.pos; return this.finishToken(types$1.braceL)\n    case 125: ++this.pos; return this.finishToken(types$1.braceR)\n    case 58: ++this.pos; return this.finishToken(types$1.colon)\n\n    case 96: // '`'\n      if (this.options.ecmaVersion < 6) { break }\n      ++this.pos;\n      return this.finishToken(types$1.backQuote)\n\n    case 48: // '0'\n      var next = this.input.charCodeAt(this.pos + 1);\n      if (next === 120 || next === 88) { return this.readRadixNumber(16) } // '0x', '0X' - hex number\n      if (this.options.ecmaVersion >= 6) {\n        if (next === 111 || next === 79) { return this.readRadixNumber(8) } // '0o', '0O' - octal number\n        if (next === 98 || next === 66) { return this.readRadixNumber(2) } // '0b', '0B' - binary number\n      }\n\n    // Anything else beginning with a digit is an integer, octal\n    // number, or float.\n    case 49: case 50: case 51: case 52: case 53: case 54: case 55: case 56: case 57: // 1-9\n      return this.readNumber(false)\n\n    // Quotes produce strings.\n    case 34: case 39: // '\"', \"'\"\n      return this.readString(code)\n\n    // Operators are parsed inline in tiny state machines. '=' (61) is\n    // often referred to. `finishOp` simply skips the amount of\n    // characters it is given as second argument, and returns a token\n    // of the type given by its first argument.\n    case 47: // '/'\n      return this.readToken_slash()\n\n    case 37: case 42: // '%*'\n      return this.readToken_mult_modulo_exp(code)\n\n    case 124: case 38: // '|&'\n      return this.readToken_pipe_amp(code)\n\n    case 94: // '^'\n      return this.readToken_caret()\n\n    case 43: case 45: // '+-'\n      return this.readToken_plus_min(code)\n\n    case 60: case 62: // '<>'\n      return this.readToken_lt_gt(code)\n\n    case 61: case 33: // '=!'\n      return this.readToken_eq_excl(code)\n\n    case 63: // '?'\n      return this.readToken_question()\n\n    case 126: // '~'\n      return this.finishOp(types$1.prefix, 1)\n\n    case 35: // '#'\n      return this.readToken_numberSign()\n    }\n\n    this.raise(this.pos, \"Unexpected character '\" + codePointToString(code) + \"'\");\n  };\n\n  pp.finishOp = function(type, size) {\n    var str = this.input.slice(this.pos, this.pos + size);\n    this.pos += size;\n    return this.finishToken(type, str)\n  };\n\n  pp.readRegexp = function() {\n    var escaped, inClass, start = this.pos;\n    for (;;) {\n      if (this.pos >= this.input.length) { this.raise(start, \"Unterminated regular expression\"); }\n      var ch = this.input.charAt(this.pos);\n      if (lineBreak.test(ch)) { this.raise(start, \"Unterminated regular expression\"); }\n      if (!escaped) {\n        if (ch === \"[\") { inClass = true; }\n        else if (ch === \"]\" && inClass) { inClass = false; }\n        else if (ch === \"/\" && !inClass) { break }\n        escaped = ch === \"\\\\\";\n      } else { escaped = false; }\n      ++this.pos;\n    }\n    var pattern = this.input.slice(start, this.pos);\n    ++this.pos;\n    var flagsStart = this.pos;\n    var flags = this.readWord1();\n    if (this.containsEsc) { this.unexpected(flagsStart); }\n\n    // Validate pattern\n    var state = this.regexpState || (this.regexpState = new RegExpValidationState(this));\n    state.reset(start, pattern, flags);\n    this.validateRegExpFlags(state);\n    this.validateRegExpPattern(state);\n\n    // Create Literal#value property value.\n    var value = null;\n    try {\n      value = new RegExp(pattern, flags);\n    } catch (e) {\n      // ESTree requires null if it failed to instantiate RegExp object.\n      // https://github.com/estree/estree/blob/a27003adf4fd7bfad44de9cef372a2eacd527b1c/es5.md#regexpliteral\n    }\n\n    return this.finishToken(types$1.regexp, {pattern: pattern, flags: flags, value: value})\n  };\n\n  // Read an integer in the given radix. Return null if zero digits\n  // were read, the integer value otherwise. When `len` is given, this\n  // will return `null` unless the integer has exactly `len` digits.\n\n  pp.readInt = function(radix, len, maybeLegacyOctalNumericLiteral) {\n    // `len` is used for character escape sequences. In that case, disallow separators.\n    var allowSeparators = this.options.ecmaVersion >= 12 && len === undefined;\n\n    // `maybeLegacyOctalNumericLiteral` is true if it doesn't have prefix (0x,0o,0b)\n    // and isn't fraction part nor exponent part. In that case, if the first digit\n    // is zero then disallow separators.\n    var isLegacyOctalNumericLiteral = maybeLegacyOctalNumericLiteral && this.input.charCodeAt(this.pos) === 48;\n\n    var start = this.pos, total = 0, lastCode = 0;\n    for (var i = 0, e = len == null ? Infinity : len; i < e; ++i, ++this.pos) {\n      var code = this.input.charCodeAt(this.pos), val = (void 0);\n\n      if (allowSeparators && code === 95) {\n        if (isLegacyOctalNumericLiteral) { this.raiseRecoverable(this.pos, \"Numeric separator is not allowed in legacy octal numeric literals\"); }\n        if (lastCode === 95) { this.raiseRecoverable(this.pos, \"Numeric separator must be exactly one underscore\"); }\n        if (i === 0) { this.raiseRecoverable(this.pos, \"Numeric separator is not allowed at the first of digits\"); }\n        lastCode = code;\n        continue\n      }\n\n      if (code >= 97) { val = code - 97 + 10; } // a\n      else if (code >= 65) { val = code - 65 + 10; } // A\n      else if (code >= 48 && code <= 57) { val = code - 48; } // 0-9\n      else { val = Infinity; }\n      if (val >= radix) { break }\n      lastCode = code;\n      total = total * radix + val;\n    }\n\n    if (allowSeparators && lastCode === 95) { this.raiseRecoverable(this.pos - 1, \"Numeric separator is not allowed at the last of digits\"); }\n    if (this.pos === start || len != null && this.pos - start !== len) { return null }\n\n    return total\n  };\n\n  function stringToNumber(str, isLegacyOctalNumericLiteral) {\n    if (isLegacyOctalNumericLiteral) {\n      return parseInt(str, 8)\n    }\n\n    // `parseFloat(value)` stops parsing at the first numeric separator then returns a wrong value.\n    return parseFloat(str.replace(/_/g, \"\"))\n  }\n\n  function stringToBigInt(str) {\n    if (typeof BigInt !== \"function\") {\n      return null\n    }\n\n    // `BigInt(value)` throws syntax error if the string contains numeric separators.\n    return BigInt(str.replace(/_/g, \"\"))\n  }\n\n  pp.readRadixNumber = function(radix) {\n    var start = this.pos;\n    this.pos += 2; // 0x\n    var val = this.readInt(radix);\n    if (val == null) { this.raise(this.start + 2, \"Expected number in radix \" + radix); }\n    if (this.options.ecmaVersion >= 11 && this.input.charCodeAt(this.pos) === 110) {\n      val = stringToBigInt(this.input.slice(start, this.pos));\n      ++this.pos;\n    } else if (isIdentifierStart(this.fullCharCodeAtPos())) { this.raise(this.pos, \"Identifier directly after number\"); }\n    return this.finishToken(types$1.num, val)\n  };\n\n  // Read an integer, octal integer, or floating-point number.\n\n  pp.readNumber = function(startsWithDot) {\n    var start = this.pos;\n    if (!startsWithDot && this.readInt(10, undefined, true) === null) { this.raise(start, \"Invalid number\"); }\n    var octal = this.pos - start >= 2 && this.input.charCodeAt(start) === 48;\n    if (octal && this.strict) { this.raise(start, \"Invalid number\"); }\n    var next = this.input.charCodeAt(this.pos);\n    if (!octal && !startsWithDot && this.options.ecmaVersion >= 11 && next === 110) {\n      var val$1 = stringToBigInt(this.input.slice(start, this.pos));\n      ++this.pos;\n      if (isIdentifierStart(this.fullCharCodeAtPos())) { this.raise(this.pos, \"Identifier directly after number\"); }\n      return this.finishToken(types$1.num, val$1)\n    }\n    if (octal && /[89]/.test(this.input.slice(start, this.pos))) { octal = false; }\n    if (next === 46 && !octal) { // '.'\n      ++this.pos;\n      this.readInt(10);\n      next = this.input.charCodeAt(this.pos);\n    }\n    if ((next === 69 || next === 101) && !octal) { // 'eE'\n      next = this.input.charCodeAt(++this.pos);\n      if (next === 43 || next === 45) { ++this.pos; } // '+-'\n      if (this.readInt(10) === null) { this.raise(start, \"Invalid number\"); }\n    }\n    if (isIdentifierStart(this.fullCharCodeAtPos())) { this.raise(this.pos, \"Identifier directly after number\"); }\n\n    var val = stringToNumber(this.input.slice(start, this.pos), octal);\n    return this.finishToken(types$1.num, val)\n  };\n\n  // Read a string value, interpreting backslash-escapes.\n\n  pp.readCodePoint = function() {\n    var ch = this.input.charCodeAt(this.pos), code;\n\n    if (ch === 123) { // '{'\n      if (this.options.ecmaVersion < 6) { this.unexpected(); }\n      var codePos = ++this.pos;\n      code = this.readHexChar(this.input.indexOf(\"}\", this.pos) - this.pos);\n      ++this.pos;\n      if (code > 0x10FFFF) { this.invalidStringToken(codePos, \"Code point out of bounds\"); }\n    } else {\n      code = this.readHexChar(4);\n    }\n    return code\n  };\n\n  pp.readString = function(quote) {\n    var out = \"\", chunkStart = ++this.pos;\n    for (;;) {\n      if (this.pos >= this.input.length) { this.raise(this.start, \"Unterminated string constant\"); }\n      var ch = this.input.charCodeAt(this.pos);\n      if (ch === quote) { break }\n      if (ch === 92) { // '\\'\n        out += this.input.slice(chunkStart, this.pos);\n        out += this.readEscapedChar(false);\n        chunkStart = this.pos;\n      } else if (ch === 0x2028 || ch === 0x2029) {\n        if (this.options.ecmaVersion < 10) { this.raise(this.start, \"Unterminated string constant\"); }\n        ++this.pos;\n        if (this.options.locations) {\n          this.curLine++;\n          this.lineStart = this.pos;\n        }\n      } else {\n        if (isNewLine(ch)) { this.raise(this.start, \"Unterminated string constant\"); }\n        ++this.pos;\n      }\n    }\n    out += this.input.slice(chunkStart, this.pos++);\n    return this.finishToken(types$1.string, out)\n  };\n\n  // Reads template string tokens.\n\n  var INVALID_TEMPLATE_ESCAPE_ERROR = {};\n\n  pp.tryReadTemplateToken = function() {\n    this.inTemplateElement = true;\n    try {\n      this.readTmplToken();\n    } catch (err) {\n      if (err === INVALID_TEMPLATE_ESCAPE_ERROR) {\n        this.readInvalidTemplateToken();\n      } else {\n        throw err\n      }\n    }\n\n    this.inTemplateElement = false;\n  };\n\n  pp.invalidStringToken = function(position, message) {\n    if (this.inTemplateElement && this.options.ecmaVersion >= 9) {\n      throw INVALID_TEMPLATE_ESCAPE_ERROR\n    } else {\n      this.raise(position, message);\n    }\n  };\n\n  pp.readTmplToken = function() {\n    var out = \"\", chunkStart = this.pos;\n    for (;;) {\n      if (this.pos >= this.input.length) { this.raise(this.start, \"Unterminated template\"); }\n      var ch = this.input.charCodeAt(this.pos);\n      if (ch === 96 || ch === 36 && this.input.charCodeAt(this.pos + 1) === 123) { // '`', '${'\n        if (this.pos === this.start && (this.type === types$1.template || this.type === types$1.invalidTemplate)) {\n          if (ch === 36) {\n            this.pos += 2;\n            return this.finishToken(types$1.dollarBraceL)\n          } else {\n            ++this.pos;\n            return this.finishToken(types$1.backQuote)\n          }\n        }\n        out += this.input.slice(chunkStart, this.pos);\n        return this.finishToken(types$1.template, out)\n      }\n      if (ch === 92) { // '\\'\n        out += this.input.slice(chunkStart, this.pos);\n        out += this.readEscapedChar(true);\n        chunkStart = this.pos;\n      } else if (isNewLine(ch)) {\n        out += this.input.slice(chunkStart, this.pos);\n        ++this.pos;\n        switch (ch) {\n        case 13:\n          if (this.input.charCodeAt(this.pos) === 10) { ++this.pos; }\n        case 10:\n          out += \"\\n\";\n          break\n        default:\n          out += String.fromCharCode(ch);\n          break\n        }\n        if (this.options.locations) {\n          ++this.curLine;\n          this.lineStart = this.pos;\n        }\n        chunkStart = this.pos;\n      } else {\n        ++this.pos;\n      }\n    }\n  };\n\n  // Reads a template token to search for the end, without validating any escape sequences\n  pp.readInvalidTemplateToken = function() {\n    for (; this.pos < this.input.length; this.pos++) {\n      switch (this.input[this.pos]) {\n      case \"\\\\\":\n        ++this.pos;\n        break\n\n      case \"$\":\n        if (this.input[this.pos + 1] !== \"{\") { break }\n        // fall through\n      case \"`\":\n        return this.finishToken(types$1.invalidTemplate, this.input.slice(this.start, this.pos))\n\n      case \"\\r\":\n        if (this.input[this.pos + 1] === \"\\n\") { ++this.pos; }\n        // fall through\n      case \"\\n\": case \"\\u2028\": case \"\\u2029\":\n        ++this.curLine;\n        this.lineStart = this.pos + 1;\n        break\n      }\n    }\n    this.raise(this.start, \"Unterminated template\");\n  };\n\n  // Used to read escaped characters\n\n  pp.readEscapedChar = function(inTemplate) {\n    var ch = this.input.charCodeAt(++this.pos);\n    ++this.pos;\n    switch (ch) {\n    case 110: return \"\\n\" // 'n' -> '\\n'\n    case 114: return \"\\r\" // 'r' -> '\\r'\n    case 120: return String.fromCharCode(this.readHexChar(2)) // 'x'\n    case 117: return codePointToString(this.readCodePoint()) // 'u'\n    case 116: return \"\\t\" // 't' -> '\\t'\n    case 98: return \"\\b\" // 'b' -> '\\b'\n    case 118: return \"\\u000b\" // 'v' -> '\\u000b'\n    case 102: return \"\\f\" // 'f' -> '\\f'\n    case 13: if (this.input.charCodeAt(this.pos) === 10) { ++this.pos; } // '\\r\\n'\n    case 10: // ' \\n'\n      if (this.options.locations) { this.lineStart = this.pos; ++this.curLine; }\n      return \"\"\n    case 56:\n    case 57:\n      if (this.strict) {\n        this.invalidStringToken(\n          this.pos - 1,\n          \"Invalid escape sequence\"\n        );\n      }\n      if (inTemplate) {\n        var codePos = this.pos - 1;\n\n        this.invalidStringToken(\n          codePos,\n          \"Invalid escape sequence in template string\"\n        );\n      }\n    default:\n      if (ch >= 48 && ch <= 55) {\n        var octalStr = this.input.substr(this.pos - 1, 3).match(/^[0-7]+/)[0];\n        var octal = parseInt(octalStr, 8);\n        if (octal > 255) {\n          octalStr = octalStr.slice(0, -1);\n          octal = parseInt(octalStr, 8);\n        }\n        this.pos += octalStr.length - 1;\n        ch = this.input.charCodeAt(this.pos);\n        if ((octalStr !== \"0\" || ch === 56 || ch === 57) && (this.strict || inTemplate)) {\n          this.invalidStringToken(\n            this.pos - 1 - octalStr.length,\n            inTemplate\n              ? \"Octal literal in template string\"\n              : \"Octal literal in strict mode\"\n          );\n        }\n        return String.fromCharCode(octal)\n      }\n      if (isNewLine(ch)) {\n        // Unicode new line characters after \\ get removed from output in both\n        // template literals and strings\n        if (this.options.locations) { this.lineStart = this.pos; ++this.curLine; }\n        return \"\"\n      }\n      return String.fromCharCode(ch)\n    }\n  };\n\n  // Used to read character escape sequences ('\\x', '\\u', '\\U').\n\n  pp.readHexChar = function(len) {\n    var codePos = this.pos;\n    var n = this.readInt(16, len);\n    if (n === null) { this.invalidStringToken(codePos, \"Bad character escape sequence\"); }\n    return n\n  };\n\n  // Read an identifier, and return it as a string. Sets `this.containsEsc`\n  // to whether the word contained a '\\u' escape.\n  //\n  // Incrementally adds only escaped chars, adding other chunks as-is\n  // as a micro-optimization.\n\n  pp.readWord1 = function() {\n    this.containsEsc = false;\n    var word = \"\", first = true, chunkStart = this.pos;\n    var astral = this.options.ecmaVersion >= 6;\n    while (this.pos < this.input.length) {\n      var ch = this.fullCharCodeAtPos();\n      if (isIdentifierChar(ch, astral)) {\n        this.pos += ch <= 0xffff ? 1 : 2;\n      } else if (ch === 92) { // \"\\\"\n        this.containsEsc = true;\n        word += this.input.slice(chunkStart, this.pos);\n        var escStart = this.pos;\n        if (this.input.charCodeAt(++this.pos) !== 117) // \"u\"\n          { this.invalidStringToken(this.pos, \"Expecting Unicode escape sequence \\\\uXXXX\"); }\n        ++this.pos;\n        var esc = this.readCodePoint();\n        if (!(first ? isIdentifierStart : isIdentifierChar)(esc, astral))\n          { this.invalidStringToken(escStart, \"Invalid Unicode escape\"); }\n        word += codePointToString(esc);\n        chunkStart = this.pos;\n      } else {\n        break\n      }\n      first = false;\n    }\n    return word + this.input.slice(chunkStart, this.pos)\n  };\n\n  // Read an identifier or keyword token. Will check for reserved\n  // words when necessary.\n\n  pp.readWord = function() {\n    var word = this.readWord1();\n    var type = types$1.name;\n    if (this.keywords.test(word)) {\n      type = keywords[word];\n    }\n    return this.finishToken(type, word)\n  };\n\n  // Acorn is a tiny, fast JavaScript parser written in JavaScript.\n  //\n  // Acorn was written by Marijn Haverbeke, Ingvar Stepanyan, and\n  // various contributors and released under an MIT license.\n  //\n  // Git repositories for Acorn are available at\n  //\n  //     http://marijnhaverbeke.nl/git/acorn\n  //     https://github.com/acornjs/acorn.git\n  //\n  // Please use the [github bug tracker][ghbt] to report issues.\n  //\n  // [ghbt]: https://github.com/acornjs/acorn/issues\n\n\n  var version = \"8.15.0\";\n\n  Parser.acorn = {\n    Parser: Parser,\n    version: version,\n    defaultOptions: defaultOptions,\n    Position: Position,\n    SourceLocation: SourceLocation,\n    getLineInfo: getLineInfo,\n    Node: Node,\n    TokenType: TokenType,\n    tokTypes: types$1,\n    keywordTypes: keywords,\n    TokContext: TokContext,\n    tokContexts: types,\n    isIdentifierChar: isIdentifierChar,\n    isIdentifierStart: isIdentifierStart,\n    Token: Token,\n    isNewLine: isNewLine,\n    lineBreak: lineBreak,\n    lineBreakG: lineBreakG,\n    nonASCIIwhitespace: nonASCIIwhitespace\n  };\n\n  // The main exported interface (under `self.acorn` when in the\n  // browser) is a `parse` function that takes a code string and returns\n  // an abstract syntax tree as specified by the [ESTree spec][estree].\n  //\n  // [estree]: https://github.com/estree/estree\n\n  function parse(input, options) {\n    return Parser.parse(input, options)\n  }\n\n  // This function tries to parse a single expression at a given\n  // offset in a string. Useful for parsing mixed-language formats\n  // that embed JavaScript expressions.\n\n  function parseExpressionAt(input, pos, options) {\n    return Parser.parseExpressionAt(input, pos, options)\n  }\n\n  // Acorn is organized as a tokenizer and a recursive-descent parser.\n  // The `tokenizer` export provides an interface to the tokenizer.\n\n  function tokenizer(input, options) {\n    return Parser.tokenizer(input, options)\n  }\n\n  exports.Node = Node;\n  exports.Parser = Parser;\n  exports.Position = Position;\n  exports.SourceLocation = SourceLocation;\n  exports.TokContext = TokContext;\n  exports.Token = Token;\n  exports.TokenType = TokenType;\n  exports.defaultOptions = defaultOptions;\n  exports.getLineInfo = getLineInfo;\n  exports.isIdentifierChar = isIdentifierChar;\n  exports.isIdentifierStart = isIdentifierStart;\n  exports.isNewLine = isNewLine;\n  exports.keywordTypes = keywords;\n  exports.lineBreak = lineBreak;\n  exports.lineBreakG = lineBreakG;\n  exports.nonASCIIwhitespace = nonASCIIwhitespace;\n  exports.parse = parse;\n  exports.parseExpressionAt = parseExpressionAt;\n  exports.tokContexts = types;\n  exports.tokTypes = types$1;\n  exports.tokenizer = tokenizer;\n  exports.version = version;\n\n}));\n", "'use strict';\n\nconst XHTMLEntities = require('./xhtml');\n\nconst hexNumber = /^[\\da-fA-F]+$/;\nconst decimalNumber = /^\\d+$/;\n\n// The map to `acorn-jsx` tokens from `acorn` namespace objects.\nconst acornJsxMap = new WeakMap();\n\n// Get the original tokens for the given `acorn` namespace object.\nfunction getJsxTokens(acorn) {\n  acorn = acorn.Parser.acorn || acorn;\n  let acornJsx = acornJsxMap.get(acorn);\n  if (!acornJsx) {\n    const tt = acorn.tokTypes;\n    const TokContext = acorn.TokContext;\n    const TokenType = acorn.TokenType;\n    const tc_oTag = new TokContext('<tag', false);\n    const tc_cTag = new TokContext('</tag', false);\n    const tc_expr = new TokContext('<tag>...</tag>', true, true);\n    const tokContexts = {\n      tc_oTag: tc_oTag,\n      tc_cTag: tc_cTag,\n      tc_expr: tc_expr\n    };\n    const tokTypes = {\n      jsxName: new TokenType('jsxName'),\n      jsxText: new TokenType('jsxText', {beforeExpr: true}),\n      jsxTagStart: new TokenType('jsxTagStart', {startsExpr: true}),\n      jsxTagEnd: new TokenType('jsxTagEnd')\n    };\n\n    tokTypes.jsxTagStart.updateContext = function() {\n      this.context.push(tc_expr); // treat as beginning of JSX expression\n      this.context.push(tc_oTag); // start opening tag context\n      this.exprAllowed = false;\n    };\n    tokTypes.jsxTagEnd.updateContext = function(prevType) {\n      let out = this.context.pop();\n      if (out === tc_oTag && prevType === tt.slash || out === tc_cTag) {\n        this.context.pop();\n        this.exprAllowed = this.curContext() === tc_expr;\n      } else {\n        this.exprAllowed = true;\n      }\n    };\n\n    acornJsx = { tokContexts: tokContexts, tokTypes: tokTypes };\n    acornJsxMap.set(acorn, acornJsx);\n  }\n\n  return acornJsx;\n}\n\n// Transforms JSX element name to string.\n\nfunction getQualifiedJSXName(object) {\n  if (!object)\n    return object;\n\n  if (object.type === 'JSXIdentifier')\n    return object.name;\n\n  if (object.type === 'JSXNamespacedName')\n    return object.namespace.name + ':' + object.name.name;\n\n  if (object.type === 'JSXMemberExpression')\n    return getQualifiedJSXName(object.object) + '.' +\n    getQualifiedJSXName(object.property);\n}\n\nmodule.exports = function(options) {\n  options = options || {};\n  return function(Parser) {\n    return plugin({\n      allowNamespaces: options.allowNamespaces !== false,\n      allowNamespacedObjects: !!options.allowNamespacedObjects\n    }, Parser);\n  };\n};\n\n// This is `tokTypes` of the peer dep.\n// This can be different instances from the actual `tokTypes` this plugin uses.\nObject.defineProperty(module.exports, \"tokTypes\", {\n  get: function get_tokTypes() {\n    return getJsxTokens(require(\"acorn\")).tokTypes;\n  },\n  configurable: true,\n  enumerable: true\n});\n\nfunction plugin(options, Parser) {\n  const acorn = Parser.acorn || require(\"acorn\");\n  const acornJsx = getJsxTokens(acorn);\n  const tt = acorn.tokTypes;\n  const tok = acornJsx.tokTypes;\n  const tokContexts = acorn.tokContexts;\n  const tc_oTag = acornJsx.tokContexts.tc_oTag;\n  const tc_cTag = acornJsx.tokContexts.tc_cTag;\n  const tc_expr = acornJsx.tokContexts.tc_expr;\n  const isNewLine = acorn.isNewLine;\n  const isIdentifierStart = acorn.isIdentifierStart;\n  const isIdentifierChar = acorn.isIdentifierChar;\n\n  return class extends Parser {\n    // Expose actual `tokTypes` and `tokContexts` to other plugins.\n    static get acornJsx() {\n      return acornJsx;\n    }\n\n    // Reads inline JSX contents token.\n    jsx_readToken() {\n      let out = '', chunkStart = this.pos;\n      for (;;) {\n        if (this.pos >= this.input.length)\n          this.raise(this.start, 'Unterminated JSX contents');\n        let ch = this.input.charCodeAt(this.pos);\n\n        switch (ch) {\n        case 60: // '<'\n        case 123: // '{'\n          if (this.pos === this.start) {\n            if (ch === 60 && this.exprAllowed) {\n              ++this.pos;\n              return this.finishToken(tok.jsxTagStart);\n            }\n            return this.getTokenFromCode(ch);\n          }\n          out += this.input.slice(chunkStart, this.pos);\n          return this.finishToken(tok.jsxText, out);\n\n        case 38: // '&'\n          out += this.input.slice(chunkStart, this.pos);\n          out += this.jsx_readEntity();\n          chunkStart = this.pos;\n          break;\n\n        case 62: // '>'\n        case 125: // '}'\n          this.raise(\n            this.pos,\n            \"Unexpected token `\" + this.input[this.pos] + \"`. Did you mean `\" +\n              (ch === 62 ? \"&gt;\" : \"&rbrace;\") + \"` or \" + \"`{\\\"\" + this.input[this.pos] + \"\\\"}\" + \"`?\"\n          );\n\n        default:\n          if (isNewLine(ch)) {\n            out += this.input.slice(chunkStart, this.pos);\n            out += this.jsx_readNewLine(true);\n            chunkStart = this.pos;\n          } else {\n            ++this.pos;\n          }\n        }\n      }\n    }\n\n    jsx_readNewLine(normalizeCRLF) {\n      let ch = this.input.charCodeAt(this.pos);\n      let out;\n      ++this.pos;\n      if (ch === 13 && this.input.charCodeAt(this.pos) === 10) {\n        ++this.pos;\n        out = normalizeCRLF ? '\\n' : '\\r\\n';\n      } else {\n        out = String.fromCharCode(ch);\n      }\n      if (this.options.locations) {\n        ++this.curLine;\n        this.lineStart = this.pos;\n      }\n\n      return out;\n    }\n\n    jsx_readString(quote) {\n      let out = '', chunkStart = ++this.pos;\n      for (;;) {\n        if (this.pos >= this.input.length)\n          this.raise(this.start, 'Unterminated string constant');\n        let ch = this.input.charCodeAt(this.pos);\n        if (ch === quote) break;\n        if (ch === 38) { // '&'\n          out += this.input.slice(chunkStart, this.pos);\n          out += this.jsx_readEntity();\n          chunkStart = this.pos;\n        } else if (isNewLine(ch)) {\n          out += this.input.slice(chunkStart, this.pos);\n          out += this.jsx_readNewLine(false);\n          chunkStart = this.pos;\n        } else {\n          ++this.pos;\n        }\n      }\n      out += this.input.slice(chunkStart, this.pos++);\n      return this.finishToken(tt.string, out);\n    }\n\n    jsx_readEntity() {\n      let str = '', count = 0, entity;\n      let ch = this.input[this.pos];\n      if (ch !== '&')\n        this.raise(this.pos, 'Entity must start with an ampersand');\n      let startPos = ++this.pos;\n      while (this.pos < this.input.length && count++ < 10) {\n        ch = this.input[this.pos++];\n        if (ch === ';') {\n          if (str[0] === '#') {\n            if (str[1] === 'x') {\n              str = str.substr(2);\n              if (hexNumber.test(str))\n                entity = String.fromCharCode(parseInt(str, 16));\n            } else {\n              str = str.substr(1);\n              if (decimalNumber.test(str))\n                entity = String.fromCharCode(parseInt(str, 10));\n            }\n          } else {\n            entity = XHTMLEntities[str];\n          }\n          break;\n        }\n        str += ch;\n      }\n      if (!entity) {\n        this.pos = startPos;\n        return '&';\n      }\n      return entity;\n    }\n\n    // Read a JSX identifier (valid tag or attribute name).\n    //\n    // Optimized version since JSX identifiers can't contain\n    // escape characters and so can be read as single slice.\n    // Also assumes that first character was already checked\n    // by isIdentifierStart in readToken.\n\n    jsx_readWord() {\n      let ch, start = this.pos;\n      do {\n        ch = this.input.charCodeAt(++this.pos);\n      } while (isIdentifierChar(ch) || ch === 45); // '-'\n      return this.finishToken(tok.jsxName, this.input.slice(start, this.pos));\n    }\n\n    // Parse next token as JSX identifier\n\n    jsx_parseIdentifier() {\n      let node = this.startNode();\n      if (this.type === tok.jsxName)\n        node.name = this.value;\n      else if (this.type.keyword)\n        node.name = this.type.keyword;\n      else\n        this.unexpected();\n      this.next();\n      return this.finishNode(node, 'JSXIdentifier');\n    }\n\n    // Parse namespaced identifier.\n\n    jsx_parseNamespacedName() {\n      let startPos = this.start, startLoc = this.startLoc;\n      let name = this.jsx_parseIdentifier();\n      if (!options.allowNamespaces || !this.eat(tt.colon)) return name;\n      var node = this.startNodeAt(startPos, startLoc);\n      node.namespace = name;\n      node.name = this.jsx_parseIdentifier();\n      return this.finishNode(node, 'JSXNamespacedName');\n    }\n\n    // Parses element name in any form - namespaced, member\n    // or single identifier.\n\n    jsx_parseElementName() {\n      if (this.type === tok.jsxTagEnd) return '';\n      let startPos = this.start, startLoc = this.startLoc;\n      let node = this.jsx_parseNamespacedName();\n      if (this.type === tt.dot && node.type === 'JSXNamespacedName' && !options.allowNamespacedObjects) {\n        this.unexpected();\n      }\n      while (this.eat(tt.dot)) {\n        let newNode = this.startNodeAt(startPos, startLoc);\n        newNode.object = node;\n        newNode.property = this.jsx_parseIdentifier();\n        node = this.finishNode(newNode, 'JSXMemberExpression');\n      }\n      return node;\n    }\n\n    // Parses any type of JSX attribute value.\n\n    jsx_parseAttributeValue() {\n      switch (this.type) {\n      case tt.braceL:\n        let node = this.jsx_parseExpressionContainer();\n        if (node.expression.type === 'JSXEmptyExpression')\n          this.raise(node.start, 'JSX attributes must only be assigned a non-empty expression');\n        return node;\n\n      case tok.jsxTagStart:\n      case tt.string:\n        return this.parseExprAtom();\n\n      default:\n        this.raise(this.start, 'JSX value should be either an expression or a quoted JSX text');\n      }\n    }\n\n    // JSXEmptyExpression is unique type since it doesn't actually parse anything,\n    // and so it should start at the end of last read token (left brace) and finish\n    // at the beginning of the next one (right brace).\n\n    jsx_parseEmptyExpression() {\n      let node = this.startNodeAt(this.lastTokEnd, this.lastTokEndLoc);\n      return this.finishNodeAt(node, 'JSXEmptyExpression', this.start, this.startLoc);\n    }\n\n    // Parses JSX expression enclosed into curly brackets.\n\n    jsx_parseExpressionContainer() {\n      let node = this.startNode();\n      this.next();\n      node.expression = this.type === tt.braceR\n        ? this.jsx_parseEmptyExpression()\n        : this.parseExpression();\n      this.expect(tt.braceR);\n      return this.finishNode(node, 'JSXExpressionContainer');\n    }\n\n    // Parses following JSX attribute name-value pair.\n\n    jsx_parseAttribute() {\n      let node = this.startNode();\n      if (this.eat(tt.braceL)) {\n        this.expect(tt.ellipsis);\n        node.argument = this.parseMaybeAssign();\n        this.expect(tt.braceR);\n        return this.finishNode(node, 'JSXSpreadAttribute');\n      }\n      node.name = this.jsx_parseNamespacedName();\n      node.value = this.eat(tt.eq) ? this.jsx_parseAttributeValue() : null;\n      return this.finishNode(node, 'JSXAttribute');\n    }\n\n    // Parses JSX opening tag starting after '<'.\n\n    jsx_parseOpeningElementAt(startPos, startLoc) {\n      let node = this.startNodeAt(startPos, startLoc);\n      node.attributes = [];\n      let nodeName = this.jsx_parseElementName();\n      if (nodeName) node.name = nodeName;\n      while (this.type !== tt.slash && this.type !== tok.jsxTagEnd)\n        node.attributes.push(this.jsx_parseAttribute());\n      node.selfClosing = this.eat(tt.slash);\n      this.expect(tok.jsxTagEnd);\n      return this.finishNode(node, nodeName ? 'JSXOpeningElement' : 'JSXOpeningFragment');\n    }\n\n    // Parses JSX closing tag starting after '</'.\n\n    jsx_parseClosingElementAt(startPos, startLoc) {\n      let node = this.startNodeAt(startPos, startLoc);\n      let nodeName = this.jsx_parseElementName();\n      if (nodeName) node.name = nodeName;\n      this.expect(tok.jsxTagEnd);\n      return this.finishNode(node, nodeName ? 'JSXClosingElement' : 'JSXClosingFragment');\n    }\n\n    // Parses entire JSX element, including it's opening tag\n    // (starting after '<'), attributes, contents and closing tag.\n\n    jsx_parseElementAt(startPos, startLoc) {\n      let node = this.startNodeAt(startPos, startLoc);\n      let children = [];\n      let openingElement = this.jsx_parseOpeningElementAt(startPos, startLoc);\n      let closingElement = null;\n\n      if (!openingElement.selfClosing) {\n        contents: for (;;) {\n          switch (this.type) {\n          case tok.jsxTagStart:\n            startPos = this.start; startLoc = this.startLoc;\n            this.next();\n            if (this.eat(tt.slash)) {\n              closingElement = this.jsx_parseClosingElementAt(startPos, startLoc);\n              break contents;\n            }\n            children.push(this.jsx_parseElementAt(startPos, startLoc));\n            break;\n\n          case tok.jsxText:\n            children.push(this.parseExprAtom());\n            break;\n\n          case tt.braceL:\n            children.push(this.jsx_parseExpressionContainer());\n            break;\n\n          default:\n            this.unexpected();\n          }\n        }\n        if (getQualifiedJSXName(closingElement.name) !== getQualifiedJSXName(openingElement.name)) {\n          this.raise(\n            closingElement.start,\n            'Expected corresponding JSX closing tag for <' + getQualifiedJSXName(openingElement.name) + '>');\n        }\n      }\n      let fragmentOrElement = openingElement.name ? 'Element' : 'Fragment';\n\n      node['opening' + fragmentOrElement] = openingElement;\n      node['closing' + fragmentOrElement] = closingElement;\n      node.children = children;\n      if (this.type === tt.relational && this.value === \"<\") {\n        this.raise(this.start, \"Adjacent JSX elements must be wrapped in an enclosing tag\");\n      }\n      return this.finishNode(node, 'JSX' + fragmentOrElement);\n    }\n\n    // Parse JSX text\n\n    jsx_parseText() {\n      let node = this.parseLiteral(this.value);\n      node.type = \"JSXText\";\n      return node;\n    }\n\n    // Parses entire JSX element from current position.\n\n    jsx_parseElement() {\n      let startPos = this.start, startLoc = this.startLoc;\n      this.next();\n      return this.jsx_parseElementAt(startPos, startLoc);\n    }\n\n    parseExprAtom(refShortHandDefaultPos) {\n      if (this.type === tok.jsxText)\n        return this.jsx_parseText();\n      else if (this.type === tok.jsxTagStart)\n        return this.jsx_parseElement();\n      else\n        return super.parseExprAtom(refShortHandDefaultPos);\n    }\n\n    readToken(code) {\n      let context = this.curContext();\n\n      if (context === tc_expr) return this.jsx_readToken();\n\n      if (context === tc_oTag || context === tc_cTag) {\n        if (isIdentifierStart(code)) return this.jsx_readWord();\n\n        if (code == 62) {\n          ++this.pos;\n          return this.finishToken(tok.jsxTagEnd);\n        }\n\n        if ((code === 34 || code === 39) && context == tc_oTag)\n          return this.jsx_readString(code);\n      }\n\n      if (code === 60 && this.exprAllowed && this.input.charCodeAt(this.pos + 1) !== 33) {\n        ++this.pos;\n        return this.finishToken(tok.jsxTagStart);\n      }\n      return super.readToken(code);\n    }\n\n    updateContext(prevType) {\n      if (this.type == tt.braceL) {\n        var curContext = this.curContext();\n        if (curContext == tc_oTag) this.context.push(tokContexts.b_expr);\n        else if (curContext == tc_expr) this.context.push(tokContexts.b_tmpl);\n        else super.updateContext(prevType);\n        this.exprAllowed = true;\n      } else if (this.type === tt.slash && prevType === tok.jsxTagStart) {\n        this.context.length -= 2; // do not consider JSX expr -> JSX open tag -> ... anymore\n        this.context.push(tc_cTag); // reconsider as closing tag context\n        this.exprAllowed = false;\n      } else {\n        return super.updateContext(prevType);\n      }\n    }\n  };\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,WAAO,UAAU;AAAA,MACf,MAAM;AAAA,MACN,KAAK;AAAA,MACL,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,SAAS;AAAA,MACT,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,SAAS;AAAA,MACT,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,OAAO;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,KAAK;AAAA,MACL,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA;AAAA;;;AC9PA;AAAA;AAAA,KAAC,SAAU,QAAQ,SAAS;AAC1B,aAAO,YAAY,YAAY,OAAO,WAAW,cAAc,QAAQ,OAAO,IAC9E,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,CAAC,SAAS,GAAG,OAAO,KACvE,SAAS,OAAO,eAAe,cAAc,aAAa,UAAU,MAAM,QAAQ,OAAO,QAAQ,CAAC,CAAC;AAAA,IACtG,GAAG,UAAO,SAAUA,UAAS;AAAE;AAG7B,UAAI,wBAAwB,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,GAAG,IAAI,OAAO,IAAI,MAAM,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,MAAM,GAAG,KAAK,IAAI,GAAG,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,GAAG,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,QAAQ,GAAG;AAGzoC,UAAI,6BAA6B,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG,KAAK,MAAM,IAAI,MAAM,KAAK,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,MAAM,GAAG,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,MAAM,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,MAAM,IAAI,GAAG,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,MAAM,OAAO,IAAI,MAAM,GAAG,KAAK,GAAG,MAAM,IAAI,MAAM,IAAI,KAAK,MAAM,KAAK,MAAM,MAAM,GAAG,IAAI;AAGnpE,UAAI,0BAA0B;AAG9B,UAAI,+BAA+B;AASnC,UAAI,gBAAgB;AAAA,QAClB,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,QAAQ;AAAA,QACR,YAAY;AAAA,MACd;AAIA,UAAI,uBAAuB;AAE3B,UAAI,aAAa;AAAA,QACf,GAAG;AAAA,QACH,WAAW,uBAAuB;AAAA,QAClC,GAAG,uBAAuB;AAAA,MAC5B;AAEA,UAAI,4BAA4B;AAIhC,UAAI,0BAA0B,IAAI,OAAO,MAAM,+BAA+B,GAAG;AACjF,UAAI,qBAAqB,IAAI,OAAO,MAAM,+BAA+B,0BAA0B,GAAG;AAKtG,eAAS,cAAc,MAAM,KAAK;AAChC,YAAI,MAAM;AACV,iBAASC,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK,GAAG;AACtC,iBAAO,IAAIA,EAAC;AACZ,cAAI,MAAM,MAAM;AAAE,mBAAO;AAAA,UAAM;AAC/B,iBAAO,IAAIA,KAAI,CAAC;AAChB,cAAI,OAAO,MAAM;AAAE,mBAAO;AAAA,UAAK;AAAA,QACjC;AACA,eAAO;AAAA,MACT;AAIA,eAAS,kBAAkB,MAAM,QAAQ;AACvC,YAAI,OAAO,IAAI;AAAE,iBAAO,SAAS;AAAA,QAAG;AACpC,YAAI,OAAO,IAAI;AAAE,iBAAO;AAAA,QAAK;AAC7B,YAAI,OAAO,IAAI;AAAE,iBAAO,SAAS;AAAA,QAAG;AACpC,YAAI,OAAO,KAAK;AAAE,iBAAO;AAAA,QAAK;AAC9B,YAAI,QAAQ,OAAQ;AAAE,iBAAO,QAAQ,OAAQ,wBAAwB,KAAK,OAAO,aAAa,IAAI,CAAC;AAAA,QAAE;AACrG,YAAI,WAAW,OAAO;AAAE,iBAAO;AAAA,QAAM;AACrC,eAAO,cAAc,MAAM,0BAA0B;AAAA,MACvD;AAIA,eAAS,iBAAiB,MAAM,QAAQ;AACtC,YAAI,OAAO,IAAI;AAAE,iBAAO,SAAS;AAAA,QAAG;AACpC,YAAI,OAAO,IAAI;AAAE,iBAAO;AAAA,QAAK;AAC7B,YAAI,OAAO,IAAI;AAAE,iBAAO;AAAA,QAAM;AAC9B,YAAI,OAAO,IAAI;AAAE,iBAAO;AAAA,QAAK;AAC7B,YAAI,OAAO,IAAI;AAAE,iBAAO,SAAS;AAAA,QAAG;AACpC,YAAI,OAAO,KAAK;AAAE,iBAAO;AAAA,QAAK;AAC9B,YAAI,QAAQ,OAAQ;AAAE,iBAAO,QAAQ,OAAQ,mBAAmB,KAAK,OAAO,aAAa,IAAI,CAAC;AAAA,QAAE;AAChG,YAAI,WAAW,OAAO;AAAE,iBAAO;AAAA,QAAM;AACrC,eAAO,cAAc,MAAM,0BAA0B,KAAK,cAAc,MAAM,qBAAqB;AAAA,MACrG;AAyBA,UAAI,YAAY,SAASC,WAAU,OAAO,MAAM;AAC9C,YAAK,SAAS,OAAS,QAAO,CAAC;AAE/B,aAAK,QAAQ;AACb,aAAK,UAAU,KAAK;AACpB,aAAK,aAAa,CAAC,CAAC,KAAK;AACzB,aAAK,aAAa,CAAC,CAAC,KAAK;AACzB,aAAK,SAAS,CAAC,CAAC,KAAK;AACrB,aAAK,WAAW,CAAC,CAAC,KAAK;AACvB,aAAK,SAAS,CAAC,CAAC,KAAK;AACrB,aAAK,UAAU,CAAC,CAAC,KAAK;AACtB,aAAK,QAAQ,KAAK,SAAS;AAC3B,aAAK,gBAAgB;AAAA,MACvB;AAEA,eAAS,MAAM,MAAM,MAAM;AACzB,eAAO,IAAI,UAAU,MAAM,EAAC,YAAY,MAAM,OAAO,KAAI,CAAC;AAAA,MAC5D;AACA,UAAI,aAAa,EAAC,YAAY,KAAI,GAAG,aAAa,EAAC,YAAY,KAAI;AAInE,UAAI,WAAW,CAAC;AAGhB,eAAS,GAAG,MAAM,SAAS;AACzB,YAAK,YAAY,OAAS,WAAU,CAAC;AAErC,gBAAQ,UAAU;AAClB,eAAO,SAAS,IAAI,IAAI,IAAI,UAAU,MAAM,OAAO;AAAA,MACrD;AAEA,UAAI,UAAU;AAAA,QACZ,KAAK,IAAI,UAAU,OAAO,UAAU;AAAA,QACpC,QAAQ,IAAI,UAAU,UAAU,UAAU;AAAA,QAC1C,QAAQ,IAAI,UAAU,UAAU,UAAU;AAAA,QAC1C,MAAM,IAAI,UAAU,QAAQ,UAAU;AAAA,QACtC,WAAW,IAAI,UAAU,aAAa,UAAU;AAAA,QAChD,KAAK,IAAI,UAAU,KAAK;AAAA;AAAA,QAGxB,UAAU,IAAI,UAAU,KAAK,EAAC,YAAY,MAAM,YAAY,KAAI,CAAC;AAAA,QACjE,UAAU,IAAI,UAAU,GAAG;AAAA,QAC3B,QAAQ,IAAI,UAAU,KAAK,EAAC,YAAY,MAAM,YAAY,KAAI,CAAC;AAAA,QAC/D,QAAQ,IAAI,UAAU,GAAG;AAAA,QACzB,QAAQ,IAAI,UAAU,KAAK,EAAC,YAAY,MAAM,YAAY,KAAI,CAAC;AAAA,QAC/D,QAAQ,IAAI,UAAU,GAAG;AAAA,QACzB,OAAO,IAAI,UAAU,KAAK,UAAU;AAAA,QACpC,MAAM,IAAI,UAAU,KAAK,UAAU;AAAA,QACnC,OAAO,IAAI,UAAU,KAAK,UAAU;AAAA,QACpC,KAAK,IAAI,UAAU,GAAG;AAAA,QACtB,UAAU,IAAI,UAAU,KAAK,UAAU;AAAA,QACvC,aAAa,IAAI,UAAU,IAAI;AAAA,QAC/B,OAAO,IAAI,UAAU,MAAM,UAAU;AAAA,QACrC,UAAU,IAAI,UAAU,UAAU;AAAA,QAClC,iBAAiB,IAAI,UAAU,iBAAiB;AAAA,QAChD,UAAU,IAAI,UAAU,OAAO,UAAU;AAAA,QACzC,WAAW,IAAI,UAAU,KAAK,UAAU;AAAA,QACxC,cAAc,IAAI,UAAU,MAAM,EAAC,YAAY,MAAM,YAAY,KAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAgBtE,IAAI,IAAI,UAAU,KAAK,EAAC,YAAY,MAAM,UAAU,KAAI,CAAC;AAAA,QACzD,QAAQ,IAAI,UAAU,MAAM,EAAC,YAAY,MAAM,UAAU,KAAI,CAAC;AAAA,QAC9D,QAAQ,IAAI,UAAU,SAAS,EAAC,QAAQ,MAAM,SAAS,MAAM,YAAY,KAAI,CAAC;AAAA,QAC9E,QAAQ,IAAI,UAAU,OAAO,EAAC,YAAY,MAAM,QAAQ,MAAM,YAAY,KAAI,CAAC;AAAA,QAC/E,WAAW,MAAM,MAAM,CAAC;AAAA,QACxB,YAAY,MAAM,MAAM,CAAC;AAAA,QACzB,WAAW,MAAM,KAAK,CAAC;AAAA,QACvB,YAAY,MAAM,KAAK,CAAC;AAAA,QACxB,YAAY,MAAM,KAAK,CAAC;AAAA,QACxB,UAAU,MAAM,iBAAiB,CAAC;AAAA,QAClC,YAAY,MAAM,aAAa,CAAC;AAAA,QAChC,UAAU,MAAM,aAAa,CAAC;AAAA,QAC9B,SAAS,IAAI,UAAU,OAAO,EAAC,YAAY,MAAM,OAAO,GAAG,QAAQ,MAAM,YAAY,KAAI,CAAC;AAAA,QAC1F,QAAQ,MAAM,KAAK,EAAE;AAAA,QACrB,MAAM,MAAM,KAAK,EAAE;AAAA,QACnB,OAAO,MAAM,KAAK,EAAE;AAAA,QACpB,UAAU,IAAI,UAAU,MAAM,EAAC,YAAY,KAAI,CAAC;AAAA,QAChD,UAAU,MAAM,MAAM,CAAC;AAAA;AAAA,QAGvB,QAAQ,GAAG,OAAO;AAAA,QAClB,OAAO,GAAG,QAAQ,UAAU;AAAA,QAC5B,QAAQ,GAAG,OAAO;AAAA,QAClB,WAAW,GAAG,UAAU;AAAA,QACxB,WAAW,GAAG,UAAU;AAAA,QACxB,UAAU,GAAG,WAAW,UAAU;AAAA,QAClC,KAAK,GAAG,MAAM,EAAC,QAAQ,MAAM,YAAY,KAAI,CAAC;AAAA,QAC9C,OAAO,GAAG,QAAQ,UAAU;AAAA,QAC5B,UAAU,GAAG,SAAS;AAAA,QACtB,MAAM,GAAG,OAAO,EAAC,QAAQ,KAAI,CAAC;AAAA,QAC9B,WAAW,GAAG,YAAY,UAAU;AAAA,QACpC,KAAK,GAAG,IAAI;AAAA,QACZ,SAAS,GAAG,UAAU,UAAU;AAAA,QAChC,SAAS,GAAG,QAAQ;AAAA,QACpB,QAAQ,GAAG,SAAS,UAAU;AAAA,QAC9B,MAAM,GAAG,KAAK;AAAA,QACd,MAAM,GAAG,KAAK;AAAA,QACd,QAAQ,GAAG,OAAO;AAAA,QAClB,QAAQ,GAAG,SAAS,EAAC,QAAQ,KAAI,CAAC;AAAA,QAClC,OAAO,GAAG,MAAM;AAAA,QAChB,MAAM,GAAG,OAAO,EAAC,YAAY,MAAM,YAAY,KAAI,CAAC;AAAA,QACpD,OAAO,GAAG,QAAQ,UAAU;AAAA,QAC5B,QAAQ,GAAG,SAAS,UAAU;AAAA,QAC9B,QAAQ,GAAG,SAAS,UAAU;AAAA,QAC9B,UAAU,GAAG,WAAW,UAAU;AAAA,QAClC,SAAS,GAAG,QAAQ;AAAA,QACpB,SAAS,GAAG,UAAU,UAAU;AAAA,QAChC,OAAO,GAAG,QAAQ,UAAU;AAAA,QAC5B,OAAO,GAAG,QAAQ,UAAU;AAAA,QAC5B,QAAQ,GAAG,SAAS,UAAU;AAAA,QAC9B,KAAK,GAAG,MAAM,EAAC,YAAY,MAAM,OAAO,EAAC,CAAC;AAAA,QAC1C,aAAa,GAAG,cAAc,EAAC,YAAY,MAAM,OAAO,EAAC,CAAC;AAAA,QAC1D,SAAS,GAAG,UAAU,EAAC,YAAY,MAAM,QAAQ,MAAM,YAAY,KAAI,CAAC;AAAA,QACxE,OAAO,GAAG,QAAQ,EAAC,YAAY,MAAM,QAAQ,MAAM,YAAY,KAAI,CAAC;AAAA,QACpE,SAAS,GAAG,UAAU,EAAC,YAAY,MAAM,QAAQ,MAAM,YAAY,KAAI,CAAC;AAAA,MAC1E;AAKA,UAAI,YAAY;AAChB,UAAI,aAAa,IAAI,OAAO,UAAU,QAAQ,GAAG;AAEjD,eAAS,UAAU,MAAM;AACvB,eAAO,SAAS,MAAM,SAAS,MAAM,SAAS,QAAU,SAAS;AAAA,MACnE;AAEA,eAAS,cAAc,MAAM,MAAM,KAAK;AACtC,YAAK,QAAQ,OAAS,OAAM,KAAK;AAEjC,iBAASD,KAAI,MAAMA,KAAI,KAAKA,MAAK;AAC/B,cAAI,OAAO,KAAK,WAAWA,EAAC;AAC5B,cAAI,UAAU,IAAI,GAChB;AAAE,mBAAOA,KAAI,MAAM,KAAK,SAAS,MAAM,KAAK,WAAWA,KAAI,CAAC,MAAM,KAAKA,KAAI,IAAIA,KAAI;AAAA,UAAE;AAAA,QACzF;AACA,eAAO;AAAA,MACT;AAEA,UAAI,qBAAqB;AAEzB,UAAI,iBAAiB;AAErB,UAAI,MAAM,OAAO;AACjB,UAAI,iBAAiB,IAAI;AACzB,UAAI,WAAW,IAAI;AAEnB,UAAI,SAAS,OAAO,WAAW,SAAU,KAAK,UAAU;AAAE,eACxD,eAAe,KAAK,KAAK,QAAQ;AAAA,MAChC;AAEH,UAAI,UAAU,MAAM,YAAY,SAAU,KAAK;AAAE,eAC/C,SAAS,KAAK,GAAG,MAAM;AAAA,MACtB;AAEH,UAAI,cAAc,uBAAO,OAAO,IAAI;AAEpC,eAAS,YAAY,OAAO;AAC1B,eAAO,YAAY,KAAK,MAAM,YAAY,KAAK,IAAI,IAAI,OAAO,SAAS,MAAM,QAAQ,MAAM,GAAG,IAAI,IAAI;AAAA,MACxG;AAEA,eAAS,kBAAkB,MAAM;AAE/B,YAAI,QAAQ,OAAQ;AAAE,iBAAO,OAAO,aAAa,IAAI;AAAA,QAAE;AACvD,gBAAQ;AACR,eAAO,OAAO,cAAc,QAAQ,MAAM,QAAS,OAAO,QAAQ,KAAM;AAAA,MAC1E;AAEA,UAAI,gBAAgB;AAKpB,UAAI,WAAW,SAASE,UAAS,MAAM,KAAK;AAC1C,aAAK,OAAO;AACZ,aAAK,SAAS;AAAA,MAChB;AAEA,eAAS,UAAU,SAAS,SAAS,OAAQ,GAAG;AAC9C,eAAO,IAAI,SAAS,KAAK,MAAM,KAAK,SAAS,CAAC;AAAA,MAChD;AAEA,UAAI,iBAAiB,SAASC,gBAAe,GAAG,OAAO,KAAK;AAC1D,aAAK,QAAQ;AACb,aAAK,MAAM;AACX,YAAI,EAAE,eAAe,MAAM;AAAE,eAAK,SAAS,EAAE;AAAA,QAAY;AAAA,MAC3D;AAQA,eAAS,YAAY,OAAO,QAAQ;AAClC,iBAAS,OAAO,GAAG,MAAM,OAAK;AAC5B,cAAI,YAAY,cAAc,OAAO,KAAK,MAAM;AAChD,cAAI,YAAY,GAAG;AAAE,mBAAO,IAAI,SAAS,MAAM,SAAS,GAAG;AAAA,UAAE;AAC7D,YAAE;AACF,gBAAM;AAAA,QACR;AAAA,MACF;AAKA,UAAI,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOnB,aAAa;AAAA;AAAA;AAAA;AAAA,QAIb,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMZ,qBAAqB;AAAA;AAAA;AAAA,QAGrB,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,QAKjB,eAAe;AAAA;AAAA;AAAA,QAGf,4BAA4B;AAAA;AAAA;AAAA;AAAA,QAI5B,6BAA6B;AAAA;AAAA;AAAA;AAAA,QAI7B,2BAA2B;AAAA;AAAA;AAAA,QAG3B,yBAAyB;AAAA;AAAA;AAAA;AAAA,QAIzB,eAAe;AAAA;AAAA;AAAA;AAAA,QAIf,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,QAKpB,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMX,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAaT,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASX,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMR,SAAS;AAAA;AAAA;AAAA,QAGT,YAAY;AAAA;AAAA;AAAA,QAGZ,kBAAkB;AAAA;AAAA;AAAA,QAGlB,gBAAgB;AAAA,MAClB;AAIA,UAAI,yBAAyB;AAE7B,eAAS,WAAW,MAAM;AACxB,YAAI,UAAU,CAAC;AAEf,iBAAS,OAAO,gBACd;AAAE,kBAAQ,GAAG,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI,eAAe,GAAG;AAAA,QAAG;AAEhF,YAAI,QAAQ,gBAAgB,UAAU;AACpC,kBAAQ,cAAc;AAAA,QACxB,WAAW,QAAQ,eAAe,MAAM;AACtC,cAAI,CAAC,0BAA0B,OAAO,YAAY,YAAY,QAAQ,MAAM;AAC1E,qCAAyB;AACzB,oBAAQ,KAAK,oHAAoH;AAAA,UACnI;AACA,kBAAQ,cAAc;AAAA,QACxB,WAAW,QAAQ,eAAe,MAAM;AACtC,kBAAQ,eAAe;AAAA,QACzB;AAEA,YAAI,QAAQ,iBAAiB,MAC3B;AAAE,kBAAQ,gBAAgB,QAAQ,cAAc;AAAA,QAAG;AAErD,YAAI,CAAC,QAAQ,KAAK,iBAAiB,MACjC;AAAE,kBAAQ,gBAAgB,QAAQ,eAAe;AAAA,QAAI;AAEvD,YAAI,QAAQ,QAAQ,OAAO,GAAG;AAC5B,cAAI,SAAS,QAAQ;AACrB,kBAAQ,UAAU,SAAU,OAAO;AAAE,mBAAO,OAAO,KAAK,KAAK;AAAA,UAAG;AAAA,QAClE;AACA,YAAI,QAAQ,QAAQ,SAAS,GAC3B;AAAE,kBAAQ,YAAY,YAAY,SAAS,QAAQ,SAAS;AAAA,QAAG;AAEjE,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,SAAS,OAAO;AACnC,eAAO,SAAS,OAAO,MAAM,OAAO,KAAK,UAAU,QAAQ;AACzD,cAAI,UAAU;AAAA,YACZ,MAAM,QAAQ,UAAU;AAAA,YACxB,OAAO;AAAA,YACP;AAAA,YACA;AAAA,UACF;AACA,cAAI,QAAQ,WACV;AAAE,oBAAQ,MAAM,IAAI,eAAe,MAAM,UAAU,MAAM;AAAA,UAAG;AAC9D,cAAI,QAAQ,QACV;AAAE,oBAAQ,QAAQ,CAAC,OAAO,GAAG;AAAA,UAAG;AAClC,gBAAM,KAAK,OAAO;AAAA,QACpB;AAAA,MACF;AAGA,UACI,YAAY,GACZ,iBAAiB,GACjB,cAAc,GACd,kBAAkB,GAClB,cAAc,IACd,qBAAqB,IACrB,cAAc,IACd,qBAAqB,KACrB,2BAA2B,KAC3B,yBAAyB,KACzB,YAAY,YAAY,iBAAiB;AAE7C,eAAS,cAAc,OAAO,WAAW;AACvC,eAAO,kBAAkB,QAAQ,cAAc,MAAM,YAAY,kBAAkB;AAAA,MACrF;AAGA,UACI,YAAY,GACZ,WAAW,GACX,eAAe,GACf,gBAAgB,GAChB,oBAAoB,GACpB,eAAe;AAEnB,UAAI,SAAS,SAASC,QAAO,SAAS,OAAO,UAAU;AACrD,aAAK,UAAU,UAAU,WAAW,OAAO;AAC3C,aAAK,aAAa,QAAQ;AAC1B,aAAK,WAAW,YAAY,WAAW,QAAQ,eAAe,IAAI,IAAI,QAAQ,eAAe,WAAW,YAAY,CAAC,CAAC;AACtH,YAAI,WAAW;AACf,YAAI,QAAQ,kBAAkB,MAAM;AAClC,qBAAW,cAAc,QAAQ,eAAe,IAAI,IAAI,QAAQ,gBAAgB,IAAI,IAAI,CAAC;AACzF,cAAI,QAAQ,eAAe,UAAU;AAAE,wBAAY;AAAA,UAAU;AAAA,QAC/D;AACA,aAAK,gBAAgB,YAAY,QAAQ;AACzC,YAAI,kBAAkB,WAAW,WAAW,MAAM,MAAM,cAAc;AACtE,aAAK,sBAAsB,YAAY,cAAc;AACrD,aAAK,0BAA0B,YAAY,iBAAiB,MAAM,cAAc,UAAU;AAC1F,aAAK,QAAQ,OAAO,KAAK;AAKzB,aAAK,cAAc;AAKnB,YAAI,UAAU;AACZ,eAAK,MAAM;AACX,eAAK,YAAY,KAAK,MAAM,YAAY,MAAM,WAAW,CAAC,IAAI;AAC9D,eAAK,UAAU,KAAK,MAAM,MAAM,GAAG,KAAK,SAAS,EAAE,MAAM,SAAS,EAAE;AAAA,QACtE,OAAO;AACL,eAAK,MAAM,KAAK,YAAY;AAC5B,eAAK,UAAU;AAAA,QACjB;AAIA,aAAK,OAAO,QAAQ;AAEpB,aAAK,QAAQ;AAEb,aAAK,QAAQ,KAAK,MAAM,KAAK;AAG7B,aAAK,WAAW,KAAK,SAAS,KAAK,YAAY;AAG/C,aAAK,gBAAgB,KAAK,kBAAkB;AAC5C,aAAK,eAAe,KAAK,aAAa,KAAK;AAK3C,aAAK,UAAU,KAAK,eAAe;AACnC,aAAK,cAAc;AAGnB,aAAK,WAAW,QAAQ,eAAe;AACvC,aAAK,SAAS,KAAK,YAAY,KAAK,gBAAgB,KAAK,GAAG;AAG5D,aAAK,mBAAmB;AACxB,aAAK,2BAA2B;AAGhC,aAAK,WAAW,KAAK,WAAW,KAAK,gBAAgB;AAErD,aAAK,SAAS,CAAC;AAEf,aAAK,mBAAmB,uBAAO,OAAO,IAAI;AAG1C,YAAI,KAAK,QAAQ,KAAK,QAAQ,iBAAiB,KAAK,MAAM,MAAM,GAAG,CAAC,MAAM,MACxE;AAAE,eAAK,gBAAgB,CAAC;AAAA,QAAG;AAG7B,aAAK,aAAa,CAAC;AACnB,aAAK,WAAW,SAAS;AAGzB,aAAK,cAAc;AAKnB,aAAK,mBAAmB,CAAC;AAAA,MAC3B;AAEA,UAAI,qBAAqB,EAAE,YAAY,EAAE,cAAc,KAAK,GAAE,aAAa,EAAE,cAAc,KAAK,GAAE,SAAS,EAAE,cAAc,KAAK,GAAE,UAAU,EAAE,cAAc,KAAK,GAAE,YAAY,EAAE,cAAc,KAAK,GAAE,kBAAkB,EAAE,cAAc,KAAK,GAAE,qBAAqB,EAAE,cAAc,KAAK,GAAE,mBAAmB,EAAE,cAAc,KAAK,GAAE,oBAAoB,EAAE,cAAc,KAAK,EAAE;AAEhX,aAAO,UAAU,QAAQ,SAASC,SAAS;AACzC,YAAI,OAAO,KAAK,QAAQ,WAAW,KAAK,UAAU;AAClD,aAAK,UAAU;AACf,eAAO,KAAK,cAAc,IAAI;AAAA,MAChC;AAEA,yBAAmB,WAAW,MAAM,WAAY;AAAE,gBAAQ,KAAK,gBAAgB,EAAE,QAAQ,kBAAkB;AAAA,MAAE;AAE7G,yBAAmB,YAAY,MAAM,WAAY;AAAE,gBAAQ,KAAK,gBAAgB,EAAE,QAAQ,mBAAmB;AAAA,MAAE;AAE/G,yBAAmB,QAAQ,MAAM,WAAY;AAAE,gBAAQ,KAAK,gBAAgB,EAAE,QAAQ,eAAe;AAAA,MAAE;AAEvG,yBAAmB,SAAS,MAAM,WAAY;AAC5C,iBAASL,KAAI,KAAK,WAAW,SAAS,GAAGA,MAAK,GAAGA,MAAK;AACpD,cAAIM,OAAM,KAAK,WAAWN,EAAC;AACzB,cAAI,QAAQM,KAAI;AAClB,cAAI,SAAS,2BAA2B,yBAAyB;AAAE,mBAAO;AAAA,UAAM;AAChF,cAAI,QAAQ,gBAAgB;AAAE,oBAAQ,QAAQ,eAAe;AAAA,UAAE;AAAA,QACjE;AACA,eAAQ,KAAK,YAAY,KAAK,QAAQ,eAAe,MAAO,KAAK,QAAQ;AAAA,MAC3E;AAEA,yBAAmB,WAAW,MAAM,WAAY;AAC9C,YAAIA,OAAM,KAAK,iBAAiB;AAC9B,YAAI,QAAQA,KAAI;AAClB,gBAAQ,QAAQ,eAAe,KAAK,KAAK,QAAQ;AAAA,MACnD;AAEA,yBAAmB,iBAAiB,MAAM,WAAY;AAAE,gBAAQ,KAAK,iBAAiB,EAAE,QAAQ,sBAAsB;AAAA,MAAE;AAExH,yBAAmB,oBAAoB,MAAM,WAAY;AAAE,eAAO,KAAK,2BAA2B,KAAK,aAAa,CAAC;AAAA,MAAE;AAEvH,yBAAmB,kBAAkB,MAAM,WAAY;AACrD,iBAASN,KAAI,KAAK,WAAW,SAAS,GAAGA,MAAK,GAAGA,MAAK;AACpD,cAAIM,OAAM,KAAK,WAAWN,EAAC;AACzB,cAAI,QAAQM,KAAI;AAClB,cAAI,SAAS,2BAA2B,2BAClC,QAAQ,kBAAmB,EAAE,QAAQ,cAAe;AAAE,mBAAO;AAAA,UAAK;AAAA,QAC1E;AACA,eAAO;AAAA,MACT;AAEA,yBAAmB,mBAAmB,MAAM,WAAY;AACtD,gBAAQ,KAAK,gBAAgB,EAAE,QAAQ,4BAA4B;AAAA,MACrE;AAEA,aAAO,SAAS,SAAS,SAAU;AAC/B,YAAI,UAAU,CAAC,GAAG,MAAM,UAAU;AAClC,eAAQ,MAAQ,SAAS,GAAI,IAAI,UAAW,GAAI;AAElD,YAAI,MAAM;AACV,iBAASN,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AAAE,gBAAM,QAAQA,EAAC,EAAE,GAAG;AAAA,QAAG;AAClE,eAAO;AAAA,MACT;AAEA,aAAO,QAAQ,SAASK,OAAO,OAAO,SAAS;AAC7C,eAAO,IAAI,KAAK,SAAS,KAAK,EAAE,MAAM;AAAA,MACxC;AAEA,aAAO,oBAAoB,SAASE,mBAAmB,OAAO,KAAK,SAAS;AAC1E,YAAI,SAAS,IAAI,KAAK,SAAS,OAAO,GAAG;AACzC,eAAO,UAAU;AACjB,eAAO,OAAO,gBAAgB;AAAA,MAChC;AAEA,aAAO,YAAY,SAASC,WAAW,OAAO,SAAS;AACrD,eAAO,IAAI,KAAK,SAAS,KAAK;AAAA,MAChC;AAEA,aAAO,iBAAkB,OAAO,WAAW,kBAAmB;AAE9D,UAAI,OAAO,OAAO;AAIlB,UAAI,UAAU;AACd,WAAK,kBAAkB,SAAS,OAAO;AACrC,YAAI,KAAK,QAAQ,cAAc,GAAG;AAAE,iBAAO;AAAA,QAAM;AACjD,mBAAS;AAEP,yBAAe,YAAY;AAC3B,mBAAS,eAAe,KAAK,KAAK,KAAK,EAAE,CAAC,EAAE;AAC5C,cAAI,QAAQ,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK,CAAC;AAChD,cAAI,CAAC,OAAO;AAAE,mBAAO;AAAA,UAAM;AAC3B,eAAK,MAAM,CAAC,KAAK,MAAM,CAAC,OAAO,cAAc;AAC3C,2BAAe,YAAY,QAAQ,MAAM,CAAC,EAAE;AAC5C,gBAAI,aAAa,eAAe,KAAK,KAAK,KAAK,GAAG,MAAM,WAAW,QAAQ,WAAW,CAAC,EAAE;AACzF,gBAAI,OAAO,KAAK,MAAM,OAAO,GAAG;AAChC,mBAAO,SAAS,OAAO,SAAS,OAC7B,UAAU,KAAK,WAAW,CAAC,CAAC,KAC5B,EAAE,sBAAsB,KAAK,IAAI,KAAK,SAAS,OAAO,KAAK,MAAM,OAAO,MAAM,CAAC,MAAM;AAAA,UAC1F;AACA,mBAAS,MAAM,CAAC,EAAE;AAGlB,yBAAe,YAAY;AAC3B,mBAAS,eAAe,KAAK,KAAK,KAAK,EAAE,CAAC,EAAE;AAC5C,cAAI,KAAK,MAAM,KAAK,MAAM,KACxB;AAAE;AAAA,UAAS;AAAA,QACf;AAAA,MACF;AAKA,WAAK,MAAM,SAAS,MAAM;AACxB,YAAI,KAAK,SAAS,MAAM;AACtB,eAAK,KAAK;AACV,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAIA,WAAK,eAAe,SAAS,MAAM;AACjC,eAAO,KAAK,SAAS,QAAQ,QAAQ,KAAK,UAAU,QAAQ,CAAC,KAAK;AAAA,MACpE;AAIA,WAAK,gBAAgB,SAAS,MAAM;AAClC,YAAI,CAAC,KAAK,aAAa,IAAI,GAAG;AAAE,iBAAO;AAAA,QAAM;AAC7C,aAAK,KAAK;AACV,eAAO;AAAA,MACT;AAIA,WAAK,mBAAmB,SAAS,MAAM;AACrC,YAAI,CAAC,KAAK,cAAc,IAAI,GAAG;AAAE,eAAK,WAAW;AAAA,QAAG;AAAA,MACtD;AAIA,WAAK,qBAAqB,WAAW;AACnC,eAAO,KAAK,SAAS,QAAQ,OAC3B,KAAK,SAAS,QAAQ,UACtB,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,KAAK,CAAC;AAAA,MAChE;AAEA,WAAK,kBAAkB,WAAW;AAChC,YAAI,KAAK,mBAAmB,GAAG;AAC7B,cAAI,KAAK,QAAQ,qBACf;AAAE,iBAAK,QAAQ,oBAAoB,KAAK,YAAY,KAAK,aAAa;AAAA,UAAG;AAC3E,iBAAO;AAAA,QACT;AAAA,MACF;AAKA,WAAK,YAAY,WAAW;AAC1B,YAAI,CAAC,KAAK,IAAI,QAAQ,IAAI,KAAK,CAAC,KAAK,gBAAgB,GAAG;AAAE,eAAK,WAAW;AAAA,QAAG;AAAA,MAC/E;AAEA,WAAK,qBAAqB,SAAS,SAAS,SAAS;AACnD,YAAI,KAAK,SAAS,SAAS;AACzB,cAAI,KAAK,QAAQ,iBACf;AAAE,iBAAK,QAAQ,gBAAgB,KAAK,cAAc,KAAK,eAAe;AAAA,UAAG;AAC3E,cAAI,CAAC,SACH;AAAE,iBAAK,KAAK;AAAA,UAAG;AACjB,iBAAO;AAAA,QACT;AAAA,MACF;AAKA,WAAK,SAAS,SAAS,MAAM;AAC3B,aAAK,IAAI,IAAI,KAAK,KAAK,WAAW;AAAA,MACpC;AAIA,WAAK,aAAa,SAAS,KAAK;AAC9B,aAAK,MAAM,OAAO,OAAO,MAAM,KAAK,OAAO,kBAAkB;AAAA,MAC/D;AAEA,UAAI,sBAAsB,SAASC,uBAAsB;AACvD,aAAK,kBACL,KAAK,gBACL,KAAK,sBACL,KAAK,oBACL,KAAK,cACH;AAAA,MACJ;AAEA,WAAK,qBAAqB,SAAS,wBAAwB,UAAU;AACnE,YAAI,CAAC,wBAAwB;AAAE;AAAA,QAAO;AACtC,YAAI,uBAAuB,gBAAgB,IACzC;AAAE,eAAK,iBAAiB,uBAAuB,eAAe,+CAA+C;AAAA,QAAG;AAClH,YAAI,SAAS,WAAW,uBAAuB,sBAAsB,uBAAuB;AAC5F,YAAI,SAAS,IAAI;AAAE,eAAK,iBAAiB,QAAQ,WAAW,wBAAwB,uBAAuB;AAAA,QAAG;AAAA,MAChH;AAEA,WAAK,wBAAwB,SAAS,wBAAwB,UAAU;AACtE,YAAI,CAAC,wBAAwB;AAAE,iBAAO;AAAA,QAAM;AAC5C,YAAI,kBAAkB,uBAAuB;AAC7C,YAAI,cAAc,uBAAuB;AACzC,YAAI,CAAC,UAAU;AAAE,iBAAO,mBAAmB,KAAK,eAAe;AAAA,QAAE;AACjE,YAAI,mBAAmB,GACrB;AAAE,eAAK,MAAM,iBAAiB,yEAAyE;AAAA,QAAG;AAC5G,YAAI,eAAe,GACjB;AAAE,eAAK,iBAAiB,aAAa,oCAAoC;AAAA,QAAG;AAAA,MAChF;AAEA,WAAK,iCAAiC,WAAW;AAC/C,YAAI,KAAK,aAAa,CAAC,KAAK,YAAY,KAAK,WAAW,KAAK,WAC3D;AAAE,eAAK,MAAM,KAAK,UAAU,4CAA4C;AAAA,QAAG;AAC7E,YAAI,KAAK,UACP;AAAE,eAAK,MAAM,KAAK,UAAU,4CAA4C;AAAA,QAAG;AAAA,MAC/E;AAEA,WAAK,uBAAuB,SAAS,MAAM;AACzC,YAAI,KAAK,SAAS,2BAChB;AAAE,iBAAO,KAAK,qBAAqB,KAAK,UAAU;AAAA,QAAE;AACtD,eAAO,KAAK,SAAS,gBAAgB,KAAK,SAAS;AAAA,MACrD;AAEA,UAAI,OAAO,OAAO;AASlB,WAAK,gBAAgB,SAAS,MAAM;AAClC,YAAIV,WAAU,uBAAO,OAAO,IAAI;AAChC,YAAI,CAAC,KAAK,MAAM;AAAE,eAAK,OAAO,CAAC;AAAA,QAAG;AAClC,eAAO,KAAK,SAAS,QAAQ,KAAK;AAChC,cAAI,OAAO,KAAK,eAAe,MAAM,MAAMA,QAAO;AAClD,eAAK,KAAK,KAAK,IAAI;AAAA,QACrB;AACA,YAAI,KAAK,UACP;AAAE,mBAASC,KAAI,GAAGU,QAAO,OAAO,KAAK,KAAK,gBAAgB,GAAGV,KAAIU,MAAK,QAAQV,MAAK,GACjF;AACE,gBAAI,OAAOU,MAAKV,EAAC;AAEjB,iBAAK,iBAAiB,KAAK,iBAAiB,IAAI,EAAE,OAAQ,aAAa,OAAO,kBAAmB;AAAA,UACnG;AAAA,QAAE;AACN,aAAK,uBAAuB,KAAK,IAAI;AACrC,aAAK,KAAK;AACV,aAAK,aAAa,KAAK,QAAQ;AAC/B,eAAO,KAAK,WAAW,MAAM,SAAS;AAAA,MACxC;AAEA,UAAI,YAAY,EAAC,MAAM,OAAM,GAAG,cAAc,EAAC,MAAM,SAAQ;AAE7D,WAAK,QAAQ,SAAS,SAAS;AAC7B,YAAI,KAAK,QAAQ,cAAc,KAAK,CAAC,KAAK,aAAa,KAAK,GAAG;AAAE,iBAAO;AAAA,QAAM;AAC9E,uBAAe,YAAY,KAAK;AAChC,YAAI,OAAO,eAAe,KAAK,KAAK,KAAK;AACzC,YAAI,OAAO,KAAK,MAAM,KAAK,CAAC,EAAE,QAAQ,SAAS,KAAK,MAAM,WAAW,IAAI;AAKzE,YAAI,WAAW,MAAM,WAAW,IAAI;AAAE,iBAAO;AAAA,QAAK;AAClD,YAAI,SAAS;AAAE,iBAAO;AAAA,QAAM;AAE5B,YAAI,WAAW,OAAO,SAAS,SAAU,SAAS,OAAQ;AAAE,iBAAO;AAAA,QAAK;AACxE,YAAI,kBAAkB,QAAQ,IAAI,GAAG;AACnC,cAAI,MAAM,OAAO;AACjB,iBAAO,iBAAiB,SAAS,KAAK,MAAM,WAAW,GAAG,GAAG,IAAI,GAAG;AAAE,cAAE;AAAA,UAAK;AAC7E,cAAI,WAAW,MAAM,SAAS,SAAU,SAAS,OAAQ;AAAE,mBAAO;AAAA,UAAK;AACvE,cAAI,QAAQ,KAAK,MAAM,MAAM,MAAM,GAAG;AACtC,cAAI,CAAC,0BAA0B,KAAK,KAAK,GAAG;AAAE,mBAAO;AAAA,UAAK;AAAA,QAC5D;AACA,eAAO;AAAA,MACT;AAKA,WAAK,kBAAkB,WAAW;AAChC,YAAI,KAAK,QAAQ,cAAc,KAAK,CAAC,KAAK,aAAa,OAAO,GAC5D;AAAE,iBAAO;AAAA,QAAM;AAEjB,uBAAe,YAAY,KAAK;AAChC,YAAI,OAAO,eAAe,KAAK,KAAK,KAAK;AACzC,YAAI,OAAO,KAAK,MAAM,KAAK,CAAC,EAAE,QAAQ;AACtC,eAAO,CAAC,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,IAAI,CAAC,KACrD,KAAK,MAAM,MAAM,MAAM,OAAO,CAAC,MAAM,eACpC,OAAO,MAAM,KAAK,MAAM,UACxB,EAAE,iBAAiB,QAAQ,KAAK,MAAM,WAAW,OAAO,CAAC,CAAC,KAAK,QAAQ,SAAU,QAAQ;AAAA,MAC9F;AAEA,WAAK,iBAAiB,SAAS,cAAc,OAAO;AAClD,YAAI,KAAK,QAAQ,cAAc,MAAM,CAAC,KAAK,aAAa,eAAe,UAAU,OAAO,GACtF;AAAE,iBAAO;AAAA,QAAM;AAEjB,uBAAe,YAAY,KAAK;AAChC,YAAI,OAAO,eAAe,KAAK,KAAK,KAAK;AACzC,YAAI,OAAO,KAAK,MAAM,KAAK,CAAC,EAAE;AAE9B,YAAI,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,IAAI,CAAC,GAAG;AAAE,iBAAO;AAAA,QAAM;AAErE,YAAI,cAAc;AAChB,cAAI,cAAc,OAAO,GAAe;AACxC,cAAI,KAAK,MAAM,MAAM,MAAM,WAAW,MAAM,WAC1C,gBAAgB,KAAK,MAAM,UAC3B,iBAAiB,QAAQ,KAAK,MAAM,WAAW,WAAW,CAAC,KAC1D,QAAQ,SAAU,QAAQ,OAC3B;AAAE,mBAAO;AAAA,UAAM;AAEjB,yBAAe,YAAY;AAC3B,cAAI,iBAAiB,eAAe,KAAK,KAAK,KAAK;AACnD,cAAI,kBAAkB,UAAU,KAAK,KAAK,MAAM,MAAM,aAAa,cAAc,eAAe,CAAC,EAAE,MAAM,CAAC,GAAG;AAAE,mBAAO;AAAA,UAAM;AAAA,QAC9H;AAEA,YAAI,OAAO;AACT,cAAI,WAAW,OAAO,GAAY;AAClC,cAAI,KAAK,MAAM,MAAM,MAAM,QAAQ,MAAM,MAAM;AAC7C,gBAAI,aAAa,KAAK,MAAM,UACzB,CAAC,iBAAiB,UAAU,KAAK,MAAM,WAAW,QAAQ,CAAC,KAAK,EAAE,UAAU,SAAU,UAAU,QAAU;AAAE,qBAAO;AAAA,YAAM;AAAA,UAC9H;AAAA,QACF;AAEA,YAAI,KAAK,KAAK,MAAM,WAAW,IAAI;AACnC,eAAO,kBAAkB,IAAI,IAAI,KAAK,OAAO;AAAA,MAC/C;AAEA,WAAK,eAAe,SAAS,OAAO;AAClC,eAAO,KAAK,eAAe,MAAM,KAAK;AAAA,MACxC;AAEA,WAAK,UAAU,SAAS,OAAO;AAC7B,eAAO,KAAK,eAAe,OAAO,KAAK;AAAA,MACzC;AASA,WAAK,iBAAiB,SAAS,SAAS,UAAUD,UAAS;AACzD,YAAI,YAAY,KAAK,MAAM,OAAO,KAAK,UAAU,GAAG;AAEpD,YAAI,KAAK,MAAM,OAAO,GAAG;AACvB,sBAAY,QAAQ;AACpB,iBAAO;AAAA,QACT;AAMA,gBAAQ,WAAW;AAAA,UACnB,KAAK,QAAQ;AAAA,UAAQ,KAAK,QAAQ;AAAW,mBAAO,KAAK,4BAA4B,MAAM,UAAU,OAAO;AAAA,UAC5G,KAAK,QAAQ;AAAW,mBAAO,KAAK,uBAAuB,IAAI;AAAA,UAC/D,KAAK,QAAQ;AAAK,mBAAO,KAAK,iBAAiB,IAAI;AAAA,UACnD,KAAK,QAAQ;AAAM,mBAAO,KAAK,kBAAkB,IAAI;AAAA,UACrD,KAAK,QAAQ;AAIX,gBAAK,YAAY,KAAK,UAAU,YAAY,QAAQ,YAAY,YAAa,KAAK,QAAQ,eAAe,GAAG;AAAE,mBAAK,WAAW;AAAA,YAAG;AACjI,mBAAO,KAAK,uBAAuB,MAAM,OAAO,CAAC,OAAO;AAAA,UAC1D,KAAK,QAAQ;AACX,gBAAI,SAAS;AAAE,mBAAK,WAAW;AAAA,YAAG;AAClC,mBAAO,KAAK,WAAW,MAAM,IAAI;AAAA,UACnC,KAAK,QAAQ;AAAK,mBAAO,KAAK,iBAAiB,IAAI;AAAA,UACnD,KAAK,QAAQ;AAAS,mBAAO,KAAK,qBAAqB,IAAI;AAAA,UAC3D,KAAK,QAAQ;AAAS,mBAAO,KAAK,qBAAqB,IAAI;AAAA,UAC3D,KAAK,QAAQ;AAAQ,mBAAO,KAAK,oBAAoB,IAAI;AAAA,UACzD,KAAK,QAAQ;AAAM,mBAAO,KAAK,kBAAkB,IAAI;AAAA,UACrD,KAAK,QAAQ;AAAA,UAAQ,KAAK,QAAQ;AAChC,mBAAO,QAAQ,KAAK;AACpB,gBAAI,WAAW,SAAS,OAAO;AAAE,mBAAK,WAAW;AAAA,YAAG;AACpD,mBAAO,KAAK,kBAAkB,MAAM,IAAI;AAAA,UAC1C,KAAK,QAAQ;AAAQ,mBAAO,KAAK,oBAAoB,IAAI;AAAA,UACzD,KAAK,QAAQ;AAAO,mBAAO,KAAK,mBAAmB,IAAI;AAAA,UACvD,KAAK,QAAQ;AAAQ,mBAAO,KAAK,WAAW,MAAM,IAAI;AAAA,UACtD,KAAK,QAAQ;AAAM,mBAAO,KAAK,oBAAoB,IAAI;AAAA,UACvD,KAAK,QAAQ;AAAA,UACb,KAAK,QAAQ;AACX,gBAAI,KAAK,QAAQ,cAAc,MAAM,cAAc,QAAQ,SAAS;AAClE,6BAAe,YAAY,KAAK;AAChC,kBAAI,OAAO,eAAe,KAAK,KAAK,KAAK;AACzC,kBAAI,OAAO,KAAK,MAAM,KAAK,CAAC,EAAE,QAAQ,SAAS,KAAK,MAAM,WAAW,IAAI;AACzE,kBAAI,WAAW,MAAM,WAAW,IAC9B;AAAE,uBAAO,KAAK,yBAAyB,MAAM,KAAK,gBAAgB,CAAC;AAAA,cAAE;AAAA,YACzE;AAEA,gBAAI,CAAC,KAAK,QAAQ,6BAA6B;AAC7C,kBAAI,CAAC,UACH;AAAE,qBAAK,MAAM,KAAK,OAAO,wDAAwD;AAAA,cAAG;AACtF,kBAAI,CAAC,KAAK,UACR;AAAE,qBAAK,MAAM,KAAK,OAAO,iEAAiE;AAAA,cAAG;AAAA,YACjG;AACA,mBAAO,cAAc,QAAQ,UAAU,KAAK,YAAY,IAAI,IAAI,KAAK,YAAY,MAAMA,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOhG;AACE,gBAAI,KAAK,gBAAgB,GAAG;AAC1B,kBAAI,SAAS;AAAE,qBAAK,WAAW;AAAA,cAAG;AAClC,mBAAK,KAAK;AACV,qBAAO,KAAK,uBAAuB,MAAM,MAAM,CAAC,OAAO;AAAA,YACzD;AAEA,gBAAI,YAAY,KAAK,aAAa,KAAK,IAAI,gBAAgB,KAAK,QAAQ,KAAK,IAAI,UAAU;AAC3F,gBAAI,WAAW;AACb,kBAAI,YAAY,KAAK,QAAQ,eAAe,UAAU;AACpD,qBAAK,MAAM,KAAK,OAAO,+EAA+E;AAAA,cACxG;AACA,kBAAI,cAAc,eAAe;AAC/B,oBAAI,CAAC,KAAK,UAAU;AAClB,uBAAK,MAAM,KAAK,OAAO,qDAAqD;AAAA,gBAC9E;AACA,qBAAK,KAAK;AAAA,cACZ;AACA,mBAAK,KAAK;AACV,mBAAK,SAAS,MAAM,OAAO,SAAS;AACpC,mBAAK,UAAU;AACf,qBAAO,KAAK,WAAW,MAAM,qBAAqB;AAAA,YACpD;AAEA,gBAAI,YAAY,KAAK,OAAO,OAAO,KAAK,gBAAgB;AACxD,gBAAI,cAAc,QAAQ,QAAQ,KAAK,SAAS,gBAAgB,KAAK,IAAI,QAAQ,KAAK,GACpF;AAAE,qBAAO,KAAK,sBAAsB,MAAM,WAAW,MAAM,OAAO;AAAA,YAAE,OACjE;AAAE,qBAAO,KAAK,yBAAyB,MAAM,IAAI;AAAA,YAAE;AAAA,QAC1D;AAAA,MACF;AAEA,WAAK,8BAA8B,SAAS,MAAM,SAAS;AACzD,YAAI,UAAU,YAAY;AAC1B,aAAK,KAAK;AACV,YAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,KAAK,gBAAgB,GAAG;AAAE,eAAK,QAAQ;AAAA,QAAM,WAClE,KAAK,SAAS,QAAQ,MAAM;AAAE,eAAK,WAAW;AAAA,QAAG,OACrD;AACH,eAAK,QAAQ,KAAK,WAAW;AAC7B,eAAK,UAAU;AAAA,QACjB;AAIA,YAAIC,KAAI;AACR,eAAOA,KAAI,KAAK,OAAO,QAAQ,EAAEA,IAAG;AAClC,cAAI,MAAM,KAAK,OAAOA,EAAC;AACvB,cAAI,KAAK,SAAS,QAAQ,IAAI,SAAS,KAAK,MAAM,MAAM;AACtD,gBAAI,IAAI,QAAQ,SAAS,WAAW,IAAI,SAAS,SAAS;AAAE;AAAA,YAAM;AAClE,gBAAI,KAAK,SAAS,SAAS;AAAE;AAAA,YAAM;AAAA,UACrC;AAAA,QACF;AACA,YAAIA,OAAM,KAAK,OAAO,QAAQ;AAAE,eAAK,MAAM,KAAK,OAAO,iBAAiB,OAAO;AAAA,QAAG;AAClF,eAAO,KAAK,WAAW,MAAM,UAAU,mBAAmB,mBAAmB;AAAA,MAC/E;AAEA,WAAK,yBAAyB,SAAS,MAAM;AAC3C,aAAK,KAAK;AACV,aAAK,UAAU;AACf,eAAO,KAAK,WAAW,MAAM,mBAAmB;AAAA,MAClD;AAEA,WAAK,mBAAmB,SAAS,MAAM;AACrC,aAAK,KAAK;AACV,aAAK,OAAO,KAAK,SAAS;AAC1B,aAAK,OAAO,KAAK,eAAe,IAAI;AACpC,aAAK,OAAO,IAAI;AAChB,aAAK,OAAO,QAAQ,MAAM;AAC1B,aAAK,OAAO,KAAK,qBAAqB;AACtC,YAAI,KAAK,QAAQ,eAAe,GAC9B;AAAE,eAAK,IAAI,QAAQ,IAAI;AAAA,QAAG,OAE1B;AAAE,eAAK,UAAU;AAAA,QAAG;AACtB,eAAO,KAAK,WAAW,MAAM,kBAAkB;AAAA,MACjD;AAUA,WAAK,oBAAoB,SAAS,MAAM;AACtC,aAAK,KAAK;AACV,YAAI,UAAW,KAAK,QAAQ,eAAe,KAAK,KAAK,YAAY,KAAK,cAAc,OAAO,IAAK,KAAK,eAAe;AACpH,aAAK,OAAO,KAAK,SAAS;AAC1B,aAAK,WAAW,CAAC;AACjB,aAAK,OAAO,QAAQ,MAAM;AAC1B,YAAI,KAAK,SAAS,QAAQ,MAAM;AAC9B,cAAI,UAAU,IAAI;AAAE,iBAAK,WAAW,OAAO;AAAA,UAAG;AAC9C,iBAAO,KAAK,SAAS,MAAM,IAAI;AAAA,QACjC;AACA,YAAI,QAAQ,KAAK,MAAM;AACvB,YAAI,KAAK,SAAS,QAAQ,QAAQ,KAAK,SAAS,QAAQ,UAAU,OAAO;AACvE,cAAI,SAAS,KAAK,UAAU,GAAG,OAAO,QAAQ,QAAQ,KAAK;AAC3D,eAAK,KAAK;AACV,eAAK,SAAS,QAAQ,MAAM,IAAI;AAChC,eAAK,WAAW,QAAQ,qBAAqB;AAC7C,iBAAO,KAAK,kBAAkB,MAAM,QAAQ,OAAO;AAAA,QACrD;AACA,YAAI,gBAAgB,KAAK,aAAa,KAAK,GAAG,UAAU;AAExD,YAAI,YAAY,KAAK,QAAQ,IAAI,IAAI,UAAU,KAAK,aAAa,IAAI,IAAI,gBAAgB;AACzF,YAAI,WAAW;AACb,cAAI,SAAS,KAAK,UAAU;AAC5B,eAAK,KAAK;AACV,cAAI,cAAc,eAAe;AAAE,iBAAK,KAAK;AAAA,UAAG;AAChD,eAAK,SAAS,QAAQ,MAAM,SAAS;AACrC,eAAK,WAAW,QAAQ,qBAAqB;AAC7C,iBAAO,KAAK,kBAAkB,MAAM,QAAQ,OAAO;AAAA,QACrD;AACA,YAAI,cAAc,KAAK;AACvB,YAAI,yBAAyB,IAAI;AACjC,YAAI,UAAU,KAAK;AACnB,YAAI,OAAO,UAAU,KACjB,KAAK,oBAAoB,wBAAwB,OAAO,IACxD,KAAK,gBAAgB,MAAM,sBAAsB;AACrD,YAAI,KAAK,SAAS,QAAQ,QAAQ,UAAU,KAAK,QAAQ,eAAe,KAAK,KAAK,aAAa,IAAI,IAAI;AACrG,cAAI,UAAU,IAAI;AAChB,gBAAI,KAAK,SAAS,QAAQ,KAAK;AAAE,mBAAK,WAAW,OAAO;AAAA,YAAG;AAC3D,iBAAK,QAAQ;AAAA,UACf,WAAW,WAAW,KAAK,QAAQ,eAAe,GAAG;AACnD,gBAAI,KAAK,UAAU,WAAW,CAAC,eAAe,KAAK,SAAS,gBAAgB,KAAK,SAAS,SAAS;AAAE,mBAAK,WAAW;AAAA,YAAG,WAC/G,KAAK,QAAQ,eAAe,GAAG;AAAE,mBAAK,QAAQ;AAAA,YAAO;AAAA,UAChE;AACA,cAAI,iBAAiB,SAAS;AAAE,iBAAK,MAAM,KAAK,OAAO,+DAA+D;AAAA,UAAG;AACzH,eAAK,aAAa,MAAM,OAAO,sBAAsB;AACrD,eAAK,iBAAiB,IAAI;AAC1B,iBAAO,KAAK,WAAW,MAAM,IAAI;AAAA,QACnC,OAAO;AACL,eAAK,sBAAsB,wBAAwB,IAAI;AAAA,QACzD;AACA,YAAI,UAAU,IAAI;AAAE,eAAK,WAAW,OAAO;AAAA,QAAG;AAC9C,eAAO,KAAK,SAAS,MAAM,IAAI;AAAA,MACjC;AAGA,WAAK,oBAAoB,SAAS,MAAM,MAAM,SAAS;AACrD,aAAK,KAAK,SAAS,QAAQ,OAAQ,KAAK,QAAQ,eAAe,KAAK,KAAK,aAAa,IAAI,MAAO,KAAK,aAAa,WAAW,GAAG;AAC/H,cAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,gBAAI,KAAK,SAAS,QAAQ,KAAK;AAC7B,kBAAI,UAAU,IAAI;AAAE,qBAAK,WAAW,OAAO;AAAA,cAAG;AAAA,YAChD,OAAO;AAAE,mBAAK,QAAQ,UAAU;AAAA,YAAI;AAAA,UACtC;AACA,iBAAO,KAAK,WAAW,MAAM,IAAI;AAAA,QACnC;AACA,YAAI,UAAU,IAAI;AAAE,eAAK,WAAW,OAAO;AAAA,QAAG;AAC9C,eAAO,KAAK,SAAS,MAAM,IAAI;AAAA,MACjC;AAEA,WAAK,yBAAyB,SAAS,MAAM,SAAS,qBAAqB;AACzE,aAAK,KAAK;AACV,eAAO,KAAK,cAAc,MAAM,kBAAkB,sBAAsB,IAAI,yBAAyB,OAAO,OAAO;AAAA,MACrH;AAEA,WAAK,mBAAmB,SAAS,MAAM;AACrC,aAAK,KAAK;AACV,aAAK,OAAO,KAAK,qBAAqB;AAEtC,aAAK,aAAa,KAAK,eAAe,IAAI;AAC1C,aAAK,YAAY,KAAK,IAAI,QAAQ,KAAK,IAAI,KAAK,eAAe,IAAI,IAAI;AACvE,eAAO,KAAK,WAAW,MAAM,aAAa;AAAA,MAC5C;AAEA,WAAK,uBAAuB,SAAS,MAAM;AACzC,YAAI,CAAC,KAAK,cAAc,CAAC,KAAK,QAAQ,4BACpC;AAAE,eAAK,MAAM,KAAK,OAAO,8BAA8B;AAAA,QAAG;AAC5D,aAAK,KAAK;AAMV,YAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,KAAK,gBAAgB,GAAG;AAAE,eAAK,WAAW;AAAA,QAAM,OACzE;AAAE,eAAK,WAAW,KAAK,gBAAgB;AAAG,eAAK,UAAU;AAAA,QAAG;AACjE,eAAO,KAAK,WAAW,MAAM,iBAAiB;AAAA,MAChD;AAEA,WAAK,uBAAuB,SAAS,MAAM;AACzC,aAAK,KAAK;AACV,aAAK,eAAe,KAAK,qBAAqB;AAC9C,aAAK,QAAQ,CAAC;AACd,aAAK,OAAO,QAAQ,MAAM;AAC1B,aAAK,OAAO,KAAK,WAAW;AAC5B,aAAK,WAAW,CAAC;AAMjB,YAAI;AACJ,iBAAS,aAAa,OAAO,KAAK,SAAS,QAAQ,UAAS;AAC1D,cAAI,KAAK,SAAS,QAAQ,SAAS,KAAK,SAAS,QAAQ,UAAU;AACjE,gBAAI,SAAS,KAAK,SAAS,QAAQ;AACnC,gBAAI,KAAK;AAAE,mBAAK,WAAW,KAAK,YAAY;AAAA,YAAG;AAC/C,iBAAK,MAAM,KAAK,MAAM,KAAK,UAAU,CAAC;AACtC,gBAAI,aAAa,CAAC;AAClB,iBAAK,KAAK;AACV,gBAAI,QAAQ;AACV,kBAAI,OAAO,KAAK,gBAAgB;AAAA,YAClC,OAAO;AACL,kBAAI,YAAY;AAAE,qBAAK,iBAAiB,KAAK,cAAc,0BAA0B;AAAA,cAAG;AACxF,2BAAa;AACb,kBAAI,OAAO;AAAA,YACb;AACA,iBAAK,OAAO,QAAQ,KAAK;AAAA,UAC3B,OAAO;AACL,gBAAI,CAAC,KAAK;AAAE,mBAAK,WAAW;AAAA,YAAG;AAC/B,gBAAI,WAAW,KAAK,KAAK,eAAe,IAAI,CAAC;AAAA,UAC/C;AAAA,QACF;AACA,aAAK,UAAU;AACf,YAAI,KAAK;AAAE,eAAK,WAAW,KAAK,YAAY;AAAA,QAAG;AAC/C,aAAK,KAAK;AACV,aAAK,OAAO,IAAI;AAChB,eAAO,KAAK,WAAW,MAAM,iBAAiB;AAAA,MAChD;AAEA,WAAK,sBAAsB,SAAS,MAAM;AACxC,aAAK,KAAK;AACV,YAAI,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,KAAK,CAAC,GAC9D;AAAE,eAAK,MAAM,KAAK,YAAY,6BAA6B;AAAA,QAAG;AAChE,aAAK,WAAW,KAAK,gBAAgB;AACrC,aAAK,UAAU;AACf,eAAO,KAAK,WAAW,MAAM,gBAAgB;AAAA,MAC/C;AAIA,UAAI,UAAU,CAAC;AAEf,WAAK,wBAAwB,WAAW;AACtC,YAAI,QAAQ,KAAK,iBAAiB;AAClC,YAAI,SAAS,MAAM,SAAS;AAC5B,aAAK,WAAW,SAAS,qBAAqB,CAAC;AAC/C,aAAK,iBAAiB,OAAO,SAAS,oBAAoB,YAAY;AACtE,aAAK,OAAO,QAAQ,MAAM;AAE1B,eAAO;AAAA,MACT;AAEA,WAAK,oBAAoB,SAAS,MAAM;AACtC,aAAK,KAAK;AACV,aAAK,QAAQ,KAAK,WAAW;AAC7B,aAAK,UAAU;AACf,YAAI,KAAK,SAAS,QAAQ,QAAQ;AAChC,cAAI,SAAS,KAAK,UAAU;AAC5B,eAAK,KAAK;AACV,cAAI,KAAK,IAAI,QAAQ,MAAM,GAAG;AAC5B,mBAAO,QAAQ,KAAK,sBAAsB;AAAA,UAC5C,OAAO;AACL,gBAAI,KAAK,QAAQ,cAAc,IAAI;AAAE,mBAAK,WAAW;AAAA,YAAG;AACxD,mBAAO,QAAQ;AACf,iBAAK,WAAW,CAAC;AAAA,UACnB;AACA,iBAAO,OAAO,KAAK,WAAW,KAAK;AACnC,eAAK,UAAU;AACf,eAAK,UAAU,KAAK,WAAW,QAAQ,aAAa;AAAA,QACtD;AACA,aAAK,YAAY,KAAK,IAAI,QAAQ,QAAQ,IAAI,KAAK,WAAW,IAAI;AAClE,YAAI,CAAC,KAAK,WAAW,CAAC,KAAK,WACzB;AAAE,eAAK,MAAM,KAAK,OAAO,iCAAiC;AAAA,QAAG;AAC/D,eAAO,KAAK,WAAW,MAAM,cAAc;AAAA,MAC7C;AAEA,WAAK,oBAAoB,SAAS,MAAM,MAAM,yBAAyB;AACrE,aAAK,KAAK;AACV,aAAK,SAAS,MAAM,OAAO,MAAM,uBAAuB;AACxD,aAAK,UAAU;AACf,eAAO,KAAK,WAAW,MAAM,qBAAqB;AAAA,MACpD;AAEA,WAAK,sBAAsB,SAAS,MAAM;AACxC,aAAK,KAAK;AACV,aAAK,OAAO,KAAK,qBAAqB;AACtC,aAAK,OAAO,KAAK,SAAS;AAC1B,aAAK,OAAO,KAAK,eAAe,OAAO;AACvC,aAAK,OAAO,IAAI;AAChB,eAAO,KAAK,WAAW,MAAM,gBAAgB;AAAA,MAC/C;AAEA,WAAK,qBAAqB,SAAS,MAAM;AACvC,YAAI,KAAK,QAAQ;AAAE,eAAK,MAAM,KAAK,OAAO,uBAAuB;AAAA,QAAG;AACpE,aAAK,KAAK;AACV,aAAK,SAAS,KAAK,qBAAqB;AACxC,aAAK,OAAO,KAAK,eAAe,MAAM;AACtC,eAAO,KAAK,WAAW,MAAM,eAAe;AAAA,MAC9C;AAEA,WAAK,sBAAsB,SAAS,MAAM;AACxC,aAAK,KAAK;AACV,eAAO,KAAK,WAAW,MAAM,gBAAgB;AAAA,MAC/C;AAEA,WAAK,wBAAwB,SAAS,MAAM,WAAW,MAAM,SAAS;AACpE,iBAAS,MAAM,GAAGU,QAAO,KAAK,QAAQ,MAAMA,MAAK,QAAQ,OAAO,GAC9D;AACA,cAAI,QAAQA,MAAK,GAAG;AAEpB,cAAI,MAAM,SAAS,WACjB;AAAE,iBAAK,MAAM,KAAK,OAAO,YAAY,YAAY,uBAAuB;AAAA,UAC5E;AAAA,QAAE;AACF,YAAI,OAAO,KAAK,KAAK,SAAS,SAAS,KAAK,SAAS,QAAQ,UAAU,WAAW;AAClF,iBAASV,KAAI,KAAK,OAAO,SAAS,GAAGA,MAAK,GAAGA,MAAK;AAChD,cAAI,UAAU,KAAK,OAAOA,EAAC;AAC3B,cAAI,QAAQ,mBAAmB,KAAK,OAAO;AAEzC,oBAAQ,iBAAiB,KAAK;AAC9B,oBAAQ,OAAO;AAAA,UACjB,OAAO;AAAE;AAAA,UAAM;AAAA,QACjB;AACA,aAAK,OAAO,KAAK,EAAC,MAAM,WAAW,MAAY,gBAAgB,KAAK,MAAK,CAAC;AAC1E,aAAK,OAAO,KAAK,eAAe,UAAU,QAAQ,QAAQ,OAAO,MAAM,KAAK,UAAU,UAAU,UAAU,OAAO;AACjH,aAAK,OAAO,IAAI;AAChB,aAAK,QAAQ;AACb,eAAO,KAAK,WAAW,MAAM,kBAAkB;AAAA,MACjD;AAEA,WAAK,2BAA2B,SAAS,MAAM,MAAM;AACnD,aAAK,aAAa;AAClB,aAAK,UAAU;AACf,eAAO,KAAK,WAAW,MAAM,qBAAqB;AAAA,MACpD;AAMA,WAAK,aAAa,SAAS,uBAAuB,MAAM,YAAY;AAClE,YAAK,0BAA0B,OAAS,yBAAwB;AAChE,YAAK,SAAS,OAAS,QAAO,KAAK,UAAU;AAE7C,aAAK,OAAO,CAAC;AACb,aAAK,OAAO,QAAQ,MAAM;AAC1B,YAAI,uBAAuB;AAAE,eAAK,WAAW,CAAC;AAAA,QAAG;AACjD,eAAO,KAAK,SAAS,QAAQ,QAAQ;AACnC,cAAI,OAAO,KAAK,eAAe,IAAI;AACnC,eAAK,KAAK,KAAK,IAAI;AAAA,QACrB;AACA,YAAI,YAAY;AAAE,eAAK,SAAS;AAAA,QAAO;AACvC,aAAK,KAAK;AACV,YAAI,uBAAuB;AAAE,eAAK,UAAU;AAAA,QAAG;AAC/C,eAAO,KAAK,WAAW,MAAM,gBAAgB;AAAA,MAC/C;AAMA,WAAK,WAAW,SAAS,MAAM,MAAM;AACnC,aAAK,OAAO;AACZ,aAAK,OAAO,QAAQ,IAAI;AACxB,aAAK,OAAO,KAAK,SAAS,QAAQ,OAAO,OAAO,KAAK,gBAAgB;AACrE,aAAK,OAAO,QAAQ,IAAI;AACxB,aAAK,SAAS,KAAK,SAAS,QAAQ,SAAS,OAAO,KAAK,gBAAgB;AACzE,aAAK,OAAO,QAAQ,MAAM;AAC1B,aAAK,OAAO,KAAK,eAAe,KAAK;AACrC,aAAK,UAAU;AACf,aAAK,OAAO,IAAI;AAChB,eAAO,KAAK,WAAW,MAAM,cAAc;AAAA,MAC7C;AAKA,WAAK,aAAa,SAAS,MAAM,MAAM;AACrC,YAAI,UAAU,KAAK,SAAS,QAAQ;AACpC,aAAK,KAAK;AAEV,YACE,KAAK,SAAS,yBACd,KAAK,aAAa,CAAC,EAAE,QAAQ,SAE3B,CAAC,WACD,KAAK,QAAQ,cAAc,KAC3B,KAAK,UACL,KAAK,SAAS,SACd,KAAK,aAAa,CAAC,EAAE,GAAG,SAAS,eAEnC;AACA,eAAK;AAAA,YACH,KAAK;AAAA,aACH,UAAU,WAAW,YAAY;AAAA,UACrC;AAAA,QACF;AACA,aAAK,OAAO;AACZ,aAAK,QAAQ,UAAU,KAAK,gBAAgB,IAAI,KAAK,iBAAiB;AACtE,aAAK,OAAO,QAAQ,MAAM;AAC1B,aAAK,OAAO,KAAK,eAAe,KAAK;AACrC,aAAK,UAAU;AACf,aAAK,OAAO,IAAI;AAChB,eAAO,KAAK,WAAW,MAAM,UAAU,mBAAmB,gBAAgB;AAAA,MAC5E;AAIA,WAAK,WAAW,SAAS,MAAM,OAAO,MAAM,yBAAyB;AACnE,aAAK,eAAe,CAAC;AACrB,aAAK,OAAO;AACZ,mBAAS;AACP,cAAI,OAAO,KAAK,UAAU;AAC1B,eAAK,WAAW,MAAM,IAAI;AAC1B,cAAI,KAAK,IAAI,QAAQ,EAAE,GAAG;AACxB,iBAAK,OAAO,KAAK,iBAAiB,KAAK;AAAA,UACzC,WAAW,CAAC,2BAA2B,SAAS,WAAW,EAAE,KAAK,SAAS,QAAQ,OAAQ,KAAK,QAAQ,eAAe,KAAK,KAAK,aAAa,IAAI,IAAK;AACrJ,iBAAK,WAAW;AAAA,UAClB,WAAW,CAAC,4BAA4B,SAAS,WAAW,SAAS,kBAAkB,KAAK,QAAQ,eAAe,MAAM,KAAK,SAAS,QAAQ,OAAO,CAAC,KAAK,aAAa,IAAI,GAAG;AAC9K,iBAAK,MAAM,KAAK,YAAa,4BAA4B,OAAO,cAAe;AAAA,UACjF,WAAW,CAAC,2BAA2B,KAAK,GAAG,SAAS,gBAAgB,EAAE,UAAU,KAAK,SAAS,QAAQ,OAAO,KAAK,aAAa,IAAI,KAAK;AAC1I,iBAAK,MAAM,KAAK,YAAY,0DAA0D;AAAA,UACxF,OAAO;AACL,iBAAK,OAAO;AAAA,UACd;AACA,eAAK,aAAa,KAAK,KAAK,WAAW,MAAM,oBAAoB,CAAC;AAClE,cAAI,CAAC,KAAK,IAAI,QAAQ,KAAK,GAAG;AAAE;AAAA,UAAM;AAAA,QACxC;AACA,eAAO;AAAA,MACT;AAEA,WAAK,aAAa,SAAS,MAAM,MAAM;AACrC,aAAK,KAAK,SAAS,WAAW,SAAS,gBACnC,KAAK,WAAW,IAChB,KAAK,iBAAiB;AAE1B,aAAK,iBAAiB,KAAK,IAAI,SAAS,QAAQ,WAAW,cAAc,KAAK;AAAA,MAChF;AAEA,UAAI,iBAAiB,GAAG,yBAAyB,GAAG,mBAAmB;AAMvE,WAAK,gBAAgB,SAAS,MAAM,WAAW,qBAAqB,SAAS,SAAS;AACpF,aAAK,aAAa,IAAI;AACtB,YAAI,KAAK,QAAQ,eAAe,KAAK,KAAK,QAAQ,eAAe,KAAK,CAAC,SAAS;AAC9E,cAAI,KAAK,SAAS,QAAQ,QAAS,YAAY,wBAC7C;AAAE,iBAAK,WAAW;AAAA,UAAG;AACvB,eAAK,YAAY,KAAK,IAAI,QAAQ,IAAI;AAAA,QACxC;AACA,YAAI,KAAK,QAAQ,eAAe,GAC9B;AAAE,eAAK,QAAQ,CAAC,CAAC;AAAA,QAAS;AAE5B,YAAI,YAAY,gBAAgB;AAC9B,eAAK,KAAM,YAAY,oBAAqB,KAAK,SAAS,QAAQ,OAAO,OAAO,KAAK,WAAW;AAChG,cAAI,KAAK,MAAM,EAAE,YAAY,yBAK3B;AAAE,iBAAK,gBAAgB,KAAK,IAAK,KAAK,UAAU,KAAK,aAAa,KAAK,QAAS,KAAK,sBAAsB,WAAW,eAAe,aAAa;AAAA,UAAG;AAAA,QACzJ;AAEA,YAAI,cAAc,KAAK,UAAU,cAAc,KAAK,UAAU,mBAAmB,KAAK;AACtF,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,gBAAgB;AACrB,aAAK,WAAW,cAAc,KAAK,OAAO,KAAK,SAAS,CAAC;AAEzD,YAAI,EAAE,YAAY,iBAChB;AAAE,eAAK,KAAK,KAAK,SAAS,QAAQ,OAAO,KAAK,WAAW,IAAI;AAAA,QAAM;AAErE,aAAK,oBAAoB,IAAI;AAC7B,aAAK,kBAAkB,MAAM,qBAAqB,OAAO,OAAO;AAEhE,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,gBAAgB;AACrB,eAAO,KAAK,WAAW,MAAO,YAAY,iBAAkB,wBAAwB,oBAAoB;AAAA,MAC1G;AAEA,WAAK,sBAAsB,SAAS,MAAM;AACxC,aAAK,OAAO,QAAQ,MAAM;AAC1B,aAAK,SAAS,KAAK,iBAAiB,QAAQ,QAAQ,OAAO,KAAK,QAAQ,eAAe,CAAC;AACxF,aAAK,+BAA+B;AAAA,MACtC;AAKA,WAAK,aAAa,SAAS,MAAM,aAAa;AAC5C,aAAK,KAAK;AAIV,YAAI,YAAY,KAAK;AACrB,aAAK,SAAS;AAEd,aAAK,aAAa,MAAM,WAAW;AACnC,aAAK,gBAAgB,IAAI;AACzB,YAAI,iBAAiB,KAAK,eAAe;AACzC,YAAI,YAAY,KAAK,UAAU;AAC/B,YAAI,iBAAiB;AACrB,kBAAU,OAAO,CAAC;AAClB,aAAK,OAAO,QAAQ,MAAM;AAC1B,eAAO,KAAK,SAAS,QAAQ,QAAQ;AACnC,cAAI,UAAU,KAAK,kBAAkB,KAAK,eAAe,IAAI;AAC7D,cAAI,SAAS;AACX,sBAAU,KAAK,KAAK,OAAO;AAC3B,gBAAI,QAAQ,SAAS,sBAAsB,QAAQ,SAAS,eAAe;AACzE,kBAAI,gBAAgB;AAAE,qBAAK,iBAAiB,QAAQ,OAAO,yCAAyC;AAAA,cAAG;AACvG,+BAAiB;AAAA,YACnB,WAAW,QAAQ,OAAO,QAAQ,IAAI,SAAS,uBAAuB,wBAAwB,gBAAgB,OAAO,GAAG;AACtH,mBAAK,iBAAiB,QAAQ,IAAI,OAAQ,kBAAmB,QAAQ,IAAI,OAAQ,6BAA8B;AAAA,YACjH;AAAA,UACF;AAAA,QACF;AACA,aAAK,SAAS;AACd,aAAK,KAAK;AACV,aAAK,OAAO,KAAK,WAAW,WAAW,WAAW;AAClD,aAAK,cAAc;AACnB,eAAO,KAAK,WAAW,MAAM,cAAc,qBAAqB,iBAAiB;AAAA,MACnF;AAEA,WAAK,oBAAoB,SAAS,wBAAwB;AACxD,YAAI,KAAK,IAAI,QAAQ,IAAI,GAAG;AAAE,iBAAO;AAAA,QAAK;AAE1C,YAAIW,eAAc,KAAK,QAAQ;AAC/B,YAAI,OAAO,KAAK,UAAU;AAC1B,YAAI,UAAU;AACd,YAAI,cAAc;AAClB,YAAI,UAAU;AACd,YAAI,OAAO;AACX,YAAI,WAAW;AAEf,YAAI,KAAK,cAAc,QAAQ,GAAG;AAEhC,cAAIA,gBAAe,MAAM,KAAK,IAAI,QAAQ,MAAM,GAAG;AACjD,iBAAK,sBAAsB,IAAI;AAC/B,mBAAO;AAAA,UACT;AACA,cAAI,KAAK,wBAAwB,KAAK,KAAK,SAAS,QAAQ,MAAM;AAChE,uBAAW;AAAA,UACb,OAAO;AACL,sBAAU;AAAA,UACZ;AAAA,QACF;AACA,aAAK,SAAS;AACd,YAAI,CAAC,WAAWA,gBAAe,KAAK,KAAK,cAAc,OAAO,GAAG;AAC/D,eAAK,KAAK,wBAAwB,KAAK,KAAK,SAAS,QAAQ,SAAS,CAAC,KAAK,mBAAmB,GAAG;AAChG,sBAAU;AAAA,UACZ,OAAO;AACL,sBAAU;AAAA,UACZ;AAAA,QACF;AACA,YAAI,CAAC,YAAYA,gBAAe,KAAK,CAAC,YAAY,KAAK,IAAI,QAAQ,IAAI,GAAG;AACxE,wBAAc;AAAA,QAChB;AACA,YAAI,CAAC,WAAW,CAAC,WAAW,CAAC,aAAa;AACxC,cAAI,YAAY,KAAK;AACrB,cAAI,KAAK,cAAc,KAAK,KAAK,KAAK,cAAc,KAAK,GAAG;AAC1D,gBAAI,KAAK,wBAAwB,GAAG;AAClC,qBAAO;AAAA,YACT,OAAO;AACL,wBAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AAGA,YAAI,SAAS;AAGX,eAAK,WAAW;AAChB,eAAK,MAAM,KAAK,YAAY,KAAK,cAAc,KAAK,eAAe;AACnE,eAAK,IAAI,OAAO;AAChB,eAAK,WAAW,KAAK,KAAK,YAAY;AAAA,QACxC,OAAO;AACL,eAAK,sBAAsB,IAAI;AAAA,QACjC;AAGA,YAAIA,eAAc,MAAM,KAAK,SAAS,QAAQ,UAAU,SAAS,YAAY,eAAe,SAAS;AACnG,cAAI,gBAAgB,CAAC,KAAK,UAAU,aAAa,MAAM,aAAa;AACpE,cAAI,oBAAoB,iBAAiB;AAEzC,cAAI,iBAAiB,SAAS,UAAU;AAAE,iBAAK,MAAM,KAAK,IAAI,OAAO,yCAAyC;AAAA,UAAG;AACjH,eAAK,OAAO,gBAAgB,gBAAgB;AAC5C,eAAK,iBAAiB,MAAM,aAAa,SAAS,iBAAiB;AAAA,QACrE,OAAO;AACL,eAAK,gBAAgB,IAAI;AAAA,QAC3B;AAEA,eAAO;AAAA,MACT;AAEA,WAAK,0BAA0B,WAAW;AACxC,eACE,KAAK,SAAS,QAAQ,QACtB,KAAK,SAAS,QAAQ,aACtB,KAAK,SAAS,QAAQ,OACtB,KAAK,SAAS,QAAQ,UACtB,KAAK,SAAS,QAAQ,YACtB,KAAK,KAAK;AAAA,MAEd;AAEA,WAAK,wBAAwB,SAAS,SAAS;AAC7C,YAAI,KAAK,SAAS,QAAQ,WAAW;AACnC,cAAI,KAAK,UAAU,eAAe;AAChC,iBAAK,MAAM,KAAK,OAAO,oDAAoD;AAAA,UAC7E;AACA,kBAAQ,WAAW;AACnB,kBAAQ,MAAM,KAAK,kBAAkB;AAAA,QACvC,OAAO;AACL,eAAK,kBAAkB,OAAO;AAAA,QAChC;AAAA,MACF;AAEA,WAAK,mBAAmB,SAAS,QAAQ,aAAa,SAAS,mBAAmB;AAEhF,YAAI,MAAM,OAAO;AACjB,YAAI,OAAO,SAAS,eAAe;AACjC,cAAI,aAAa;AAAE,iBAAK,MAAM,IAAI,OAAO,kCAAkC;AAAA,UAAG;AAC9E,cAAI,SAAS;AAAE,iBAAK,MAAM,IAAI,OAAO,sCAAsC;AAAA,UAAG;AAAA,QAChF,WAAW,OAAO,UAAU,aAAa,QAAQ,WAAW,GAAG;AAC7D,eAAK,MAAM,IAAI,OAAO,wDAAwD;AAAA,QAChF;AAGA,YAAI,QAAQ,OAAO,QAAQ,KAAK,YAAY,aAAa,SAAS,iBAAiB;AAGnF,YAAI,OAAO,SAAS,SAAS,MAAM,OAAO,WAAW,GACnD;AAAE,eAAK,iBAAiB,MAAM,OAAO,8BAA8B;AAAA,QAAG;AACxE,YAAI,OAAO,SAAS,SAAS,MAAM,OAAO,WAAW,GACnD;AAAE,eAAK,iBAAiB,MAAM,OAAO,sCAAsC;AAAA,QAAG;AAChF,YAAI,OAAO,SAAS,SAAS,MAAM,OAAO,CAAC,EAAE,SAAS,eACpD;AAAE,eAAK,iBAAiB,MAAM,OAAO,CAAC,EAAE,OAAO,+BAA+B;AAAA,QAAG;AAEnF,eAAO,KAAK,WAAW,QAAQ,kBAAkB;AAAA,MACnD;AAEA,WAAK,kBAAkB,SAAS,OAAO;AACrC,YAAI,aAAa,OAAO,aAAa,GAAG;AACtC,eAAK,MAAM,MAAM,IAAI,OAAO,gDAAgD;AAAA,QAC9E,WAAW,MAAM,UAAU,aAAa,OAAO,WAAW,GAAG;AAC3D,eAAK,MAAM,MAAM,IAAI,OAAO,qDAAqD;AAAA,QACnF;AAEA,YAAI,KAAK,IAAI,QAAQ,EAAE,GAAG;AAExB,eAAK,WAAW,yBAAyB,WAAW;AACpD,gBAAM,QAAQ,KAAK,iBAAiB;AACpC,eAAK,UAAU;AAAA,QACjB,OAAO;AACL,gBAAM,QAAQ;AAAA,QAChB;AACA,aAAK,UAAU;AAEf,eAAO,KAAK,WAAW,OAAO,oBAAoB;AAAA,MACpD;AAEA,WAAK,wBAAwB,SAAS,MAAM;AAC1C,aAAK,OAAO,CAAC;AAEb,YAAI,YAAY,KAAK;AACrB,aAAK,SAAS,CAAC;AACf,aAAK,WAAW,2BAA2B,WAAW;AACtD,eAAO,KAAK,SAAS,QAAQ,QAAQ;AACnC,cAAI,OAAO,KAAK,eAAe,IAAI;AACnC,eAAK,KAAK,KAAK,IAAI;AAAA,QACrB;AACA,aAAK,KAAK;AACV,aAAK,UAAU;AACf,aAAK,SAAS;AAEd,eAAO,KAAK,WAAW,MAAM,aAAa;AAAA,MAC5C;AAEA,WAAK,eAAe,SAAS,MAAM,aAAa;AAC9C,YAAI,KAAK,SAAS,QAAQ,MAAM;AAC9B,eAAK,KAAK,KAAK,WAAW;AAC1B,cAAI,aACF;AAAE,iBAAK,gBAAgB,KAAK,IAAI,cAAc,KAAK;AAAA,UAAG;AAAA,QAC1D,OAAO;AACL,cAAI,gBAAgB,MAClB;AAAE,iBAAK,WAAW;AAAA,UAAG;AACvB,eAAK,KAAK;AAAA,QACZ;AAAA,MACF;AAEA,WAAK,kBAAkB,SAAS,MAAM;AACpC,aAAK,aAAa,KAAK,IAAI,QAAQ,QAAQ,IAAI,KAAK,oBAAoB,MAAM,KAAK,IAAI;AAAA,MACzF;AAEA,WAAK,iBAAiB,WAAW;AAC/B,YAAI,UAAU,EAAC,UAAU,uBAAO,OAAO,IAAI,GAAG,MAAM,CAAC,EAAC;AACtD,aAAK,iBAAiB,KAAK,OAAO;AAClC,eAAO,QAAQ;AAAA,MACjB;AAEA,WAAK,gBAAgB,WAAW;AAC9B,YAAIL,OAAM,KAAK,iBAAiB,IAAI;AACpC,YAAI,WAAWA,KAAI;AACnB,YAAI,OAAOA,KAAI;AACf,YAAI,CAAC,KAAK,QAAQ,oBAAoB;AAAE;AAAA,QAAO;AAC/C,YAAI,MAAM,KAAK,iBAAiB;AAChC,YAAI,SAAS,QAAQ,IAAI,OAAO,KAAK,iBAAiB,MAAM,CAAC;AAC7D,iBAASN,KAAI,GAAGA,KAAI,KAAK,QAAQ,EAAEA,IAAG;AACpC,cAAI,KAAK,KAAKA,EAAC;AACf,cAAI,CAAC,OAAO,UAAU,GAAG,IAAI,GAAG;AAC9B,gBAAI,QAAQ;AACV,qBAAO,KAAK,KAAK,EAAE;AAAA,YACrB,OAAO;AACL,mBAAK,iBAAiB,GAAG,OAAQ,qBAAsB,GAAG,OAAQ,0CAA2C;AAAA,YAC/G;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,eAAS,wBAAwB,gBAAgB,SAAS;AACxD,YAAI,OAAO,QAAQ,IAAI;AACvB,YAAI,OAAO,eAAe,IAAI;AAE9B,YAAI,OAAO;AACX,YAAI,QAAQ,SAAS,uBAAuB,QAAQ,SAAS,SAAS,QAAQ,SAAS,QAAQ;AAC7F,kBAAQ,QAAQ,SAAS,MAAM,OAAO,QAAQ;AAAA,QAChD;AAGA,YACE,SAAS,UAAU,SAAS,UAC5B,SAAS,UAAU,SAAS,UAC5B,SAAS,UAAU,SAAS,UAC5B,SAAS,UAAU,SAAS,QAC5B;AACA,yBAAe,IAAI,IAAI;AACvB,iBAAO;AAAA,QACT,WAAW,CAAC,MAAM;AAChB,yBAAe,IAAI,IAAI;AACvB,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,aAAa,MAAM,MAAM;AAChC,YAAI,WAAW,KAAK;AACpB,YAAI,MAAM,KAAK;AACf,eAAO,CAAC,aACN,IAAI,SAAS,gBAAgB,IAAI,SAAS,QAC1C,IAAI,SAAS,aAAa,IAAI,UAAU;AAAA,MAE5C;AAIA,WAAK,4BAA4B,SAAS,MAAMD,UAAS;AACvD,YAAI,KAAK,QAAQ,eAAe,IAAI;AAClC,cAAI,KAAK,cAAc,IAAI,GAAG;AAC5B,iBAAK,WAAW,KAAK,sBAAsB;AAC3C,iBAAK,YAAYA,UAAS,KAAK,UAAU,KAAK,YAAY;AAAA,UAC5D,OAAO;AACL,iBAAK,WAAW;AAAA,UAClB;AAAA,QACF;AACA,aAAK,iBAAiB,MAAM;AAC5B,YAAI,KAAK,SAAS,QAAQ,QAAQ;AAAE,eAAK,WAAW;AAAA,QAAG;AACvD,aAAK,SAAS,KAAK,cAAc;AACjC,YAAI,KAAK,QAAQ,eAAe,IAC9B;AAAE,eAAK,aAAa,KAAK,gBAAgB;AAAA,QAAG;AAC9C,aAAK,UAAU;AACf,eAAO,KAAK,WAAW,MAAM,sBAAsB;AAAA,MACrD;AAEA,WAAK,cAAc,SAAS,MAAMA,UAAS;AACzC,aAAK,KAAK;AAEV,YAAI,KAAK,IAAI,QAAQ,IAAI,GAAG;AAC1B,iBAAO,KAAK,0BAA0B,MAAMA,QAAO;AAAA,QACrD;AACA,YAAI,KAAK,IAAI,QAAQ,QAAQ,GAAG;AAC9B,eAAK,YAAYA,UAAS,WAAW,KAAK,YAAY;AACtD,eAAK,cAAc,KAAK,8BAA8B;AACtD,iBAAO,KAAK,WAAW,MAAM,0BAA0B;AAAA,QACzD;AAEA,YAAI,KAAK,2BAA2B,GAAG;AACrC,eAAK,cAAc,KAAK,uBAAuB,IAAI;AACnD,cAAI,KAAK,YAAY,SAAS,uBAC5B;AAAE,iBAAK,oBAAoBA,UAAS,KAAK,YAAY,YAAY;AAAA,UAAG,OAEpE;AAAE,iBAAK,YAAYA,UAAS,KAAK,YAAY,IAAI,KAAK,YAAY,GAAG,KAAK;AAAA,UAAG;AAC/E,eAAK,aAAa,CAAC;AACnB,eAAK,SAAS;AACd,cAAI,KAAK,QAAQ,eAAe,IAC9B;AAAE,iBAAK,aAAa,CAAC;AAAA,UAAG;AAAA,QAC5B,OAAO;AACL,eAAK,cAAc;AACnB,eAAK,aAAa,KAAK,sBAAsBA,QAAO;AACpD,cAAI,KAAK,cAAc,MAAM,GAAG;AAC9B,gBAAI,KAAK,SAAS,QAAQ,QAAQ;AAAE,mBAAK,WAAW;AAAA,YAAG;AACvD,iBAAK,SAAS,KAAK,cAAc;AACjC,gBAAI,KAAK,QAAQ,eAAe,IAC9B;AAAE,mBAAK,aAAa,KAAK,gBAAgB;AAAA,YAAG;AAAA,UAChD,OAAO;AACL,qBAASC,KAAI,GAAGU,QAAO,KAAK,YAAYV,KAAIU,MAAK,QAAQV,MAAK,GAAG;AAE/D,kBAAI,OAAOU,MAAKV,EAAC;AAEjB,mBAAK,gBAAgB,KAAK,KAAK;AAE/B,mBAAK,iBAAiB,KAAK,KAAK;AAEhC,kBAAI,KAAK,MAAM,SAAS,WAAW;AACjC,qBAAK,MAAM,KAAK,MAAM,OAAO,wEAAwE;AAAA,cACvG;AAAA,YACF;AAEA,iBAAK,SAAS;AACd,gBAAI,KAAK,QAAQ,eAAe,IAC9B;AAAE,mBAAK,aAAa,CAAC;AAAA,YAAG;AAAA,UAC5B;AACA,eAAK,UAAU;AAAA,QACjB;AACA,eAAO,KAAK,WAAW,MAAM,wBAAwB;AAAA,MACvD;AAEA,WAAK,yBAAyB,SAAS,MAAM;AAC3C,eAAO,KAAK,eAAe,IAAI;AAAA,MACjC;AAEA,WAAK,gCAAgC,WAAW;AAC9C,YAAI;AACJ,YAAI,KAAK,SAAS,QAAQ,cAAc,UAAU,KAAK,gBAAgB,IAAI;AACzE,cAAI,QAAQ,KAAK,UAAU;AAC3B,eAAK,KAAK;AACV,cAAI,SAAS;AAAE,iBAAK,KAAK;AAAA,UAAG;AAC5B,iBAAO,KAAK,cAAc,OAAO,iBAAiB,kBAAkB,OAAO,OAAO;AAAA,QACpF,WAAW,KAAK,SAAS,QAAQ,QAAQ;AACvC,cAAI,QAAQ,KAAK,UAAU;AAC3B,iBAAO,KAAK,WAAW,OAAO,YAAY;AAAA,QAC5C,OAAO;AACL,cAAI,cAAc,KAAK,iBAAiB;AACxC,eAAK,UAAU;AACf,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,WAAK,cAAc,SAASD,UAAS,MAAM,KAAK;AAC9C,YAAI,CAACA,UAAS;AAAE;AAAA,QAAO;AACvB,YAAI,OAAO,SAAS,UAClB;AAAE,iBAAO,KAAK,SAAS,eAAe,KAAK,OAAO,KAAK;AAAA,QAAO;AAChE,YAAI,OAAOA,UAAS,IAAI,GACtB;AAAE,eAAK,iBAAiB,KAAK,uBAAuB,OAAO,GAAG;AAAA,QAAG;AACnE,QAAAA,SAAQ,IAAI,IAAI;AAAA,MAClB;AAEA,WAAK,qBAAqB,SAASA,UAAS,KAAK;AAC/C,YAAI,OAAO,IAAI;AACf,YAAI,SAAS,cACX;AAAE,eAAK,YAAYA,UAAS,KAAK,IAAI,KAAK;AAAA,QAAG,WACtC,SAAS,iBAChB;AAAE,mBAASC,KAAI,GAAGU,QAAO,IAAI,YAAYV,KAAIU,MAAK,QAAQV,MAAK,GAC7D;AACE,gBAAI,OAAOU,MAAKV,EAAC;AAEjB,iBAAK,mBAAmBD,UAAS,IAAI;AAAA,UACvC;AAAA,QAAE,WACG,SAAS,gBAChB;AAAE,mBAAS,MAAM,GAAG,SAAS,IAAI,UAAU,MAAM,OAAO,QAAQ,OAAO,GAAG;AACxE,gBAAI,MAAM,OAAO,GAAG;AAElB,gBAAI,KAAK;AAAE,mBAAK,mBAAmBA,UAAS,GAAG;AAAA,YAAG;AAAA,UACtD;AAAA,QAAE,WACK,SAAS,YAChB;AAAE,eAAK,mBAAmBA,UAAS,IAAI,KAAK;AAAA,QAAG,WACxC,SAAS,qBAChB;AAAE,eAAK,mBAAmBA,UAAS,IAAI,IAAI;AAAA,QAAG,WACvC,SAAS,eAChB;AAAE,eAAK,mBAAmBA,UAAS,IAAI,QAAQ;AAAA,QAAG;AAAA,MACtD;AAEA,WAAK,sBAAsB,SAASA,UAAS,OAAO;AAClD,YAAI,CAACA,UAAS;AAAE;AAAA,QAAO;AACvB,iBAASC,KAAI,GAAGU,QAAO,OAAOV,KAAIU,MAAK,QAAQV,MAAK,GAClD;AACA,cAAI,OAAOU,MAAKV,EAAC;AAEjB,eAAK,mBAAmBD,UAAS,KAAK,EAAE;AAAA,QAC1C;AAAA,MACF;AAEA,WAAK,6BAA6B,WAAW;AAC3C,eAAO,KAAK,KAAK,YAAY,SAC3B,KAAK,KAAK,YAAY,WACtB,KAAK,KAAK,YAAY,WACtB,KAAK,KAAK,YAAY,cACtB,KAAK,MAAM,KACX,KAAK,gBAAgB;AAAA,MACzB;AAIA,WAAK,uBAAuB,SAASA,UAAS;AAC5C,YAAI,OAAO,KAAK,UAAU;AAC1B,aAAK,QAAQ,KAAK,sBAAsB;AAExC,aAAK,WAAW,KAAK,cAAc,IAAI,IAAI,KAAK,sBAAsB,IAAI,KAAK;AAC/E,aAAK;AAAA,UACHA;AAAA,UACA,KAAK;AAAA,UACL,KAAK,SAAS;AAAA,QAChB;AAEA,eAAO,KAAK,WAAW,MAAM,iBAAiB;AAAA,MAChD;AAEA,WAAK,wBAAwB,SAASA,UAAS;AAC7C,YAAI,QAAQ,CAAC,GAAG,QAAQ;AAExB,aAAK,OAAO,QAAQ,MAAM;AAC1B,eAAO,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAChC,cAAI,CAAC,OAAO;AACV,iBAAK,OAAO,QAAQ,KAAK;AACzB,gBAAI,KAAK,mBAAmB,QAAQ,MAAM,GAAG;AAAE;AAAA,YAAM;AAAA,UACvD,OAAO;AAAE,oBAAQ;AAAA,UAAO;AAExB,gBAAM,KAAK,KAAK,qBAAqBA,QAAO,CAAC;AAAA,QAC/C;AACA,eAAO;AAAA,MACT;AAIA,WAAK,cAAc,SAAS,MAAM;AAChC,aAAK,KAAK;AAGV,YAAI,KAAK,SAAS,QAAQ,QAAQ;AAChC,eAAK,aAAa;AAClB,eAAK,SAAS,KAAK,cAAc;AAAA,QACnC,OAAO;AACL,eAAK,aAAa,KAAK,sBAAsB;AAC7C,eAAK,iBAAiB,MAAM;AAC5B,eAAK,SAAS,KAAK,SAAS,QAAQ,SAAS,KAAK,cAAc,IAAI,KAAK,WAAW;AAAA,QACtF;AACA,YAAI,KAAK,QAAQ,eAAe,IAC9B;AAAE,eAAK,aAAa,KAAK,gBAAgB;AAAA,QAAG;AAC9C,aAAK,UAAU;AACf,eAAO,KAAK,WAAW,MAAM,mBAAmB;AAAA,MAClD;AAIA,WAAK,uBAAuB,WAAW;AACrC,YAAI,OAAO,KAAK,UAAU;AAC1B,aAAK,WAAW,KAAK,sBAAsB;AAE3C,YAAI,KAAK,cAAc,IAAI,GAAG;AAC5B,eAAK,QAAQ,KAAK,WAAW;AAAA,QAC/B,OAAO;AACL,eAAK,gBAAgB,KAAK,QAAQ;AAClC,eAAK,QAAQ,KAAK;AAAA,QACpB;AACA,aAAK,gBAAgB,KAAK,OAAO,YAAY;AAE7C,eAAO,KAAK,WAAW,MAAM,iBAAiB;AAAA,MAChD;AAEA,WAAK,8BAA8B,WAAW;AAE5C,YAAI,OAAO,KAAK,UAAU;AAC1B,aAAK,QAAQ,KAAK,WAAW;AAC7B,aAAK,gBAAgB,KAAK,OAAO,YAAY;AAC7C,eAAO,KAAK,WAAW,MAAM,wBAAwB;AAAA,MACvD;AAEA,WAAK,gCAAgC,WAAW;AAC9C,YAAI,OAAO,KAAK,UAAU;AAC1B,aAAK,KAAK;AACV,aAAK,iBAAiB,IAAI;AAC1B,aAAK,QAAQ,KAAK,WAAW;AAC7B,aAAK,gBAAgB,KAAK,OAAO,YAAY;AAC7C,eAAO,KAAK,WAAW,MAAM,0BAA0B;AAAA,MACzD;AAEA,WAAK,wBAAwB,WAAW;AACtC,YAAI,QAAQ,CAAC,GAAG,QAAQ;AACxB,YAAI,KAAK,SAAS,QAAQ,MAAM;AAC9B,gBAAM,KAAK,KAAK,4BAA4B,CAAC;AAC7C,cAAI,CAAC,KAAK,IAAI,QAAQ,KAAK,GAAG;AAAE,mBAAO;AAAA,UAAM;AAAA,QAC/C;AACA,YAAI,KAAK,SAAS,QAAQ,MAAM;AAC9B,gBAAM,KAAK,KAAK,8BAA8B,CAAC;AAC/C,iBAAO;AAAA,QACT;AACA,aAAK,OAAO,QAAQ,MAAM;AAC1B,eAAO,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAChC,cAAI,CAAC,OAAO;AACV,iBAAK,OAAO,QAAQ,KAAK;AACzB,gBAAI,KAAK,mBAAmB,QAAQ,MAAM,GAAG;AAAE;AAAA,YAAM;AAAA,UACvD,OAAO;AAAE,oBAAQ;AAAA,UAAO;AAExB,gBAAM,KAAK,KAAK,qBAAqB,CAAC;AAAA,QACxC;AACA,eAAO;AAAA,MACT;AAEA,WAAK,kBAAkB,WAAW;AAChC,YAAI,QAAQ,CAAC;AACb,YAAI,CAAC,KAAK,IAAI,QAAQ,KAAK,GAAG;AAC5B,iBAAO;AAAA,QACT;AACA,aAAK,OAAO,QAAQ,MAAM;AAC1B,YAAI,gBAAgB,CAAC;AACrB,YAAI,QAAQ;AACZ,eAAO,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAChC,cAAI,CAAC,OAAO;AACV,iBAAK,OAAO,QAAQ,KAAK;AACzB,gBAAI,KAAK,mBAAmB,QAAQ,MAAM,GAAG;AAAE;AAAA,YAAM;AAAA,UACvD,OAAO;AAAE,oBAAQ;AAAA,UAAO;AAExB,cAAI,OAAO,KAAK,qBAAqB;AACrC,cAAI,UAAU,KAAK,IAAI,SAAS,eAAe,KAAK,IAAI,OAAO,KAAK,IAAI;AACxE,cAAI,OAAO,eAAe,OAAO,GAC/B;AAAE,iBAAK,iBAAiB,KAAK,IAAI,OAAO,8BAA8B,UAAU,GAAG;AAAA,UAAG;AACxF,wBAAc,OAAO,IAAI;AACzB,gBAAM,KAAK,IAAI;AAAA,QACjB;AACA,eAAO;AAAA,MACT;AAEA,WAAK,uBAAuB,WAAW;AACrC,YAAI,OAAO,KAAK,UAAU;AAC1B,aAAK,MAAM,KAAK,SAAS,QAAQ,SAAS,KAAK,cAAc,IAAI,KAAK,WAAW,KAAK,QAAQ,kBAAkB,OAAO;AACvH,aAAK,OAAO,QAAQ,KAAK;AACzB,YAAI,KAAK,SAAS,QAAQ,QAAQ;AAChC,eAAK,WAAW;AAAA,QAClB;AACA,aAAK,QAAQ,KAAK,cAAc;AAChC,eAAO,KAAK,WAAW,MAAM,iBAAiB;AAAA,MAChD;AAEA,WAAK,wBAAwB,WAAW;AACtC,YAAI,KAAK,QAAQ,eAAe,MAAM,KAAK,SAAS,QAAQ,QAAQ;AAClE,cAAI,gBAAgB,KAAK,aAAa,KAAK,KAAK;AAChD,cAAI,cAAc,KAAK,cAAc,KAAK,GAAG;AAC3C,iBAAK,MAAM,cAAc,OAAO,iDAAiD;AAAA,UACnF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,WAAW,IAAI;AAAA,MAC7B;AAGA,WAAK,yBAAyB,SAAS,YAAY;AACjD,iBAASC,KAAI,GAAGA,KAAI,WAAW,UAAU,KAAK,qBAAqB,WAAWA,EAAC,CAAC,GAAG,EAAEA,IAAG;AACtF,qBAAWA,EAAC,EAAE,YAAY,WAAWA,EAAC,EAAE,WAAW,IAAI,MAAM,GAAG,EAAE;AAAA,QACpE;AAAA,MACF;AACA,WAAK,uBAAuB,SAAS,WAAW;AAC9C,eACE,KAAK,QAAQ,eAAe,KAC5B,UAAU,SAAS,yBACnB,UAAU,WAAW,SAAS,aAC9B,OAAO,UAAU,WAAW,UAAU;AAAA,SAErC,KAAK,MAAM,UAAU,KAAK,MAAM,OAAQ,KAAK,MAAM,UAAU,KAAK,MAAM;AAAA,MAE7E;AAEA,UAAI,OAAO,OAAO;AAKlB,WAAK,eAAe,SAAS,MAAM,WAAW,wBAAwB;AACpE,YAAI,KAAK,QAAQ,eAAe,KAAK,MAAM;AACzC,kBAAQ,KAAK,MAAM;AAAA,YACnB,KAAK;AACH,kBAAI,KAAK,WAAW,KAAK,SAAS,SAChC;AAAE,qBAAK,MAAM,KAAK,OAAO,2DAA2D;AAAA,cAAG;AACzF;AAAA,YAEF,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AACH;AAAA,YAEF,KAAK;AACH,mBAAK,OAAO;AACZ,kBAAI,wBAAwB;AAAE,qBAAK,mBAAmB,wBAAwB,IAAI;AAAA,cAAG;AACrF,uBAASA,KAAI,GAAGU,QAAO,KAAK,YAAYV,KAAIU,MAAK,QAAQV,MAAK,GAAG;AAC/D,oBAAI,OAAOU,MAAKV,EAAC;AAEnB,qBAAK,aAAa,MAAM,SAAS;AAM/B,oBACE,KAAK,SAAS,kBACb,KAAK,SAAS,SAAS,kBAAkB,KAAK,SAAS,SAAS,kBACjE;AACA,uBAAK,MAAM,KAAK,SAAS,OAAO,kBAAkB;AAAA,gBACpD;AAAA,cACF;AACA;AAAA,YAEF,KAAK;AAEH,kBAAI,KAAK,SAAS,QAAQ;AAAE,qBAAK,MAAM,KAAK,IAAI,OAAO,+CAA+C;AAAA,cAAG;AACzG,mBAAK,aAAa,KAAK,OAAO,SAAS;AACvC;AAAA,YAEF,KAAK;AACH,mBAAK,OAAO;AACZ,kBAAI,wBAAwB;AAAE,qBAAK,mBAAmB,wBAAwB,IAAI;AAAA,cAAG;AACrF,mBAAK,iBAAiB,KAAK,UAAU,SAAS;AAC9C;AAAA,YAEF,KAAK;AACH,mBAAK,OAAO;AACZ,mBAAK,aAAa,KAAK,UAAU,SAAS;AAC1C,kBAAI,KAAK,SAAS,SAAS,qBACzB;AAAE,qBAAK,MAAM,KAAK,SAAS,OAAO,2CAA2C;AAAA,cAAG;AAClF;AAAA,YAEF,KAAK;AACH,kBAAI,KAAK,aAAa,KAAK;AAAE,qBAAK,MAAM,KAAK,KAAK,KAAK,6DAA6D;AAAA,cAAG;AACvH,mBAAK,OAAO;AACZ,qBAAO,KAAK;AACZ,mBAAK,aAAa,KAAK,MAAM,SAAS;AACtC;AAAA,YAEF,KAAK;AACH,mBAAK,aAAa,KAAK,YAAY,WAAW,sBAAsB;AACpE;AAAA,YAEF,KAAK;AACH,mBAAK,iBAAiB,KAAK,OAAO,mDAAmD;AACrF;AAAA,YAEF,KAAK;AACH,kBAAI,CAAC,WAAW;AAAE;AAAA,cAAM;AAAA,YAE1B;AACE,mBAAK,MAAM,KAAK,OAAO,qBAAqB;AAAA,UAC9C;AAAA,QACF,WAAW,wBAAwB;AAAE,eAAK,mBAAmB,wBAAwB,IAAI;AAAA,QAAG;AAC5F,eAAO;AAAA,MACT;AAIA,WAAK,mBAAmB,SAAS,UAAU,WAAW;AACpD,YAAI,MAAM,SAAS;AACnB,iBAASA,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC5B,cAAI,MAAM,SAASA,EAAC;AACpB,cAAI,KAAK;AAAE,iBAAK,aAAa,KAAK,SAAS;AAAA,UAAG;AAAA,QAChD;AACA,YAAI,KAAK;AACP,cAAI,OAAO,SAAS,MAAM,CAAC;AAC3B,cAAI,KAAK,QAAQ,gBAAgB,KAAK,aAAa,QAAQ,KAAK,SAAS,iBAAiB,KAAK,SAAS,SAAS,cAC/G;AAAE,iBAAK,WAAW,KAAK,SAAS,KAAK;AAAA,UAAG;AAAA,QAC5C;AACA,eAAO;AAAA,MACT;AAIA,WAAK,cAAc,SAAS,wBAAwB;AAClD,YAAI,OAAO,KAAK,UAAU;AAC1B,aAAK,KAAK;AACV,aAAK,WAAW,KAAK,iBAAiB,OAAO,sBAAsB;AACnE,eAAO,KAAK,WAAW,MAAM,eAAe;AAAA,MAC9C;AAEA,WAAK,mBAAmB,WAAW;AACjC,YAAI,OAAO,KAAK,UAAU;AAC1B,aAAK,KAAK;AAGV,YAAI,KAAK,QAAQ,gBAAgB,KAAK,KAAK,SAAS,QAAQ,MAC1D;AAAE,eAAK,WAAW;AAAA,QAAG;AAEvB,aAAK,WAAW,KAAK,iBAAiB;AAEtC,eAAO,KAAK,WAAW,MAAM,aAAa;AAAA,MAC5C;AAIA,WAAK,mBAAmB,WAAW;AACjC,YAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,kBAAQ,KAAK,MAAM;AAAA,YACnB,KAAK,QAAQ;AACX,kBAAI,OAAO,KAAK,UAAU;AAC1B,mBAAK,KAAK;AACV,mBAAK,WAAW,KAAK,iBAAiB,QAAQ,UAAU,MAAM,IAAI;AAClE,qBAAO,KAAK,WAAW,MAAM,cAAc;AAAA,YAE7C,KAAK,QAAQ;AACX,qBAAO,KAAK,SAAS,IAAI;AAAA,UAC3B;AAAA,QACF;AACA,eAAO,KAAK,WAAW;AAAA,MACzB;AAEA,WAAK,mBAAmB,SAAS,OAAO,YAAY,oBAAoB,gBAAgB;AACtF,YAAI,OAAO,CAAC,GAAG,QAAQ;AACvB,eAAO,CAAC,KAAK,IAAI,KAAK,GAAG;AACvB,cAAI,OAAO;AAAE,oBAAQ;AAAA,UAAO,OACvB;AAAE,iBAAK,OAAO,QAAQ,KAAK;AAAA,UAAG;AACnC,cAAI,cAAc,KAAK,SAAS,QAAQ,OAAO;AAC7C,iBAAK,KAAK,IAAI;AAAA,UAChB,WAAW,sBAAsB,KAAK,mBAAmB,KAAK,GAAG;AAC/D;AAAA,UACF,WAAW,KAAK,SAAS,QAAQ,UAAU;AACzC,gBAAI,OAAO,KAAK,iBAAiB;AACjC,iBAAK,qBAAqB,IAAI;AAC9B,iBAAK,KAAK,IAAI;AACd,gBAAI,KAAK,SAAS,QAAQ,OAAO;AAAE,mBAAK,iBAAiB,KAAK,OAAO,+CAA+C;AAAA,YAAG;AACvH,iBAAK,OAAO,KAAK;AACjB;AAAA,UACF,OAAO;AACL,iBAAK,KAAK,KAAK,wBAAwB,cAAc,CAAC;AAAA,UACxD;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,WAAK,0BAA0B,SAAS,gBAAgB;AACtD,YAAI,OAAO,KAAK,kBAAkB,KAAK,OAAO,KAAK,QAAQ;AAC3D,aAAK,qBAAqB,IAAI;AAC9B,eAAO;AAAA,MACT;AAEA,WAAK,uBAAuB,SAAS,OAAO;AAC1C,eAAO;AAAA,MACT;AAIA,WAAK,oBAAoB,SAAS,UAAU,UAAU,MAAM;AAC1D,eAAO,QAAQ,KAAK,iBAAiB;AACrC,YAAI,KAAK,QAAQ,cAAc,KAAK,CAAC,KAAK,IAAI,QAAQ,EAAE,GAAG;AAAE,iBAAO;AAAA,QAAK;AACzE,YAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,aAAK,OAAO;AACZ,aAAK,QAAQ,KAAK,iBAAiB;AACnC,eAAO,KAAK,WAAW,MAAM,mBAAmB;AAAA,MAClD;AAkEA,WAAK,kBAAkB,SAAS,MAAM,aAAa,cAAc;AAC/D,YAAK,gBAAgB,OAAS,eAAc;AAE5C,YAAI,SAAS,gBAAgB;AAE7B,gBAAQ,KAAK,MAAM;AAAA,UACnB,KAAK;AACH,gBAAI,KAAK,UAAU,KAAK,wBAAwB,KAAK,KAAK,IAAI,GAC5D;AAAE,mBAAK,iBAAiB,KAAK,QAAQ,SAAS,aAAa,mBAAmB,KAAK,OAAO,iBAAiB;AAAA,YAAG;AAChH,gBAAI,QAAQ;AACV,kBAAI,gBAAgB,gBAAgB,KAAK,SAAS,OAChD;AAAE,qBAAK,iBAAiB,KAAK,OAAO,6CAA6C;AAAA,cAAG;AACtF,kBAAI,cAAc;AAChB,oBAAI,OAAO,cAAc,KAAK,IAAI,GAChC;AAAE,uBAAK,iBAAiB,KAAK,OAAO,qBAAqB;AAAA,gBAAG;AAC9D,6BAAa,KAAK,IAAI,IAAI;AAAA,cAC5B;AACA,kBAAI,gBAAgB,cAAc;AAAE,qBAAK,YAAY,KAAK,MAAM,aAAa,KAAK,KAAK;AAAA,cAAG;AAAA,YAC5F;AACA;AAAA,UAEF,KAAK;AACH,iBAAK,iBAAiB,KAAK,OAAO,mDAAmD;AACrF;AAAA,UAEF,KAAK;AACH,gBAAI,QAAQ;AAAE,mBAAK,iBAAiB,KAAK,OAAO,2BAA2B;AAAA,YAAG;AAC9E;AAAA,UAEF,KAAK;AACH,gBAAI,QAAQ;AAAE,mBAAK,iBAAiB,KAAK,OAAO,kCAAkC;AAAA,YAAG;AACrF,mBAAO,KAAK,gBAAgB,KAAK,YAAY,aAAa,YAAY;AAAA,UAExE;AACE,iBAAK,MAAM,KAAK,QAAQ,SAAS,YAAY,kBAAkB,SAAS;AAAA,QAC1E;AAAA,MACF;AAEA,WAAK,mBAAmB,SAAS,MAAM,aAAa,cAAc;AAChE,YAAK,gBAAgB,OAAS,eAAc;AAE5C,gBAAQ,KAAK,MAAM;AAAA,UACnB,KAAK;AACH,qBAASA,KAAI,GAAGU,QAAO,KAAK,YAAYV,KAAIU,MAAK,QAAQV,MAAK,GAAG;AAC/D,kBAAI,OAAOU,MAAKV,EAAC;AAEnB,mBAAK,sBAAsB,MAAM,aAAa,YAAY;AAAA,YAC1D;AACA;AAAA,UAEF,KAAK;AACH,qBAAS,MAAM,GAAG,SAAS,KAAK,UAAU,MAAM,OAAO,QAAQ,OAAO,GAAG;AACvE,kBAAI,OAAO,OAAO,GAAG;AAEvB,kBAAI,MAAM;AAAE,qBAAK,sBAAsB,MAAM,aAAa,YAAY;AAAA,cAAG;AAAA,YACzE;AACA;AAAA,UAEF;AACE,iBAAK,gBAAgB,MAAM,aAAa,YAAY;AAAA,QACtD;AAAA,MACF;AAEA,WAAK,wBAAwB,SAAS,MAAM,aAAa,cAAc;AACrE,YAAK,gBAAgB,OAAS,eAAc;AAE5C,gBAAQ,KAAK,MAAM;AAAA,UACnB,KAAK;AAEH,iBAAK,sBAAsB,KAAK,OAAO,aAAa,YAAY;AAChE;AAAA,UAEF,KAAK;AACH,iBAAK,iBAAiB,KAAK,MAAM,aAAa,YAAY;AAC1D;AAAA,UAEF,KAAK;AACH,iBAAK,iBAAiB,KAAK,UAAU,aAAa,YAAY;AAC9D;AAAA,UAEF;AACE,iBAAK,iBAAiB,MAAM,aAAa,YAAY;AAAA,QACvD;AAAA,MACF;AAOA,UAAI,aAAa,SAASY,YAAW,OAAO,QAAQ,eAAe,UAAU,WAAW;AACtF,aAAK,QAAQ;AACb,aAAK,SAAS,CAAC,CAAC;AAChB,aAAK,gBAAgB,CAAC,CAAC;AACvB,aAAK,WAAW;AAChB,aAAK,YAAY,CAAC,CAAC;AAAA,MACrB;AAEA,UAAI,QAAQ;AAAA,QACV,QAAQ,IAAI,WAAW,KAAK,KAAK;AAAA,QACjC,QAAQ,IAAI,WAAW,KAAK,IAAI;AAAA,QAChC,QAAQ,IAAI,WAAW,MAAM,KAAK;AAAA,QAClC,QAAQ,IAAI,WAAW,KAAK,KAAK;AAAA,QACjC,QAAQ,IAAI,WAAW,KAAK,IAAI;AAAA,QAChC,QAAQ,IAAI,WAAW,KAAK,MAAM,MAAM,SAAU,GAAG;AAAE,iBAAO,EAAE,qBAAqB;AAAA,QAAG,CAAC;AAAA,QACzF,QAAQ,IAAI,WAAW,YAAY,KAAK;AAAA,QACxC,QAAQ,IAAI,WAAW,YAAY,IAAI;AAAA,QACvC,YAAY,IAAI,WAAW,YAAY,MAAM,OAAO,MAAM,IAAI;AAAA,QAC9D,OAAO,IAAI,WAAW,YAAY,OAAO,OAAO,MAAM,IAAI;AAAA,MAC5D;AAEA,UAAI,OAAO,OAAO;AAElB,WAAK,iBAAiB,WAAW;AAC/B,eAAO,CAAC,MAAM,MAAM;AAAA,MACtB;AAEA,WAAK,aAAa,WAAW;AAC3B,eAAO,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC;AAAA,MAC7C;AAEA,WAAK,eAAe,SAAS,UAAU;AACrC,YAAI,SAAS,KAAK,WAAW;AAC7B,YAAI,WAAW,MAAM,UAAU,WAAW,MAAM,QAC9C;AAAE,iBAAO;AAAA,QAAK;AAChB,YAAI,aAAa,QAAQ,UAAU,WAAW,MAAM,UAAU,WAAW,MAAM,SAC7E;AAAE,iBAAO,CAAC,OAAO;AAAA,QAAO;AAK1B,YAAI,aAAa,QAAQ,WAAW,aAAa,QAAQ,QAAQ,KAAK,aACpE;AAAE,iBAAO,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,KAAK,CAAC;AAAA,QAAE;AACzE,YAAI,aAAa,QAAQ,SAAS,aAAa,QAAQ,QAAQ,aAAa,QAAQ,OAAO,aAAa,QAAQ,UAAU,aAAa,QAAQ,OAC7I;AAAE,iBAAO;AAAA,QAAK;AAChB,YAAI,aAAa,QAAQ,QACvB;AAAE,iBAAO,WAAW,MAAM;AAAA,QAAO;AACnC,YAAI,aAAa,QAAQ,QAAQ,aAAa,QAAQ,UAAU,aAAa,QAAQ,MACnF;AAAE,iBAAO;AAAA,QAAM;AACjB,eAAO,CAAC,KAAK;AAAA,MACf;AAEA,WAAK,qBAAqB,WAAW;AACnC,iBAASZ,KAAI,KAAK,QAAQ,SAAS,GAAGA,MAAK,GAAGA,MAAK;AACjD,cAAI,UAAU,KAAK,QAAQA,EAAC;AAC5B,cAAI,QAAQ,UAAU,YACpB;AAAE,mBAAO,QAAQ;AAAA,UAAU;AAAA,QAC/B;AACA,eAAO;AAAA,MACT;AAEA,WAAK,gBAAgB,SAAS,UAAU;AACtC,YAAI,QAAQ,OAAO,KAAK;AACxB,YAAI,KAAK,WAAW,aAAa,QAAQ,KACvC;AAAE,eAAK,cAAc;AAAA,QAAO,WACrB,SAAS,KAAK,eACrB;AAAE,iBAAO,KAAK,MAAM,QAAQ;AAAA,QAAG,OAE/B;AAAE,eAAK,cAAc,KAAK;AAAA,QAAY;AAAA,MAC1C;AAIA,WAAK,kBAAkB,SAAS,UAAU;AACxC,YAAI,KAAK,WAAW,MAAM,UAAU;AAClC,eAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,IAAI;AAAA,QAC1C;AAAA,MACF;AAIA,cAAQ,OAAO,gBAAgB,QAAQ,OAAO,gBAAgB,WAAW;AACvE,YAAI,KAAK,QAAQ,WAAW,GAAG;AAC7B,eAAK,cAAc;AACnB;AAAA,QACF;AACA,YAAI,MAAM,KAAK,QAAQ,IAAI;AAC3B,YAAI,QAAQ,MAAM,UAAU,KAAK,WAAW,EAAE,UAAU,YAAY;AAClE,gBAAM,KAAK,QAAQ,IAAI;AAAA,QACzB;AACA,aAAK,cAAc,CAAC,IAAI;AAAA,MAC1B;AAEA,cAAQ,OAAO,gBAAgB,SAAS,UAAU;AAChD,aAAK,QAAQ,KAAK,KAAK,aAAa,QAAQ,IAAI,MAAM,SAAS,MAAM,MAAM;AAC3E,aAAK,cAAc;AAAA,MACrB;AAEA,cAAQ,aAAa,gBAAgB,WAAW;AAC9C,aAAK,QAAQ,KAAK,MAAM,MAAM;AAC9B,aAAK,cAAc;AAAA,MACrB;AAEA,cAAQ,OAAO,gBAAgB,SAAS,UAAU;AAChD,YAAI,kBAAkB,aAAa,QAAQ,OAAO,aAAa,QAAQ,QAAQ,aAAa,QAAQ,SAAS,aAAa,QAAQ;AAClI,aAAK,QAAQ,KAAK,kBAAkB,MAAM,SAAS,MAAM,MAAM;AAC/D,aAAK,cAAc;AAAA,MACrB;AAEA,cAAQ,OAAO,gBAAgB,WAAW;AAAA,MAE1C;AAEA,cAAQ,UAAU,gBAAgB,QAAQ,OAAO,gBAAgB,SAAS,UAAU;AAClF,YAAI,SAAS,cAAc,aAAa,QAAQ,SAC5C,EAAE,aAAa,QAAQ,QAAQ,KAAK,WAAW,MAAM,MAAM,WAC3D,EAAE,aAAa,QAAQ,WAAW,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,KAAK,CAAC,MAC9F,GAAG,aAAa,QAAQ,SAAS,aAAa,QAAQ,WAAW,KAAK,WAAW,MAAM,MAAM,SAC/F;AAAE,eAAK,QAAQ,KAAK,MAAM,MAAM;AAAA,QAAG,OAEnC;AAAE,eAAK,QAAQ,KAAK,MAAM,MAAM;AAAA,QAAG;AACrC,aAAK,cAAc;AAAA,MACrB;AAEA,cAAQ,MAAM,gBAAgB,WAAW;AACvC,YAAI,KAAK,WAAW,EAAE,UAAU,YAAY;AAAE,eAAK,QAAQ,IAAI;AAAA,QAAG;AAClE,aAAK,cAAc;AAAA,MACrB;AAEA,cAAQ,UAAU,gBAAgB,WAAW;AAC3C,YAAI,KAAK,WAAW,MAAM,MAAM,QAC9B;AAAE,eAAK,QAAQ,IAAI;AAAA,QAAG,OAEtB;AAAE,eAAK,QAAQ,KAAK,MAAM,MAAM;AAAA,QAAG;AACrC,aAAK,cAAc;AAAA,MACrB;AAEA,cAAQ,KAAK,gBAAgB,SAAS,UAAU;AAC9C,YAAI,aAAa,QAAQ,WAAW;AAClC,cAAI,QAAQ,KAAK,QAAQ,SAAS;AAClC,cAAI,KAAK,QAAQ,KAAK,MAAM,MAAM,QAChC;AAAE,iBAAK,QAAQ,KAAK,IAAI,MAAM;AAAA,UAAY,OAE1C;AAAE,iBAAK,QAAQ,KAAK,IAAI,MAAM;AAAA,UAAO;AAAA,QACzC;AACA,aAAK,cAAc;AAAA,MACrB;AAEA,cAAQ,KAAK,gBAAgB,SAAS,UAAU;AAC9C,YAAI,UAAU;AACd,YAAI,KAAK,QAAQ,eAAe,KAAK,aAAa,QAAQ,KAAK;AAC7D,cAAI,KAAK,UAAU,QAAQ,CAAC,KAAK,eAC7B,KAAK,UAAU,WAAW,KAAK,mBAAmB,GACpD;AAAE,sBAAU;AAAA,UAAM;AAAA,QACtB;AACA,aAAK,cAAc;AAAA,MACrB;AAqBA,UAAI,OAAO,OAAO;AAOlB,WAAK,iBAAiB,SAAS,MAAM,UAAU,wBAAwB;AACrE,YAAI,KAAK,QAAQ,eAAe,KAAK,KAAK,SAAS,iBACjD;AAAE;AAAA,QAAO;AACX,YAAI,KAAK,QAAQ,eAAe,MAAM,KAAK,YAAY,KAAK,UAAU,KAAK,YACzE;AAAE;AAAA,QAAO;AACX,YAAI,MAAM,KAAK;AACf,YAAI;AACJ,gBAAQ,IAAI,MAAM;AAAA,UAClB,KAAK;AAAc,mBAAO,IAAI;AAAM;AAAA,UACpC,KAAK;AAAW,mBAAO,OAAO,IAAI,KAAK;AAAG;AAAA,UAC1C;AAAS;AAAA,QACT;AACA,YAAI,OAAO,KAAK;AAChB,YAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,cAAI,SAAS,eAAe,SAAS,QAAQ;AAC3C,gBAAI,SAAS,OAAO;AAClB,kBAAI,wBAAwB;AAC1B,oBAAI,uBAAuB,cAAc,GAAG;AAC1C,yCAAuB,cAAc,IAAI;AAAA,gBAC3C;AAAA,cACF,OAAO;AACL,qBAAK,iBAAiB,IAAI,OAAO,oCAAoC;AAAA,cACvE;AAAA,YACF;AACA,qBAAS,QAAQ;AAAA,UACnB;AACA;AAAA,QACF;AACA,eAAO,MAAM;AACb,YAAI,QAAQ,SAAS,IAAI;AACzB,YAAI,OAAO;AACT,cAAI;AACJ,cAAI,SAAS,QAAQ;AACnB,2BAAe,KAAK,UAAU,MAAM,QAAQ,MAAM,OAAO,MAAM;AAAA,UACjE,OAAO;AACL,2BAAe,MAAM,QAAQ,MAAM,IAAI;AAAA,UACzC;AACA,cAAI,cACF;AAAE,iBAAK,iBAAiB,IAAI,OAAO,0BAA0B;AAAA,UAAG;AAAA,QACpE,OAAO;AACL,kBAAQ,SAAS,IAAI,IAAI;AAAA,YACvB,MAAM;AAAA,YACN,KAAK;AAAA,YACL,KAAK;AAAA,UACP;AAAA,QACF;AACA,cAAM,IAAI,IAAI;AAAA,MAChB;AAiBA,WAAK,kBAAkB,SAAS,SAAS,wBAAwB;AAC/D,YAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,YAAI,OAAO,KAAK,iBAAiB,SAAS,sBAAsB;AAChE,YAAI,KAAK,SAAS,QAAQ,OAAO;AAC/B,cAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,eAAK,cAAc,CAAC,IAAI;AACxB,iBAAO,KAAK,IAAI,QAAQ,KAAK,GAAG;AAAE,iBAAK,YAAY,KAAK,KAAK,iBAAiB,SAAS,sBAAsB,CAAC;AAAA,UAAG;AACjH,iBAAO,KAAK,WAAW,MAAM,oBAAoB;AAAA,QACnD;AACA,eAAO;AAAA,MACT;AAKA,WAAK,mBAAmB,SAAS,SAAS,wBAAwB,gBAAgB;AAChF,YAAI,KAAK,aAAa,OAAO,GAAG;AAC9B,cAAI,KAAK,aAAa;AAAE,mBAAO,KAAK,WAAW,OAAO;AAAA,UAAE,OAGnD;AAAE,iBAAK,cAAc;AAAA,UAAO;AAAA,QACnC;AAEA,YAAI,yBAAyB,OAAO,iBAAiB,IAAI,mBAAmB,IAAI,iBAAiB;AACjG,YAAI,wBAAwB;AAC1B,2BAAiB,uBAAuB;AACxC,6BAAmB,uBAAuB;AAC1C,2BAAiB,uBAAuB;AACxC,iCAAuB,sBAAsB,uBAAuB,gBAAgB;AAAA,QACtF,OAAO;AACL,mCAAyB,IAAI;AAC7B,mCAAyB;AAAA,QAC3B;AAEA,YAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,YAAI,KAAK,SAAS,QAAQ,UAAU,KAAK,SAAS,QAAQ,MAAM;AAC9D,eAAK,mBAAmB,KAAK;AAC7B,eAAK,2BAA2B,YAAY;AAAA,QAC9C;AACA,YAAI,OAAO,KAAK,sBAAsB,SAAS,sBAAsB;AACrE,YAAI,gBAAgB;AAAE,iBAAO,eAAe,KAAK,MAAM,MAAM,UAAU,QAAQ;AAAA,QAAG;AAClF,YAAI,KAAK,KAAK,UAAU;AACtB,cAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,eAAK,WAAW,KAAK;AACrB,cAAI,KAAK,SAAS,QAAQ,IACxB;AAAE,mBAAO,KAAK,aAAa,MAAM,OAAO,sBAAsB;AAAA,UAAG;AACnE,cAAI,CAAC,wBAAwB;AAC3B,mCAAuB,sBAAsB,uBAAuB,gBAAgB,uBAAuB,cAAc;AAAA,UAC3H;AACA,cAAI,uBAAuB,mBAAmB,KAAK,OACjD;AAAE,mCAAuB,kBAAkB;AAAA,UAAI;AACjD,cAAI,KAAK,SAAS,QAAQ,IACxB;AAAE,iBAAK,iBAAiB,IAAI;AAAA,UAAG,OAE/B;AAAE,iBAAK,gBAAgB,IAAI;AAAA,UAAG;AAChC,eAAK,OAAO;AACZ,eAAK,KAAK;AACV,eAAK,QAAQ,KAAK,iBAAiB,OAAO;AAC1C,cAAI,iBAAiB,IAAI;AAAE,mCAAuB,cAAc;AAAA,UAAgB;AAChF,iBAAO,KAAK,WAAW,MAAM,sBAAsB;AAAA,QACrD,OAAO;AACL,cAAI,wBAAwB;AAAE,iBAAK,sBAAsB,wBAAwB,IAAI;AAAA,UAAG;AAAA,QAC1F;AACA,YAAI,iBAAiB,IAAI;AAAE,iCAAuB,sBAAsB;AAAA,QAAgB;AACxF,YAAI,mBAAmB,IAAI;AAAE,iCAAuB,gBAAgB;AAAA,QAAkB;AACtF,eAAO;AAAA,MACT;AAIA,WAAK,wBAAwB,SAAS,SAAS,wBAAwB;AACrE,YAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,YAAI,OAAO,KAAK,aAAa,SAAS,sBAAsB;AAC5D,YAAI,KAAK,sBAAsB,sBAAsB,GAAG;AAAE,iBAAO;AAAA,QAAK;AACtE,YAAI,KAAK,IAAI,QAAQ,QAAQ,GAAG;AAC9B,cAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,eAAK,OAAO;AACZ,eAAK,aAAa,KAAK,iBAAiB;AACxC,eAAK,OAAO,QAAQ,KAAK;AACzB,eAAK,YAAY,KAAK,iBAAiB,OAAO;AAC9C,iBAAO,KAAK,WAAW,MAAM,uBAAuB;AAAA,QACtD;AACA,eAAO;AAAA,MACT;AAIA,WAAK,eAAe,SAAS,SAAS,wBAAwB;AAC5D,YAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,YAAI,OAAO,KAAK,gBAAgB,wBAAwB,OAAO,OAAO,OAAO;AAC7E,YAAI,KAAK,sBAAsB,sBAAsB,GAAG;AAAE,iBAAO;AAAA,QAAK;AACtE,eAAO,KAAK,UAAU,YAAY,KAAK,SAAS,4BAA4B,OAAO,KAAK,YAAY,MAAM,UAAU,UAAU,IAAI,OAAO;AAAA,MAC3I;AAQA,WAAK,cAAc,SAAS,MAAM,cAAc,cAAc,SAAS,SAAS;AAC9E,YAAI,OAAO,KAAK,KAAK;AACrB,YAAI,QAAQ,SAAS,CAAC,WAAW,KAAK,SAAS,QAAQ,MAAM;AAC3D,cAAI,OAAO,SAAS;AAClB,gBAAI,UAAU,KAAK,SAAS,QAAQ,aAAa,KAAK,SAAS,QAAQ;AACvE,gBAAI,WAAW,KAAK,SAAS,QAAQ;AACrC,gBAAI,UAAU;AAGZ,qBAAO,QAAQ,WAAW;AAAA,YAC5B;AACA,gBAAI,KAAK,KAAK;AACd,iBAAK,KAAK;AACV,gBAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,gBAAI,QAAQ,KAAK,YAAY,KAAK,gBAAgB,MAAM,OAAO,OAAO,OAAO,GAAG,UAAU,UAAU,MAAM,OAAO;AACjH,gBAAI,OAAO,KAAK,YAAY,cAAc,cAAc,MAAM,OAAO,IAAI,WAAW,QAAQ;AAC5F,gBAAK,WAAW,KAAK,SAAS,QAAQ,YAAc,aAAa,KAAK,SAAS,QAAQ,aAAa,KAAK,SAAS,QAAQ,aAAc;AACtI,mBAAK,iBAAiB,KAAK,OAAO,0FAA0F;AAAA,YAC9H;AACA,mBAAO,KAAK,YAAY,MAAM,cAAc,cAAc,SAAS,OAAO;AAAA,UAC5E;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,WAAK,cAAc,SAAS,UAAU,UAAU,MAAM,OAAO,IAAI,SAAS;AACxE,YAAI,MAAM,SAAS,qBAAqB;AAAE,eAAK,MAAM,MAAM,OAAO,+DAA+D;AAAA,QAAG;AACpI,YAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,aAAK,OAAO;AACZ,aAAK,WAAW;AAChB,aAAK,QAAQ;AACb,eAAO,KAAK,WAAW,MAAM,UAAU,sBAAsB,kBAAkB;AAAA,MACjF;AAIA,WAAK,kBAAkB,SAAS,wBAAwB,UAAU,QAAQ,SAAS;AACjF,YAAI,WAAW,KAAK,OAAO,WAAW,KAAK,UAAU;AACrD,YAAI,KAAK,aAAa,OAAO,KAAK,KAAK,UAAU;AAC/C,iBAAO,KAAK,WAAW,OAAO;AAC9B,qBAAW;AAAA,QACb,WAAW,KAAK,KAAK,QAAQ;AAC3B,cAAI,OAAO,KAAK,UAAU,GAAG,SAAS,KAAK,SAAS,QAAQ;AAC5D,eAAK,WAAW,KAAK;AACrB,eAAK,SAAS;AACd,eAAK,KAAK;AACV,eAAK,WAAW,KAAK,gBAAgB,MAAM,MAAM,QAAQ,OAAO;AAChE,eAAK,sBAAsB,wBAAwB,IAAI;AACvD,cAAI,QAAQ;AAAE,iBAAK,gBAAgB,KAAK,QAAQ;AAAA,UAAG,WAC1C,KAAK,UAAU,KAAK,aAAa,YAAY,sBAAsB,KAAK,QAAQ,GACvF;AAAE,iBAAK,iBAAiB,KAAK,OAAO,wCAAwC;AAAA,UAAG,WACxE,KAAK,aAAa,YAAY,qBAAqB,KAAK,QAAQ,GACvE;AAAE,iBAAK,iBAAiB,KAAK,OAAO,mCAAmC;AAAA,UAAG,OACvE;AAAE,uBAAW;AAAA,UAAM;AACxB,iBAAO,KAAK,WAAW,MAAM,SAAS,qBAAqB,iBAAiB;AAAA,QAC9E,WAAW,CAAC,YAAY,KAAK,SAAS,QAAQ,WAAW;AACvD,eAAK,WAAW,KAAK,iBAAiB,WAAW,MAAM,KAAK,QAAQ,oBAAoB;AAAE,iBAAK,WAAW;AAAA,UAAG;AAC7G,iBAAO,KAAK,kBAAkB;AAE9B,cAAI,KAAK,SAAS,QAAQ,KAAK;AAAE,iBAAK,WAAW;AAAA,UAAG;AAAA,QACtD,OAAO;AACL,iBAAO,KAAK,oBAAoB,wBAAwB,OAAO;AAC/D,cAAI,KAAK,sBAAsB,sBAAsB,GAAG;AAAE,mBAAO;AAAA,UAAK;AACtE,iBAAO,KAAK,KAAK,WAAW,CAAC,KAAK,mBAAmB,GAAG;AACtD,gBAAI,SAAS,KAAK,YAAY,UAAU,QAAQ;AAChD,mBAAO,WAAW,KAAK;AACvB,mBAAO,SAAS;AAChB,mBAAO,WAAW;AAClB,iBAAK,gBAAgB,IAAI;AACzB,iBAAK,KAAK;AACV,mBAAO,KAAK,WAAW,QAAQ,kBAAkB;AAAA,UACnD;AAAA,QACF;AAEA,YAAI,CAAC,UAAU,KAAK,IAAI,QAAQ,QAAQ,GAAG;AACzC,cAAI,UACF;AAAE,iBAAK,WAAW,KAAK,YAAY;AAAA,UAAG,OAEtC;AAAE,mBAAO,KAAK,YAAY,UAAU,UAAU,MAAM,KAAK,gBAAgB,MAAM,OAAO,OAAO,OAAO,GAAG,MAAM,KAAK;AAAA,UAAE;AAAA,QACxH,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,sBAAsB,MAAM;AACnC,eACE,KAAK,SAAS,gBACd,KAAK,SAAS,6BAA6B,sBAAsB,KAAK,UAAU;AAAA,MAEpF;AAEA,eAAS,qBAAqB,MAAM;AAClC,eACE,KAAK,SAAS,sBAAsB,KAAK,SAAS,SAAS,uBAC3D,KAAK,SAAS,qBAAqB,qBAAqB,KAAK,UAAU,KACvE,KAAK,SAAS,6BAA6B,qBAAqB,KAAK,UAAU;AAAA,MAEnF;AAIA,WAAK,sBAAsB,SAAS,wBAAwB,SAAS;AACnE,YAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,YAAI,OAAO,KAAK,cAAc,wBAAwB,OAAO;AAC7D,YAAI,KAAK,SAAS,6BAA6B,KAAK,MAAM,MAAM,KAAK,cAAc,KAAK,UAAU,MAAM,KACtG;AAAE,iBAAO;AAAA,QAAK;AAChB,YAAI,SAAS,KAAK,gBAAgB,MAAM,UAAU,UAAU,OAAO,OAAO;AAC1E,YAAI,0BAA0B,OAAO,SAAS,oBAAoB;AAChE,cAAI,uBAAuB,uBAAuB,OAAO,OAAO;AAAE,mCAAuB,sBAAsB;AAAA,UAAI;AACnH,cAAI,uBAAuB,qBAAqB,OAAO,OAAO;AAAE,mCAAuB,oBAAoB;AAAA,UAAI;AAC/G,cAAI,uBAAuB,iBAAiB,OAAO,OAAO;AAAE,mCAAuB,gBAAgB;AAAA,UAAI;AAAA,QACzG;AACA,eAAO;AAAA,MACT;AAEA,WAAK,kBAAkB,SAAS,MAAM,UAAU,UAAU,SAAS,SAAS;AAC1E,YAAI,kBAAkB,KAAK,QAAQ,eAAe,KAAK,KAAK,SAAS,gBAAgB,KAAK,SAAS,WAC/F,KAAK,eAAe,KAAK,OAAO,CAAC,KAAK,mBAAmB,KAAK,KAAK,MAAM,KAAK,UAAU,KACxF,KAAK,qBAAqB,KAAK;AACnC,YAAI,kBAAkB;AAEtB,eAAO,MAAM;AACX,cAAI,UAAU,KAAK,eAAe,MAAM,UAAU,UAAU,SAAS,iBAAiB,iBAAiB,OAAO;AAE9G,cAAI,QAAQ,UAAU;AAAE,8BAAkB;AAAA,UAAM;AAChD,cAAI,YAAY,QAAQ,QAAQ,SAAS,2BAA2B;AAClE,gBAAI,iBAAiB;AACnB,kBAAI,YAAY,KAAK,YAAY,UAAU,QAAQ;AACnD,wBAAU,aAAa;AACvB,wBAAU,KAAK,WAAW,WAAW,iBAAiB;AAAA,YACxD;AACA,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,WAAK,wBAAwB,WAAW;AACtC,eAAO,CAAC,KAAK,mBAAmB,KAAK,KAAK,IAAI,QAAQ,KAAK;AAAA,MAC7D;AAEA,WAAK,2BAA2B,SAAS,UAAU,UAAU,UAAU,SAAS;AAC9E,eAAO,KAAK,qBAAqB,KAAK,YAAY,UAAU,QAAQ,GAAG,UAAU,MAAM,OAAO;AAAA,MAChG;AAEA,WAAK,iBAAiB,SAAS,MAAM,UAAU,UAAU,SAAS,iBAAiB,iBAAiB,SAAS;AAC3G,YAAI,oBAAoB,KAAK,QAAQ,eAAe;AACpD,YAAI,WAAW,qBAAqB,KAAK,IAAI,QAAQ,WAAW;AAChE,YAAI,WAAW,UAAU;AAAE,eAAK,MAAM,KAAK,cAAc,kEAAkE;AAAA,QAAG;AAE9H,YAAI,WAAW,KAAK,IAAI,QAAQ,QAAQ;AACxC,YAAI,YAAa,YAAY,KAAK,SAAS,QAAQ,UAAU,KAAK,SAAS,QAAQ,aAAc,KAAK,IAAI,QAAQ,GAAG,GAAG;AACtH,cAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,eAAK,SAAS;AACd,cAAI,UAAU;AACZ,iBAAK,WAAW,KAAK,gBAAgB;AACrC,iBAAK,OAAO,QAAQ,QAAQ;AAAA,UAC9B,WAAW,KAAK,SAAS,QAAQ,aAAa,KAAK,SAAS,SAAS;AACnE,iBAAK,WAAW,KAAK,kBAAkB;AAAA,UACzC,OAAO;AACL,iBAAK,WAAW,KAAK,WAAW,KAAK,QAAQ,kBAAkB,OAAO;AAAA,UACxE;AACA,eAAK,WAAW,CAAC,CAAC;AAClB,cAAI,mBAAmB;AACrB,iBAAK,WAAW;AAAA,UAClB;AACA,iBAAO,KAAK,WAAW,MAAM,kBAAkB;AAAA,QACjD,WAAW,CAAC,WAAW,KAAK,IAAI,QAAQ,MAAM,GAAG;AAC/C,cAAI,yBAAyB,IAAI,uBAAqB,cAAc,KAAK,UAAU,cAAc,KAAK,UAAU,mBAAmB,KAAK;AACxI,eAAK,WAAW;AAChB,eAAK,WAAW;AAChB,eAAK,gBAAgB;AACrB,cAAI,WAAW,KAAK,cAAc,QAAQ,QAAQ,KAAK,QAAQ,eAAe,GAAG,OAAO,sBAAsB;AAC9G,cAAI,mBAAmB,CAAC,YAAY,KAAK,sBAAsB,GAAG;AAChE,iBAAK,mBAAmB,wBAAwB,KAAK;AACrD,iBAAK,+BAA+B;AACpC,gBAAI,KAAK,gBAAgB,GACvB;AAAE,mBAAK,MAAM,KAAK,eAAe,2DAA2D;AAAA,YAAG;AACjG,iBAAK,WAAW;AAChB,iBAAK,WAAW;AAChB,iBAAK,gBAAgB;AACrB,mBAAO,KAAK,yBAAyB,UAAU,UAAU,UAAU,OAAO;AAAA,UAC5E;AACA,eAAK,sBAAsB,wBAAwB,IAAI;AACvD,eAAK,WAAW,eAAe,KAAK;AACpC,eAAK,WAAW,eAAe,KAAK;AACpC,eAAK,gBAAgB,oBAAoB,KAAK;AAC9C,cAAI,SAAS,KAAK,YAAY,UAAU,QAAQ;AAChD,iBAAO,SAAS;AAChB,iBAAO,YAAY;AACnB,cAAI,mBAAmB;AACrB,mBAAO,WAAW;AAAA,UACpB;AACA,iBAAO,KAAK,WAAW,QAAQ,gBAAgB;AAAA,QACjD,WAAW,KAAK,SAAS,QAAQ,WAAW;AAC1C,cAAI,YAAY,iBAAiB;AAC/B,iBAAK,MAAM,KAAK,OAAO,2EAA2E;AAAA,UACpG;AACA,cAAI,SAAS,KAAK,YAAY,UAAU,QAAQ;AAChD,iBAAO,MAAM;AACb,iBAAO,QAAQ,KAAK,cAAc,EAAC,UAAU,KAAI,CAAC;AAClD,iBAAO,KAAK,WAAW,QAAQ,0BAA0B;AAAA,QAC3D;AACA,eAAO;AAAA,MACT;AAOA,WAAK,gBAAgB,SAAS,wBAAwB,SAAS,QAAQ;AAGrE,YAAI,KAAK,SAAS,QAAQ,OAAO;AAAE,eAAK,WAAW;AAAA,QAAG;AAEtD,YAAI,MAAM,aAAa,KAAK,qBAAqB,KAAK;AACtD,gBAAQ,KAAK,MAAM;AAAA,UACnB,KAAK,QAAQ;AACX,gBAAI,CAAC,KAAK,YACR;AAAE,mBAAK,MAAM,KAAK,OAAO,kCAAkC;AAAA,YAAG;AAChE,mBAAO,KAAK,UAAU;AACtB,iBAAK,KAAK;AACV,gBAAI,KAAK,SAAS,QAAQ,UAAU,CAAC,KAAK,kBACxC;AAAE,mBAAK,MAAM,KAAK,OAAO,gDAAgD;AAAA,YAAG;AAO9E,gBAAI,KAAK,SAAS,QAAQ,OAAO,KAAK,SAAS,QAAQ,YAAY,KAAK,SAAS,QAAQ,QACvF;AAAE,mBAAK,WAAW;AAAA,YAAG;AACvB,mBAAO,KAAK,WAAW,MAAM,OAAO;AAAA,UAEtC,KAAK,QAAQ;AACX,mBAAO,KAAK,UAAU;AACtB,iBAAK,KAAK;AACV,mBAAO,KAAK,WAAW,MAAM,gBAAgB;AAAA,UAE/C,KAAK,QAAQ;AACX,gBAAI,WAAW,KAAK,OAAO,WAAW,KAAK,UAAU,cAAc,KAAK;AACxE,gBAAI,KAAK,KAAK,WAAW,KAAK;AAC9B,gBAAI,KAAK,QAAQ,eAAe,KAAK,CAAC,eAAe,GAAG,SAAS,WAAW,CAAC,KAAK,mBAAmB,KAAK,KAAK,IAAI,QAAQ,SAAS,GAAG;AACrI,mBAAK,gBAAgB,MAAM,MAAM;AACjC,qBAAO,KAAK,cAAc,KAAK,YAAY,UAAU,QAAQ,GAAG,GAAG,OAAO,MAAM,OAAO;AAAA,YACzF;AACA,gBAAI,cAAc,CAAC,KAAK,mBAAmB,GAAG;AAC5C,kBAAI,KAAK,IAAI,QAAQ,KAAK,GACxB;AAAE,uBAAO,KAAK,qBAAqB,KAAK,YAAY,UAAU,QAAQ,GAAG,CAAC,EAAE,GAAG,OAAO,OAAO;AAAA,cAAE;AACjG,kBAAI,KAAK,QAAQ,eAAe,KAAK,GAAG,SAAS,WAAW,KAAK,SAAS,QAAQ,QAAQ,CAAC,gBACtF,CAAC,KAAK,4BAA4B,KAAK,UAAU,QAAQ,KAAK,cAAc;AAC/E,qBAAK,KAAK,WAAW,KAAK;AAC1B,oBAAI,KAAK,mBAAmB,KAAK,CAAC,KAAK,IAAI,QAAQ,KAAK,GACtD;AAAE,uBAAK,WAAW;AAAA,gBAAG;AACvB,uBAAO,KAAK,qBAAqB,KAAK,YAAY,UAAU,QAAQ,GAAG,CAAC,EAAE,GAAG,MAAM,OAAO;AAAA,cAC5F;AAAA,YACF;AACA,mBAAO;AAAA,UAET,KAAK,QAAQ;AACX,gBAAI,QAAQ,KAAK;AACjB,mBAAO,KAAK,aAAa,MAAM,KAAK;AACpC,iBAAK,QAAQ,EAAC,SAAS,MAAM,SAAS,OAAO,MAAM,MAAK;AACxD,mBAAO;AAAA,UAET,KAAK,QAAQ;AAAA,UAAK,KAAK,QAAQ;AAC7B,mBAAO,KAAK,aAAa,KAAK,KAAK;AAAA,UAErC,KAAK,QAAQ;AAAA,UAAO,KAAK,QAAQ;AAAA,UAAO,KAAK,QAAQ;AACnD,mBAAO,KAAK,UAAU;AACtB,iBAAK,QAAQ,KAAK,SAAS,QAAQ,QAAQ,OAAO,KAAK,SAAS,QAAQ;AACxE,iBAAK,MAAM,KAAK,KAAK;AACrB,iBAAK,KAAK;AACV,mBAAO,KAAK,WAAW,MAAM,SAAS;AAAA,UAExC,KAAK,QAAQ;AACX,gBAAI,QAAQ,KAAK,OAAO,OAAO,KAAK,mCAAmC,YAAY,OAAO;AAC1F,gBAAI,wBAAwB;AAC1B,kBAAI,uBAAuB,sBAAsB,KAAK,CAAC,KAAK,qBAAqB,IAAI,GACnF;AAAE,uCAAuB,sBAAsB;AAAA,cAAO;AACxD,kBAAI,uBAAuB,oBAAoB,GAC7C;AAAE,uCAAuB,oBAAoB;AAAA,cAAO;AAAA,YACxD;AACA,mBAAO;AAAA,UAET,KAAK,QAAQ;AACX,mBAAO,KAAK,UAAU;AACtB,iBAAK,KAAK;AACV,iBAAK,WAAW,KAAK,cAAc,QAAQ,UAAU,MAAM,MAAM,sBAAsB;AACvF,mBAAO,KAAK,WAAW,MAAM,iBAAiB;AAAA,UAEhD,KAAK,QAAQ;AACX,iBAAK,gBAAgB,MAAM,MAAM;AACjC,mBAAO,KAAK,SAAS,OAAO,sBAAsB;AAAA,UAEpD,KAAK,QAAQ;AACX,mBAAO,KAAK,UAAU;AACtB,iBAAK,KAAK;AACV,mBAAO,KAAK,cAAc,MAAM,CAAC;AAAA,UAEnC,KAAK,QAAQ;AACX,mBAAO,KAAK,WAAW,KAAK,UAAU,GAAG,KAAK;AAAA,UAEhD,KAAK,QAAQ;AACX,mBAAO,KAAK,SAAS;AAAA,UAEvB,KAAK,QAAQ;AACX,mBAAO,KAAK,cAAc;AAAA,UAE5B,KAAK,QAAQ;AACX,gBAAI,KAAK,QAAQ,eAAe,IAAI;AAClC,qBAAO,KAAK,gBAAgB,MAAM;AAAA,YACpC,OAAO;AACL,qBAAO,KAAK,WAAW;AAAA,YACzB;AAAA,UAEF;AACE,mBAAO,KAAK,qBAAqB;AAAA,QACnC;AAAA,MACF;AAEA,WAAK,uBAAuB,WAAW;AACrC,aAAK,WAAW;AAAA,MAClB;AAEA,WAAK,kBAAkB,SAAS,QAAQ;AACtC,YAAI,OAAO,KAAK,UAAU;AAI1B,YAAI,KAAK,aAAa;AAAE,eAAK,iBAAiB,KAAK,OAAO,mCAAmC;AAAA,QAAG;AAChG,aAAK,KAAK;AAEV,YAAI,KAAK,SAAS,QAAQ,UAAU,CAAC,QAAQ;AAC3C,iBAAO,KAAK,mBAAmB,IAAI;AAAA,QACrC,WAAW,KAAK,SAAS,QAAQ,KAAK;AACpC,cAAI,OAAO,KAAK,YAAY,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,KAAK;AAClE,eAAK,OAAO;AACZ,eAAK,OAAO,KAAK,WAAW,MAAM,YAAY;AAC9C,iBAAO,KAAK,gBAAgB,IAAI;AAAA,QAClC,OAAO;AACL,eAAK,WAAW;AAAA,QAClB;AAAA,MACF;AAEA,WAAK,qBAAqB,SAAS,MAAM;AACvC,aAAK,KAAK;AAGV,aAAK,SAAS,KAAK,iBAAiB;AAEpC,YAAI,KAAK,QAAQ,eAAe,IAAI;AAClC,cAAI,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAC7B,iBAAK,OAAO,QAAQ,KAAK;AACzB,gBAAI,CAAC,KAAK,mBAAmB,QAAQ,MAAM,GAAG;AAC5C,mBAAK,UAAU,KAAK,iBAAiB;AACrC,kBAAI,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAC7B,qBAAK,OAAO,QAAQ,KAAK;AACzB,oBAAI,CAAC,KAAK,mBAAmB,QAAQ,MAAM,GAAG;AAC5C,uBAAK,WAAW;AAAA,gBAClB;AAAA,cACF;AAAA,YACF,OAAO;AACL,mBAAK,UAAU;AAAA,YACjB;AAAA,UACF,OAAO;AACL,iBAAK,UAAU;AAAA,UACjB;AAAA,QACF,OAAO;AAEL,cAAI,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAC7B,gBAAI,WAAW,KAAK;AACpB,gBAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,KAAK,IAAI,QAAQ,MAAM,GAAG;AACvD,mBAAK,iBAAiB,UAAU,2CAA2C;AAAA,YAC7E,OAAO;AACL,mBAAK,WAAW,QAAQ;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AAEA,eAAO,KAAK,WAAW,MAAM,kBAAkB;AAAA,MACjD;AAEA,WAAK,kBAAkB,SAAS,MAAM;AACpC,aAAK,KAAK;AAEV,YAAI,cAAc,KAAK;AACvB,aAAK,WAAW,KAAK,WAAW,IAAI;AAEpC,YAAI,KAAK,SAAS,SAAS,QACzB;AAAE,eAAK,iBAAiB,KAAK,SAAS,OAAO,0DAA0D;AAAA,QAAG;AAC5G,YAAI,aACF;AAAE,eAAK,iBAAiB,KAAK,OAAO,mDAAmD;AAAA,QAAG;AAC5F,YAAI,KAAK,QAAQ,eAAe,YAAY,CAAC,KAAK,QAAQ,6BACxD;AAAE,eAAK,iBAAiB,KAAK,OAAO,2CAA2C;AAAA,QAAG;AAEpF,eAAO,KAAK,WAAW,MAAM,cAAc;AAAA,MAC7C;AAEA,WAAK,eAAe,SAAS,OAAO;AAClC,YAAI,OAAO,KAAK,UAAU;AAC1B,aAAK,QAAQ;AACb,aAAK,MAAM,KAAK,MAAM,MAAM,KAAK,OAAO,KAAK,GAAG;AAChD,YAAI,KAAK,IAAI,WAAW,KAAK,IAAI,SAAS,CAAC,MAAM,KAC/C;AAAE,eAAK,SAAS,KAAK,SAAS,OAAO,KAAK,MAAM,SAAS,IAAI,KAAK,IAAI,MAAM,GAAG,EAAE,EAAE,QAAQ,MAAM,EAAE;AAAA,QAAG;AACxG,aAAK,KAAK;AACV,eAAO,KAAK,WAAW,MAAM,SAAS;AAAA,MACxC;AAEA,WAAK,uBAAuB,WAAW;AACrC,aAAK,OAAO,QAAQ,MAAM;AAC1B,YAAI,MAAM,KAAK,gBAAgB;AAC/B,aAAK,OAAO,QAAQ,MAAM;AAC1B,eAAO;AAAA,MACT;AAEA,WAAK,mBAAmB,SAAS,UAAU;AACzC,eAAO,CAAC,KAAK,mBAAmB;AAAA,MAClC;AAEA,WAAK,qCAAqC,SAAS,YAAY,SAAS;AACtE,YAAI,WAAW,KAAK,OAAO,WAAW,KAAK,UAAU,KAAK,qBAAqB,KAAK,QAAQ,eAAe;AAC3G,YAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,eAAK,KAAK;AAEV,cAAI,gBAAgB,KAAK,OAAO,gBAAgB,KAAK;AACrD,cAAI,WAAW,CAAC,GAAG,QAAQ,MAAM,cAAc;AAC/C,cAAI,yBAAyB,IAAI,uBAAqB,cAAc,KAAK,UAAU,cAAc,KAAK,UAAU;AAChH,eAAK,WAAW;AAChB,eAAK,WAAW;AAEhB,iBAAO,KAAK,SAAS,QAAQ,QAAQ;AACnC,oBAAQ,QAAQ,QAAQ,KAAK,OAAO,QAAQ,KAAK;AACjD,gBAAI,sBAAsB,KAAK,mBAAmB,QAAQ,QAAQ,IAAI,GAAG;AACvE,4BAAc;AACd;AAAA,YACF,WAAW,KAAK,SAAS,QAAQ,UAAU;AACzC,4BAAc,KAAK;AACnB,uBAAS,KAAK,KAAK,eAAe,KAAK,iBAAiB,CAAC,CAAC;AAC1D,kBAAI,KAAK,SAAS,QAAQ,OAAO;AAC/B,qBAAK;AAAA,kBACH,KAAK;AAAA,kBACL;AAAA,gBACF;AAAA,cACF;AACA;AAAA,YACF,OAAO;AACL,uBAAS,KAAK,KAAK,iBAAiB,OAAO,wBAAwB,KAAK,cAAc,CAAC;AAAA,YACzF;AAAA,UACF;AACA,cAAI,cAAc,KAAK,YAAY,cAAc,KAAK;AACtD,eAAK,OAAO,QAAQ,MAAM;AAE1B,cAAI,cAAc,KAAK,iBAAiB,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,GAAG;AAC5E,iBAAK,mBAAmB,wBAAwB,KAAK;AACrD,iBAAK,+BAA+B;AACpC,iBAAK,WAAW;AAChB,iBAAK,WAAW;AAChB,mBAAO,KAAK,oBAAoB,UAAU,UAAU,UAAU,OAAO;AAAA,UACvE;AAEA,cAAI,CAAC,SAAS,UAAU,aAAa;AAAE,iBAAK,WAAW,KAAK,YAAY;AAAA,UAAG;AAC3E,cAAI,aAAa;AAAE,iBAAK,WAAW,WAAW;AAAA,UAAG;AACjD,eAAK,sBAAsB,wBAAwB,IAAI;AACvD,eAAK,WAAW,eAAe,KAAK;AACpC,eAAK,WAAW,eAAe,KAAK;AAEpC,cAAI,SAAS,SAAS,GAAG;AACvB,kBAAM,KAAK,YAAY,eAAe,aAAa;AACnD,gBAAI,cAAc;AAClB,iBAAK,aAAa,KAAK,sBAAsB,aAAa,WAAW;AAAA,UACvE,OAAO;AACL,kBAAM,SAAS,CAAC;AAAA,UAClB;AAAA,QACF,OAAO;AACL,gBAAM,KAAK,qBAAqB;AAAA,QAClC;AAEA,YAAI,KAAK,QAAQ,gBAAgB;AAC/B,cAAI,MAAM,KAAK,YAAY,UAAU,QAAQ;AAC7C,cAAI,aAAa;AACjB,iBAAO,KAAK,WAAW,KAAK,yBAAyB;AAAA,QACvD,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,WAAK,iBAAiB,SAAS,MAAM;AACnC,eAAO;AAAA,MACT;AAEA,WAAK,sBAAsB,SAAS,UAAU,UAAU,UAAU,SAAS;AACzE,eAAO,KAAK,qBAAqB,KAAK,YAAY,UAAU,QAAQ,GAAG,UAAU,OAAO,OAAO;AAAA,MACjG;AAQA,UAAI,QAAQ,CAAC;AAEb,WAAK,WAAW,WAAW;AACzB,YAAI,KAAK,aAAa;AAAE,eAAK,iBAAiB,KAAK,OAAO,gCAAgC;AAAA,QAAG;AAC7F,YAAI,OAAO,KAAK,UAAU;AAC1B,aAAK,KAAK;AACV,YAAI,KAAK,QAAQ,eAAe,KAAK,KAAK,SAAS,QAAQ,KAAK;AAC9D,cAAI,OAAO,KAAK,YAAY,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,KAAK;AAClE,eAAK,OAAO;AACZ,eAAK,OAAO,KAAK,WAAW,MAAM,YAAY;AAC9C,eAAK,KAAK;AACV,cAAI,cAAc,KAAK;AACvB,eAAK,WAAW,KAAK,WAAW,IAAI;AACpC,cAAI,KAAK,SAAS,SAAS,UACzB;AAAE,iBAAK,iBAAiB,KAAK,SAAS,OAAO,sDAAsD;AAAA,UAAG;AACxG,cAAI,aACF;AAAE,iBAAK,iBAAiB,KAAK,OAAO,kDAAkD;AAAA,UAAG;AAC3F,cAAI,CAAC,KAAK,mBACR;AAAE,iBAAK,iBAAiB,KAAK,OAAO,mEAAmE;AAAA,UAAG;AAC5G,iBAAO,KAAK,WAAW,MAAM,cAAc;AAAA,QAC7C;AACA,YAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,aAAK,SAAS,KAAK,gBAAgB,KAAK,cAAc,MAAM,OAAO,IAAI,GAAG,UAAU,UAAU,MAAM,KAAK;AACzG,YAAI,KAAK,IAAI,QAAQ,MAAM,GAAG;AAAE,eAAK,YAAY,KAAK,cAAc,QAAQ,QAAQ,KAAK,QAAQ,eAAe,GAAG,KAAK;AAAA,QAAG,OACtH;AAAE,eAAK,YAAY;AAAA,QAAO;AAC/B,eAAO,KAAK,WAAW,MAAM,eAAe;AAAA,MAC9C;AAIA,WAAK,uBAAuB,SAASM,MAAK;AACxC,YAAI,WAAWA,KAAI;AAEnB,YAAI,OAAO,KAAK,UAAU;AAC1B,YAAI,KAAK,SAAS,QAAQ,iBAAiB;AACzC,cAAI,CAAC,UAAU;AACb,iBAAK,iBAAiB,KAAK,OAAO,kDAAkD;AAAA,UACtF;AACA,eAAK,QAAQ;AAAA,YACX,KAAK,KAAK,MAAM,QAAQ,UAAU,IAAI;AAAA,YACtC,QAAQ;AAAA,UACV;AAAA,QACF,OAAO;AACL,eAAK,QAAQ;AAAA,YACX,KAAK,KAAK,MAAM,MAAM,KAAK,OAAO,KAAK,GAAG,EAAE,QAAQ,UAAU,IAAI;AAAA,YAClE,QAAQ,KAAK;AAAA,UACf;AAAA,QACF;AACA,aAAK,KAAK;AACV,aAAK,OAAO,KAAK,SAAS,QAAQ;AAClC,eAAO,KAAK,WAAW,MAAM,iBAAiB;AAAA,MAChD;AAEA,WAAK,gBAAgB,SAASA,MAAK;AACjC,YAAKA,SAAQ,OAAS,CAAAA,OAAM,CAAC;AAC7B,YAAI,WAAWA,KAAI;AAAU,YAAK,aAAa,OAAS,YAAW;AAEnE,YAAI,OAAO,KAAK,UAAU;AAC1B,aAAK,KAAK;AACV,aAAK,cAAc,CAAC;AACpB,YAAI,SAAS,KAAK,qBAAqB,EAAC,SAAkB,CAAC;AAC3D,aAAK,SAAS,CAAC,MAAM;AACrB,eAAO,CAAC,OAAO,MAAM;AACnB,cAAI,KAAK,SAAS,QAAQ,KAAK;AAAE,iBAAK,MAAM,KAAK,KAAK,+BAA+B;AAAA,UAAG;AACxF,eAAK,OAAO,QAAQ,YAAY;AAChC,eAAK,YAAY,KAAK,KAAK,gBAAgB,CAAC;AAC5C,eAAK,OAAO,QAAQ,MAAM;AAC1B,eAAK,OAAO,KAAK,SAAS,KAAK,qBAAqB,EAAC,SAAkB,CAAC,CAAC;AAAA,QAC3E;AACA,aAAK,KAAK;AACV,eAAO,KAAK,WAAW,MAAM,iBAAiB;AAAA,MAChD;AAEA,WAAK,cAAc,SAAS,MAAM;AAChC,eAAO,CAAC,KAAK,YAAY,KAAK,IAAI,SAAS,gBAAgB,KAAK,IAAI,SAAS,YAC1E,KAAK,SAAS,QAAQ,QAAQ,KAAK,SAAS,QAAQ,OAAO,KAAK,SAAS,QAAQ,UAAU,KAAK,SAAS,QAAQ,YAAY,KAAK,KAAK,WAAY,KAAK,QAAQ,eAAe,KAAK,KAAK,SAAS,QAAQ,SAC3M,CAAC,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,KAAK,CAAC;AAAA,MACjE;AAIA,WAAK,WAAW,SAAS,WAAW,wBAAwB;AAC1D,YAAI,OAAO,KAAK,UAAU,GAAG,QAAQ,MAAM,WAAW,CAAC;AACvD,aAAK,aAAa,CAAC;AACnB,aAAK,KAAK;AACV,eAAO,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAChC,cAAI,CAAC,OAAO;AACV,iBAAK,OAAO,QAAQ,KAAK;AACzB,gBAAI,KAAK,QAAQ,eAAe,KAAK,KAAK,mBAAmB,QAAQ,MAAM,GAAG;AAAE;AAAA,YAAM;AAAA,UACxF,OAAO;AAAE,oBAAQ;AAAA,UAAO;AAExB,cAAI,OAAO,KAAK,cAAc,WAAW,sBAAsB;AAC/D,cAAI,CAAC,WAAW;AAAE,iBAAK,eAAe,MAAM,UAAU,sBAAsB;AAAA,UAAG;AAC/E,eAAK,WAAW,KAAK,IAAI;AAAA,QAC3B;AACA,eAAO,KAAK,WAAW,MAAM,YAAY,kBAAkB,kBAAkB;AAAA,MAC/E;AAEA,WAAK,gBAAgB,SAAS,WAAW,wBAAwB;AAC/D,YAAI,OAAO,KAAK,UAAU,GAAG,aAAa,SAAS,UAAU;AAC7D,YAAI,KAAK,QAAQ,eAAe,KAAK,KAAK,IAAI,QAAQ,QAAQ,GAAG;AAC/D,cAAI,WAAW;AACb,iBAAK,WAAW,KAAK,WAAW,KAAK;AACrC,gBAAI,KAAK,SAAS,QAAQ,OAAO;AAC/B,mBAAK,iBAAiB,KAAK,OAAO,+CAA+C;AAAA,YACnF;AACA,mBAAO,KAAK,WAAW,MAAM,aAAa;AAAA,UAC5C;AAEA,eAAK,WAAW,KAAK,iBAAiB,OAAO,sBAAsB;AAEnE,cAAI,KAAK,SAAS,QAAQ,SAAS,0BAA0B,uBAAuB,gBAAgB,GAAG;AACrG,mCAAuB,gBAAgB,KAAK;AAAA,UAC9C;AAEA,iBAAO,KAAK,WAAW,MAAM,eAAe;AAAA,QAC9C;AACA,YAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,eAAK,SAAS;AACd,eAAK,YAAY;AACjB,cAAI,aAAa,wBAAwB;AACvC,uBAAW,KAAK;AAChB,uBAAW,KAAK;AAAA,UAClB;AACA,cAAI,CAAC,WACH;AAAE,0BAAc,KAAK,IAAI,QAAQ,IAAI;AAAA,UAAG;AAAA,QAC5C;AACA,YAAI,cAAc,KAAK;AACvB,aAAK,kBAAkB,IAAI;AAC3B,YAAI,CAAC,aAAa,CAAC,eAAe,KAAK,QAAQ,eAAe,KAAK,CAAC,eAAe,KAAK,YAAY,IAAI,GAAG;AACzG,oBAAU;AACV,wBAAc,KAAK,QAAQ,eAAe,KAAK,KAAK,IAAI,QAAQ,IAAI;AACpE,eAAK,kBAAkB,IAAI;AAAA,QAC7B,OAAO;AACL,oBAAU;AAAA,QACZ;AACA,aAAK,mBAAmB,MAAM,WAAW,aAAa,SAAS,UAAU,UAAU,wBAAwB,WAAW;AACtH,eAAO,KAAK,WAAW,MAAM,UAAU;AAAA,MACzC;AAEA,WAAK,oBAAoB,SAAS,MAAM;AACtC,YAAI,OAAO,KAAK,IAAI;AACpB,aAAK,kBAAkB,IAAI;AAC3B,aAAK,QAAQ,KAAK,YAAY,KAAK;AACnC,aAAK,OAAO;AACZ,YAAI,aAAa,KAAK,SAAS,QAAQ,IAAI;AAC3C,YAAI,KAAK,MAAM,OAAO,WAAW,YAAY;AAC3C,cAAI,QAAQ,KAAK,MAAM;AACvB,cAAI,KAAK,SAAS,OAChB;AAAE,iBAAK,iBAAiB,OAAO,8BAA8B;AAAA,UAAG,OAEhE;AAAE,iBAAK,iBAAiB,OAAO,sCAAsC;AAAA,UAAG;AAAA,QAC5E,OAAO;AACL,cAAI,KAAK,SAAS,SAAS,KAAK,MAAM,OAAO,CAAC,EAAE,SAAS,eACvD;AAAE,iBAAK,iBAAiB,KAAK,MAAM,OAAO,CAAC,EAAE,OAAO,+BAA+B;AAAA,UAAG;AAAA,QAC1F;AAAA,MACF;AAEA,WAAK,qBAAqB,SAAS,MAAM,WAAW,aAAa,SAAS,UAAU,UAAU,wBAAwB,aAAa;AACjI,aAAK,eAAe,YAAY,KAAK,SAAS,QAAQ,OACpD;AAAE,eAAK,WAAW;AAAA,QAAG;AAEvB,YAAI,KAAK,IAAI,QAAQ,KAAK,GAAG;AAC3B,eAAK,QAAQ,YAAY,KAAK,kBAAkB,KAAK,OAAO,KAAK,QAAQ,IAAI,KAAK,iBAAiB,OAAO,sBAAsB;AAChI,eAAK,OAAO;AAAA,QACd,WAAW,KAAK,QAAQ,eAAe,KAAK,KAAK,SAAS,QAAQ,QAAQ;AACxE,cAAI,WAAW;AAAE,iBAAK,WAAW;AAAA,UAAG;AACpC,eAAK,SAAS;AACd,eAAK,QAAQ,KAAK,YAAY,aAAa,OAAO;AAClD,eAAK,OAAO;AAAA,QACd,WAAW,CAAC,aAAa,CAAC,eACf,KAAK,QAAQ,eAAe,KAAK,CAAC,KAAK,YAAY,KAAK,IAAI,SAAS,iBACpE,KAAK,IAAI,SAAS,SAAS,KAAK,IAAI,SAAS,WAC7C,KAAK,SAAS,QAAQ,SAAS,KAAK,SAAS,QAAQ,UAAU,KAAK,SAAS,QAAQ,KAAK;AACpG,cAAI,eAAe,SAAS;AAAE,iBAAK,WAAW;AAAA,UAAG;AACjD,eAAK,kBAAkB,IAAI;AAAA,QAC7B,WAAW,KAAK,QAAQ,eAAe,KAAK,CAAC,KAAK,YAAY,KAAK,IAAI,SAAS,cAAc;AAC5F,cAAI,eAAe,SAAS;AAAE,iBAAK,WAAW;AAAA,UAAG;AACjD,eAAK,gBAAgB,KAAK,GAAG;AAC7B,cAAI,KAAK,IAAI,SAAS,WAAW,CAAC,KAAK,eACrC;AAAE,iBAAK,gBAAgB;AAAA,UAAU;AACnC,cAAI,WAAW;AACb,iBAAK,QAAQ,KAAK,kBAAkB,UAAU,UAAU,KAAK,SAAS,KAAK,GAAG,CAAC;AAAA,UACjF,WAAW,KAAK,SAAS,QAAQ,MAAM,wBAAwB;AAC7D,gBAAI,uBAAuB,kBAAkB,GAC3C;AAAE,qCAAuB,kBAAkB,KAAK;AAAA,YAAO;AACzD,iBAAK,QAAQ,KAAK,kBAAkB,UAAU,UAAU,KAAK,SAAS,KAAK,GAAG,CAAC;AAAA,UACjF,OAAO;AACL,iBAAK,QAAQ,KAAK,SAAS,KAAK,GAAG;AAAA,UACrC;AACA,eAAK,OAAO;AACZ,eAAK,YAAY;AAAA,QACnB,OAAO;AAAE,eAAK,WAAW;AAAA,QAAG;AAAA,MAC9B;AAEA,WAAK,oBAAoB,SAAS,MAAM;AACtC,YAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,cAAI,KAAK,IAAI,QAAQ,QAAQ,GAAG;AAC9B,iBAAK,WAAW;AAChB,iBAAK,MAAM,KAAK,iBAAiB;AACjC,iBAAK,OAAO,QAAQ,QAAQ;AAC5B,mBAAO,KAAK;AAAA,UACd,OAAO;AACL,iBAAK,WAAW;AAAA,UAClB;AAAA,QACF;AACA,eAAO,KAAK,MAAM,KAAK,SAAS,QAAQ,OAAO,KAAK,SAAS,QAAQ,SAAS,KAAK,cAAc,IAAI,KAAK,WAAW,KAAK,QAAQ,kBAAkB,OAAO;AAAA,MAC7J;AAIA,WAAK,eAAe,SAAS,MAAM;AACjC,aAAK,KAAK;AACV,YAAI,KAAK,QAAQ,eAAe,GAAG;AAAE,eAAK,YAAY,KAAK,aAAa;AAAA,QAAO;AAC/E,YAAI,KAAK,QAAQ,eAAe,GAAG;AAAE,eAAK,QAAQ;AAAA,QAAO;AAAA,MAC3D;AAIA,WAAK,cAAc,SAAS,aAAa,SAAS,kBAAkB;AAClE,YAAI,OAAO,KAAK,UAAU,GAAG,cAAc,KAAK,UAAU,cAAc,KAAK,UAAU,mBAAmB,KAAK;AAE/G,aAAK,aAAa,IAAI;AACtB,YAAI,KAAK,QAAQ,eAAe,GAC9B;AAAE,eAAK,YAAY;AAAA,QAAa;AAClC,YAAI,KAAK,QAAQ,eAAe,GAC9B;AAAE,eAAK,QAAQ,CAAC,CAAC;AAAA,QAAS;AAE5B,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,gBAAgB;AACrB,aAAK,WAAW,cAAc,SAAS,KAAK,SAAS,IAAI,eAAe,mBAAmB,qBAAqB,EAAE;AAElH,aAAK,OAAO,QAAQ,MAAM;AAC1B,aAAK,SAAS,KAAK,iBAAiB,QAAQ,QAAQ,OAAO,KAAK,QAAQ,eAAe,CAAC;AACxF,aAAK,+BAA+B;AACpC,aAAK,kBAAkB,MAAM,OAAO,MAAM,KAAK;AAE/C,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,gBAAgB;AACrB,eAAO,KAAK,WAAW,MAAM,oBAAoB;AAAA,MACnD;AAIA,WAAK,uBAAuB,SAAS,MAAM,QAAQ,SAAS,SAAS;AACnE,YAAI,cAAc,KAAK,UAAU,cAAc,KAAK,UAAU,mBAAmB,KAAK;AAEtF,aAAK,WAAW,cAAc,SAAS,KAAK,IAAI,WAAW;AAC3D,aAAK,aAAa,IAAI;AACtB,YAAI,KAAK,QAAQ,eAAe,GAAG;AAAE,eAAK,QAAQ,CAAC,CAAC;AAAA,QAAS;AAE7D,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,gBAAgB;AAErB,aAAK,SAAS,KAAK,iBAAiB,QAAQ,IAAI;AAChD,aAAK,kBAAkB,MAAM,MAAM,OAAO,OAAO;AAEjD,aAAK,WAAW;AAChB,aAAK,WAAW;AAChB,aAAK,gBAAgB;AACrB,eAAO,KAAK,WAAW,MAAM,yBAAyB;AAAA,MACxD;AAIA,WAAK,oBAAoB,SAAS,MAAM,iBAAiB,UAAU,SAAS;AAC1E,YAAI,eAAe,mBAAmB,KAAK,SAAS,QAAQ;AAC5D,YAAI,YAAY,KAAK,QAAQ,YAAY;AAEzC,YAAI,cAAc;AAChB,eAAK,OAAO,KAAK,iBAAiB,OAAO;AACzC,eAAK,aAAa;AAClB,eAAK,YAAY,MAAM,KAAK;AAAA,QAC9B,OAAO;AACL,cAAI,YAAY,KAAK,QAAQ,eAAe,KAAK,CAAC,KAAK,kBAAkB,KAAK,MAAM;AACpF,cAAI,CAAC,aAAa,WAAW;AAC3B,wBAAY,KAAK,gBAAgB,KAAK,GAAG;AAIzC,gBAAI,aAAa,WACf;AAAE,mBAAK,iBAAiB,KAAK,OAAO,2EAA2E;AAAA,YAAG;AAAA,UACtH;AAGA,cAAI,YAAY,KAAK;AACrB,eAAK,SAAS,CAAC;AACf,cAAI,WAAW;AAAE,iBAAK,SAAS;AAAA,UAAM;AAIrC,eAAK,YAAY,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,mBAAmB,CAAC,YAAY,KAAK,kBAAkB,KAAK,MAAM,CAAC;AAEvH,cAAI,KAAK,UAAU,KAAK,IAAI;AAAE,iBAAK,gBAAgB,KAAK,IAAI,YAAY;AAAA,UAAG;AAC3E,eAAK,OAAO,KAAK,WAAW,OAAO,QAAW,aAAa,CAAC,SAAS;AACrE,eAAK,aAAa;AAClB,eAAK,uBAAuB,KAAK,KAAK,IAAI;AAC1C,eAAK,SAAS;AAAA,QAChB;AACA,aAAK,UAAU;AAAA,MACjB;AAEA,WAAK,oBAAoB,SAAS,QAAQ;AACxC,iBAASN,KAAI,GAAGU,QAAO,QAAQV,KAAIU,MAAK,QAAQV,MAAK,GACnD;AACA,cAAI,QAAQU,MAAKV,EAAC;AAElB,cAAI,MAAM,SAAS,cAAc;AAAE,mBAAO;AAAA,UAC5C;AAAA,QAAE;AACF,eAAO;AAAA,MACT;AAKA,WAAK,cAAc,SAAS,MAAM,iBAAiB;AACjD,YAAI,WAAW,uBAAO,OAAO,IAAI;AACjC,iBAASA,KAAI,GAAGU,QAAO,KAAK,QAAQV,KAAIU,MAAK,QAAQV,MAAK,GACxD;AACA,cAAI,QAAQU,MAAKV,EAAC;AAElB,eAAK,sBAAsB,OAAO,UAAU,kBAAkB,OAAO,QAAQ;AAAA,QAC/E;AAAA,MACF;AAQA,WAAK,gBAAgB,SAAS,OAAO,oBAAoB,YAAY,wBAAwB;AAC3F,YAAI,OAAO,CAAC,GAAG,QAAQ;AACvB,eAAO,CAAC,KAAK,IAAI,KAAK,GAAG;AACvB,cAAI,CAAC,OAAO;AACV,iBAAK,OAAO,QAAQ,KAAK;AACzB,gBAAI,sBAAsB,KAAK,mBAAmB,KAAK,GAAG;AAAE;AAAA,YAAM;AAAA,UACpE,OAAO;AAAE,oBAAQ;AAAA,UAAO;AAExB,cAAI,MAAO;AACX,cAAI,cAAc,KAAK,SAAS,QAAQ,OACtC;AAAE,kBAAM;AAAA,UAAM,WACP,KAAK,SAAS,QAAQ,UAAU;AACvC,kBAAM,KAAK,YAAY,sBAAsB;AAC7C,gBAAI,0BAA0B,KAAK,SAAS,QAAQ,SAAS,uBAAuB,gBAAgB,GAClG;AAAE,qCAAuB,gBAAgB,KAAK;AAAA,YAAO;AAAA,UACzD,OAAO;AACL,kBAAM,KAAK,iBAAiB,OAAO,sBAAsB;AAAA,UAC3D;AACA,eAAK,KAAK,GAAG;AAAA,QACf;AACA,eAAO;AAAA,MACT;AAEA,WAAK,kBAAkB,SAASM,MAAK;AACnC,YAAI,QAAQA,KAAI;AAChB,YAAI,MAAMA,KAAI;AACd,YAAI,OAAOA,KAAI;AAEf,YAAI,KAAK,eAAe,SAAS,SAC/B;AAAE,eAAK,iBAAiB,OAAO,qDAAqD;AAAA,QAAG;AACzF,YAAI,KAAK,WAAW,SAAS,SAC3B;AAAE,eAAK,iBAAiB,OAAO,2DAA2D;AAAA,QAAG;AAC/F,YAAI,EAAE,KAAK,iBAAiB,EAAE,QAAQ,cAAc,SAAS,aAC3D;AAAE,eAAK,iBAAiB,OAAO,mDAAmD;AAAA,QAAG;AACvF,YAAI,KAAK,uBAAuB,SAAS,eAAe,SAAS,UAC/D;AAAE,eAAK,MAAM,OAAQ,gBAAgB,OAAO,uCAAwC;AAAA,QAAG;AACzF,YAAI,KAAK,SAAS,KAAK,IAAI,GACzB;AAAE,eAAK,MAAM,OAAQ,yBAAyB,OAAO,GAAI;AAAA,QAAG;AAC9D,YAAI,KAAK,QAAQ,cAAc,KAC7B,KAAK,MAAM,MAAM,OAAO,GAAG,EAAE,QAAQ,IAAI,MAAM,IAAI;AAAE;AAAA,QAAO;AAC9D,YAAI,KAAK,KAAK,SAAS,KAAK,sBAAsB,KAAK;AACvD,YAAI,GAAG,KAAK,IAAI,GAAG;AACjB,cAAI,CAAC,KAAK,WAAW,SAAS,SAC5B;AAAE,iBAAK,iBAAiB,OAAO,sDAAsD;AAAA,UAAG;AAC1F,eAAK,iBAAiB,OAAQ,kBAAkB,OAAO,eAAgB;AAAA,QACzE;AAAA,MACF;AAMA,WAAK,aAAa,SAAS,SAAS;AAClC,YAAI,OAAO,KAAK,eAAe;AAC/B,aAAK,KAAK,CAAC,CAAC,OAAO;AACnB,aAAK,WAAW,MAAM,YAAY;AAClC,YAAI,CAAC,SAAS;AACZ,eAAK,gBAAgB,IAAI;AACzB,cAAI,KAAK,SAAS,WAAW,CAAC,KAAK,eACjC;AAAE,iBAAK,gBAAgB,KAAK;AAAA,UAAO;AAAA,QACvC;AACA,eAAO;AAAA,MACT;AAEA,WAAK,iBAAiB,WAAW;AAC/B,YAAI,OAAO,KAAK,UAAU;AAC1B,YAAI,KAAK,SAAS,QAAQ,MAAM;AAC9B,eAAK,OAAO,KAAK;AAAA,QACnB,WAAW,KAAK,KAAK,SAAS;AAC5B,eAAK,OAAO,KAAK,KAAK;AAMtB,eAAK,KAAK,SAAS,WAAW,KAAK,SAAS,gBACzC,KAAK,eAAe,KAAK,eAAe,KAAK,KAAK,MAAM,WAAW,KAAK,YAAY,MAAM,KAAK;AAChG,iBAAK,QAAQ,IAAI;AAAA,UACnB;AACA,eAAK,OAAO,QAAQ;AAAA,QACtB,OAAO;AACL,eAAK,WAAW;AAAA,QAClB;AACA,eAAO;AAAA,MACT;AAEA,WAAK,oBAAoB,WAAW;AAClC,YAAI,OAAO,KAAK,UAAU;AAC1B,YAAI,KAAK,SAAS,QAAQ,WAAW;AACnC,eAAK,OAAO,KAAK;AAAA,QACnB,OAAO;AACL,eAAK,WAAW;AAAA,QAClB;AACA,aAAK,KAAK;AACV,aAAK,WAAW,MAAM,mBAAmB;AAGzC,YAAI,KAAK,QAAQ,oBAAoB;AACnC,cAAI,KAAK,iBAAiB,WAAW,GAAG;AACtC,iBAAK,MAAM,KAAK,OAAQ,qBAAsB,KAAK,OAAQ,0CAA2C;AAAA,UACxG,OAAO;AACL,iBAAK,iBAAiB,KAAK,iBAAiB,SAAS,CAAC,EAAE,KAAK,KAAK,IAAI;AAAA,UACxE;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAIA,WAAK,aAAa,SAAS,SAAS;AAClC,YAAI,CAAC,KAAK,UAAU;AAAE,eAAK,WAAW,KAAK;AAAA,QAAO;AAElD,YAAI,OAAO,KAAK,UAAU;AAC1B,aAAK,KAAK;AACV,YAAI,KAAK,SAAS,QAAQ,QAAQ,KAAK,mBAAmB,KAAM,KAAK,SAAS,QAAQ,QAAQ,CAAC,KAAK,KAAK,YAAa;AACpH,eAAK,WAAW;AAChB,eAAK,WAAW;AAAA,QAClB,OAAO;AACL,eAAK,WAAW,KAAK,IAAI,QAAQ,IAAI;AACrC,eAAK,WAAW,KAAK,iBAAiB,OAAO;AAAA,QAC/C;AACA,eAAO,KAAK,WAAW,MAAM,iBAAiB;AAAA,MAChD;AAEA,WAAK,aAAa,SAAS,SAAS;AAClC,YAAI,CAAC,KAAK,UAAU;AAAE,eAAK,WAAW,KAAK;AAAA,QAAO;AAElD,YAAI,OAAO,KAAK,UAAU;AAC1B,aAAK,KAAK;AACV,aAAK,WAAW,KAAK,gBAAgB,MAAM,MAAM,OAAO,OAAO;AAC/D,eAAO,KAAK,WAAW,MAAM,iBAAiB;AAAA,MAChD;AAEA,UAAI,OAAO,OAAO;AAQlB,WAAK,QAAQ,SAAS,KAAK,SAAS;AAClC,YAAI,MAAM,YAAY,KAAK,OAAO,GAAG;AACrC,mBAAW,OAAO,IAAI,OAAO,MAAM,IAAI,SAAS;AAChD,YAAI,KAAK,YAAY;AACnB,qBAAW,SAAS,KAAK;AAAA,QAC3B;AACA,YAAI,MAAM,IAAI,YAAY,OAAO;AACjC,YAAI,MAAM;AAAK,YAAI,MAAM;AAAK,YAAI,WAAW,KAAK;AAClD,cAAM;AAAA,MACR;AAEA,WAAK,mBAAmB,KAAK;AAE7B,WAAK,cAAc,WAAW;AAC5B,YAAI,KAAK,QAAQ,WAAW;AAC1B,iBAAO,IAAI,SAAS,KAAK,SAAS,KAAK,MAAM,KAAK,SAAS;AAAA,QAC7D;AAAA,MACF;AAEA,UAAI,OAAO,OAAO;AAElB,UAAI,QAAQ,SAASO,OAAM,OAAO;AAChC,aAAK,QAAQ;AAEb,aAAK,MAAM,CAAC;AAEZ,aAAK,UAAU,CAAC;AAEhB,aAAK,YAAY,CAAC;AAAA,MACpB;AAIA,WAAK,aAAa,SAAS,OAAO;AAChC,aAAK,WAAW,KAAK,IAAI,MAAM,KAAK,CAAC;AAAA,MACvC;AAEA,WAAK,YAAY,WAAW;AAC1B,aAAK,WAAW,IAAI;AAAA,MACtB;AAKA,WAAK,6BAA6B,SAAS,OAAO;AAChD,eAAQ,MAAM,QAAQ,kBAAmB,CAAC,KAAK,YAAa,MAAM,QAAQ;AAAA,MAC5E;AAEA,WAAK,cAAc,SAAS,MAAM,aAAa,KAAK;AAClD,YAAI,aAAa;AACjB,YAAI,gBAAgB,cAAc;AAChC,cAAI,QAAQ,KAAK,aAAa;AAC9B,uBAAa,MAAM,QAAQ,QAAQ,IAAI,IAAI,MAAM,MAAM,UAAU,QAAQ,IAAI,IAAI,MAAM,MAAM,IAAI,QAAQ,IAAI,IAAI;AACjH,gBAAM,QAAQ,KAAK,IAAI;AACvB,cAAI,KAAK,YAAa,MAAM,QAAQ,WAClC;AAAE,mBAAO,KAAK,iBAAiB,IAAI;AAAA,UAAG;AAAA,QAC1C,WAAW,gBAAgB,mBAAmB;AAC5C,cAAI,UAAU,KAAK,aAAa;AAChC,kBAAQ,QAAQ,KAAK,IAAI;AAAA,QAC3B,WAAW,gBAAgB,eAAe;AACxC,cAAI,UAAU,KAAK,aAAa;AAChC,cAAI,KAAK,qBACP;AAAE,yBAAa,QAAQ,QAAQ,QAAQ,IAAI,IAAI;AAAA,UAAI,OAEnD;AAAE,yBAAa,QAAQ,QAAQ,QAAQ,IAAI,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,IAAI;AAAA,UAAI;AACvF,kBAAQ,UAAU,KAAK,IAAI;AAAA,QAC7B,OAAO;AACL,mBAASb,KAAI,KAAK,WAAW,SAAS,GAAGA,MAAK,GAAG,EAAEA,IAAG;AACpD,gBAAI,UAAU,KAAK,WAAWA,EAAC;AAC/B,gBAAI,QAAQ,QAAQ,QAAQ,IAAI,IAAI,MAAM,EAAG,QAAQ,QAAQ,sBAAuB,QAAQ,QAAQ,CAAC,MAAM,SACvG,CAAC,KAAK,2BAA2B,OAAO,KAAK,QAAQ,UAAU,QAAQ,IAAI,IAAI,IAAI;AACrF,2BAAa;AACb;AAAA,YACF;AACA,oBAAQ,IAAI,KAAK,IAAI;AACrB,gBAAI,KAAK,YAAa,QAAQ,QAAQ,WACpC;AAAE,qBAAO,KAAK,iBAAiB,IAAI;AAAA,YAAG;AACxC,gBAAI,QAAQ,QAAQ,WAAW;AAAE;AAAA,YAAM;AAAA,UACzC;AAAA,QACF;AACA,YAAI,YAAY;AAAE,eAAK,iBAAiB,KAAM,iBAAiB,OAAO,6BAA8B;AAAA,QAAG;AAAA,MACzG;AAEA,WAAK,mBAAmB,SAAS,IAAI;AAEnC,YAAI,KAAK,WAAW,CAAC,EAAE,QAAQ,QAAQ,GAAG,IAAI,MAAM,MAChD,KAAK,WAAW,CAAC,EAAE,IAAI,QAAQ,GAAG,IAAI,MAAM,IAAI;AAClD,eAAK,iBAAiB,GAAG,IAAI,IAAI;AAAA,QACnC;AAAA,MACF;AAEA,WAAK,eAAe,WAAW;AAC7B,eAAO,KAAK,WAAW,KAAK,WAAW,SAAS,CAAC;AAAA,MACnD;AAEA,WAAK,kBAAkB,WAAW;AAChC,iBAASA,KAAI,KAAK,WAAW,SAAS,KAAIA,MAAK;AAC7C,cAAI,QAAQ,KAAK,WAAWA,EAAC;AAC7B,cAAI,MAAM,SAAS,YAAY,yBAAyB,2BAA2B;AAAE,mBAAO;AAAA,UAAM;AAAA,QACpG;AAAA,MACF;AAGA,WAAK,mBAAmB,WAAW;AACjC,iBAASA,KAAI,KAAK,WAAW,SAAS,KAAIA,MAAK;AAC7C,cAAI,QAAQ,KAAK,WAAWA,EAAC;AAC7B,cAAI,MAAM,SAAS,YAAY,yBAAyB,6BACpD,EAAE,MAAM,QAAQ,cAAc;AAAE,mBAAO;AAAA,UAAM;AAAA,QACnD;AAAA,MACF;AAEA,UAAI,OAAO,SAASc,MAAK,QAAQ,KAAK,KAAK;AACzC,aAAK,OAAO;AACZ,aAAK,QAAQ;AACb,aAAK,MAAM;AACX,YAAI,OAAO,QAAQ,WACjB;AAAE,eAAK,MAAM,IAAI,eAAe,QAAQ,GAAG;AAAA,QAAG;AAChD,YAAI,OAAO,QAAQ,kBACjB;AAAE,eAAK,aAAa,OAAO,QAAQ;AAAA,QAAkB;AACvD,YAAI,OAAO,QAAQ,QACjB;AAAE,eAAK,QAAQ,CAAC,KAAK,CAAC;AAAA,QAAG;AAAA,MAC7B;AAIA,UAAI,OAAO,OAAO;AAElB,WAAK,YAAY,WAAW;AAC1B,eAAO,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK,QAAQ;AAAA,MACjD;AAEA,WAAK,cAAc,SAAS,KAAK,KAAK;AACpC,eAAO,IAAI,KAAK,MAAM,KAAK,GAAG;AAAA,MAChC;AAIA,eAAS,aAAa,MAAM,MAAM,KAAK,KAAK;AAC1C,aAAK,OAAO;AACZ,aAAK,MAAM;AACX,YAAI,KAAK,QAAQ,WACf;AAAE,eAAK,IAAI,MAAM;AAAA,QAAK;AACxB,YAAI,KAAK,QAAQ,QACf;AAAE,eAAK,MAAM,CAAC,IAAI;AAAA,QAAK;AACzB,eAAO;AAAA,MACT;AAEA,WAAK,aAAa,SAAS,MAAM,MAAM;AACrC,eAAO,aAAa,KAAK,MAAM,MAAM,MAAM,KAAK,YAAY,KAAK,aAAa;AAAA,MAChF;AAIA,WAAK,eAAe,SAAS,MAAM,MAAM,KAAK,KAAK;AACjD,eAAO,aAAa,KAAK,MAAM,MAAM,MAAM,KAAK,GAAG;AAAA,MACrD;AAEA,WAAK,WAAW,SAAS,MAAM;AAC7B,YAAI,UAAU,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK,QAAQ;AACtD,iBAAS,QAAQ,MAAM;AAAE,kBAAQ,IAAI,IAAI,KAAK,IAAI;AAAA,QAAG;AACrD,eAAO;AAAA,MACT;AAGA,UAAI,6BAA6B;AAOjC,UAAI,wBAAwB;AAC5B,UAAI,yBAAyB,wBAAwB;AACrD,UAAI,yBAAyB;AAC7B,UAAI,yBAAyB,yBAAyB;AACtD,UAAI,yBAAyB;AAC7B,UAAI,yBAAyB;AAE7B,UAAI,0BAA0B;AAAA,QAC5B,GAAG;AAAA,QACH,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,MACN;AAGA,UAAI,kCAAkC;AAEtC,UAAI,mCAAmC;AAAA,QACrC,GAAG;AAAA,QACH,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,MACN;AAGA,UAAI,+BAA+B;AAGnC,UAAI,oBAAoB;AACxB,UAAI,qBAAqB,oBAAoB;AAC7C,UAAI,qBAAqB,qBAAqB;AAC9C,UAAI,qBAAqB,qBAAqB;AAC9C,UAAI,qBAAqB,qBAAqB;AAC9C,UAAI,qBAAqB,qBAAqB,MAAM;AAEpD,UAAI,sBAAsB;AAAA,QACxB,GAAG;AAAA,QACH,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,MACN;AAEA,UAAI,OAAO,CAAC;AACZ,eAAS,iBAAiBH,cAAa;AACrC,YAAI,IAAI,KAAKA,YAAW,IAAI;AAAA,UAC1B,QAAQ,YAAY,wBAAwBA,YAAW,IAAI,MAAM,4BAA4B;AAAA,UAC7F,iBAAiB,YAAY,iCAAiCA,YAAW,CAAC;AAAA,UAC1E,WAAW;AAAA,YACT,kBAAkB,YAAY,4BAA4B;AAAA,YAC1D,QAAQ,YAAY,oBAAoBA,YAAW,CAAC;AAAA,UACtD;AAAA,QACF;AACA,UAAE,UAAU,oBAAoB,EAAE,UAAU;AAE5C,UAAE,UAAU,KAAK,EAAE,UAAU;AAC7B,UAAE,UAAU,KAAK,EAAE,UAAU;AAC7B,UAAE,UAAU,MAAM,EAAE,UAAU;AAAA,MAChC;AAEA,eAAS,IAAI,GAAG,OAAO,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvE,YAAI,cAAc,KAAK,CAAC;AAExB,yBAAiB,WAAW;AAAA,MAC9B;AAEA,UAAI,OAAO,OAAO;AAIlB,UAAI,WAAW,SAASI,UAAS,QAAQ,MAAM;AAE7C,aAAK,SAAS;AAEd,aAAK,OAAO,QAAQ;AAAA,MACtB;AAEA,eAAS,UAAU,gBAAgB,SAAS,cAAe,KAAK;AAG9D,iBAASC,QAAO,MAAMA,OAAMA,QAAOA,MAAK,QAAQ;AAC9C,mBAAS,QAAQ,KAAK,OAAO,QAAQ,MAAM,QAAQ;AACjD,gBAAIA,MAAK,SAAS,MAAM,QAAQA,UAAS,OAAO;AAAE,qBAAO;AAAA,YAAK;AAAA,UAChE;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,eAAS,UAAU,UAAU,SAAS,UAAW;AAC/C,eAAO,IAAI,SAAS,KAAK,QAAQ,KAAK,IAAI;AAAA,MAC5C;AAEA,UAAI,wBAAwB,SAASC,uBAAsB,QAAQ;AACjE,aAAK,SAAS;AACd,aAAK,aAAa,SAAS,OAAO,QAAQ,eAAe,IAAI,OAAO,OAAO,OAAO,QAAQ,eAAe,IAAI,MAAM,OAAO,OAAO,QAAQ,eAAe,KAAK,MAAM,OAAO,OAAO,QAAQ,eAAe,KAAK,MAAM;AACnN,aAAK,oBAAoB,KAAK,OAAO,QAAQ,eAAe,KAAK,KAAK,OAAO,QAAQ,WAAW;AAChG,aAAK,SAAS;AACd,aAAK,QAAQ;AACb,aAAK,QAAQ;AACb,aAAK,UAAU;AACf,aAAK,UAAU;AACf,aAAK,UAAU;AACf,aAAK,MAAM;AACX,aAAK,eAAe;AACpB,aAAK,kBAAkB;AACvB,aAAK,8BAA8B;AACnC,aAAK,qBAAqB;AAC1B,aAAK,mBAAmB;AACxB,aAAK,aAAa,uBAAO,OAAO,IAAI;AACpC,aAAK,qBAAqB,CAAC;AAC3B,aAAK,WAAW;AAAA,MAClB;AAEA,4BAAsB,UAAU,QAAQ,SAAS,MAAO,OAAO,SAAS,OAAO;AAC7E,YAAI,cAAc,MAAM,QAAQ,GAAG,MAAM;AACzC,YAAI,UAAU,MAAM,QAAQ,GAAG,MAAM;AACrC,aAAK,QAAQ,QAAQ;AACrB,aAAK,SAAS,UAAU;AACxB,aAAK,QAAQ;AACb,YAAI,eAAe,KAAK,OAAO,QAAQ,eAAe,IAAI;AACxD,eAAK,UAAU;AACf,eAAK,UAAU;AACf,eAAK,UAAU;AAAA,QACjB,OAAO;AACL,eAAK,UAAU,WAAW,KAAK,OAAO,QAAQ,eAAe;AAC7D,eAAK,UAAU;AACf,eAAK,UAAU,WAAW,KAAK,OAAO,QAAQ,eAAe;AAAA,QAC/D;AAAA,MACF;AAEA,4BAAsB,UAAU,QAAQ,SAAS,MAAO,SAAS;AAC/D,aAAK,OAAO,iBAAiB,KAAK,OAAQ,kCAAmC,KAAK,SAAU,QAAQ,OAAQ;AAAA,MAC9G;AAIA,4BAAsB,UAAU,KAAK,SAAS,GAAIjB,IAAG,QAAQ;AACzD,YAAK,WAAW,OAAS,UAAS;AAEpC,YAAI,IAAI,KAAK;AACb,YAAI,IAAI,EAAE;AACV,YAAIA,MAAK,GAAG;AACV,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,EAAE,WAAWA,EAAC;AACtB,YAAI,EAAE,UAAU,KAAK,YAAY,KAAK,SAAU,KAAK,SAAUA,KAAI,KAAK,GAAG;AACzE,iBAAO;AAAA,QACT;AACA,YAAI,OAAO,EAAE,WAAWA,KAAI,CAAC;AAC7B,eAAO,QAAQ,SAAU,QAAQ,SAAU,KAAK,MAAM,OAAO,WAAY;AAAA,MAC3E;AAEA,4BAAsB,UAAU,YAAY,SAAS,UAAWA,IAAG,QAAQ;AACvE,YAAK,WAAW,OAAS,UAAS;AAEpC,YAAI,IAAI,KAAK;AACb,YAAI,IAAI,EAAE;AACV,YAAIA,MAAK,GAAG;AACV,iBAAO;AAAA,QACT;AACA,YAAI,IAAI,EAAE,WAAWA,EAAC,GAAG;AACzB,YAAI,EAAE,UAAU,KAAK,YAAY,KAAK,SAAU,KAAK,SAAUA,KAAI,KAAK,MACnE,OAAO,EAAE,WAAWA,KAAI,CAAC,KAAK,SAAU,OAAO,OAAQ;AAC1D,iBAAOA,KAAI;AAAA,QACb;AACA,eAAOA,KAAI;AAAA,MACb;AAEA,4BAAsB,UAAU,UAAU,SAAS,QAAS,QAAQ;AAChE,YAAK,WAAW,OAAS,UAAS;AAEpC,eAAO,KAAK,GAAG,KAAK,KAAK,MAAM;AAAA,MACjC;AAEA,4BAAsB,UAAU,YAAY,SAAS,UAAW,QAAQ;AACpE,YAAK,WAAW,OAAS,UAAS;AAEpC,eAAO,KAAK,GAAG,KAAK,UAAU,KAAK,KAAK,MAAM,GAAG,MAAM;AAAA,MACzD;AAEA,4BAAsB,UAAU,UAAU,SAAS,QAAS,QAAQ;AAChE,YAAK,WAAW,OAAS,UAAS;AAEpC,aAAK,MAAM,KAAK,UAAU,KAAK,KAAK,MAAM;AAAA,MAC5C;AAEA,4BAAsB,UAAU,MAAM,SAAS,IAAK,IAAI,QAAQ;AAC5D,YAAK,WAAW,OAAS,UAAS;AAEpC,YAAI,KAAK,QAAQ,MAAM,MAAM,IAAI;AAC/B,eAAK,QAAQ,MAAM;AACnB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAEA,4BAAsB,UAAU,WAAW,SAAS,SAAU,KAAK,QAAQ;AACvE,YAAK,WAAW,OAAS,UAAS;AAEpC,YAAI,MAAM,KAAK;AACf,iBAASA,KAAI,GAAGU,QAAO,KAAKV,KAAIU,MAAK,QAAQV,MAAK,GAAG;AACnD,cAAI,KAAKU,MAAKV,EAAC;AAEb,cAAI,UAAU,KAAK,GAAG,KAAK,MAAM;AACnC,cAAI,YAAY,MAAM,YAAY,IAAI;AACpC,mBAAO;AAAA,UACT;AACA,gBAAM,KAAK,UAAU,KAAK,MAAM;AAAA,QAClC;AACA,aAAK,MAAM;AACX,eAAO;AAAA,MACT;AAQA,WAAK,sBAAsB,SAAS,OAAO;AACzC,YAAI,aAAa,MAAM;AACvB,YAAI,QAAQ,MAAM;AAElB,YAAI,IAAI;AACR,YAAI,IAAI;AAER,iBAASA,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,cAAI,OAAO,MAAM,OAAOA,EAAC;AACzB,cAAI,WAAW,QAAQ,IAAI,MAAM,IAAI;AACnC,iBAAK,MAAM,MAAM,OAAO,iCAAiC;AAAA,UAC3D;AACA,cAAI,MAAM,QAAQ,MAAMA,KAAI,CAAC,IAAI,IAAI;AACnC,iBAAK,MAAM,MAAM,OAAO,mCAAmC;AAAA,UAC7D;AACA,cAAI,SAAS,KAAK;AAAE,gBAAI;AAAA,UAAM;AAC9B,cAAI,SAAS,KAAK;AAAE,gBAAI;AAAA,UAAM;AAAA,QAChC;AACA,YAAI,KAAK,QAAQ,eAAe,MAAM,KAAK,GAAG;AAC5C,eAAK,MAAM,MAAM,OAAO,iCAAiC;AAAA,QAC3D;AAAA,MACF;AAEA,eAAS,QAAQ,KAAK;AACpB,iBAAS,KAAK,KAAK;AAAE,iBAAO;AAAA,QAAK;AACjC,eAAO;AAAA,MACT;AAQA,WAAK,wBAAwB,SAAS,OAAO;AAC3C,aAAK,eAAe,KAAK;AAOzB,YAAI,CAAC,MAAM,WAAW,KAAK,QAAQ,eAAe,KAAK,QAAQ,MAAM,UAAU,GAAG;AAChF,gBAAM,UAAU;AAChB,eAAK,eAAe,KAAK;AAAA,QAC3B;AAAA,MACF;AAGA,WAAK,iBAAiB,SAAS,OAAO;AACpC,cAAM,MAAM;AACZ,cAAM,eAAe;AACrB,cAAM,kBAAkB;AACxB,cAAM,8BAA8B;AACpC,cAAM,qBAAqB;AAC3B,cAAM,mBAAmB;AACzB,cAAM,aAAa,uBAAO,OAAO,IAAI;AACrC,cAAM,mBAAmB,SAAS;AAClC,cAAM,WAAW;AAEjB,aAAK,mBAAmB,KAAK;AAE7B,YAAI,MAAM,QAAQ,MAAM,OAAO,QAAQ;AAErC,cAAI,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,GAAG;AAC3B,kBAAM,MAAM,eAAe;AAAA,UAC7B;AACA,cAAI,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,KAAK,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,GAAG;AACtD,kBAAM,MAAM,0BAA0B;AAAA,UACxC;AAAA,QACF;AACA,YAAI,MAAM,mBAAmB,MAAM,oBAAoB;AACrD,gBAAM,MAAM,gBAAgB;AAAA,QAC9B;AACA,iBAASA,KAAI,GAAGU,QAAO,MAAM,oBAAoBV,KAAIU,MAAK,QAAQV,MAAK,GAAG;AACxE,cAAI,OAAOU,MAAKV,EAAC;AAEjB,cAAI,CAAC,MAAM,WAAW,IAAI,GAAG;AAC3B,kBAAM,MAAM,kCAAkC;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAGA,WAAK,qBAAqB,SAAS,OAAO;AACxC,YAAI,mBAAmB,KAAK,QAAQ,eAAe;AACnD,YAAI,kBAAkB;AAAE,gBAAM,WAAW,IAAI,SAAS,MAAM,UAAU,IAAI;AAAA,QAAG;AAC7E,aAAK,mBAAmB,KAAK;AAC7B,eAAO,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC9B,cAAI,kBAAkB;AAAE,kBAAM,WAAW,MAAM,SAAS,QAAQ;AAAA,UAAG;AACnE,eAAK,mBAAmB,KAAK;AAAA,QAC/B;AACA,YAAI,kBAAkB;AAAE,gBAAM,WAAW,MAAM,SAAS;AAAA,QAAQ;AAGhE,YAAI,KAAK,qBAAqB,OAAO,IAAI,GAAG;AAC1C,gBAAM,MAAM,mBAAmB;AAAA,QACjC;AACA,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,gBAAM,MAAM,0BAA0B;AAAA,QACxC;AAAA,MACF;AAGA,WAAK,qBAAqB,SAAS,OAAO;AACxC,eAAO,MAAM,MAAM,MAAM,OAAO,UAAU,KAAK,eAAe,KAAK,GAAG;AAAA,QAAC;AAAA,MACzE;AAGA,WAAK,iBAAiB,SAAS,OAAO;AACpC,YAAI,KAAK,oBAAoB,KAAK,GAAG;AAInC,cAAI,MAAM,+BAA+B,KAAK,qBAAqB,KAAK,GAAG;AAEzE,gBAAI,MAAM,SAAS;AACjB,oBAAM,MAAM,oBAAoB;AAAA,YAClC;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,YAAI,MAAM,UAAU,KAAK,eAAe,KAAK,IAAI,KAAK,uBAAuB,KAAK,GAAG;AACnF,eAAK,qBAAqB,KAAK;AAC/B,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAGA,WAAK,sBAAsB,SAAS,OAAO;AACzC,YAAI,QAAQ,MAAM;AAClB,cAAM,8BAA8B;AAGpC,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,KAAK,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AACtD,iBAAO;AAAA,QACT;AAGA,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,KAAK,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,GAAG;AACtD,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM;AAAA,QACd;AAGA,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,KAAK,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AACtD,cAAI,aAAa;AACjB,cAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,yBAAa,MAAM;AAAA,cAAI;AAAA;AAAA,YAAY;AAAA,UACrC;AACA,cAAI,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,KAAK,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,GAAG;AACtD,iBAAK,mBAAmB,KAAK;AAC7B,gBAAI,CAAC,MAAM;AAAA,cAAI;AAAA;AAAA,YAAY,GAAG;AAC5B,oBAAM,MAAM,oBAAoB;AAAA,YAClC;AACA,kBAAM,8BAA8B,CAAC;AACrC,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,cAAM,MAAM;AACZ,eAAO;AAAA,MACT;AAGA,WAAK,uBAAuB,SAAS,OAAO,SAAS;AACnD,YAAK,YAAY,OAAS,WAAU;AAEpC,YAAI,KAAK,2BAA2B,OAAO,OAAO,GAAG;AACnD,gBAAM;AAAA,YAAI;AAAA;AAAA,UAAY;AACtB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAGA,WAAK,6BAA6B,SAAS,OAAO,SAAS;AACzD,eACE,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,KACtB,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,KACtB,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,KACtB,KAAK,2BAA2B,OAAO,OAAO;AAAA,MAElD;AACA,WAAK,6BAA6B,SAAS,OAAO,SAAS;AACzD,YAAI,QAAQ,MAAM;AAClB,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,MAAM,GAAG,MAAM;AACnB,cAAI,KAAK,wBAAwB,KAAK,GAAG;AACvC,kBAAM,MAAM;AACZ,gBAAI,MAAM;AAAA,cAAI;AAAA;AAAA,YAAY,KAAK,KAAK,wBAAwB,KAAK,GAAG;AAClE,oBAAM,MAAM;AAAA,YACd;AACA,gBAAI,MAAM;AAAA,cAAI;AAAA;AAAA,YAAY,GAAG;AAE3B,kBAAI,QAAQ,MAAM,MAAM,OAAO,CAAC,SAAS;AACvC,sBAAM,MAAM,uCAAuC;AAAA,cACrD;AACA,qBAAO;AAAA,YACT;AAAA,UACF;AACA,cAAI,MAAM,WAAW,CAAC,SAAS;AAC7B,kBAAM,MAAM,uBAAuB;AAAA,UACrC;AACA,gBAAM,MAAM;AAAA,QACd;AACA,eAAO;AAAA,MACT;AAGA,WAAK,iBAAiB,SAAS,OAAO;AACpC,eACE,KAAK,4BAA4B,KAAK,KACtC,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,KACtB,KAAK,mCAAmC,KAAK,KAC7C,KAAK,yBAAyB,KAAK,KACnC,KAAK,2BAA2B,KAAK,KACrC,KAAK,yBAAyB,KAAK;AAAA,MAEvC;AACA,WAAK,qCAAqC,SAAS,OAAO;AACxD,YAAI,QAAQ,MAAM;AAClB,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,KAAK,qBAAqB,KAAK,GAAG;AACpC,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM;AAAA,QACd;AACA,eAAO;AAAA,MACT;AACA,WAAK,6BAA6B,SAAS,OAAO;AAChD,YAAI,QAAQ,MAAM;AAClB,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,GAAG;AAC3B,gBAAI,KAAK,QAAQ,eAAe,IAAI;AAClC,kBAAI,eAAe,KAAK,oBAAoB,KAAK;AACjD,kBAAI,YAAY,MAAM;AAAA,gBAAI;AAAA;AAAA,cAAY;AACtC,kBAAI,gBAAgB,WAAW;AAC7B,yBAASA,KAAI,GAAGA,KAAI,aAAa,QAAQA,MAAK;AAC5C,sBAAI,WAAW,aAAa,OAAOA,EAAC;AACpC,sBAAI,aAAa,QAAQ,UAAUA,KAAI,CAAC,IAAI,IAAI;AAC9C,0BAAM,MAAM,wCAAwC;AAAA,kBACtD;AAAA,gBACF;AACA,oBAAI,WAAW;AACb,sBAAI,kBAAkB,KAAK,oBAAoB,KAAK;AACpD,sBAAI,CAAC,gBAAgB,CAAC,mBAAmB,MAAM,QAAQ,MAAM,IAAc;AACzE,0BAAM,MAAM,sCAAsC;AAAA,kBACpD;AACA,2BAAS,MAAM,GAAG,MAAM,gBAAgB,QAAQ,OAAO;AACrD,wBAAI,aAAa,gBAAgB,OAAO,GAAG;AAC3C,wBACE,gBAAgB,QAAQ,YAAY,MAAM,CAAC,IAAI,MAC/C,aAAa,QAAQ,UAAU,IAAI,IACnC;AACA,4BAAM,MAAM,wCAAwC;AAAA,oBACtD;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,gBAAI,MAAM;AAAA,cAAI;AAAA;AAAA,YAAY,GAAG;AAC3B,mBAAK,mBAAmB,KAAK;AAC7B,kBAAI,MAAM;AAAA,gBAAI;AAAA;AAAA,cAAY,GAAG;AAC3B,uBAAO;AAAA,cACT;AACA,oBAAM,MAAM,oBAAoB;AAAA,YAClC;AAAA,UACF;AACA,gBAAM,MAAM;AAAA,QACd;AACA,eAAO;AAAA,MACT;AACA,WAAK,2BAA2B,SAAS,OAAO;AAC9C,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,iBAAK,sBAAsB,KAAK;AAAA,UAClC,WAAW,MAAM,QAAQ,MAAM,IAAc;AAC3C,kBAAM,MAAM,eAAe;AAAA,UAC7B;AACA,eAAK,mBAAmB,KAAK;AAC7B,cAAI,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,GAAG;AAC3B,kBAAM,sBAAsB;AAC5B,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM,oBAAoB;AAAA,QAClC;AACA,eAAO;AAAA,MACT;AAIA,WAAK,sBAAsB,SAAS,OAAO;AACzC,YAAI,YAAY;AAChB,YAAI,KAAK;AACT,gBAAQ,KAAK,MAAM,QAAQ,OAAO,MAAM,4BAA4B,EAAE,GAAG;AACvE,uBAAa,kBAAkB,EAAE;AACjC,gBAAM,QAAQ;AAAA,QAChB;AACA,eAAO;AAAA,MACT;AAGA,eAAS,4BAA4B,IAAI;AACvC,eAAO,OAAO,OAAgB,OAAO,OAAgB,OAAO;AAAA,MAC9D;AAGA,WAAK,yBAAyB,SAAS,OAAO;AAC5C,eACE,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,KACtB,KAAK,mCAAmC,KAAK,KAC7C,KAAK,yBAAyB,KAAK,KACnC,KAAK,2BAA2B,KAAK,KACrC,KAAK,yBAAyB,KAAK,KACnC,KAAK,kCAAkC,KAAK,KAC5C,KAAK,mCAAmC,KAAK;AAAA,MAEjD;AAGA,WAAK,oCAAoC,SAAS,OAAO;AACvD,YAAI,KAAK,2BAA2B,OAAO,IAAI,GAAG;AAChD,gBAAM,MAAM,mBAAmB;AAAA,QACjC;AACA,eAAO;AAAA,MACT;AAGA,WAAK,4BAA4B,SAAS,OAAO;AAC/C,YAAI,KAAK,MAAM,QAAQ;AACvB,YAAI,kBAAkB,EAAE,GAAG;AACzB,gBAAM,eAAe;AACrB,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,eAAS,kBAAkB,IAAI;AAC7B,eACE,OAAO,MACP,MAAM,MAAgB,MAAM,MAC5B,OAAO,MACP,OAAO,MACP,MAAM,MAAgB,MAAM,MAC5B,MAAM,OAAgB,MAAM;AAAA,MAEhC;AAIA,WAAK,8BAA8B,SAAS,OAAO;AACjD,YAAI,QAAQ,MAAM;AAClB,YAAI,KAAK;AACT,gBAAQ,KAAK,MAAM,QAAQ,OAAO,MAAM,CAAC,kBAAkB,EAAE,GAAG;AAC9D,gBAAM,QAAQ;AAAA,QAChB;AACA,eAAO,MAAM,QAAQ;AAAA,MACvB;AAGA,WAAK,qCAAqC,SAAS,OAAO;AACxD,YAAI,KAAK,MAAM,QAAQ;AACvB,YACE,OAAO,MACP,OAAO,MACP,EAAE,MAAM,MAAgB,MAAM,OAC9B,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,KACP;AACA,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAKA,WAAK,wBAAwB,SAAS,OAAO;AAC3C,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,CAAC,KAAK,oBAAoB,KAAK,GAAG;AAAE,kBAAM,MAAM,eAAe;AAAA,UAAG;AACtE,cAAI,mBAAmB,KAAK,QAAQ,eAAe;AACnD,cAAI,QAAQ,MAAM,WAAW,MAAM,eAAe;AAClD,cAAI,OAAO;AACT,gBAAI,kBAAkB;AACpB,uBAASA,KAAI,GAAGU,QAAO,OAAOV,KAAIU,MAAK,QAAQV,MAAK,GAAG;AACrD,oBAAI,QAAQU,MAAKV,EAAC;AAElB,oBAAI,CAAC,MAAM,cAAc,MAAM,QAAQ,GACrC;AAAE,wBAAM,MAAM,8BAA8B;AAAA,gBAAG;AAAA,cACnD;AAAA,YACF,OAAO;AACL,oBAAM,MAAM,8BAA8B;AAAA,YAC5C;AAAA,UACF;AACA,cAAI,kBAAkB;AACpB,aAAC,UAAU,MAAM,WAAW,MAAM,eAAe,IAAI,CAAC,IAAI,KAAK,MAAM,QAAQ;AAAA,UAC/E,OAAO;AACL,kBAAM,WAAW,MAAM,eAAe,IAAI;AAAA,UAC5C;AAAA,QACF;AAAA,MACF;AAKA,WAAK,sBAAsB,SAAS,OAAO;AACzC,cAAM,kBAAkB;AACxB,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,KAAK,+BAA+B,KAAK,KAAK,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,GAAG;AACzE,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM,4BAA4B;AAAA,QAC1C;AACA,eAAO;AAAA,MACT;AAMA,WAAK,iCAAiC,SAAS,OAAO;AACpD,cAAM,kBAAkB;AACxB,YAAI,KAAK,gCAAgC,KAAK,GAAG;AAC/C,gBAAM,mBAAmB,kBAAkB,MAAM,YAAY;AAC7D,iBAAO,KAAK,+BAA+B,KAAK,GAAG;AACjD,kBAAM,mBAAmB,kBAAkB,MAAM,YAAY;AAAA,UAC/D;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAOA,WAAK,kCAAkC,SAAS,OAAO;AACrD,YAAI,QAAQ,MAAM;AAClB,YAAI,SAAS,KAAK,QAAQ,eAAe;AACzC,YAAI,KAAK,MAAM,QAAQ,MAAM;AAC7B,cAAM,QAAQ,MAAM;AAEpB,YAAI,OAAO,MAAgB,KAAK,sCAAsC,OAAO,MAAM,GAAG;AACpF,eAAK,MAAM;AAAA,QACb;AACA,YAAI,wBAAwB,EAAE,GAAG;AAC/B,gBAAM,eAAe;AACrB,iBAAO;AAAA,QACT;AAEA,cAAM,MAAM;AACZ,eAAO;AAAA,MACT;AACA,eAAS,wBAAwB,IAAI;AACnC,eAAO,kBAAkB,IAAI,IAAI,KAAK,OAAO,MAAgB,OAAO;AAAA,MACtE;AASA,WAAK,iCAAiC,SAAS,OAAO;AACpD,YAAI,QAAQ,MAAM;AAClB,YAAI,SAAS,KAAK,QAAQ,eAAe;AACzC,YAAI,KAAK,MAAM,QAAQ,MAAM;AAC7B,cAAM,QAAQ,MAAM;AAEpB,YAAI,OAAO,MAAgB,KAAK,sCAAsC,OAAO,MAAM,GAAG;AACpF,eAAK,MAAM;AAAA,QACb;AACA,YAAI,uBAAuB,EAAE,GAAG;AAC9B,gBAAM,eAAe;AACrB,iBAAO;AAAA,QACT;AAEA,cAAM,MAAM;AACZ,eAAO;AAAA,MACT;AACA,eAAS,uBAAuB,IAAI;AAClC,eAAO,iBAAiB,IAAI,IAAI,KAAK,OAAO,MAAgB,OAAO,MAAgB,OAAO,QAAuB,OAAO;AAAA,MAC1H;AAGA,WAAK,uBAAuB,SAAS,OAAO;AAC1C,YACE,KAAK,wBAAwB,KAAK,KAClC,KAAK,+BAA+B,KAAK,KACzC,KAAK,0BAA0B,KAAK,KACnC,MAAM,WAAW,KAAK,qBAAqB,KAAK,GACjD;AACA,iBAAO;AAAA,QACT;AACA,YAAI,MAAM,SAAS;AAEjB,cAAI,MAAM,QAAQ,MAAM,IAAc;AACpC,kBAAM,MAAM,wBAAwB;AAAA,UACtC;AACA,gBAAM,MAAM,gBAAgB;AAAA,QAC9B;AACA,eAAO;AAAA,MACT;AACA,WAAK,0BAA0B,SAAS,OAAO;AAC7C,YAAI,QAAQ,MAAM;AAClB,YAAI,KAAK,wBAAwB,KAAK,GAAG;AACvC,cAAI,IAAI,MAAM;AACd,cAAI,MAAM,SAAS;AAEjB,gBAAI,IAAI,MAAM,kBAAkB;AAC9B,oBAAM,mBAAmB;AAAA,YAC3B;AACA,mBAAO;AAAA,UACT;AACA,cAAI,KAAK,MAAM,oBAAoB;AACjC,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM;AAAA,QACd;AACA,eAAO;AAAA,MACT;AACA,WAAK,uBAAuB,SAAS,OAAO;AAC1C,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,KAAK,oBAAoB,KAAK,GAAG;AACnC,kBAAM,mBAAmB,KAAK,MAAM,eAAe;AACnD,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM,yBAAyB;AAAA,QACvC;AACA,eAAO;AAAA,MACT;AAGA,WAAK,4BAA4B,SAAS,OAAO;AAC/C,eACE,KAAK,wBAAwB,KAAK,KAClC,KAAK,yBAAyB,KAAK,KACnC,KAAK,eAAe,KAAK,KACzB,KAAK,4BAA4B,KAAK,KACtC,KAAK,sCAAsC,OAAO,KAAK,KACtD,CAAC,MAAM,WAAW,KAAK,oCAAoC,KAAK,KACjE,KAAK,yBAAyB,KAAK;AAAA,MAEvC;AACA,WAAK,2BAA2B,SAAS,OAAO;AAC9C,YAAI,QAAQ,MAAM;AAClB,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,KAAK,wBAAwB,KAAK,GAAG;AACvC,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM;AAAA,QACd;AACA,eAAO;AAAA,MACT;AACA,WAAK,iBAAiB,SAAS,OAAO;AACpC,YAAI,MAAM,QAAQ,MAAM,MAAgB,CAAC,eAAe,MAAM,UAAU,CAAC,GAAG;AAC1E,gBAAM,eAAe;AACrB,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAGA,WAAK,0BAA0B,SAAS,OAAO;AAC7C,YAAI,KAAK,MAAM,QAAQ;AACvB,YAAI,OAAO,KAAc;AACvB,gBAAM,eAAe;AACrB,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AACA,YAAI,OAAO,KAAc;AACvB,gBAAM,eAAe;AACrB,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AACA,YAAI,OAAO,KAAc;AACvB,gBAAM,eAAe;AACrB,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AACA,YAAI,OAAO,KAAc;AACvB,gBAAM,eAAe;AACrB,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AACA,YAAI,OAAO,KAAc;AACvB,gBAAM,eAAe;AACrB,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAGA,WAAK,0BAA0B,SAAS,OAAO;AAC7C,YAAI,KAAK,MAAM,QAAQ;AACvB,YAAI,gBAAgB,EAAE,GAAG;AACvB,gBAAM,eAAe,KAAK;AAC1B,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,eAAS,gBAAgB,IAAI;AAC3B,eACG,MAAM,MAAgB,MAAM,MAC5B,MAAM,MAAgB,MAAM;AAAA,MAEjC;AAGA,WAAK,wCAAwC,SAAS,OAAO,QAAQ;AACnE,YAAK,WAAW,OAAS,UAAS;AAElC,YAAI,QAAQ,MAAM;AAClB,YAAI,UAAU,UAAU,MAAM;AAE9B,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,KAAK,yBAAyB,OAAO,CAAC,GAAG;AAC3C,gBAAI,OAAO,MAAM;AACjB,gBAAI,WAAW,QAAQ,SAAU,QAAQ,OAAQ;AAC/C,kBAAI,mBAAmB,MAAM;AAC7B,kBAAI,MAAM;AAAA,gBAAI;AAAA;AAAA,cAAY,KAAK,MAAM;AAAA,gBAAI;AAAA;AAAA,cAAY,KAAK,KAAK,yBAAyB,OAAO,CAAC,GAAG;AACjG,oBAAI,QAAQ,MAAM;AAClB,oBAAI,SAAS,SAAU,SAAS,OAAQ;AACtC,wBAAM,gBAAgB,OAAO,SAAU,QAAS,QAAQ,SAAU;AAClE,yBAAO;AAAA,gBACT;AAAA,cACF;AACA,oBAAM,MAAM;AACZ,oBAAM,eAAe;AAAA,YACvB;AACA,mBAAO;AAAA,UACT;AACA,cACE,WACA,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,KACtB,KAAK,oBAAoB,KAAK,KAC9B,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,KACtB,eAAe,MAAM,YAAY,GACjC;AACA,mBAAO;AAAA,UACT;AACA,cAAI,SAAS;AACX,kBAAM,MAAM,wBAAwB;AAAA,UACtC;AACA,gBAAM,MAAM;AAAA,QACd;AAEA,eAAO;AAAA,MACT;AACA,eAAS,eAAe,IAAI;AAC1B,eAAO,MAAM,KAAK,MAAM;AAAA,MAC1B;AAGA,WAAK,2BAA2B,SAAS,OAAO;AAC9C,YAAI,MAAM,SAAS;AACjB,cAAI,KAAK,0BAA0B,KAAK,GAAG;AACzC,mBAAO;AAAA,UACT;AACA,cAAI,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,GAAG;AAC3B,kBAAM,eAAe;AACrB,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK,MAAM,QAAQ;AACvB,YAAI,OAAO,OAAiB,CAAC,MAAM,WAAW,OAAO,MAAe;AAClE,gBAAM,eAAe;AACrB,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAGA,WAAK,0BAA0B,SAAS,OAAO;AAC7C,cAAM,eAAe;AACrB,YAAI,KAAK,MAAM,QAAQ;AACvB,YAAI,MAAM,MAAgB,MAAM,IAAc;AAC5C,aAAG;AACD,kBAAM,eAAe,KAAK,MAAM,gBAAgB,KAAK;AACrD,kBAAM,QAAQ;AAAA,UAChB,UAAU,KAAK,MAAM,QAAQ,MAAM,MAAgB,MAAM;AACzD,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAIA,UAAI,cAAc;AAClB,UAAI,YAAY;AAChB,UAAI,gBAAgB;AAGpB,WAAK,iCAAiC,SAAS,OAAO;AACpD,YAAI,KAAK,MAAM,QAAQ;AAEvB,YAAI,uBAAuB,EAAE,GAAG;AAC9B,gBAAM,eAAe;AACrB,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS;AACb,YACE,MAAM,WACN,KAAK,QAAQ,eAAe,OAC1B,SAAS,OAAO,OAAiB,OAAO,MAC1C;AACA,gBAAM,eAAe;AACrB,gBAAM,QAAQ;AACd,cAAI;AACJ,cACE,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,MACrB,SAAS,KAAK,yCAAyC,KAAK,MAC7D,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,GACtB;AACA,gBAAI,UAAU,WAAW,eAAe;AAAE,oBAAM,MAAM,uBAAuB;AAAA,YAAG;AAChF,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM,uBAAuB;AAAA,QACrC;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,uBAAuB,IAAI;AAClC,eACE,OAAO,OACP,OAAO,MACP,OAAO,OACP,OAAO,MACP,OAAO,OACP,OAAO;AAAA,MAEX;AAKA,WAAK,2CAA2C,SAAS,OAAO;AAC9D,YAAI,QAAQ,MAAM;AAGlB,YAAI,KAAK,8BAA8B,KAAK,KAAK,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AACxE,cAAI,OAAO,MAAM;AACjB,cAAI,KAAK,+BAA+B,KAAK,GAAG;AAC9C,gBAAI,QAAQ,MAAM;AAClB,iBAAK,2CAA2C,OAAO,MAAM,KAAK;AAClE,mBAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM,MAAM;AAGZ,YAAI,KAAK,yCAAyC,KAAK,GAAG;AACxD,cAAI,cAAc,MAAM;AACxB,iBAAO,KAAK,0CAA0C,OAAO,WAAW;AAAA,QAC1E;AACA,eAAO;AAAA,MACT;AAEA,WAAK,6CAA6C,SAAS,OAAO,MAAM,OAAO;AAC7E,YAAI,CAAC,OAAO,MAAM,kBAAkB,WAAW,IAAI,GACjD;AAAE,gBAAM,MAAM,uBAAuB;AAAA,QAAG;AAC1C,YAAI,CAAC,MAAM,kBAAkB,UAAU,IAAI,EAAE,KAAK,KAAK,GACrD;AAAE,gBAAM,MAAM,wBAAwB;AAAA,QAAG;AAAA,MAC7C;AAEA,WAAK,4CAA4C,SAAS,OAAO,aAAa;AAC5E,YAAI,MAAM,kBAAkB,OAAO,KAAK,WAAW,GAAG;AAAE,iBAAO;AAAA,QAAU;AACzE,YAAI,MAAM,WAAW,MAAM,kBAAkB,gBAAgB,KAAK,WAAW,GAAG;AAAE,iBAAO;AAAA,QAAc;AACvG,cAAM,MAAM,uBAAuB;AAAA,MACrC;AAIA,WAAK,gCAAgC,SAAS,OAAO;AACnD,YAAI,KAAK;AACT,cAAM,kBAAkB;AACxB,eAAO,+BAA+B,KAAK,MAAM,QAAQ,CAAC,GAAG;AAC3D,gBAAM,mBAAmB,kBAAkB,EAAE;AAC7C,gBAAM,QAAQ;AAAA,QAChB;AACA,eAAO,MAAM,oBAAoB;AAAA,MACnC;AAEA,eAAS,+BAA+B,IAAI;AAC1C,eAAO,gBAAgB,EAAE,KAAK,OAAO;AAAA,MACvC;AAIA,WAAK,iCAAiC,SAAS,OAAO;AACpD,YAAI,KAAK;AACT,cAAM,kBAAkB;AACxB,eAAO,gCAAgC,KAAK,MAAM,QAAQ,CAAC,GAAG;AAC5D,gBAAM,mBAAmB,kBAAkB,EAAE;AAC7C,gBAAM,QAAQ;AAAA,QAChB;AACA,eAAO,MAAM,oBAAoB;AAAA,MACnC;AACA,eAAS,gCAAgC,IAAI;AAC3C,eAAO,+BAA+B,EAAE,KAAK,eAAe,EAAE;AAAA,MAChE;AAIA,WAAK,2CAA2C,SAAS,OAAO;AAC9D,eAAO,KAAK,+BAA+B,KAAK;AAAA,MAClD;AAGA,WAAK,2BAA2B,SAAS,OAAO;AAC9C,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,SAAS,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY;AACnC,cAAI,SAAS,KAAK,qBAAqB,KAAK;AAC5C,cAAI,CAAC,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,GACzB;AAAE,kBAAM,MAAM,8BAA8B;AAAA,UAAG;AACjD,cAAI,UAAU,WAAW,eACvB;AAAE,kBAAM,MAAM,6CAA6C;AAAA,UAAG;AAChE,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAIA,WAAK,uBAAuB,SAAS,OAAO;AAC1C,YAAI,MAAM,QAAQ,MAAM,IAAc;AAAE,iBAAO;AAAA,QAAU;AACzD,YAAI,MAAM,SAAS;AAAE,iBAAO,KAAK,0BAA0B,KAAK;AAAA,QAAE;AAClE,aAAK,2BAA2B,KAAK;AACrC,eAAO;AAAA,MACT;AAIA,WAAK,6BAA6B,SAAS,OAAO;AAChD,eAAO,KAAK,oBAAoB,KAAK,GAAG;AACtC,cAAI,OAAO,MAAM;AACjB,cAAI,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,KAAK,KAAK,oBAAoB,KAAK,GAAG;AAC9D,gBAAI,QAAQ,MAAM;AAClB,gBAAI,MAAM,YAAY,SAAS,MAAM,UAAU,KAAK;AAClD,oBAAM,MAAM,yBAAyB;AAAA,YACvC;AACA,gBAAI,SAAS,MAAM,UAAU,MAAM,OAAO,OAAO;AAC/C,oBAAM,MAAM,uCAAuC;AAAA,YACrD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAIA,WAAK,sBAAsB,SAAS,OAAO;AACzC,YAAI,QAAQ,MAAM;AAElB,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,KAAK,sBAAsB,KAAK,GAAG;AACrC,mBAAO;AAAA,UACT;AACA,cAAI,MAAM,SAAS;AAEjB,gBAAI,OAAO,MAAM,QAAQ;AACzB,gBAAI,SAAS,MAAgB,aAAa,IAAI,GAAG;AAC/C,oBAAM,MAAM,sBAAsB;AAAA,YACpC;AACA,kBAAM,MAAM,gBAAgB;AAAA,UAC9B;AACA,gBAAM,MAAM;AAAA,QACd;AAEA,YAAI,KAAK,MAAM,QAAQ;AACvB,YAAI,OAAO,IAAc;AACvB,gBAAM,eAAe;AACrB,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAGA,WAAK,wBAAwB,SAAS,OAAO;AAC3C,YAAI,QAAQ,MAAM;AAElB,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,gBAAM,eAAe;AACrB,iBAAO;AAAA,QACT;AAEA,YAAI,MAAM,WAAW,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC5C,gBAAM,eAAe;AACrB,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,MAAM,WAAW,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC7C,cAAI,KAAK,6BAA6B,KAAK,GAAG;AAC5C,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM;AAAA,QACd;AAEA,eACE,KAAK,+BAA+B,KAAK,KACzC,KAAK,0BAA0B,KAAK;AAAA,MAExC;AAMA,WAAK,4BAA4B,SAAS,OAAO;AAC/C,YAAI,SAAS,WAAW;AACxB,YAAI,KAAK,wBAAwB,KAAK,EAAG;AAAA,iBAAW,YAAY,KAAK,0BAA0B,KAAK,GAAG;AACrG,cAAI,cAAc,eAAe;AAAE,qBAAS;AAAA,UAAe;AAE3D,cAAI,QAAQ,MAAM;AAClB,iBAAO,MAAM;AAAA,YAAS,CAAC,IAAM,EAAI;AAAA;AAAA,UAAU,GAAG;AAC5C,gBACE,MAAM,QAAQ,MAAM,OACnB,YAAY,KAAK,0BAA0B,KAAK,IACjD;AACA,kBAAI,cAAc,eAAe;AAAE,yBAAS;AAAA,cAAW;AACvD;AAAA,YACF;AACA,kBAAM,MAAM,sCAAsC;AAAA,UACpD;AACA,cAAI,UAAU,MAAM,KAAK;AAAE,mBAAO;AAAA,UAAO;AAEzC,iBAAO,MAAM;AAAA,YAAS,CAAC,IAAM,EAAI;AAAA;AAAA,UAAU,GAAG;AAC5C,gBAAI,KAAK,0BAA0B,KAAK,GAAG;AAAE;AAAA,YAAS;AACtD,kBAAM,MAAM,sCAAsC;AAAA,UACpD;AACA,cAAI,UAAU,MAAM,KAAK;AAAE,mBAAO;AAAA,UAAO;AAAA,QAC3C,OAAO;AACL,gBAAM,MAAM,sCAAsC;AAAA,QACpD;AAEA,mBAAS;AACP,cAAI,KAAK,wBAAwB,KAAK,GAAG;AAAE;AAAA,UAAS;AACpD,sBAAY,KAAK,0BAA0B,KAAK;AAChD,cAAI,CAAC,WAAW;AAAE,mBAAO;AAAA,UAAO;AAChC,cAAI,cAAc,eAAe;AAAE,qBAAS;AAAA,UAAe;AAAA,QAC7D;AAAA,MACF;AAGA,WAAK,0BAA0B,SAAS,OAAO;AAC7C,YAAI,QAAQ,MAAM;AAClB,YAAI,KAAK,4BAA4B,KAAK,GAAG;AAC3C,cAAI,OAAO,MAAM;AACjB,cAAI,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,KAAK,KAAK,4BAA4B,KAAK,GAAG;AACtE,gBAAI,QAAQ,MAAM;AAClB,gBAAI,SAAS,MAAM,UAAU,MAAM,OAAO,OAAO;AAC/C,oBAAM,MAAM,uCAAuC;AAAA,YACrD;AACA,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM;AAAA,QACd;AACA,eAAO;AAAA,MACT;AAGA,WAAK,4BAA4B,SAAS,OAAO;AAC/C,YAAI,KAAK,4BAA4B,KAAK,GAAG;AAAE,iBAAO;AAAA,QAAU;AAChE,eAAO,KAAK,iCAAiC,KAAK,KAAK,KAAK,sBAAsB,KAAK;AAAA,MACzF;AAGA,WAAK,wBAAwB,SAAS,OAAO;AAC3C,YAAI,QAAQ,MAAM;AAClB,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,SAAS,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY;AACnC,cAAI,SAAS,KAAK,qBAAqB,KAAK;AAC5C,cAAI,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,GAAG;AAC3B,gBAAI,UAAU,WAAW,eAAe;AACtC,oBAAM,MAAM,6CAA6C;AAAA,YAC3D;AACA,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM;AAAA,QACd;AACA,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,WAAW,KAAK,+BAA+B,KAAK;AACxD,cAAI,UAAU;AACZ,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM;AAAA,QACd;AACA,eAAO;AAAA,MACT;AAGA,WAAK,mCAAmC,SAAS,OAAO;AACtD,YAAI,QAAQ,MAAM;AAClB,YAAI,MAAM;AAAA,UAAS,CAAC,IAAM,GAAI;AAAA;AAAA,QAAU,GAAG;AACzC,cAAI,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,GAAG;AAC3B,gBAAI,SAAS,KAAK,sCAAsC,KAAK;AAC7D,gBAAI,MAAM;AAAA,cAAI;AAAA;AAAA,YAAY,GAAG;AAC3B,qBAAO;AAAA,YACT;AAAA,UACF,OAAO;AAEL,kBAAM,MAAM,gBAAgB;AAAA,UAC9B;AACA,gBAAM,MAAM;AAAA,QACd;AACA,eAAO;AAAA,MACT;AAGA,WAAK,wCAAwC,SAAS,OAAO;AAC3D,YAAI,SAAS,KAAK,mBAAmB,KAAK;AAC1C,eAAO,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC9B,cAAI,KAAK,mBAAmB,KAAK,MAAM,eAAe;AAAE,qBAAS;AAAA,UAAe;AAAA,QAClF;AACA,eAAO;AAAA,MACT;AAIA,WAAK,qBAAqB,SAAS,OAAO;AACxC,YAAI,QAAQ;AACZ,eAAO,KAAK,4BAA4B,KAAK,GAAG;AAAE;AAAA,QAAS;AAC3D,eAAO,UAAU,IAAI,YAAY;AAAA,MACnC;AAGA,WAAK,8BAA8B,SAAS,OAAO;AACjD,YAAI,QAAQ,MAAM;AAClB,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cACE,KAAK,0BAA0B,KAAK,KACpC,KAAK,qCAAqC,KAAK,GAC/C;AACA,mBAAO;AAAA,UACT;AACA,cAAI,MAAM;AAAA,YAAI;AAAA;AAAA,UAAY,GAAG;AAC3B,kBAAM,eAAe;AACrB,mBAAO;AAAA,UACT;AACA,gBAAM,MAAM;AACZ,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,MAAM,QAAQ;AACvB,YAAI,KAAK,KAAK,OAAO,MAAM,UAAU,KAAK,4CAA4C,EAAE,GAAG;AAAE,iBAAO;AAAA,QAAM;AAC1G,YAAI,0BAA0B,EAAE,GAAG;AAAE,iBAAO;AAAA,QAAM;AAClD,cAAM,QAAQ;AACd,cAAM,eAAe;AACrB,eAAO;AAAA,MACT;AAGA,eAAS,4CAA4C,IAAI;AACvD,eACE,OAAO,MACP,MAAM,MAAgB,MAAM,MAC5B,MAAM,MAAgB,MAAM,MAC5B,OAAO,MACP,MAAM,MAAgB,MAAM,MAC5B,OAAO,MACP,OAAO,MACP,OAAO;AAAA,MAEX;AAGA,eAAS,0BAA0B,IAAI;AACrC,eACE,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,MAAM,MAAgB,MAAM,MAC5B,MAAM,OAAgB,MAAM;AAAA,MAEhC;AAGA,WAAK,uCAAuC,SAAS,OAAO;AAC1D,YAAI,KAAK,MAAM,QAAQ;AACvB,YAAI,6BAA6B,EAAE,GAAG;AACpC,gBAAM,eAAe;AACrB,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAGA,eAAS,6BAA6B,IAAI;AACxC,eACE,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,MAAM,MAAgB,MAAM,MAC5B,OAAO,MACP,OAAO,MACP,OAAO;AAAA,MAEX;AAGA,WAAK,+BAA+B,SAAS,OAAO;AAClD,YAAI,KAAK,MAAM,QAAQ;AACvB,YAAI,eAAe,EAAE,KAAK,OAAO,IAAc;AAC7C,gBAAM,eAAe,KAAK;AAC1B,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAGA,WAAK,8BAA8B,SAAS,OAAO;AACjD,YAAI,QAAQ,MAAM;AAClB,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,cAAI,KAAK,yBAAyB,OAAO,CAAC,GAAG;AAC3C,mBAAO;AAAA,UACT;AACA,cAAI,MAAM,SAAS;AACjB,kBAAM,MAAM,gBAAgB;AAAA,UAC9B;AACA,gBAAM,MAAM;AAAA,QACd;AACA,eAAO;AAAA,MACT;AAGA,WAAK,0BAA0B,SAAS,OAAO;AAC7C,YAAI,QAAQ,MAAM;AAClB,YAAI,KAAK;AACT,cAAM,eAAe;AACrB,eAAO,eAAe,KAAK,MAAM,QAAQ,CAAC,GAAG;AAC3C,gBAAM,eAAe,KAAK,MAAM,gBAAgB,KAAK;AACrD,gBAAM,QAAQ;AAAA,QAChB;AACA,eAAO,MAAM,QAAQ;AAAA,MACvB;AACA,eAAS,eAAe,IAAI;AAC1B,eAAO,MAAM,MAAgB,MAAM;AAAA,MACrC;AAGA,WAAK,sBAAsB,SAAS,OAAO;AACzC,YAAI,QAAQ,MAAM;AAClB,YAAI,KAAK;AACT,cAAM,eAAe;AACrB,eAAO,WAAW,KAAK,MAAM,QAAQ,CAAC,GAAG;AACvC,gBAAM,eAAe,KAAK,MAAM,eAAe,SAAS,EAAE;AAC1D,gBAAM,QAAQ;AAAA,QAChB;AACA,eAAO,MAAM,QAAQ;AAAA,MACvB;AACA,eAAS,WAAW,IAAI;AACtB,eACG,MAAM,MAAgB,MAAM,MAC5B,MAAM,MAAgB,MAAM,MAC5B,MAAM,MAAgB,MAAM;AAAA,MAEjC;AACA,eAAS,SAAS,IAAI;AACpB,YAAI,MAAM,MAAgB,MAAM,IAAc;AAC5C,iBAAO,MAAM,KAAK;AAAA,QACpB;AACA,YAAI,MAAM,MAAgB,MAAM,KAAc;AAC5C,iBAAO,MAAM,KAAK;AAAA,QACpB;AACA,eAAO,KAAK;AAAA,MACd;AAIA,WAAK,sCAAsC,SAAS,OAAO;AACzD,YAAI,KAAK,qBAAqB,KAAK,GAAG;AACpC,cAAI,KAAK,MAAM;AACf,cAAI,KAAK,qBAAqB,KAAK,GAAG;AACpC,gBAAI,KAAK,MAAM;AACf,gBAAI,MAAM,KAAK,KAAK,qBAAqB,KAAK,GAAG;AAC/C,oBAAM,eAAe,KAAK,KAAK,KAAK,IAAI,MAAM;AAAA,YAChD,OAAO;AACL,oBAAM,eAAe,KAAK,IAAI;AAAA,YAChC;AAAA,UACF,OAAO;AACL,kBAAM,eAAe;AAAA,UACvB;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAGA,WAAK,uBAAuB,SAAS,OAAO;AAC1C,YAAI,KAAK,MAAM,QAAQ;AACvB,YAAI,aAAa,EAAE,GAAG;AACpB,gBAAM,eAAe,KAAK;AAC1B,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AACA,cAAM,eAAe;AACrB,eAAO;AAAA,MACT;AACA,eAAS,aAAa,IAAI;AACxB,eAAO,MAAM,MAAgB,MAAM;AAAA,MACrC;AAKA,WAAK,2BAA2B,SAAS,OAAO,QAAQ;AACtD,YAAI,QAAQ,MAAM;AAClB,cAAM,eAAe;AACrB,iBAASA,KAAI,GAAGA,KAAI,QAAQ,EAAEA,IAAG;AAC/B,cAAI,KAAK,MAAM,QAAQ;AACvB,cAAI,CAAC,WAAW,EAAE,GAAG;AACnB,kBAAM,MAAM;AACZ,mBAAO;AAAA,UACT;AACA,gBAAM,eAAe,KAAK,MAAM,eAAe,SAAS,EAAE;AAC1D,gBAAM,QAAQ;AAAA,QAChB;AACA,eAAO;AAAA,MACT;AAMA,UAAI,QAAQ,SAASkB,OAAM,GAAG;AAC5B,aAAK,OAAO,EAAE;AACd,aAAK,QAAQ,EAAE;AACf,aAAK,QAAQ,EAAE;AACf,aAAK,MAAM,EAAE;AACb,YAAI,EAAE,QAAQ,WACZ;AAAE,eAAK,MAAM,IAAI,eAAe,GAAG,EAAE,UAAU,EAAE,MAAM;AAAA,QAAG;AAC5D,YAAI,EAAE,QAAQ,QACZ;AAAE,eAAK,QAAQ,CAAC,EAAE,OAAO,EAAE,GAAG;AAAA,QAAG;AAAA,MACrC;AAIA,UAAI,KAAK,OAAO;AAIhB,SAAG,OAAO,SAAS,+BAA+B;AAChD,YAAI,CAAC,iCAAiC,KAAK,KAAK,WAAW,KAAK,aAC9D;AAAE,eAAK,iBAAiB,KAAK,OAAO,gCAAgC,KAAK,KAAK,OAAO;AAAA,QAAG;AAC1F,YAAI,KAAK,QAAQ,SACf;AAAE,eAAK,QAAQ,QAAQ,IAAI,MAAM,IAAI,CAAC;AAAA,QAAG;AAE3C,aAAK,aAAa,KAAK;AACvB,aAAK,eAAe,KAAK;AACzB,aAAK,gBAAgB,KAAK;AAC1B,aAAK,kBAAkB,KAAK;AAC5B,aAAK,UAAU;AAAA,MACjB;AAEA,SAAG,WAAW,WAAW;AACvB,aAAK,KAAK;AACV,eAAO,IAAI,MAAM,IAAI;AAAA,MACvB;AAGA,UAAI,OAAO,WAAW,aACpB;AAAE,WAAG,OAAO,QAAQ,IAAI,WAAW;AACjC,cAAI,WAAW;AAEf,iBAAO;AAAA,YACL,MAAM,WAAY;AAChB,kBAAI,QAAQ,SAAS,SAAS;AAC9B,qBAAO;AAAA,gBACL,MAAM,MAAM,SAAS,QAAQ;AAAA,gBAC7B,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MAAG;AAQL,SAAG,YAAY,WAAW;AACxB,YAAI,aAAa,KAAK,WAAW;AACjC,YAAI,CAAC,cAAc,CAAC,WAAW,eAAe;AAAE,eAAK,UAAU;AAAA,QAAG;AAElE,aAAK,QAAQ,KAAK;AAClB,YAAI,KAAK,QAAQ,WAAW;AAAE,eAAK,WAAW,KAAK,YAAY;AAAA,QAAG;AAClE,YAAI,KAAK,OAAO,KAAK,MAAM,QAAQ;AAAE,iBAAO,KAAK,YAAY,QAAQ,GAAG;AAAA,QAAE;AAE1E,YAAI,WAAW,UAAU;AAAE,iBAAO,WAAW,SAAS,IAAI;AAAA,QAAE,OACvD;AAAE,eAAK,UAAU,KAAK,kBAAkB,CAAC;AAAA,QAAG;AAAA,MACnD;AAEA,SAAG,YAAY,SAAS,MAAM;AAG5B,YAAI,kBAAkB,MAAM,KAAK,QAAQ,eAAe,CAAC,KAAK,SAAS,IACrE;AAAE,iBAAO,KAAK,SAAS;AAAA,QAAE;AAE3B,eAAO,KAAK,iBAAiB,IAAI;AAAA,MACnC;AAEA,SAAG,oBAAoB,WAAW;AAChC,YAAI,OAAO,KAAK,MAAM,WAAW,KAAK,GAAG;AACzC,YAAI,QAAQ,SAAU,QAAQ,OAAQ;AAAE,iBAAO;AAAA,QAAK;AACpD,YAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,eAAO,QAAQ,SAAU,QAAQ,QAAS,QAAQ,QAAQ,MAAM,OAAO;AAAA,MACzE;AAEA,SAAG,mBAAmB,WAAW;AAC/B,YAAI,WAAW,KAAK,QAAQ,aAAa,KAAK,YAAY;AAC1D,YAAI,QAAQ,KAAK,KAAK,MAAM,KAAK,MAAM,QAAQ,MAAM,KAAK,OAAO,CAAC;AAClE,YAAI,QAAQ,IAAI;AAAE,eAAK,MAAM,KAAK,MAAM,GAAG,sBAAsB;AAAA,QAAG;AACpE,aAAK,MAAM,MAAM;AACjB,YAAI,KAAK,QAAQ,WAAW;AAC1B,mBAAS,YAAa,QAAS,MAAM,QAAQ,YAAY,cAAc,KAAK,OAAO,KAAK,KAAK,GAAG,KAAK,MAAK;AACxG,cAAE,KAAK;AACP,kBAAM,KAAK,YAAY;AAAA,UACzB;AAAA,QACF;AACA,YAAI,KAAK,QAAQ,WACf;AAAE,eAAK,QAAQ;AAAA,YAAU;AAAA,YAAM,KAAK,MAAM,MAAM,QAAQ,GAAG,GAAG;AAAA,YAAG;AAAA,YAAO,KAAK;AAAA,YACtD;AAAA,YAAU,KAAK,YAAY;AAAA,UAAC;AAAA,QAAG;AAAA,MAC1D;AAEA,SAAG,kBAAkB,SAAS,WAAW;AACvC,YAAI,QAAQ,KAAK;AACjB,YAAI,WAAW,KAAK,QAAQ,aAAa,KAAK,YAAY;AAC1D,YAAI,KAAK,KAAK,MAAM,WAAW,KAAK,OAAO,SAAS;AACpD,eAAO,KAAK,MAAM,KAAK,MAAM,UAAU,CAAC,UAAU,EAAE,GAAG;AACrD,eAAK,KAAK,MAAM,WAAW,EAAE,KAAK,GAAG;AAAA,QACvC;AACA,YAAI,KAAK,QAAQ,WACf;AAAE,eAAK,QAAQ;AAAA,YAAU;AAAA,YAAO,KAAK,MAAM,MAAM,QAAQ,WAAW,KAAK,GAAG;AAAA,YAAG;AAAA,YAAO,KAAK;AAAA,YACpE;AAAA,YAAU,KAAK,YAAY;AAAA,UAAC;AAAA,QAAG;AAAA,MAC1D;AAKA,SAAG,YAAY,WAAW;AACxB,aAAM,QAAO,KAAK,MAAM,KAAK,MAAM,QAAQ;AACzC,cAAI,KAAK,KAAK,MAAM,WAAW,KAAK,GAAG;AACvC,kBAAQ,IAAI;AAAA,YACZ,KAAK;AAAA,YAAI,KAAK;AACZ,gBAAE,KAAK;AACP;AAAA,YACF,KAAK;AACH,kBAAI,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,IAAI;AAC9C,kBAAE,KAAK;AAAA,cACT;AAAA,YACF,KAAK;AAAA,YAAI,KAAK;AAAA,YAAM,KAAK;AACvB,gBAAE,KAAK;AACP,kBAAI,KAAK,QAAQ,WAAW;AAC1B,kBAAE,KAAK;AACP,qBAAK,YAAY,KAAK;AAAA,cACxB;AACA;AAAA,YACF,KAAK;AACH,sBAAQ,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,GAAG;AAAA,gBAC7C,KAAK;AACH,uBAAK,iBAAiB;AACtB;AAAA,gBACF,KAAK;AACH,uBAAK,gBAAgB,CAAC;AACtB;AAAA,gBACF;AACE,wBAAM;AAAA,cACR;AACA;AAAA,YACF;AACE,kBAAI,KAAK,KAAK,KAAK,MAAM,MAAM,QAAQ,mBAAmB,KAAK,OAAO,aAAa,EAAE,CAAC,GAAG;AACvF,kBAAE,KAAK;AAAA,cACT,OAAO;AACL,sBAAM;AAAA,cACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAOA,SAAG,cAAc,SAAS,MAAM,KAAK;AACnC,aAAK,MAAM,KAAK;AAChB,YAAI,KAAK,QAAQ,WAAW;AAAE,eAAK,SAAS,KAAK,YAAY;AAAA,QAAG;AAChE,YAAI,WAAW,KAAK;AACpB,aAAK,OAAO;AACZ,aAAK,QAAQ;AAEb,aAAK,cAAc,QAAQ;AAAA,MAC7B;AAWA,SAAG,gBAAgB,WAAW;AAC5B,YAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,YAAI,QAAQ,MAAM,QAAQ,IAAI;AAAE,iBAAO,KAAK,WAAW,IAAI;AAAA,QAAE;AAC7D,YAAI,QAAQ,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC9C,YAAI,KAAK,QAAQ,eAAe,KAAK,SAAS,MAAM,UAAU,IAAI;AAChE,eAAK,OAAO;AACZ,iBAAO,KAAK,YAAY,QAAQ,QAAQ;AAAA,QAC1C,OAAO;AACL,YAAE,KAAK;AACP,iBAAO,KAAK,YAAY,QAAQ,GAAG;AAAA,QACrC;AAAA,MACF;AAEA,SAAG,kBAAkB,WAAW;AAC9B,YAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,YAAI,KAAK,aAAa;AAAE,YAAE,KAAK;AAAK,iBAAO,KAAK,WAAW;AAAA,QAAE;AAC7D,YAAI,SAAS,IAAI;AAAE,iBAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,QAAE;AAC3D,eAAO,KAAK,SAAS,QAAQ,OAAO,CAAC;AAAA,MACvC;AAEA,SAAG,4BAA4B,SAAS,MAAM;AAC5C,YAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,YAAI,OAAO;AACX,YAAI,YAAY,SAAS,KAAK,QAAQ,OAAO,QAAQ;AAGrD,YAAI,KAAK,QAAQ,eAAe,KAAK,SAAS,MAAM,SAAS,IAAI;AAC/D,YAAE;AACF,sBAAY,QAAQ;AACpB,iBAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAAA,QAC3C;AAEA,YAAI,SAAS,IAAI;AAAE,iBAAO,KAAK,SAAS,QAAQ,QAAQ,OAAO,CAAC;AAAA,QAAE;AAClE,eAAO,KAAK,SAAS,WAAW,IAAI;AAAA,MACtC;AAEA,SAAG,qBAAqB,SAAS,MAAM;AACrC,YAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,YAAI,SAAS,MAAM;AACjB,cAAI,KAAK,QAAQ,eAAe,IAAI;AAClC,gBAAI,QAAQ,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC9C,gBAAI,UAAU,IAAI;AAAE,qBAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,YAAE;AAAA,UAC9D;AACA,iBAAO,KAAK,SAAS,SAAS,MAAM,QAAQ,YAAY,QAAQ,YAAY,CAAC;AAAA,QAC/E;AACA,YAAI,SAAS,IAAI;AAAE,iBAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,QAAE;AAC3D,eAAO,KAAK,SAAS,SAAS,MAAM,QAAQ,YAAY,QAAQ,YAAY,CAAC;AAAA,MAC/E;AAEA,SAAG,kBAAkB,WAAW;AAC9B,YAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,YAAI,SAAS,IAAI;AAAE,iBAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,QAAE;AAC3D,eAAO,KAAK,SAAS,QAAQ,YAAY,CAAC;AAAA,MAC5C;AAEA,SAAG,qBAAqB,SAAS,MAAM;AACrC,YAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,YAAI,SAAS,MAAM;AACjB,cAAI,SAAS,MAAM,CAAC,KAAK,YAAY,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,OACxE,KAAK,eAAe,KAAK,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,GAAG,CAAC,IAAI;AAE1F,iBAAK,gBAAgB,CAAC;AACtB,iBAAK,UAAU;AACf,mBAAO,KAAK,UAAU;AAAA,UACxB;AACA,iBAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,QACxC;AACA,YAAI,SAAS,IAAI;AAAE,iBAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,QAAE;AAC3D,eAAO,KAAK,SAAS,QAAQ,SAAS,CAAC;AAAA,MACzC;AAEA,SAAG,kBAAkB,SAAS,MAAM;AAClC,YAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,YAAI,OAAO;AACX,YAAI,SAAS,MAAM;AACjB,iBAAO,SAAS,MAAM,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI;AACvE,cAAI,KAAK,MAAM,WAAW,KAAK,MAAM,IAAI,MAAM,IAAI;AAAE,mBAAO,KAAK,SAAS,QAAQ,QAAQ,OAAO,CAAC;AAAA,UAAE;AACpG,iBAAO,KAAK,SAAS,QAAQ,UAAU,IAAI;AAAA,QAC7C;AACA,YAAI,SAAS,MAAM,SAAS,MAAM,CAAC,KAAK,YAAY,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,MACxF,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,IAAI;AAE9C,eAAK,gBAAgB,CAAC;AACtB,eAAK,UAAU;AACf,iBAAO,KAAK,UAAU;AAAA,QACxB;AACA,YAAI,SAAS,IAAI;AAAE,iBAAO;AAAA,QAAG;AAC7B,eAAO,KAAK,SAAS,QAAQ,YAAY,IAAI;AAAA,MAC/C;AAEA,SAAG,oBAAoB,SAAS,MAAM;AACpC,YAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,YAAI,SAAS,IAAI;AAAE,iBAAO,KAAK,SAAS,QAAQ,UAAU,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;AAAA,QAAE;AAC9G,YAAI,SAAS,MAAM,SAAS,MAAM,KAAK,QAAQ,eAAe,GAAG;AAC/D,eAAK,OAAO;AACZ,iBAAO,KAAK,YAAY,QAAQ,KAAK;AAAA,QACvC;AACA,eAAO,KAAK,SAAS,SAAS,KAAK,QAAQ,KAAK,QAAQ,QAAQ,CAAC;AAAA,MACnE;AAEA,SAAG,qBAAqB,WAAW;AACjC,YAAIP,eAAc,KAAK,QAAQ;AAC/B,YAAIA,gBAAe,IAAI;AACrB,cAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,cAAI,SAAS,IAAI;AACf,gBAAI,QAAQ,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC9C,gBAAI,QAAQ,MAAM,QAAQ,IAAI;AAAE,qBAAO,KAAK,SAAS,QAAQ,aAAa,CAAC;AAAA,YAAE;AAAA,UAC/E;AACA,cAAI,SAAS,IAAI;AACf,gBAAIA,gBAAe,IAAI;AACrB,kBAAI,UAAU,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAChD,kBAAI,YAAY,IAAI;AAAE,uBAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,cAAE;AAAA,YAChE;AACA,mBAAO,KAAK,SAAS,QAAQ,UAAU,CAAC;AAAA,UAC1C;AAAA,QACF;AACA,eAAO,KAAK,SAAS,QAAQ,UAAU,CAAC;AAAA,MAC1C;AAEA,SAAG,uBAAuB,WAAW;AACnC,YAAIA,eAAc,KAAK,QAAQ;AAC/B,YAAI,OAAO;AACX,YAAIA,gBAAe,IAAI;AACrB,YAAE,KAAK;AACP,iBAAO,KAAK,kBAAkB;AAC9B,cAAI,kBAAkB,MAAM,IAAI,KAAK,SAAS,IAAc;AAC1D,mBAAO,KAAK,YAAY,QAAQ,WAAW,KAAK,UAAU,CAAC;AAAA,UAC7D;AAAA,QACF;AAEA,aAAK,MAAM,KAAK,KAAK,2BAA2B,kBAAkB,IAAI,IAAI,GAAG;AAAA,MAC/E;AAEA,SAAG,mBAAmB,SAAS,MAAM;AACnC,gBAAQ,MAAM;AAAA;AAAA;AAAA,UAGd,KAAK;AACH,mBAAO,KAAK,cAAc;AAAA;AAAA,UAG5B,KAAK;AAAI,cAAE,KAAK;AAAK,mBAAO,KAAK,YAAY,QAAQ,MAAM;AAAA,UAC3D,KAAK;AAAI,cAAE,KAAK;AAAK,mBAAO,KAAK,YAAY,QAAQ,MAAM;AAAA,UAC3D,KAAK;AAAI,cAAE,KAAK;AAAK,mBAAO,KAAK,YAAY,QAAQ,IAAI;AAAA,UACzD,KAAK;AAAI,cAAE,KAAK;AAAK,mBAAO,KAAK,YAAY,QAAQ,KAAK;AAAA,UAC1D,KAAK;AAAI,cAAE,KAAK;AAAK,mBAAO,KAAK,YAAY,QAAQ,QAAQ;AAAA,UAC7D,KAAK;AAAI,cAAE,KAAK;AAAK,mBAAO,KAAK,YAAY,QAAQ,QAAQ;AAAA,UAC7D,KAAK;AAAK,cAAE,KAAK;AAAK,mBAAO,KAAK,YAAY,QAAQ,MAAM;AAAA,UAC5D,KAAK;AAAK,cAAE,KAAK;AAAK,mBAAO,KAAK,YAAY,QAAQ,MAAM;AAAA,UAC5D,KAAK;AAAI,cAAE,KAAK;AAAK,mBAAO,KAAK,YAAY,QAAQ,KAAK;AAAA,UAE1D,KAAK;AACH,gBAAI,KAAK,QAAQ,cAAc,GAAG;AAAE;AAAA,YAAM;AAC1C,cAAE,KAAK;AACP,mBAAO,KAAK,YAAY,QAAQ,SAAS;AAAA,UAE3C,KAAK;AACH,gBAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,gBAAI,SAAS,OAAO,SAAS,IAAI;AAAE,qBAAO,KAAK,gBAAgB,EAAE;AAAA,YAAE;AACnE,gBAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,kBAAI,SAAS,OAAO,SAAS,IAAI;AAAE,uBAAO,KAAK,gBAAgB,CAAC;AAAA,cAAE;AAClE,kBAAI,SAAS,MAAM,SAAS,IAAI;AAAE,uBAAO,KAAK,gBAAgB,CAAC;AAAA,cAAE;AAAA,YACnE;AAAA;AAAA;AAAA,UAIF,KAAK;AAAA,UAAI,KAAK;AAAA,UAAI,KAAK;AAAA,UAAI,KAAK;AAAA,UAAI,KAAK;AAAA,UAAI,KAAK;AAAA,UAAI,KAAK;AAAA,UAAI,KAAK;AAAA,UAAI,KAAK;AAC3E,mBAAO,KAAK,WAAW,KAAK;AAAA;AAAA,UAG9B,KAAK;AAAA,UAAI,KAAK;AACZ,mBAAO,KAAK,WAAW,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,UAM7B,KAAK;AACH,mBAAO,KAAK,gBAAgB;AAAA,UAE9B,KAAK;AAAA,UAAI,KAAK;AACZ,mBAAO,KAAK,0BAA0B,IAAI;AAAA,UAE5C,KAAK;AAAA,UAAK,KAAK;AACb,mBAAO,KAAK,mBAAmB,IAAI;AAAA,UAErC,KAAK;AACH,mBAAO,KAAK,gBAAgB;AAAA,UAE9B,KAAK;AAAA,UAAI,KAAK;AACZ,mBAAO,KAAK,mBAAmB,IAAI;AAAA,UAErC,KAAK;AAAA,UAAI,KAAK;AACZ,mBAAO,KAAK,gBAAgB,IAAI;AAAA,UAElC,KAAK;AAAA,UAAI,KAAK;AACZ,mBAAO,KAAK,kBAAkB,IAAI;AAAA,UAEpC,KAAK;AACH,mBAAO,KAAK,mBAAmB;AAAA,UAEjC,KAAK;AACH,mBAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,UAExC,KAAK;AACH,mBAAO,KAAK,qBAAqB;AAAA,QACnC;AAEA,aAAK,MAAM,KAAK,KAAK,2BAA2B,kBAAkB,IAAI,IAAI,GAAG;AAAA,MAC/E;AAEA,SAAG,WAAW,SAAS,MAAM,MAAM;AACjC,YAAI,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK,MAAM,IAAI;AACpD,aAAK,OAAO;AACZ,eAAO,KAAK,YAAY,MAAM,GAAG;AAAA,MACnC;AAEA,SAAG,aAAa,WAAW;AACzB,YAAI,SAAS,SAAS,QAAQ,KAAK;AACnC,mBAAS;AACP,cAAI,KAAK,OAAO,KAAK,MAAM,QAAQ;AAAE,iBAAK,MAAM,OAAO,iCAAiC;AAAA,UAAG;AAC3F,cAAI,KAAK,KAAK,MAAM,OAAO,KAAK,GAAG;AACnC,cAAI,UAAU,KAAK,EAAE,GAAG;AAAE,iBAAK,MAAM,OAAO,iCAAiC;AAAA,UAAG;AAChF,cAAI,CAAC,SAAS;AACZ,gBAAI,OAAO,KAAK;AAAE,wBAAU;AAAA,YAAM,WACzB,OAAO,OAAO,SAAS;AAAE,wBAAU;AAAA,YAAO,WAC1C,OAAO,OAAO,CAAC,SAAS;AAAE;AAAA,YAAM;AACzC,sBAAU,OAAO;AAAA,UACnB,OAAO;AAAE,sBAAU;AAAA,UAAO;AAC1B,YAAE,KAAK;AAAA,QACT;AACA,YAAI,UAAU,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG;AAC9C,UAAE,KAAK;AACP,YAAI,aAAa,KAAK;AACtB,YAAI,QAAQ,KAAK,UAAU;AAC3B,YAAI,KAAK,aAAa;AAAE,eAAK,WAAW,UAAU;AAAA,QAAG;AAGrD,YAAI,QAAQ,KAAK,gBAAgB,KAAK,cAAc,IAAI,sBAAsB,IAAI;AAClF,cAAM,MAAM,OAAO,SAAS,KAAK;AACjC,aAAK,oBAAoB,KAAK;AAC9B,aAAK,sBAAsB,KAAK;AAGhC,YAAI,QAAQ;AACZ,YAAI;AACF,kBAAQ,IAAI,OAAO,SAAS,KAAK;AAAA,QACnC,SAAS,GAAG;AAAA,QAGZ;AAEA,eAAO,KAAK,YAAY,QAAQ,QAAQ,EAAC,SAAkB,OAAc,MAAY,CAAC;AAAA,MACxF;AAMA,SAAG,UAAU,SAAS,OAAO,KAAK,gCAAgC;AAEhE,YAAI,kBAAkB,KAAK,QAAQ,eAAe,MAAM,QAAQ;AAKhE,YAAI,8BAA8B,kCAAkC,KAAK,MAAM,WAAW,KAAK,GAAG,MAAM;AAExG,YAAI,QAAQ,KAAK,KAAK,QAAQ,GAAG,WAAW;AAC5C,iBAASX,KAAI,GAAG,IAAI,OAAO,OAAO,WAAW,KAAKA,KAAI,GAAG,EAAEA,IAAG,EAAE,KAAK,KAAK;AACxE,cAAI,OAAO,KAAK,MAAM,WAAW,KAAK,GAAG,GAAG,MAAO;AAEnD,cAAI,mBAAmB,SAAS,IAAI;AAClC,gBAAI,6BAA6B;AAAE,mBAAK,iBAAiB,KAAK,KAAK,mEAAmE;AAAA,YAAG;AACzI,gBAAI,aAAa,IAAI;AAAE,mBAAK,iBAAiB,KAAK,KAAK,kDAAkD;AAAA,YAAG;AAC5G,gBAAIA,OAAM,GAAG;AAAE,mBAAK,iBAAiB,KAAK,KAAK,yDAAyD;AAAA,YAAG;AAC3G,uBAAW;AACX;AAAA,UACF;AAEA,cAAI,QAAQ,IAAI;AAAE,kBAAM,OAAO,KAAK;AAAA,UAAI,WAC/B,QAAQ,IAAI;AAAE,kBAAM,OAAO,KAAK;AAAA,UAAI,WACpC,QAAQ,MAAM,QAAQ,IAAI;AAAE,kBAAM,OAAO;AAAA,UAAI,OACjD;AAAE,kBAAM;AAAA,UAAU;AACvB,cAAI,OAAO,OAAO;AAAE;AAAA,UAAM;AAC1B,qBAAW;AACX,kBAAQ,QAAQ,QAAQ;AAAA,QAC1B;AAEA,YAAI,mBAAmB,aAAa,IAAI;AAAE,eAAK,iBAAiB,KAAK,MAAM,GAAG,wDAAwD;AAAA,QAAG;AACzI,YAAI,KAAK,QAAQ,SAAS,OAAO,QAAQ,KAAK,MAAM,UAAU,KAAK;AAAE,iBAAO;AAAA,QAAK;AAEjF,eAAO;AAAA,MACT;AAEA,eAAS,eAAe,KAAK,6BAA6B;AACxD,YAAI,6BAA6B;AAC/B,iBAAO,SAAS,KAAK,CAAC;AAAA,QACxB;AAGA,eAAO,WAAW,IAAI,QAAQ,MAAM,EAAE,CAAC;AAAA,MACzC;AAEA,eAAS,eAAe,KAAK;AAC3B,YAAI,OAAO,WAAW,YAAY;AAChC,iBAAO;AAAA,QACT;AAGA,eAAO,OAAO,IAAI,QAAQ,MAAM,EAAE,CAAC;AAAA,MACrC;AAEA,SAAG,kBAAkB,SAAS,OAAO;AACnC,YAAI,QAAQ,KAAK;AACjB,aAAK,OAAO;AACZ,YAAI,MAAM,KAAK,QAAQ,KAAK;AAC5B,YAAI,OAAO,MAAM;AAAE,eAAK,MAAM,KAAK,QAAQ,GAAG,8BAA8B,KAAK;AAAA,QAAG;AACpF,YAAI,KAAK,QAAQ,eAAe,MAAM,KAAK,MAAM,WAAW,KAAK,GAAG,MAAM,KAAK;AAC7E,gBAAM,eAAe,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG,CAAC;AACtD,YAAE,KAAK;AAAA,QACT,WAAW,kBAAkB,KAAK,kBAAkB,CAAC,GAAG;AAAE,eAAK,MAAM,KAAK,KAAK,kCAAkC;AAAA,QAAG;AACpH,eAAO,KAAK,YAAY,QAAQ,KAAK,GAAG;AAAA,MAC1C;AAIA,SAAG,aAAa,SAAS,eAAe;AACtC,YAAI,QAAQ,KAAK;AACjB,YAAI,CAAC,iBAAiB,KAAK,QAAQ,IAAI,QAAW,IAAI,MAAM,MAAM;AAAE,eAAK,MAAM,OAAO,gBAAgB;AAAA,QAAG;AACzG,YAAI,QAAQ,KAAK,MAAM,SAAS,KAAK,KAAK,MAAM,WAAW,KAAK,MAAM;AACtE,YAAI,SAAS,KAAK,QAAQ;AAAE,eAAK,MAAM,OAAO,gBAAgB;AAAA,QAAG;AACjE,YAAI,OAAO,KAAK,MAAM,WAAW,KAAK,GAAG;AACzC,YAAI,CAAC,SAAS,CAAC,iBAAiB,KAAK,QAAQ,eAAe,MAAM,SAAS,KAAK;AAC9E,cAAI,QAAQ,eAAe,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG,CAAC;AAC5D,YAAE,KAAK;AACP,cAAI,kBAAkB,KAAK,kBAAkB,CAAC,GAAG;AAAE,iBAAK,MAAM,KAAK,KAAK,kCAAkC;AAAA,UAAG;AAC7G,iBAAO,KAAK,YAAY,QAAQ,KAAK,KAAK;AAAA,QAC5C;AACA,YAAI,SAAS,OAAO,KAAK,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG;AAAE,kBAAQ;AAAA,QAAO;AAC9E,YAAI,SAAS,MAAM,CAAC,OAAO;AACzB,YAAE,KAAK;AACP,eAAK,QAAQ,EAAE;AACf,iBAAO,KAAK,MAAM,WAAW,KAAK,GAAG;AAAA,QACvC;AACA,aAAK,SAAS,MAAM,SAAS,QAAQ,CAAC,OAAO;AAC3C,iBAAO,KAAK,MAAM,WAAW,EAAE,KAAK,GAAG;AACvC,cAAI,SAAS,MAAM,SAAS,IAAI;AAAE,cAAE,KAAK;AAAA,UAAK;AAC9C,cAAI,KAAK,QAAQ,EAAE,MAAM,MAAM;AAAE,iBAAK,MAAM,OAAO,gBAAgB;AAAA,UAAG;AAAA,QACxE;AACA,YAAI,kBAAkB,KAAK,kBAAkB,CAAC,GAAG;AAAE,eAAK,MAAM,KAAK,KAAK,kCAAkC;AAAA,QAAG;AAE7G,YAAI,MAAM,eAAe,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG,GAAG,KAAK;AACjE,eAAO,KAAK,YAAY,QAAQ,KAAK,GAAG;AAAA,MAC1C;AAIA,SAAG,gBAAgB,WAAW;AAC5B,YAAI,KAAK,KAAK,MAAM,WAAW,KAAK,GAAG,GAAG;AAE1C,YAAI,OAAO,KAAK;AACd,cAAI,KAAK,QAAQ,cAAc,GAAG;AAAE,iBAAK,WAAW;AAAA,UAAG;AACvD,cAAI,UAAU,EAAE,KAAK;AACrB,iBAAO,KAAK,YAAY,KAAK,MAAM,QAAQ,KAAK,KAAK,GAAG,IAAI,KAAK,GAAG;AACpE,YAAE,KAAK;AACP,cAAI,OAAO,SAAU;AAAE,iBAAK,mBAAmB,SAAS,0BAA0B;AAAA,UAAG;AAAA,QACvF,OAAO;AACL,iBAAO,KAAK,YAAY,CAAC;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AAEA,SAAG,aAAa,SAAS,OAAO;AAC9B,YAAI,MAAM,IAAI,aAAa,EAAE,KAAK;AAClC,mBAAS;AACP,cAAI,KAAK,OAAO,KAAK,MAAM,QAAQ;AAAE,iBAAK,MAAM,KAAK,OAAO,8BAA8B;AAAA,UAAG;AAC7F,cAAI,KAAK,KAAK,MAAM,WAAW,KAAK,GAAG;AACvC,cAAI,OAAO,OAAO;AAAE;AAAA,UAAM;AAC1B,cAAI,OAAO,IAAI;AACb,mBAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC5C,mBAAO,KAAK,gBAAgB,KAAK;AACjC,yBAAa,KAAK;AAAA,UACpB,WAAW,OAAO,QAAU,OAAO,MAAQ;AACzC,gBAAI,KAAK,QAAQ,cAAc,IAAI;AAAE,mBAAK,MAAM,KAAK,OAAO,8BAA8B;AAAA,YAAG;AAC7F,cAAE,KAAK;AACP,gBAAI,KAAK,QAAQ,WAAW;AAC1B,mBAAK;AACL,mBAAK,YAAY,KAAK;AAAA,YACxB;AAAA,UACF,OAAO;AACL,gBAAI,UAAU,EAAE,GAAG;AAAE,mBAAK,MAAM,KAAK,OAAO,8BAA8B;AAAA,YAAG;AAC7E,cAAE,KAAK;AAAA,UACT;AAAA,QACF;AACA,eAAO,KAAK,MAAM,MAAM,YAAY,KAAK,KAAK;AAC9C,eAAO,KAAK,YAAY,QAAQ,QAAQ,GAAG;AAAA,MAC7C;AAIA,UAAI,gCAAgC,CAAC;AAErC,SAAG,uBAAuB,WAAW;AACnC,aAAK,oBAAoB;AACzB,YAAI;AACF,eAAK,cAAc;AAAA,QACrB,SAAS,KAAK;AACZ,cAAI,QAAQ,+BAA+B;AACzC,iBAAK,yBAAyB;AAAA,UAChC,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAEA,aAAK,oBAAoB;AAAA,MAC3B;AAEA,SAAG,qBAAqB,SAAS,UAAU,SAAS;AAClD,YAAI,KAAK,qBAAqB,KAAK,QAAQ,eAAe,GAAG;AAC3D,gBAAM;AAAA,QACR,OAAO;AACL,eAAK,MAAM,UAAU,OAAO;AAAA,QAC9B;AAAA,MACF;AAEA,SAAG,gBAAgB,WAAW;AAC5B,YAAI,MAAM,IAAI,aAAa,KAAK;AAChC,mBAAS;AACP,cAAI,KAAK,OAAO,KAAK,MAAM,QAAQ;AAAE,iBAAK,MAAM,KAAK,OAAO,uBAAuB;AAAA,UAAG;AACtF,cAAI,KAAK,KAAK,MAAM,WAAW,KAAK,GAAG;AACvC,cAAI,OAAO,MAAM,OAAO,MAAM,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,KAAK;AACzE,gBAAI,KAAK,QAAQ,KAAK,UAAU,KAAK,SAAS,QAAQ,YAAY,KAAK,SAAS,QAAQ,kBAAkB;AACxG,kBAAI,OAAO,IAAI;AACb,qBAAK,OAAO;AACZ,uBAAO,KAAK,YAAY,QAAQ,YAAY;AAAA,cAC9C,OAAO;AACL,kBAAE,KAAK;AACP,uBAAO,KAAK,YAAY,QAAQ,SAAS;AAAA,cAC3C;AAAA,YACF;AACA,mBAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC5C,mBAAO,KAAK,YAAY,QAAQ,UAAU,GAAG;AAAA,UAC/C;AACA,cAAI,OAAO,IAAI;AACb,mBAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC5C,mBAAO,KAAK,gBAAgB,IAAI;AAChC,yBAAa,KAAK;AAAA,UACpB,WAAW,UAAU,EAAE,GAAG;AACxB,mBAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC5C,cAAE,KAAK;AACP,oBAAQ,IAAI;AAAA,cACZ,KAAK;AACH,oBAAI,KAAK,MAAM,WAAW,KAAK,GAAG,MAAM,IAAI;AAAE,oBAAE,KAAK;AAAA,gBAAK;AAAA,cAC5D,KAAK;AACH,uBAAO;AACP;AAAA,cACF;AACE,uBAAO,OAAO,aAAa,EAAE;AAC7B;AAAA,YACF;AACA,gBAAI,KAAK,QAAQ,WAAW;AAC1B,gBAAE,KAAK;AACP,mBAAK,YAAY,KAAK;AAAA,YACxB;AACA,yBAAa,KAAK;AAAA,UACpB,OAAO;AACL,cAAE,KAAK;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAGA,SAAG,2BAA2B,WAAW;AACvC,eAAO,KAAK,MAAM,KAAK,MAAM,QAAQ,KAAK,OAAO;AAC/C,kBAAQ,KAAK,MAAM,KAAK,GAAG,GAAG;AAAA,YAC9B,KAAK;AACH,gBAAE,KAAK;AACP;AAAA,YAEF,KAAK;AACH,kBAAI,KAAK,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;AAAE;AAAA,cAAM;AAAA;AAAA,YAEhD,KAAK;AACH,qBAAO,KAAK,YAAY,QAAQ,iBAAiB,KAAK,MAAM,MAAM,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,YAEzF,KAAK;AACH,kBAAI,KAAK,MAAM,KAAK,MAAM,CAAC,MAAM,MAAM;AAAE,kBAAE,KAAK;AAAA,cAAK;AAAA;AAAA,YAEvD,KAAK;AAAA,YAAM,KAAK;AAAA,YAAU,KAAK;AAC7B,gBAAE,KAAK;AACP,mBAAK,YAAY,KAAK,MAAM;AAC5B;AAAA,UACF;AAAA,QACF;AACA,aAAK,MAAM,KAAK,OAAO,uBAAuB;AAAA,MAChD;AAIA,SAAG,kBAAkB,SAAS,YAAY;AACxC,YAAI,KAAK,KAAK,MAAM,WAAW,EAAE,KAAK,GAAG;AACzC,UAAE,KAAK;AACP,gBAAQ,IAAI;AAAA,UACZ,KAAK;AAAK,mBAAO;AAAA;AAAA,UACjB,KAAK;AAAK,mBAAO;AAAA;AAAA,UACjB,KAAK;AAAK,mBAAO,OAAO,aAAa,KAAK,YAAY,CAAC,CAAC;AAAA;AAAA,UACxD,KAAK;AAAK,mBAAO,kBAAkB,KAAK,cAAc,CAAC;AAAA;AAAA,UACvD,KAAK;AAAK,mBAAO;AAAA;AAAA,UACjB,KAAK;AAAI,mBAAO;AAAA;AAAA,UAChB,KAAK;AAAK,mBAAO;AAAA;AAAA,UACjB,KAAK;AAAK,mBAAO;AAAA;AAAA,UACjB,KAAK;AAAI,gBAAI,KAAK,MAAM,WAAW,KAAK,GAAG,MAAM,IAAI;AAAE,gBAAE,KAAK;AAAA,YAAK;AAAA;AAAA,UACnE,KAAK;AACH,gBAAI,KAAK,QAAQ,WAAW;AAAE,mBAAK,YAAY,KAAK;AAAK,gBAAE,KAAK;AAAA,YAAS;AACzE,mBAAO;AAAA,UACT,KAAK;AAAA,UACL,KAAK;AACH,gBAAI,KAAK,QAAQ;AACf,mBAAK;AAAA,gBACH,KAAK,MAAM;AAAA,gBACX;AAAA,cACF;AAAA,YACF;AACA,gBAAI,YAAY;AACd,kBAAI,UAAU,KAAK,MAAM;AAEzB,mBAAK;AAAA,gBACH;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACE,gBAAI,MAAM,MAAM,MAAM,IAAI;AACxB,kBAAI,WAAW,KAAK,MAAM,OAAO,KAAK,MAAM,GAAG,CAAC,EAAE,MAAM,SAAS,EAAE,CAAC;AACpE,kBAAI,QAAQ,SAAS,UAAU,CAAC;AAChC,kBAAI,QAAQ,KAAK;AACf,2BAAW,SAAS,MAAM,GAAG,EAAE;AAC/B,wBAAQ,SAAS,UAAU,CAAC;AAAA,cAC9B;AACA,mBAAK,OAAO,SAAS,SAAS;AAC9B,mBAAK,KAAK,MAAM,WAAW,KAAK,GAAG;AACnC,mBAAK,aAAa,OAAO,OAAO,MAAM,OAAO,QAAQ,KAAK,UAAU,aAAa;AAC/E,qBAAK;AAAA,kBACH,KAAK,MAAM,IAAI,SAAS;AAAA,kBACxB,aACI,qCACA;AAAA,gBACN;AAAA,cACF;AACA,qBAAO,OAAO,aAAa,KAAK;AAAA,YAClC;AACA,gBAAI,UAAU,EAAE,GAAG;AAGjB,kBAAI,KAAK,QAAQ,WAAW;AAAE,qBAAK,YAAY,KAAK;AAAK,kBAAE,KAAK;AAAA,cAAS;AACzE,qBAAO;AAAA,YACT;AACA,mBAAO,OAAO,aAAa,EAAE;AAAA,QAC/B;AAAA,MACF;AAIA,SAAG,cAAc,SAAS,KAAK;AAC7B,YAAI,UAAU,KAAK;AACnB,YAAI,IAAI,KAAK,QAAQ,IAAI,GAAG;AAC5B,YAAI,MAAM,MAAM;AAAE,eAAK,mBAAmB,SAAS,+BAA+B;AAAA,QAAG;AACrF,eAAO;AAAA,MACT;AAQA,SAAG,YAAY,WAAW;AACxB,aAAK,cAAc;AACnB,YAAI,OAAO,IAAI,QAAQ,MAAM,aAAa,KAAK;AAC/C,YAAI,SAAS,KAAK,QAAQ,eAAe;AACzC,eAAO,KAAK,MAAM,KAAK,MAAM,QAAQ;AACnC,cAAI,KAAK,KAAK,kBAAkB;AAChC,cAAI,iBAAiB,IAAI,MAAM,GAAG;AAChC,iBAAK,OAAO,MAAM,QAAS,IAAI;AAAA,UACjC,WAAW,OAAO,IAAI;AACpB,iBAAK,cAAc;AACnB,oBAAQ,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC7C,gBAAI,WAAW,KAAK;AACpB,gBAAI,KAAK,MAAM,WAAW,EAAE,KAAK,GAAG,MAAM,KACxC;AAAE,mBAAK,mBAAmB,KAAK,KAAK,2CAA2C;AAAA,YAAG;AACpF,cAAE,KAAK;AACP,gBAAI,MAAM,KAAK,cAAc;AAC7B,gBAAI,EAAE,QAAQ,oBAAoB,kBAAkB,KAAK,MAAM,GAC7D;AAAE,mBAAK,mBAAmB,UAAU,wBAAwB;AAAA,YAAG;AACjE,oBAAQ,kBAAkB,GAAG;AAC7B,yBAAa,KAAK;AAAA,UACpB,OAAO;AACL;AAAA,UACF;AACA,kBAAQ;AAAA,QACV;AACA,eAAO,OAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAAA,MACrD;AAKA,SAAG,WAAW,WAAW;AACvB,YAAI,OAAO,KAAK,UAAU;AAC1B,YAAI,OAAO,QAAQ;AACnB,YAAI,KAAK,SAAS,KAAK,IAAI,GAAG;AAC5B,iBAAO,SAAS,IAAI;AAAA,QACtB;AACA,eAAO,KAAK,YAAY,MAAM,IAAI;AAAA,MACpC;AAiBA,UAAI,UAAU;AAEd,aAAO,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,cAAc;AAAA,QACd;AAAA,QACA,aAAa;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAQA,eAAS,MAAM,OAAO,SAAS;AAC7B,eAAO,OAAO,MAAM,OAAO,OAAO;AAAA,MACpC;AAMA,eAAS,kBAAkB,OAAO,KAAK,SAAS;AAC9C,eAAO,OAAO,kBAAkB,OAAO,KAAK,OAAO;AAAA,MACrD;AAKA,eAAS,UAAU,OAAO,SAAS;AACjC,eAAO,OAAO,UAAU,OAAO,OAAO;AAAA,MACxC;AAEA,MAAAD,SAAQ,OAAO;AACf,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,cAAc;AACtB,MAAAA,SAAQ,mBAAmB;AAC3B,MAAAA,SAAQ,oBAAoB;AAC5B,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,eAAe;AACvB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,qBAAqB;AAC7B,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,oBAAoB;AAC5B,MAAAA,SAAQ,cAAc;AACtB,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,UAAU;AAAA,IAEpB,EAAE;AAAA;AAAA;;;ACrnMF;AAAA;AAEA,QAAM,gBAAgB;AAEtB,QAAM,YAAY;AAClB,QAAM,gBAAgB;AAGtB,QAAM,cAAc,oBAAI,QAAQ;AAGhC,aAAS,aAAa,OAAO;AAC3B,cAAQ,MAAM,OAAO,SAAS;AAC9B,UAAI,WAAW,YAAY,IAAI,KAAK;AACpC,UAAI,CAAC,UAAU;AACb,cAAM,KAAK,MAAM;AACjB,cAAM,aAAa,MAAM;AACzB,cAAM,YAAY,MAAM;AACxB,cAAM,UAAU,IAAI,WAAW,QAAQ,KAAK;AAC5C,cAAM,UAAU,IAAI,WAAW,SAAS,KAAK;AAC7C,cAAM,UAAU,IAAI,WAAW,kBAAkB,MAAM,IAAI;AAC3D,cAAM,cAAc;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,cAAM,WAAW;AAAA,UACf,SAAS,IAAI,UAAU,SAAS;AAAA,UAChC,SAAS,IAAI,UAAU,WAAW,EAAC,YAAY,KAAI,CAAC;AAAA,UACpD,aAAa,IAAI,UAAU,eAAe,EAAC,YAAY,KAAI,CAAC;AAAA,UAC5D,WAAW,IAAI,UAAU,WAAW;AAAA,QACtC;AAEA,iBAAS,YAAY,gBAAgB,WAAW;AAC9C,eAAK,QAAQ,KAAK,OAAO;AACzB,eAAK,QAAQ,KAAK,OAAO;AACzB,eAAK,cAAc;AAAA,QACrB;AACA,iBAAS,UAAU,gBAAgB,SAAS,UAAU;AACpD,cAAI,MAAM,KAAK,QAAQ,IAAI;AAC3B,cAAI,QAAQ,WAAW,aAAa,GAAG,SAAS,QAAQ,SAAS;AAC/D,iBAAK,QAAQ,IAAI;AACjB,iBAAK,cAAc,KAAK,WAAW,MAAM;AAAA,UAC3C,OAAO;AACL,iBAAK,cAAc;AAAA,UACrB;AAAA,QACF;AAEA,mBAAW,EAAE,aAA0B,SAAmB;AAC1D,oBAAY,IAAI,OAAO,QAAQ;AAAA,MACjC;AAEA,aAAO;AAAA,IACT;AAIA,aAAS,oBAAoB,QAAQ;AACnC,UAAI,CAAC;AACH,eAAO;AAET,UAAI,OAAO,SAAS;AAClB,eAAO,OAAO;AAEhB,UAAI,OAAO,SAAS;AAClB,eAAO,OAAO,UAAU,OAAO,MAAM,OAAO,KAAK;AAEnD,UAAI,OAAO,SAAS;AAClB,eAAO,oBAAoB,OAAO,MAAM,IAAI,MAC5C,oBAAoB,OAAO,QAAQ;AAAA,IACvC;AAEA,WAAO,UAAU,SAAS,SAAS;AACjC,gBAAU,WAAW,CAAC;AACtB,aAAO,SAAS,QAAQ;AACtB,eAAO,OAAO;AAAA,UACZ,iBAAiB,QAAQ,oBAAoB;AAAA,UAC7C,wBAAwB,CAAC,CAAC,QAAQ;AAAA,QACpC,GAAG,MAAM;AAAA,MACX;AAAA,IACF;AAIA,WAAO,eAAe,OAAO,SAAS,YAAY;AAAA,MAChD,KAAK,SAAS,eAAe;AAC3B,eAAO,aAAa,eAAgB,EAAE;AAAA,MACxC;AAAA,MACA,cAAc;AAAA,MACd,YAAY;AAAA,IACd,CAAC;AAED,aAAS,OAAO,SAAS,QAAQ;AAC/B,YAAM,QAAQ,OAAO,SAAS;AAC9B,YAAM,WAAW,aAAa,KAAK;AACnC,YAAM,KAAK,MAAM;AACjB,YAAM,MAAM,SAAS;AACrB,YAAM,cAAc,MAAM;AAC1B,YAAM,UAAU,SAAS,YAAY;AACrC,YAAM,UAAU,SAAS,YAAY;AACrC,YAAM,UAAU,SAAS,YAAY;AACrC,YAAM,YAAY,MAAM;AACxB,YAAM,oBAAoB,MAAM;AAChC,YAAM,mBAAmB,MAAM;AAE/B,aAAO,cAAc,OAAO;AAAA;AAAA,QAE1B,WAAW,WAAW;AACpB,iBAAO;AAAA,QACT;AAAA;AAAA,QAGA,gBAAgB;AACd,cAAI,MAAM,IAAI,aAAa,KAAK;AAChC,qBAAS;AACP,gBAAI,KAAK,OAAO,KAAK,MAAM;AACzB,mBAAK,MAAM,KAAK,OAAO,2BAA2B;AACpD,gBAAI,KAAK,KAAK,MAAM,WAAW,KAAK,GAAG;AAEvC,oBAAQ,IAAI;AAAA,cACZ,KAAK;AAAA;AAAA,cACL,KAAK;AACH,oBAAI,KAAK,QAAQ,KAAK,OAAO;AAC3B,sBAAI,OAAO,MAAM,KAAK,aAAa;AACjC,sBAAE,KAAK;AACP,2BAAO,KAAK,YAAY,IAAI,WAAW;AAAA,kBACzC;AACA,yBAAO,KAAK,iBAAiB,EAAE;AAAA,gBACjC;AACA,uBAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC5C,uBAAO,KAAK,YAAY,IAAI,SAAS,GAAG;AAAA,cAE1C,KAAK;AACH,uBAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC5C,uBAAO,KAAK,eAAe;AAC3B,6BAAa,KAAK;AAClB;AAAA,cAEF,KAAK;AAAA;AAAA,cACL,KAAK;AACH,qBAAK;AAAA,kBACH,KAAK;AAAA,kBACL,uBAAuB,KAAK,MAAM,KAAK,GAAG,IAAI,uBAC3C,OAAO,KAAK,SAAS,cAAc,aAAmB,KAAK,MAAM,KAAK,GAAG,IAAI;AAAA,gBAClF;AAAA,cAEF;AACE,oBAAI,UAAU,EAAE,GAAG;AACjB,yBAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC5C,yBAAO,KAAK,gBAAgB,IAAI;AAChC,+BAAa,KAAK;AAAA,gBACpB,OAAO;AACL,oBAAE,KAAK;AAAA,gBACT;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QAEA,gBAAgB,eAAe;AAC7B,cAAI,KAAK,KAAK,MAAM,WAAW,KAAK,GAAG;AACvC,cAAI;AACJ,YAAE,KAAK;AACP,cAAI,OAAO,MAAM,KAAK,MAAM,WAAW,KAAK,GAAG,MAAM,IAAI;AACvD,cAAE,KAAK;AACP,kBAAM,gBAAgB,OAAO;AAAA,UAC/B,OAAO;AACL,kBAAM,OAAO,aAAa,EAAE;AAAA,UAC9B;AACA,cAAI,KAAK,QAAQ,WAAW;AAC1B,cAAE,KAAK;AACP,iBAAK,YAAY,KAAK;AAAA,UACxB;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,eAAe,OAAO;AACpB,cAAI,MAAM,IAAI,aAAa,EAAE,KAAK;AAClC,qBAAS;AACP,gBAAI,KAAK,OAAO,KAAK,MAAM;AACzB,mBAAK,MAAM,KAAK,OAAO,8BAA8B;AACvD,gBAAI,KAAK,KAAK,MAAM,WAAW,KAAK,GAAG;AACvC,gBAAI,OAAO,MAAO;AAClB,gBAAI,OAAO,IAAI;AACb,qBAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC5C,qBAAO,KAAK,eAAe;AAC3B,2BAAa,KAAK;AAAA,YACpB,WAAW,UAAU,EAAE,GAAG;AACxB,qBAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC5C,qBAAO,KAAK,gBAAgB,KAAK;AACjC,2BAAa,KAAK;AAAA,YACpB,OAAO;AACL,gBAAE,KAAK;AAAA,YACT;AAAA,UACF;AACA,iBAAO,KAAK,MAAM,MAAM,YAAY,KAAK,KAAK;AAC9C,iBAAO,KAAK,YAAY,GAAG,QAAQ,GAAG;AAAA,QACxC;AAAA,QAEA,iBAAiB;AACf,cAAI,MAAM,IAAI,QAAQ,GAAG;AACzB,cAAI,KAAK,KAAK,MAAM,KAAK,GAAG;AAC5B,cAAI,OAAO;AACT,iBAAK,MAAM,KAAK,KAAK,qCAAqC;AAC5D,cAAI,WAAW,EAAE,KAAK;AACtB,iBAAO,KAAK,MAAM,KAAK,MAAM,UAAU,UAAU,IAAI;AACnD,iBAAK,KAAK,MAAM,KAAK,KAAK;AAC1B,gBAAI,OAAO,KAAK;AACd,kBAAI,IAAI,CAAC,MAAM,KAAK;AAClB,oBAAI,IAAI,CAAC,MAAM,KAAK;AAClB,wBAAM,IAAI,OAAO,CAAC;AAClB,sBAAI,UAAU,KAAK,GAAG;AACpB,6BAAS,OAAO,aAAa,SAAS,KAAK,EAAE,CAAC;AAAA,gBAClD,OAAO;AACL,wBAAM,IAAI,OAAO,CAAC;AAClB,sBAAI,cAAc,KAAK,GAAG;AACxB,6BAAS,OAAO,aAAa,SAAS,KAAK,EAAE,CAAC;AAAA,gBAClD;AAAA,cACF,OAAO;AACL,yBAAS,cAAc,GAAG;AAAA,cAC5B;AACA;AAAA,YACF;AACA,mBAAO;AAAA,UACT;AACA,cAAI,CAAC,QAAQ;AACX,iBAAK,MAAM;AACX,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASA,eAAe;AACb,cAAI,IAAI,QAAQ,KAAK;AACrB,aAAG;AACD,iBAAK,KAAK,MAAM,WAAW,EAAE,KAAK,GAAG;AAAA,UACvC,SAAS,iBAAiB,EAAE,KAAK,OAAO;AACxC,iBAAO,KAAK,YAAY,IAAI,SAAS,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG,CAAC;AAAA,QACxE;AAAA;AAAA,QAIA,sBAAsB;AACpB,cAAI,OAAO,KAAK,UAAU;AAC1B,cAAI,KAAK,SAAS,IAAI;AACpB,iBAAK,OAAO,KAAK;AAAA,mBACV,KAAK,KAAK;AACjB,iBAAK,OAAO,KAAK,KAAK;AAAA;AAEtB,iBAAK,WAAW;AAClB,eAAK,KAAK;AACV,iBAAO,KAAK,WAAW,MAAM,eAAe;AAAA,QAC9C;AAAA;AAAA,QAIA,0BAA0B;AACxB,cAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,cAAI,OAAO,KAAK,oBAAoB;AACpC,cAAI,CAAC,QAAQ,mBAAmB,CAAC,KAAK,IAAI,GAAG,KAAK,EAAG,QAAO;AAC5D,cAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,eAAK,YAAY;AACjB,eAAK,OAAO,KAAK,oBAAoB;AACrC,iBAAO,KAAK,WAAW,MAAM,mBAAmB;AAAA,QAClD;AAAA;AAAA;AAAA,QAKA,uBAAuB;AACrB,cAAI,KAAK,SAAS,IAAI,UAAW,QAAO;AACxC,cAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,cAAI,OAAO,KAAK,wBAAwB;AACxC,cAAI,KAAK,SAAS,GAAG,OAAO,KAAK,SAAS,uBAAuB,CAAC,QAAQ,wBAAwB;AAChG,iBAAK,WAAW;AAAA,UAClB;AACA,iBAAO,KAAK,IAAI,GAAG,GAAG,GAAG;AACvB,gBAAI,UAAU,KAAK,YAAY,UAAU,QAAQ;AACjD,oBAAQ,SAAS;AACjB,oBAAQ,WAAW,KAAK,oBAAoB;AAC5C,mBAAO,KAAK,WAAW,SAAS,qBAAqB;AAAA,UACvD;AACA,iBAAO;AAAA,QACT;AAAA;AAAA,QAIA,0BAA0B;AACxB,kBAAQ,KAAK,MAAM;AAAA,YACnB,KAAK,GAAG;AACN,kBAAI,OAAO,KAAK,6BAA6B;AAC7C,kBAAI,KAAK,WAAW,SAAS;AAC3B,qBAAK,MAAM,KAAK,OAAO,6DAA6D;AACtF,qBAAO;AAAA,YAET,KAAK,IAAI;AAAA,YACT,KAAK,GAAG;AACN,qBAAO,KAAK,cAAc;AAAA,YAE5B;AACE,mBAAK,MAAM,KAAK,OAAO,+DAA+D;AAAA,UACxF;AAAA,QACF;AAAA;AAAA;AAAA;AAAA,QAMA,2BAA2B;AACzB,cAAI,OAAO,KAAK,YAAY,KAAK,YAAY,KAAK,aAAa;AAC/D,iBAAO,KAAK,aAAa,MAAM,sBAAsB,KAAK,OAAO,KAAK,QAAQ;AAAA,QAChF;AAAA;AAAA,QAIA,+BAA+B;AAC7B,cAAI,OAAO,KAAK,UAAU;AAC1B,eAAK,KAAK;AACV,eAAK,aAAa,KAAK,SAAS,GAAG,SAC/B,KAAK,yBAAyB,IAC9B,KAAK,gBAAgB;AACzB,eAAK,OAAO,GAAG,MAAM;AACrB,iBAAO,KAAK,WAAW,MAAM,wBAAwB;AAAA,QACvD;AAAA;AAAA,QAIA,qBAAqB;AACnB,cAAI,OAAO,KAAK,UAAU;AAC1B,cAAI,KAAK,IAAI,GAAG,MAAM,GAAG;AACvB,iBAAK,OAAO,GAAG,QAAQ;AACvB,iBAAK,WAAW,KAAK,iBAAiB;AACtC,iBAAK,OAAO,GAAG,MAAM;AACrB,mBAAO,KAAK,WAAW,MAAM,oBAAoB;AAAA,UACnD;AACA,eAAK,OAAO,KAAK,wBAAwB;AACzC,eAAK,QAAQ,KAAK,IAAI,GAAG,EAAE,IAAI,KAAK,wBAAwB,IAAI;AAChE,iBAAO,KAAK,WAAW,MAAM,cAAc;AAAA,QAC7C;AAAA;AAAA,QAIA,0BAA0B,UAAU,UAAU;AAC5C,cAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,eAAK,aAAa,CAAC;AACnB,cAAI,WAAW,KAAK,qBAAqB;AACzC,cAAI,SAAU,MAAK,OAAO;AAC1B,iBAAO,KAAK,SAAS,GAAG,SAAS,KAAK,SAAS,IAAI;AACjD,iBAAK,WAAW,KAAK,KAAK,mBAAmB,CAAC;AAChD,eAAK,cAAc,KAAK,IAAI,GAAG,KAAK;AACpC,eAAK,OAAO,IAAI,SAAS;AACzB,iBAAO,KAAK,WAAW,MAAM,WAAW,sBAAsB,oBAAoB;AAAA,QACpF;AAAA;AAAA,QAIA,0BAA0B,UAAU,UAAU;AAC5C,cAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,cAAI,WAAW,KAAK,qBAAqB;AACzC,cAAI,SAAU,MAAK,OAAO;AAC1B,eAAK,OAAO,IAAI,SAAS;AACzB,iBAAO,KAAK,WAAW,MAAM,WAAW,sBAAsB,oBAAoB;AAAA,QACpF;AAAA;AAAA;AAAA,QAKA,mBAAmB,UAAU,UAAU;AACrC,cAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,cAAI,WAAW,CAAC;AAChB,cAAI,iBAAiB,KAAK,0BAA0B,UAAU,QAAQ;AACtE,cAAI,iBAAiB;AAErB,cAAI,CAAC,eAAe,aAAa;AAC/B,qBAAU,YAAS;AACjB,sBAAQ,KAAK,MAAM;AAAA,gBACnB,KAAK,IAAI;AACP,6BAAW,KAAK;AAAO,6BAAW,KAAK;AACvC,uBAAK,KAAK;AACV,sBAAI,KAAK,IAAI,GAAG,KAAK,GAAG;AACtB,qCAAiB,KAAK,0BAA0B,UAAU,QAAQ;AAClE,0BAAM;AAAA,kBACR;AACA,2BAAS,KAAK,KAAK,mBAAmB,UAAU,QAAQ,CAAC;AACzD;AAAA,gBAEF,KAAK,IAAI;AACP,2BAAS,KAAK,KAAK,cAAc,CAAC;AAClC;AAAA,gBAEF,KAAK,GAAG;AACN,2BAAS,KAAK,KAAK,6BAA6B,CAAC;AACjD;AAAA,gBAEF;AACE,uBAAK,WAAW;AAAA,cAClB;AAAA,YACF;AACA,gBAAI,oBAAoB,eAAe,IAAI,MAAM,oBAAoB,eAAe,IAAI,GAAG;AACzF,mBAAK;AAAA,gBACH,eAAe;AAAA,gBACf,iDAAiD,oBAAoB,eAAe,IAAI,IAAI;AAAA,cAAG;AAAA,YACnG;AAAA,UACF;AACA,cAAI,oBAAoB,eAAe,OAAO,YAAY;AAE1D,eAAK,YAAY,iBAAiB,IAAI;AACtC,eAAK,YAAY,iBAAiB,IAAI;AACtC,eAAK,WAAW;AAChB,cAAI,KAAK,SAAS,GAAG,cAAc,KAAK,UAAU,KAAK;AACrD,iBAAK,MAAM,KAAK,OAAO,2DAA2D;AAAA,UACpF;AACA,iBAAO,KAAK,WAAW,MAAM,QAAQ,iBAAiB;AAAA,QACxD;AAAA;AAAA,QAIA,gBAAgB;AACd,cAAI,OAAO,KAAK,aAAa,KAAK,KAAK;AACvC,eAAK,OAAO;AACZ,iBAAO;AAAA,QACT;AAAA;AAAA,QAIA,mBAAmB;AACjB,cAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,eAAK,KAAK;AACV,iBAAO,KAAK,mBAAmB,UAAU,QAAQ;AAAA,QACnD;AAAA,QAEA,cAAc,wBAAwB;AACpC,cAAI,KAAK,SAAS,IAAI;AACpB,mBAAO,KAAK,cAAc;AAAA,mBACnB,KAAK,SAAS,IAAI;AACzB,mBAAO,KAAK,iBAAiB;AAAA;AAE7B,mBAAO,MAAM,cAAc,sBAAsB;AAAA,QACrD;AAAA,QAEA,UAAU,MAAM;AACd,cAAI,UAAU,KAAK,WAAW;AAE9B,cAAI,YAAY,QAAS,QAAO,KAAK,cAAc;AAEnD,cAAI,YAAY,WAAW,YAAY,SAAS;AAC9C,gBAAI,kBAAkB,IAAI,EAAG,QAAO,KAAK,aAAa;AAEtD,gBAAI,QAAQ,IAAI;AACd,gBAAE,KAAK;AACP,qBAAO,KAAK,YAAY,IAAI,SAAS;AAAA,YACvC;AAEA,iBAAK,SAAS,MAAM,SAAS,OAAO,WAAW;AAC7C,qBAAO,KAAK,eAAe,IAAI;AAAA,UACnC;AAEA,cAAI,SAAS,MAAM,KAAK,eAAe,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,IAAI;AACjF,cAAE,KAAK;AACP,mBAAO,KAAK,YAAY,IAAI,WAAW;AAAA,UACzC;AACA,iBAAO,MAAM,UAAU,IAAI;AAAA,QAC7B;AAAA,QAEA,cAAc,UAAU;AACtB,cAAI,KAAK,QAAQ,GAAG,QAAQ;AAC1B,gBAAI,aAAa,KAAK,WAAW;AACjC,gBAAI,cAAc,QAAS,MAAK,QAAQ,KAAK,YAAY,MAAM;AAAA,qBACtD,cAAc,QAAS,MAAK,QAAQ,KAAK,YAAY,MAAM;AAAA,gBAC/D,OAAM,cAAc,QAAQ;AACjC,iBAAK,cAAc;AAAA,UACrB,WAAW,KAAK,SAAS,GAAG,SAAS,aAAa,IAAI,aAAa;AACjE,iBAAK,QAAQ,UAAU;AACvB,iBAAK,QAAQ,KAAK,OAAO;AACzB,iBAAK,cAAc;AAAA,UACrB,OAAO;AACL,mBAAO,MAAM,cAAc,QAAQ;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": ["exports", "i", "TokenType", "Position", "SourceLocation", "<PERSON><PERSON><PERSON>", "parse", "ref", "parseExpressionAt", "tokenizer", "DestructuringErrors", "list", "ecmaVersion", "TokContext", "<PERSON><PERSON>", "Node", "BranchID", "self", "RegExpValidationState", "Token"]}