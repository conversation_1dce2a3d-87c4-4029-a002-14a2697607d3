import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { InputField } from './InputField';

describe('InputField', () => {
  it('renders with basic props', () => {
    render(
      <InputField
        label="Test Label"
        placeholder="Test placeholder"
        value="test value"
      />
    );

    expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Test placeholder')).toBeInTheDocument();
    expect(screen.getByDisplayValue('test value')).toBeInTheDocument();
  });

  it('calls onChange when input value changes', () => {
    const handleChange = vi.fn();
    render(<InputField label="Test" onChange={handleChange} />);

    const input = screen.getByLabelText('Test');
    fireEvent.change(input, { target: { value: 'new value' } });

    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({ value: 'new value' })
      })
    );
  });

  it('displays error message when invalid', () => {
    render(
      <InputField
        label="Test"
        invalid={true}
        errorMessage="This field is required"
      />
    );

    expect(screen.getByText('This field is required')).toBeInTheDocument();
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });

  it('displays helper text when not invalid', () => {
    render(
      <InputField
        label="Test"
        helperText="This is helper text"
      />
    );

    expect(screen.getByText('This is helper text')).toBeInTheDocument();
  });

  it('shows loading spinner when loading', () => {
    render(<InputField label="Test" loading={true} />);

    expect(document.querySelector('.animate-spin')).toBeInTheDocument();
  });

  it('disables input when disabled', () => {
    render(<InputField label="Test" disabled={true} />);

    expect(screen.getByLabelText('Test')).toBeDisabled();
  });

  it('disables input when loading', () => {
    render(<InputField label="Test" loading={true} />);

    expect(screen.getByLabelText('Test')).toBeDisabled();
  });
});