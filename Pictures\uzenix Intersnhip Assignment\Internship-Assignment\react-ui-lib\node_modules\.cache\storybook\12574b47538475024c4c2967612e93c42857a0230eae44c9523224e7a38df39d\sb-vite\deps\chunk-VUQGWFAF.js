import {
  require_core_events
} from "./chunk-7ZSBYTHS.js";
import {
  require_test
} from "./chunk-MUGBNPQY.js";
import {
  require_global
} from "./chunk-62Y44TTL.js";
import {
  require_client_logger
} from "./chunk-LHOSQLWN.js";
import {
  require_preview_errors
} from "./chunk-XZMMCN3X.js";
import {
  require_preview_api
} from "./chunk-ZLWVYHQP.js";
import {
  __commonJS,
  __toESM
} from "./chunk-7D4SUZUM.js";

// node_modules/.cache/sb-vite-plugin-externals/storybook/internal/channels.js
var require_channels = __commonJS({
  "node_modules/.cache/sb-vite-plugin-externals/storybook/internal/channels.js"(exports, module) {
    module.exports = __STORYBOOK_MODULE_CHANNELS__;
  }
});

// node_modules/storybook/dist/csf/index.js
var import_global2 = __toESM(require_global());
var import_channels = __toESM(require_channels());
var import_client_logger2 = __toESM(require_client_logger());
var import_core_events2 = __toESM(require_core_events());
var import_global3 = __toESM(require_global());
var import_client_logger3 = __toESM(require_client_logger());
var import_client_logger4 = __toESM(require_client_logger());
var import_preview_errors = __toESM(require_preview_errors());
var import_global4 = __toESM(require_global());
var import_global5 = __toESM(require_global());
var import_client_logger5 = __toESM(require_client_logger());
var import_client_logger6 = __toESM(require_client_logger());
var import_global6 = __toESM(require_global());
var import_preview_errors2 = __toESM(require_preview_errors());
var import_preview_errors3 = __toESM(require_preview_errors());
var import_global7 = __toESM(require_global());
var import_preview_api = __toESM(require_preview_api());
var import_test = __toESM(require_test());
var import_preview_api2 = __toESM(require_preview_api());

// node_modules/storybook/dist/instrumenter/index.js
var import_client_logger = __toESM(require_client_logger());
var import_core_events = __toESM(require_core_events());
var import_global = __toESM(require_global());
var lr = Object.defineProperty;
var i = (e, t) => lr(e, "name", { value: t, configurable: true });
var fr = {
  reset: [0, 0],
  bold: [1, 22, "\x1B[22m\x1B[1m"],
  dim: [2, 22, "\x1B[22m\x1B[2m"],
  italic: [3, 23],
  underline: [4, 24],
  inverse: [7, 27],
  hidden: [8, 28],
  strikethrough: [9, 29],
  black: [30, 39],
  red: [31, 39],
  green: [32, 39],
  yellow: [33, 39],
  blue: [34, 39],
  magenta: [35, 39],
  cyan: [36, 39],
  white: [37, 39],
  gray: [90, 39],
  bgBlack: [40, 49],
  bgRed: [41, 49],
  bgGreen: [42, 49],
  bgYellow: [43, 49],
  bgBlue: [44, 49],
  bgMagenta: [45, 49],
  bgCyan: [46, 49],
  bgWhite: [47, 49],
  blackBright: [90, 39],
  redBright: [91, 39],
  greenBright: [92, 39],
  yellowBright: [93, 39],
  blueBright: [94, 39],
  magentaBright: [95, 39],
  cyanBright: [96, 39],
  whiteBright: [97, 39],
  bgBlackBright: [100, 49],
  bgRedBright: [101, 49],
  bgGreenBright: [102, 49],
  bgYellowBright: [103, 49],
  bgBlueBright: [104, 49],
  bgMagentaBright: [105, 49],
  bgCyanBright: [106, 49],
  bgWhiteBright: [107, 49]
};
var mr = Object.entries(fr);
function Ge(e) {
  return String(e);
}
i(Ge, "a");
Ge.open = "";
Ge.close = "";
function Ft(e = false) {
  let t = typeof process < "u" ? process : void 0, n2 = t?.env || {}, r = t?.argv || [];
  return !("NO_COLOR" in n2 || r.includes("--no-color")) && ("FORCE_COLOR" in n2 || r.includes("--color") || t?.platform === "win32" || e && n2.TERM !== "dumb" || "CI" in n2) || typeof window < "u" && !!window.chrome;
}
i(Ft, "C");
function jt(e = false) {
  let t = Ft(e), n2 = i((c, a, u, m) => {
    let p = "", l = 0;
    do
      p += c.substring(l, m) + u, l = m + a.length, m = c.indexOf(a, l);
    while (~m);
    return p + c.substring(l);
  }, "i"), r = i((c, a, u = c) => {
    let m = i((p) => {
      let l = String(p), b2 = l.indexOf(a, c.length);
      return ~b2 ? c + n2(l, a, u, b2) + a : c + l + a;
    }, "o");
    return m.open = c, m.close = a, m;
  }, "g"), o = {
    isColorSupported: t
  }, s = i((c) => `\x1B[${c}m`, "d");
  for (let [c, a] of mr)
    o[c] = t ? r(
      s(a[0]),
      s(a[1]),
      a[2]
    ) : Ge;
  return o;
}
i(jt, "p");
var v = jt();
function Xt(e, t) {
  return t.forEach(function(n2) {
    n2 && typeof n2 != "string" && !Array.isArray(n2) && Object.keys(n2).forEach(function(r) {
      if (r !== "default" && !(r in e)) {
        var o = Object.getOwnPropertyDescriptor(n2, r);
        Object.defineProperty(e, r, o.get ? o : {
          enumerable: true,
          get: i(function() {
            return n2[r];
          }, "get")
        });
      }
    });
  }), Object.freeze(e);
}
i(Xt, "_mergeNamespaces");
function pr(e, t) {
  let n2 = Object.keys(e), r = t === null ? n2 : n2.sort(t);
  if (Object.getOwnPropertySymbols)
    for (let o of Object.getOwnPropertySymbols(e))
      Object.getOwnPropertyDescriptor(e, o).enumerable && r.push(o);
  return r;
}
i(pr, "getKeysOfEnumerableProperties");
function Ee(e, t, n2, r, o, s, c = ": ") {
  let a = "", u = 0, m = e.next();
  if (!m.done) {
    a += t.spacingOuter;
    let p = n2 + t.indent;
    for (; !m.done; ) {
      if (a += p, u++ === t.maxWidth) {
        a += "…";
        break;
      }
      let l = s(m.value[0], t, p, r, o), b2 = s(m.value[1], t, p, r, o);
      a += l + c + b2, m = e.next(), m.done ? t.min || (a += ",") : a += `,${t.spacingInner}`;
    }
    a += t.spacingOuter + n2;
  }
  return a;
}
i(Ee, "printIteratorEntries");
function Qe(e, t, n2, r, o, s) {
  let c = "", a = 0, u = e.next();
  if (!u.done) {
    c += t.spacingOuter;
    let m = n2 + t.indent;
    for (; !u.done; ) {
      if (c += m, a++ === t.maxWidth) {
        c += "…";
        break;
      }
      c += s(u.value, t, m, r, o), u = e.next(), u.done ? t.min || (c += ",") : c += `,${t.spacingInner}`;
    }
    c += t.spacingOuter + n2;
  }
  return c;
}
i(Qe, "printIteratorValues");
function Ae(e, t, n2, r, o, s) {
  let c = "";
  e = e instanceof ArrayBuffer ? new DataView(e) : e;
  let a = i((m) => m instanceof DataView, "isDataView"), u = a(e) ? e.byteLength : e.length;
  if (u > 0) {
    c += t.spacingOuter;
    let m = n2 + t.indent;
    for (let p = 0; p < u; p++) {
      if (c += m, p === t.maxWidth) {
        c += "…";
        break;
      }
      (a(e) || p in e) && (c += s(a(e) ? e.getInt8(p) : e[p], t, m, r, o)), p < u - 1 ? c += `,${t.spacingInner}` : t.min || (c += ",");
    }
    c += t.spacingOuter + n2;
  }
  return c;
}
i(Ae, "printListItems");
function ve(e, t, n2, r, o, s) {
  let c = "", a = pr(e, t.compareKeys);
  if (a.length > 0) {
    c += t.spacingOuter;
    let u = n2 + t.indent;
    for (let m = 0; m < a.length; m++) {
      let p = a[m], l = s(p, t, u, r, o), b2 = s(e[p], t, u, r, o);
      c += `${u + l}: ${b2}`, m < a.length - 1 ? c += `,${t.spacingInner}` : t.min || (c += ",");
    }
    c += t.spacingOuter + n2;
  }
  return c;
}
i(ve, "printObjectProperties");
var gr = typeof Symbol == "function" && Symbol.for ? Symbol.for("jest.asymmetricMatcher") : 1267621;
var we = " ";
var hr = i((e, t, n2, r, o, s) => {
  let c = e.toString();
  if (c === "ArrayContaining" || c === "ArrayNotContaining")
    return ++r > t.maxDepth ? `[${c}]` : `${c + we}[${Ae(e.sample, t, n2, r, o, s)}]`;
  if (c === "ObjectContaining" || c === "ObjectNotContaining")
    return ++r > t.maxDepth ? `[${c}]` : `${c + we}{${ve(e.sample, t, n2, r, o, s)}}`;
  if (c === "StringMatching" || c === "StringNotMatching" || c === "StringContaining" || c === "StringNotContaining")
    return c + we + s(e.sample, t, n2, r, o);
  if (typeof e.toAsymmetricMatcher != "function")
    throw new TypeError(`Asymmetric matcher ${e.constructor.name} does not implement toAsymmetricMatcher()`);
  return e.toAsymmetricMatcher();
}, "serialize$5");
var dr = i((e) => e && e.$$typeof === gr, "test$5");
var yr = {
  serialize: hr,
  test: dr
};
var br = " ";
var Zt = /* @__PURE__ */ new Set(["DOMStringMap", "NamedNodeMap"]);
var Sr = /^(?:HTML\w*Collection|NodeList)$/;
function Er(e) {
  return Zt.has(e) || Sr.test(e);
}
i(Er, "testName");
var _r = i((e) => e && e.constructor && !!e.constructor.name && Er(e.constructor.name), "test$4");
function Tr(e) {
  return e.constructor.name === "NamedNodeMap";
}
i(Tr, "isNamedNodeMap");
var Cr = i((e, t, n2, r, o, s) => {
  let c = e.constructor.name;
  return ++r > t.maxDepth ? `[${c}]` : (t.min ? "" : c + br) + (Zt.has(c) ? `{${ve(Tr(e) ? [...e].reduce(
    (a, u) => (a[u.name] = u.value, a),
    {}
  ) : { ...e }, t, n2, r, o, s)}}` : `[${Ae([...e], t, n2, r, o, s)}]`);
}, "serialize$4");
var Or = {
  serialize: Cr,
  test: _r
};
function Qt(e) {
  return e.replaceAll("<", "&lt;").replaceAll(">", "&gt;");
}
i(Qt, "escapeHTML");
function et(e, t, n2, r, o, s, c) {
  let a = r + n2.indent, u = n2.colors;
  return e.map((m) => {
    let p = t[m], l = c(p, n2, a, o, s);
    return typeof p != "string" && (l.includes(`
`) && (l = n2.spacingOuter + a + l + n2.spacingOuter + r), l = `{${l}}`), `${n2.spacingInner + r + u.prop.open + m + u.prop.close}=${u.value.open}${l}${u.value.close}`;
  }).join("");
}
i(et, "printProps");
function tt(e, t, n2, r, o, s) {
  return e.map((c) => t.spacingOuter + n2 + (typeof c == "string" ? vt(c, t) : s(c, t, n2, r, o))).join("");
}
i(tt, "printChildren");
function vt(e, t) {
  let n2 = t.colors.content;
  return n2.open + Qt(e) + n2.close;
}
i(vt, "printText");
function $r(e, t) {
  let n2 = t.colors.comment;
  return `${n2.open}<!--${Qt(e)}-->${n2.close}`;
}
i($r, "printComment");
function nt(e, t, n2, r, o) {
  let s = r.colors.tag;
  return `${s.open}<${e}${t && s.close + t + r.spacingOuter + o + s.open}${n2 ? `>${s.close}${n2}${r.spacingOuter}${o}${s.open}</${e}` : `${t && !r.min ? "" : " "}/`}>${s.close}`;
}
i(nt, "printElement");
function rt(e, t) {
  let n2 = t.colors.tag;
  return `${n2.open}<${e}${n2.close} …${n2.open} />${n2.close}`;
}
i(rt, "printElementAsLeaf");
var wr = 1;
var en = 3;
var tn = 8;
var nn = 11;
var Rr = /^(?:(?:HTML|SVG)\w*)?Element$/;
function Ar(e) {
  try {
    return typeof e.hasAttribute == "function" && e.hasAttribute("is");
  } catch {
    return false;
  }
}
i(Ar, "testHasAttribute");
function Pr(e) {
  let t = e.constructor.name, { nodeType: n2, tagName: r } = e, o = typeof r == "string" && r.includes("-") || Ar(e);
  return n2 === wr && (Rr.test(t) || o) || n2 === en && t === "Text" || n2 === tn && t === "Comment" || n2 === nn && t === "DocumentFragment";
}
i(Pr, "testNode");
var Nr = i((e) => {
  var t;
  return (e == null || (t = e.constructor) === null || t === void 0 ? void 0 : t.name) && Pr(e);
}, "test$3");
function Ir(e) {
  return e.nodeType === en;
}
i(Ir, "nodeIsText");
function Mr(e) {
  return e.nodeType === tn;
}
i(Mr, "nodeIsComment");
function He(e) {
  return e.nodeType === nn;
}
i(He, "nodeIsFragment");
var Lr = i((e, t, n2, r, o, s) => {
  if (Ir(e))
    return vt(e.data, t);
  if (Mr(e))
    return $r(e.data, t);
  let c = He(e) ? "DocumentFragment" : e.tagName.toLowerCase();
  return ++r > t.maxDepth ? rt(c, t) : nt(c, et(He(e) ? [] : Array.from(e.attributes, (a) => a.name).sort(), He(e) ? {} : [...e.attributes].reduce((a, u) => (a[u.name] = u.value, a), {}), t, n2 + t.indent, r, o, s), tt(Array.prototype.slice.call(e.childNodes || e.children), t, n2 + t.indent, r, o, s), t, n2);
}, "serialize$3");
var xr = {
  serialize: Lr,
  test: Nr
};
var Dr = "@@__IMMUTABLE_ITERABLE__@@";
var Fr = "@@__IMMUTABLE_LIST__@@";
var jr = "@@__IMMUTABLE_KEYED__@@";
var kr = "@@__IMMUTABLE_MAP__@@";
var kt = "@@__IMMUTABLE_ORDERED__@@";
var Br = "@@__IMMUTABLE_RECORD__@@";
var zr = "@@__IMMUTABLE_SEQ__@@";
var Yr = "@@__IMMUTABLE_SET__@@";
var Ur = "@@__IMMUTABLE_STACK__@@";
var de = i((e) => `Immutable.${e}`, "getImmutableName");
var Ne = i((e) => `[${e}]`, "printAsLeaf");
var Se = " ";
var Bt = "…";
function Wr(e, t, n2, r, o, s, c) {
  return ++r > t.maxDepth ? Ne(de(c)) : `${de(c) + Se}{${Ee(e.entries(), t, n2, r, o, s)}}`;
}
i(Wr, "printImmutableEntries");
function Vr(e) {
  let t = 0;
  return { next() {
    if (t < e._keys.length) {
      let n2 = e._keys[t++];
      return {
        done: false,
        value: [n2, e.get(n2)]
      };
    }
    return {
      done: true,
      value: void 0
    };
  } };
}
i(Vr, "getRecordEntries");
function qr(e, t, n2, r, o, s) {
  let c = de(e._name || "Record");
  return ++r > t.maxDepth ? Ne(c) : `${c + Se}{${Ee(Vr(e), t, n2, r, o, s)}}`;
}
i(qr, "printImmutableRecord");
function Kr(e, t, n2, r, o, s) {
  let c = de("Seq");
  return ++r > t.maxDepth ? Ne(c) : e[jr] ? `${c + Se}{${e._iter || e._object ? Ee(e.entries(), t, n2, r, o, s) : Bt}}` : `${c + Se}[${e._iter || e._array || e._collection || e._iterable ? Qe(e.values(), t, n2, r, o, s) : Bt}]`;
}
i(Kr, "printImmutableSeq");
function Je(e, t, n2, r, o, s, c) {
  return ++r > t.maxDepth ? Ne(de(c)) : `${de(c) + Se}[${Qe(e.values(), t, n2, r, o, s)}]`;
}
i(Je, "printImmutableValues");
var Gr = i((e, t, n2, r, o, s) => e[kr] ? Wr(e, t, n2, r, o, s, e[kt] ? "OrderedMap" : "Map") : e[Fr] ? Je(e, t, n2, r, o, s, "List") : e[Yr] ? Je(e, t, n2, r, o, s, e[kt] ? "OrderedSet" : "Set") : e[Ur] ? Je(e, t, n2, r, o, s, "Stack") : e[zr] ? Kr(e, t, n2, r, o, s) : qr(e, t, n2, r, o, s), "serialize$2");
var Hr = i((e) => e && (e[Dr] === true || e[Br] === true), "test$2");
var Jr = {
  serialize: Gr,
  test: Hr
};
function rn(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
i(rn, "getDefaultExportFromCjs");
var Xe = { exports: {} };
var A = {};
var zt;
function Xr() {
  return zt || (zt = 1, (function() {
    function e(f) {
      if (typeof f == "object" && f !== null) {
        var d = f.$$typeof;
        switch (d) {
          case t:
            switch (f = f.type, f) {
              case r:
              case s:
              case o:
              case m:
              case p:
              case g:
                return f;
              default:
                switch (f = f && f.$$typeof, f) {
                  case a:
                  case u:
                  case b2:
                  case l:
                    return f;
                  case c:
                    return f;
                  default:
                    return d;
                }
            }
          case n2:
            return d;
        }
      }
    }
    i(e, "typeOf");
    var t = Symbol.for("react.transitional.element"), n2 = Symbol.for("react.portal"), r = Symbol.for("react.fragment"), o = Symbol.for("react.strict_mode"), s = Symbol.for("react.profiler"), c = Symbol.for("react.consumer"), a = Symbol.for("react.context"), u = Symbol.for("react.forward_ref"), m = Symbol.for("react.suspense"), p = Symbol.for("react.suspense_list"), l = Symbol.for("react.memo"), b2 = Symbol.for("react.lazy"), g = Symbol.for("react.view_transition"), h = Symbol.for("react.client.reference");
    A.ContextConsumer = c, A.ContextProvider = a, A.Element = t, A.ForwardRef = u, A.Fragment = r, A.Lazy = b2, A.Memo = l, A.Portal = n2, A.Profiler = s, A.StrictMode = o, A.Suspense = m, A.SuspenseList = p, A.isContextConsumer = function(f) {
      return e(f) === c;
    }, A.isContextProvider = function(f) {
      return e(f) === a;
    }, A.isElement = function(f) {
      return typeof f == "object" && f !== null && f.$$typeof === t;
    }, A.isForwardRef = function(f) {
      return e(f) === u;
    }, A.isFragment = function(f) {
      return e(f) === r;
    }, A.isLazy = function(f) {
      return e(f) === b2;
    }, A.isMemo = function(f) {
      return e(f) === l;
    }, A.isPortal = function(f) {
      return e(f) === n2;
    }, A.isProfiler = function(f) {
      return e(f) === s;
    }, A.isStrictMode = function(f) {
      return e(f) === o;
    }, A.isSuspense = function(f) {
      return e(f) === m;
    }, A.isSuspenseList = function(f) {
      return e(f) === p;
    }, A.isValidElementType = function(f) {
      return typeof f == "string" || typeof f == "function" || f === r || f === s || f === o || f === m || f === p || typeof f == "object" && f !== null && (f.$$typeof === b2 || f.$$typeof === l || f.$$typeof === a || f.$$typeof === c || f.$$typeof === u || f.$$typeof === h || f.getModuleId !== void 0);
    }, A.typeOf = e;
  })()), A;
}
i(Xr, "requireReactIs_development$1");
var Yt;
function Zr() {
  return Yt || (Yt = 1, Xe.exports = Xr()), Xe.exports;
}
i(Zr, "requireReactIs$1");
var on = Zr();
var Qr = rn(on);
var vr = Xt({
  __proto__: null,
  default: Qr
}, [on]);
var Ze = { exports: {} };
var w = {};
var Ut;
function eo() {
  return Ut || (Ut = 1, (function() {
    var e = Symbol.for("react.element"), t = Symbol.for("react.portal"), n2 = Symbol.for("react.fragment"), r = Symbol.for("react.strict_mode"), o = Symbol.for("react.profiler"), s = Symbol.for("react.provider"), c = Symbol.for("react.context"), a = Symbol.for("react.server_context"), u = Symbol.for("react.forward_ref"), m = Symbol.for("react.suspense"), p = Symbol.for("react.suspense_list"), l = Symbol.for("react.memo"), b2 = Symbol.for("react.lazy"), g = Symbol.for("react.offscreen"), h = false, f = false, d = false, S = false, _2 = false, O;
    O = Symbol.for("react.module.reference");
    function y(C2) {
      return !!(typeof C2 == "string" || typeof C2 == "function" || C2 === n2 || C2 === o || _2 || C2 === r || C2 === m || C2 === p || S || C2 === g || h || f || d || typeof C2 == "object" && C2 !== null && (C2.$$typeof === b2 || C2.$$typeof === l || C2.$$typeof === s || C2.$$typeof === c || C2.$$typeof === u || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      C2.$$typeof === O || C2.getModuleId !== void 0));
    }
    i(y, "isValidElementType");
    function E(C2) {
      if (typeof C2 == "object" && C2 !== null) {
        var Ke2 = C2.$$typeof;
        switch (Ke2) {
          case e:
            var $e2 = C2.type;
            switch ($e2) {
              case n2:
              case o:
              case r:
              case m:
              case p:
                return $e2;
              default:
                var Dt2 = $e2 && $e2.$$typeof;
                switch (Dt2) {
                  case a:
                  case c:
                  case u:
                  case b2:
                  case l:
                  case s:
                    return Dt2;
                  default:
                    return Ke2;
                }
            }
          case t:
            return Ke2;
        }
      }
    }
    i(E, "typeOf");
    var $2 = c, T = s, R = e, K2 = u, Q2 = n2, I = b2, k = l, G2 = t, Y2 = o, N2 = r, L = m, x = p, H = false, F = false;
    function W2(C2) {
      return H || (H = true, console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 18+.")), false;
    }
    i(W2, "isAsyncMode");
    function re2(C2) {
      return F || (F = true, console.warn("The ReactIs.isConcurrentMode() alias has been deprecated, and will be removed in React 18+.")), false;
    }
    i(re2, "isConcurrentMode");
    function V2(C2) {
      return E(C2) === c;
    }
    i(V2, "isContextConsumer");
    function q2(C2) {
      return E(C2) === s;
    }
    i(q2, "isContextProvider");
    function se2(C2) {
      return typeof C2 == "object" && C2 !== null && C2.$$typeof === e;
    }
    i(se2, "isElement");
    function J2(C2) {
      return E(C2) === u;
    }
    i(J2, "isForwardRef");
    function U2(C2) {
      return E(C2) === n2;
    }
    i(U2, "isFragment");
    function oe2(C2) {
      return E(C2) === b2;
    }
    i(oe2, "isLazy");
    function he2(C2) {
      return E(C2) === l;
    }
    i(he2, "isMemo");
    function ue2(C2) {
      return E(C2) === t;
    }
    i(ue2, "isPortal");
    function be2(C2) {
      return E(C2) === o;
    }
    i(be2, "isProfiler");
    function Ce2(C2) {
      return E(C2) === r;
    }
    i(Ce2, "isStrictMode");
    function Oe2(C2) {
      return E(C2) === m;
    }
    i(Oe2, "isSuspense");
    function ar2(C2) {
      return E(C2) === p;
    }
    i(ar2, "isSuspenseList"), w.ContextConsumer = $2, w.ContextProvider = T, w.Element = R, w.ForwardRef = K2, w.Fragment = Q2, w.Lazy = I, w.Memo = k, w.Portal = G2, w.Profiler = Y2, w.StrictMode = N2, w.Suspense = L, w.SuspenseList = x, w.isAsyncMode = W2, w.isConcurrentMode = re2, w.isContextConsumer = V2, w.isContextProvider = q2, w.isElement = se2, w.isForwardRef = J2, w.isFragment = U2, w.isLazy = oe2, w.isMemo = he2, w.isPortal = ue2, w.isProfiler = be2, w.isStrictMode = Ce2, w.isSuspense = Oe2, w.isSuspenseList = ar2, w.isValidElementType = y, w.typeOf = E;
  })()), w;
}
i(eo, "requireReactIs_development");
var Wt;
function to() {
  return Wt || (Wt = 1, Ze.exports = eo()), Ze.exports;
}
i(to, "requireReactIs");
var sn = to();
var no = rn(sn);
var ro = Xt({
  __proto__: null,
  default: no
}, [sn]);
var oo = [
  "isAsyncMode",
  "isConcurrentMode",
  "isContextConsumer",
  "isContextProvider",
  "isElement",
  "isForwardRef",
  "isFragment",
  "isLazy",
  "isMemo",
  "isPortal",
  "isProfiler",
  "isStrictMode",
  "isSuspense",
  "isSuspenseList",
  "isValidElementType"
];
var fe = Object.fromEntries(oo.map((e) => [e, (t) => ro[e](t) || vr[e](t)]));
function cn(e, t = []) {
  if (Array.isArray(e))
    for (let n2 of e)
      cn(n2, t);
  else e != null && e !== false && e !== "" && t.push(e);
  return t;
}
i(cn, "getChildren");
function Vt(e) {
  let t = e.type;
  if (typeof t == "string")
    return t;
  if (typeof t == "function")
    return t.displayName || t.name || "Unknown";
  if (fe.isFragment(e))
    return "React.Fragment";
  if (fe.isSuspense(e))
    return "React.Suspense";
  if (typeof t == "object" && t !== null) {
    if (fe.isContextProvider(e))
      return "Context.Provider";
    if (fe.isContextConsumer(e))
      return "Context.Consumer";
    if (fe.isForwardRef(e)) {
      if (t.displayName)
        return t.displayName;
      let n2 = t.render.displayName || t.render.name || "";
      return n2 === "" ? "ForwardRef" : `ForwardRef(${n2})`;
    }
    if (fe.isMemo(e)) {
      let n2 = t.displayName || t.type.displayName || t.type.name || "";
      return n2 === "" ? "Memo" : `Memo(${n2})`;
    }
  }
  return "UNDEFINED";
}
i(Vt, "getType");
function so(e) {
  let { props: t } = e;
  return Object.keys(t).filter((n2) => n2 !== "children" && t[n2] !== void 0).sort();
}
i(so, "getPropKeys$1");
var io = i((e, t, n2, r, o, s) => ++r > t.maxDepth ? rt(Vt(e), t) : nt(Vt(e), et(so(e), e.props, t, n2 + t.indent, r, o, s), tt(
  cn(e.props.children),
  t,
  n2 + t.indent,
  r,
  o,
  s
), t, n2), "serialize$1");
var co = i((e) => e != null && fe.isElement(e), "test$1");
var uo = {
  serialize: io,
  test: co
};
var ao = typeof Symbol == "function" && Symbol.for ? Symbol.for("react.test.json") : 245830487;
function lo(e) {
  let { props: t } = e;
  return t ? Object.keys(t).filter((n2) => t[n2] !== void 0).sort() : [];
}
i(lo, "getPropKeys");
var fo = i((e, t, n2, r, o, s) => ++r > t.maxDepth ? rt(e.type, t) : nt(e.type, e.props ? et(
  lo(e),
  e.props,
  t,
  n2 + t.indent,
  r,
  o,
  s
) : "", e.children ? tt(e.children, t, n2 + t.indent, r, o, s) : "", t, n2), "serialize");
var mo = i((e) => e && e.$$typeof === ao, "test");
var po = {
  serialize: fo,
  test: mo
};
var un = Object.prototype.toString;
var go = Date.prototype.toISOString;
var ho = Error.prototype.toString;
var qt = RegExp.prototype.toString;
function Re(e) {
  return typeof e.constructor == "function" && e.constructor.name || "Object";
}
i(Re, "getConstructorName");
function yo(e) {
  return typeof window < "u" && e === window;
}
i(yo, "isWindow");
var bo = /^Symbol\((.*)\)(.*)$/;
var So = /\n/g;
var st = class st2 extends Error {
  constructor(t, n2) {
    super(t), this.stack = n2, this.name = this.constructor.name;
  }
};
i(st, "PrettyFormatPluginError");
var Pe = st;
function Eo(e) {
  return e === "[object Array]" || e === "[object ArrayBuffer]" || e === "[object DataView]" || e === "[object Float32Array]" || e === "[object Float64Array]" || e === "[object Int8Array]" || e === "[object Int16Array]" || e === "[object Int32Array]" || e === "[object Uint8Array]" || e === "[object Uint8ClampedArray]" || e === "[object Uint16Array]" || e === "[object Uint32Array]";
}
i(Eo, "isToStringedArrayType");
function _o(e) {
  return Object.is(e, -0) ? "-0" : String(e);
}
i(_o, "printNumber");
function To(e) {
  return `${e}n`;
}
i(To, "printBigInt");
function Kt(e, t) {
  return t ? `[Function ${e.name || "anonymous"}]` : "[Function]";
}
i(Kt, "printFunction");
function Gt(e) {
  return String(e).replace(bo, "Symbol($1)");
}
i(Gt, "printSymbol");
function Ht(e) {
  return `[${ho.call(e)}]`;
}
i(Ht, "printError");
function an(e, t, n2, r) {
  if (e === true || e === false)
    return `${e}`;
  if (e === void 0)
    return "undefined";
  if (e === null)
    return "null";
  let o = typeof e;
  if (o === "number")
    return _o(e);
  if (o === "bigint")
    return To(e);
  if (o === "string")
    return r ? `"${e.replaceAll(/"|\\/g, "\\$&")}"` : `"${e}"`;
  if (o === "function")
    return Kt(e, t);
  if (o === "symbol")
    return Gt(e);
  let s = un.call(e);
  return s === "[object WeakMap]" ? "WeakMap {}" : s === "[object WeakSet]" ? "WeakSet {}" : s === "[object Function]" || s === "[object GeneratorFunction]" ? Kt(e, t) : s === "[object Symbol]" ? Gt(e) : s === "[object Date]" ? Number.isNaN(+e) ? "Date { NaN }" : go.call(e) : s === "[object Error]" ? Ht(e) : s === "[object RegExp]" ? n2 ? qt.call(e).replaceAll(/[$()*+.?[\\\]^{|}]/g, "\\$&") : qt.call(e) : e instanceof Error ? Ht(e) : null;
}
i(an, "printBasicValue");
function ln(e, t, n2, r, o, s) {
  if (o.includes(e))
    return "[Circular]";
  o = [...o], o.push(e);
  let c = ++r > t.maxDepth, a = t.min;
  if (t.callToJSON && !c && e.toJSON && typeof e.toJSON == "function" && !s)
    return ae(e.toJSON(), t, n2, r, o, true);
  let u = un.call(e);
  return u === "[object Arguments]" ? c ? "[Arguments]" : `${a ? "" : "Arguments "}[${Ae(e, t, n2, r, o, ae)}]` : Eo(u) ? c ? `[${e.constructor.name}]` : `${a || !t.printBasicPrototype && e.constructor.name === "Array" ? "" : `${e.constructor.name} `}[${Ae(e, t, n2, r, o, ae)}]` : u === "[object Map]" ? c ? "[Map]" : `Map {${Ee(e.entries(), t, n2, r, o, ae, " => ")}}` : u === "[object Set]" ? c ? "[Set]" : `Set {${Qe(
    e.values(),
    t,
    n2,
    r,
    o,
    ae
  )}}` : c || yo(e) ? `[${Re(e)}]` : `${a || !t.printBasicPrototype && Re(e) === "Object" ? "" : `${Re(e)} `}{${ve(
    e,
    t,
    n2,
    r,
    o,
    ae
  )}}`;
}
i(ln, "printComplexValue");
var Co = {
  test: i((e) => e && e instanceof Error, "test"),
  serialize(e, t, n2, r, o, s) {
    if (o.includes(e))
      return "[Circular]";
    o = [...o, e];
    let c = ++r > t.maxDepth, { message: a, cause: u, ...m } = e, p = {
      message: a,
      ...typeof u < "u" ? { cause: u } : {},
      ...e instanceof AggregateError ? { errors: e.errors } : {},
      ...m
    }, l = e.name !== "Error" ? e.name : Re(e);
    return c ? `[${l}]` : `${l} {${Ee(Object.entries(p).values(), t, n2, r, o, s)}}`;
  }
};
function Oo(e) {
  return e.serialize != null;
}
i(Oo, "isNewPlugin");
function fn(e, t, n2, r, o, s) {
  let c;
  try {
    c = Oo(e) ? e.serialize(t, n2, r, o, s, ae) : e.print(t, (a) => ae(a, n2, r, o, s), (a) => {
      let u = r + n2.indent;
      return u + a.replaceAll(So, `
${u}`);
    }, {
      edgeSpacing: n2.spacingOuter,
      min: n2.min,
      spacing: n2.spacingInner
    }, n2.colors);
  } catch (a) {
    throw new Pe(a.message, a.stack);
  }
  if (typeof c != "string")
    throw new TypeError(`pretty-format: Plugin must return type "string" but instead returned "${typeof c}".`);
  return c;
}
i(fn, "printPlugin");
function mn(e, t) {
  for (let n2 of e)
    try {
      if (n2.test(t))
        return n2;
    } catch (r) {
      throw new Pe(r.message, r.stack);
    }
  return null;
}
i(mn, "findPlugin");
function ae(e, t, n2, r, o, s) {
  let c = mn(t.plugins, e);
  if (c !== null)
    return fn(c, e, t, n2, r, o);
  let a = an(e, t.printFunctionName, t.escapeRegex, t.escapeString);
  return a !== null ? a : ln(e, t, n2, r, o, s);
}
i(ae, "printer");
var ot = {
  comment: "gray",
  content: "reset",
  prop: "yellow",
  tag: "cyan",
  value: "green"
};
var pn = Object.keys(ot);
var ee = {
  callToJSON: true,
  compareKeys: void 0,
  escapeRegex: false,
  escapeString: true,
  highlight: false,
  indent: 2,
  maxDepth: Number.POSITIVE_INFINITY,
  maxWidth: Number.POSITIVE_INFINITY,
  min: false,
  plugins: [],
  printBasicPrototype: true,
  printFunctionName: true,
  theme: ot
};
function $o(e) {
  for (let t of Object.keys(e))
    if (!Object.prototype.hasOwnProperty.call(ee, t))
      throw new Error(`pretty-format: Unknown option "${t}".`);
  if (e.min && e.indent !== void 0 && e.indent !== 0)
    throw new Error('pretty-format: Options "min" and "indent" cannot be used together.');
}
i($o, "validateOptions");
function wo() {
  return pn.reduce((e, t) => {
    let n2 = ot[t], r = n2 && v[n2];
    if (r && typeof r.close == "string" && typeof r.open == "string")
      e[t] = r;
    else
      throw new Error(`pretty-format: Option "theme" has a key "${t}" whose value "${n2}" is undefined in ansi-styles.`);
    return e;
  }, /* @__PURE__ */ Object.create(null));
}
i(wo, "getColorsHighlight");
function Ro() {
  return pn.reduce((e, t) => (e[t] = {
    close: "",
    open: ""
  }, e), /* @__PURE__ */ Object.create(null));
}
i(Ro, "getColorsEmpty");
function gn(e) {
  return e?.printFunctionName ?? ee.printFunctionName;
}
i(gn, "getPrintFunctionName");
function hn(e) {
  return e?.escapeRegex ?? ee.escapeRegex;
}
i(hn, "getEscapeRegex");
function dn(e) {
  return e?.escapeString ?? ee.escapeString;
}
i(dn, "getEscapeString");
function Jt(e) {
  return {
    callToJSON: e?.callToJSON ?? ee.callToJSON,
    colors: e?.highlight ? wo() : Ro(),
    compareKeys: typeof e?.compareKeys == "function" || e?.compareKeys === null ? e.compareKeys : ee.compareKeys,
    escapeRegex: hn(e),
    escapeString: dn(e),
    indent: e?.min ? "" : Ao(e?.indent ?? ee.indent),
    maxDepth: e?.maxDepth ?? ee.maxDepth,
    maxWidth: e?.maxWidth ?? ee.maxWidth,
    min: e?.min ?? ee.min,
    plugins: e?.plugins ?? ee.plugins,
    printBasicPrototype: e?.printBasicPrototype ?? true,
    printFunctionName: gn(e),
    spacingInner: e?.min ? " " : `
`,
    spacingOuter: e?.min ? "" : `
`
  };
}
i(Jt, "getConfig");
function Ao(e) {
  return Array.from({ length: e + 1 }).join(" ");
}
i(Ao, "createIndent");
function X(e, t) {
  if (t && ($o(t), t.plugins)) {
    let r = mn(t.plugins, e);
    if (r !== null)
      return fn(r, e, Jt(t), "", 0, []);
  }
  let n2 = an(e, gn(t), hn(t), dn(t));
  return n2 !== null ? n2 : ln(e, Jt(t), "", 0, []);
}
i(X, "format");
var _e = {
  AsymmetricMatcher: yr,
  DOMCollection: Or,
  DOMElement: xr,
  Immutable: Jr,
  ReactElement: uo,
  ReactTestComponent: po,
  Error: Co
};
var yn = {
  bold: ["1", "22"],
  dim: ["2", "22"],
  italic: ["3", "23"],
  underline: ["4", "24"],
  // 5 & 6 are blinking
  inverse: ["7", "27"],
  hidden: ["8", "28"],
  strike: ["9", "29"],
  // 10-20 are fonts
  // 21-29 are resets for 1-9
  black: ["30", "39"],
  red: ["31", "39"],
  green: ["32", "39"],
  yellow: ["33", "39"],
  blue: ["34", "39"],
  magenta: ["35", "39"],
  cyan: ["36", "39"],
  white: ["37", "39"],
  brightblack: ["30;1", "39"],
  brightred: ["31;1", "39"],
  brightgreen: ["32;1", "39"],
  brightyellow: ["33;1", "39"],
  brightblue: ["34;1", "39"],
  brightmagenta: ["35;1", "39"],
  brightcyan: ["36;1", "39"],
  brightwhite: ["37;1", "39"],
  grey: ["90", "39"]
};
var Po = {
  special: "cyan",
  number: "yellow",
  bigint: "yellow",
  boolean: "yellow",
  undefined: "grey",
  null: "bold",
  string: "green",
  symbol: "green",
  date: "magenta",
  regexp: "red"
};
var ie = "…";
function No(e, t) {
  let n2 = yn[Po[t]] || yn[t] || "";
  return n2 ? `\x1B[${n2[0]}m${String(e)}\x1B[${n2[1]}m` : String(e);
}
i(No, "colorise");
function bn({
  showHidden: e = false,
  depth: t = 2,
  colors: n2 = false,
  customInspect: r = true,
  showProxy: o = false,
  maxArrayLength: s = 1 / 0,
  breakLength: c = 1 / 0,
  seen: a = [],
  // eslint-disable-next-line no-shadow
  truncate: u = 1 / 0,
  stylize: m = String
} = {}, p) {
  let l = {
    showHidden: !!e,
    depth: Number(t),
    colors: !!n2,
    customInspect: !!r,
    showProxy: !!o,
    maxArrayLength: Number(s),
    breakLength: Number(c),
    truncate: Number(u),
    seen: a,
    inspect: p,
    stylize: m
  };
  return l.colors && (l.stylize = No), l;
}
i(bn, "normaliseOptions");
function Io(e) {
  return e >= "\uD800" && e <= "\uDBFF";
}
i(Io, "isHighSurrogate");
function B(e, t, n2 = ie) {
  e = String(e);
  let r = n2.length, o = e.length;
  if (r > t && o > r)
    return n2;
  if (o > t && o > r) {
    let s = t - r;
    return s > 0 && Io(e[s - 1]) && (s = s - 1), `${e.slice(0, s)}${n2}`;
  }
  return e;
}
i(B, "truncate");
function D(e, t, n2, r = ", ") {
  n2 = n2 || t.inspect;
  let o = e.length;
  if (o === 0)
    return "";
  let s = t.truncate, c = "", a = "", u = "";
  for (let m = 0; m < o; m += 1) {
    let p = m + 1 === e.length, l = m + 2 === e.length;
    u = `${ie}(${e.length - m})`;
    let b2 = e[m];
    t.truncate = s - c.length - (p ? 0 : r.length);
    let g = a || n2(b2, t) + (p ? "" : r), h = c.length + g.length, f = h + u.length;
    if (p && h > s && c.length + u.length <= s || !p && !l && f > s || (a = p ? "" : n2(e[m + 1], t) + (l ? "" : r), !p && l && f > s && h + a.length > s))
      break;
    if (c += g, !p && !l && h + a.length >= s) {
      u = `${ie}(${e.length - m - 1})`;
      break;
    }
    u = "";
  }
  return `${c}${u}`;
}
i(D, "inspectList");
function Mo(e) {
  return e.match(/^[a-zA-Z_][a-zA-Z_0-9]*$/) ? e : JSON.stringify(e).replace(/'/g, "\\'").replace(/\\"/g, '"').replace(/(^"|"$)/g, "'");
}
i(Mo, "quoteComplexKey");
function ce([e, t], n2) {
  return n2.truncate -= 2, typeof e == "string" ? e = Mo(e) : typeof e != "number" && (e = `[${n2.inspect(e, n2)}]`), n2.truncate -= e.length, t = n2.inspect(t, n2), `${e}: ${t}`;
}
i(ce, "inspectProperty");
function it(e, t) {
  let n2 = Object.keys(e).slice(e.length);
  if (!e.length && !n2.length)
    return "[]";
  t.truncate -= 4;
  let r = D(e, t);
  t.truncate -= r.length;
  let o = "";
  return n2.length && (o = D(n2.map((s) => [s, e[s]]), t, ce)), `[ ${r}${o ? `, ${o}` : ""} ]`;
}
i(it, "inspectArray");
var Lo = i((e) => typeof Buffer == "function" && e instanceof Buffer ? "Buffer" : e[Symbol.toStringTag] ? e[Symbol.toStringTag] : e.constructor.name, "getArrayName");
function te(e, t) {
  let n2 = Lo(e);
  t.truncate -= n2.length + 4;
  let r = Object.keys(e).slice(e.length);
  if (!e.length && !r.length)
    return `${n2}[]`;
  let o = "";
  for (let c = 0; c < e.length; c++) {
    let a = `${t.stylize(B(e[c], t.truncate), "number")}${c === e.length - 1 ? "" : ", "}`;
    if (t.truncate -= a.length, e[c] !== e.length && t.truncate <= 3) {
      o += `${ie}(${e.length - e[c] + 1})`;
      break;
    }
    o += a;
  }
  let s = "";
  return r.length && (s = D(r.map((c) => [c, e[c]]), t, ce)), `${n2}[ ${o}${s ? `, ${s}` : ""} ]`;
}
i(te, "inspectTypedArray");
function ct(e, t) {
  let n2 = e.toJSON();
  if (n2 === null)
    return "Invalid Date";
  let r = n2.split("T"), o = r[0];
  return t.stylize(`${o}T${B(r[1], t.truncate - o.length - 1)}`, "date");
}
i(ct, "inspectDate");
function Ie(e, t) {
  let n2 = e[Symbol.toStringTag] || "Function", r = e.name;
  return r ? t.stylize(`[${n2} ${B(r, t.truncate - 11)}]`, "special") : t.stylize(`[${n2}]`, "special");
}
i(Ie, "inspectFunction");
function xo([e, t], n2) {
  return n2.truncate -= 4, e = n2.inspect(e, n2), n2.truncate -= e.length, t = n2.inspect(t, n2), `${e} => ${t}`;
}
i(xo, "inspectMapEntry");
function Do(e) {
  let t = [];
  return e.forEach((n2, r) => {
    t.push([r, n2]);
  }), t;
}
i(Do, "mapToEntries");
function ut(e, t) {
  return e.size === 0 ? "Map{}" : (t.truncate -= 7, `Map{ ${D(Do(e), t, xo)} }`);
}
i(ut, "inspectMap");
var Fo = Number.isNaN || ((e) => e !== e);
function Me(e, t) {
  return Fo(e) ? t.stylize("NaN", "number") : e === 1 / 0 ? t.stylize("Infinity", "number") : e === -1 / 0 ? t.stylize("-Infinity", "number") : e === 0 ? t.stylize(1 / e === 1 / 0 ? "+0" : "-0", "number") : t.stylize(B(String(e), t.truncate), "number");
}
i(Me, "inspectNumber");
function Le(e, t) {
  let n2 = B(e.toString(), t.truncate - 1);
  return n2 !== ie && (n2 += "n"), t.stylize(n2, "bigint");
}
i(Le, "inspectBigInt");
function at(e, t) {
  let n2 = e.toString().split("/")[2], r = t.truncate - (2 + n2.length), o = e.source;
  return t.stylize(`/${B(o, r)}/${n2}`, "regexp");
}
i(at, "inspectRegExp");
function jo(e) {
  let t = [];
  return e.forEach((n2) => {
    t.push(n2);
  }), t;
}
i(jo, "arrayFromSet");
function lt(e, t) {
  return e.size === 0 ? "Set{}" : (t.truncate -= 7, `Set{ ${D(jo(e), t)} }`);
}
i(lt, "inspectSet");
var Sn = new RegExp("['\\u0000-\\u001f\\u007f-\\u009f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]", "g");
var ko = {
  "\b": "\\b",
  "	": "\\t",
  "\n": "\\n",
  "\f": "\\f",
  "\r": "\\r",
  "'": "\\'",
  "\\": "\\\\"
};
var Bo = 16;
var zo = 4;
function Yo(e) {
  return ko[e] || `\\u${`0000${e.charCodeAt(0).toString(Bo)}`.slice(-zo)}`;
}
i(Yo, "escape");
function xe(e, t) {
  return Sn.test(e) && (e = e.replace(Sn, Yo)), t.stylize(`'${B(e, t.truncate - 2)}'`, "string");
}
i(xe, "inspectString");
function De(e) {
  return "description" in Symbol.prototype ? e.description ? `Symbol(${e.description})` : "Symbol()" : e.toString();
}
i(De, "inspectSymbol");
var En = i(() => "Promise{…}", "getPromiseValue");
try {
  let { getPromiseDetails: e, kPending: t, kRejected: n2 } = process.binding("util");
  Array.isArray(e(Promise.resolve())) && (En = i((r, o) => {
    let [s, c] = e(r);
    return s === t ? "Promise{<pending>}" : `Promise${s === n2 ? "!" : ""}{${o.inspect(c, o)}}`;
  }, "getPromiseValue"));
} catch {
}
var _n = En;
function me(e, t) {
  let n2 = Object.getOwnPropertyNames(e), r = Object.getOwnPropertySymbols ? Object.getOwnPropertySymbols(e) : [];
  if (n2.length === 0 && r.length === 0)
    return "{}";
  if (t.truncate -= 4, t.seen = t.seen || [], t.seen.includes(e))
    return "[Circular]";
  t.seen.push(e);
  let o = D(n2.map((a) => [a, e[a]]), t, ce), s = D(r.map((a) => [a, e[a]]), t, ce);
  t.seen.pop();
  let c = "";
  return o && s && (c = ", "), `{ ${o}${c}${s} }`;
}
i(me, "inspectObject");
var ft = typeof Symbol < "u" && Symbol.toStringTag ? Symbol.toStringTag : false;
function mt(e, t) {
  let n2 = "";
  return ft && ft in e && (n2 = e[ft]), n2 = n2 || e.constructor.name, (!n2 || n2 === "_class") && (n2 = "<Anonymous Class>"), t.truncate -= n2.length, `${n2}${me(e, t)}`;
}
i(mt, "inspectClass");
function pt(e, t) {
  return e.length === 0 ? "Arguments[]" : (t.truncate -= 13, `Arguments[ ${D(e, t)} ]`);
}
i(pt, "inspectArguments");
var Uo = [
  "stack",
  "line",
  "column",
  "name",
  "message",
  "fileName",
  "lineNumber",
  "columnNumber",
  "number",
  "description",
  "cause"
];
function gt(e, t) {
  let n2 = Object.getOwnPropertyNames(e).filter((c) => Uo.indexOf(c) === -1), r = e.name;
  t.truncate -= r.length;
  let o = "";
  if (typeof e.message == "string" ? o = B(e.message, t.truncate) : n2.unshift("message"), o = o ? `: ${o}` : "", t.truncate -= o.length + 5, t.seen = t.seen || [], t.seen.includes(e))
    return "[Circular]";
  t.seen.push(e);
  let s = D(n2.map((c) => [c, e[c]]), t, ce);
  return `${r}${o}${s ? ` { ${s} }` : ""}`;
}
i(gt, "inspectObject");
function Wo([e, t], n2) {
  return n2.truncate -= 3, t ? `${n2.stylize(String(e), "yellow")}=${n2.stylize(`"${t}"`, "string")}` : `${n2.stylize(String(e), "yellow")}`;
}
i(Wo, "inspectAttribute");
function Fe(e, t) {
  return D(e, t, Vo, `
`);
}
i(Fe, "inspectNodeCollection");
function Vo(e, t) {
  switch (e.nodeType) {
    case 1:
      return je(e, t);
    case 3:
      return t.inspect(e.data, t);
    default:
      return t.inspect(e, t);
  }
}
i(Vo, "inspectNode");
function je(e, t) {
  let n2 = e.getAttributeNames(), r = e.tagName.toLowerCase(), o = t.stylize(`<${r}`, "special"), s = t.stylize(">", "special"), c = t.stylize(
    `</${r}>`,
    "special"
  );
  t.truncate -= r.length * 2 + 5;
  let a = "";
  n2.length > 0 && (a += " ", a += D(n2.map((p) => [p, e.getAttribute(p)]), t, Wo, " ")), t.truncate -= a.length;
  let u = t.truncate, m = Fe(e.children, t);
  return m && m.length > u && (m = `${ie}(${e.children.length})`), `${o}${a}${s}${m}${c}`;
}
i(je, "inspectHTML");
var qo = typeof Symbol == "function" && typeof Symbol.for == "function";
var ht = qo ? Symbol.for("chai/inspect") : "@@chai/inspect";
var dt = Symbol.for("nodejs.util.inspect.custom");
var Tn = /* @__PURE__ */ new WeakMap();
var Cn = {};
var On = {
  undefined: i((e, t) => t.stylize("undefined", "undefined"), "undefined"),
  null: i((e, t) => t.stylize("null", "null"), "null"),
  boolean: i((e, t) => t.stylize(String(e), "boolean"), "boolean"),
  Boolean: i((e, t) => t.stylize(String(e), "boolean"), "Boolean"),
  number: Me,
  Number: Me,
  bigint: Le,
  BigInt: Le,
  string: xe,
  String: xe,
  function: Ie,
  Function: Ie,
  symbol: De,
  // A Symbol polyfill will return `Symbol` not `symbol` from typedetect
  Symbol: De,
  Array: it,
  Date: ct,
  Map: ut,
  Set: lt,
  RegExp: at,
  Promise: _n,
  // WeakSet, WeakMap are totally opaque to us
  WeakSet: i((e, t) => t.stylize("WeakSet{…}", "special"), "WeakSet"),
  WeakMap: i((e, t) => t.stylize("WeakMap{…}", "special"), "WeakMap"),
  Arguments: pt,
  Int8Array: te,
  Uint8Array: te,
  Uint8ClampedArray: te,
  Int16Array: te,
  Uint16Array: te,
  Int32Array: te,
  Uint32Array: te,
  Float32Array: te,
  Float64Array: te,
  Generator: i(() => "", "Generator"),
  DataView: i(() => "", "DataView"),
  ArrayBuffer: i(() => "", "ArrayBuffer"),
  Error: gt,
  HTMLCollection: Fe,
  NodeList: Fe
};
var Ko = i((e, t, n2) => ht in e && typeof e[ht] == "function" ? e[ht](t) : dt in e && typeof e[dt] == "function" ? e[dt](
  t.depth,
  t
) : "inspect" in e && typeof e.inspect == "function" ? e.inspect(t.depth, t) : "constructor" in e && Tn.has(e.constructor) ? Tn.get(e.constructor)(
  e,
  t
) : Cn[n2] ? Cn[n2](e, t) : "", "inspectCustom");
var Go = Object.prototype.toString;
function ke(e, t = {}) {
  let n2 = bn(t, ke), { customInspect: r } = n2, o = e === null ? "null" : typeof e;
  if (o === "object" && (o = Go.call(e).slice(8, -1)), o in On)
    return On[o](e, n2);
  if (r && e) {
    let c = Ko(e, n2, o);
    if (c)
      return typeof c == "string" ? c : ke(c, n2);
  }
  let s = e ? Object.getPrototypeOf(e) : false;
  return s === Object.prototype || s === null ? me(e, n2) : e && typeof HTMLElement == "function" && e instanceof HTMLElement ? je(e, n2) : "constructor" in e ? e.constructor !== Object ? mt(e, n2) : me(e, n2) : e === Object(e) ? me(e, n2) : n2.stylize(String(e), o);
}
i(ke, "inspect");
var { AsymmetricMatcher: Jo, DOMCollection: Xo, DOMElement: Zo, Immutable: Qo, ReactElement: vo, ReactTestComponent: es } = _e;
var $n = [
  es,
  vo,
  Zo,
  Xo,
  Qo,
  Jo
];
function pe(e, t = 10, { maxLength: n2, ...r } = {}) {
  let o = n2 ?? 1e4, s;
  try {
    s = X(e, {
      maxDepth: t,
      escapeString: false,
      plugins: $n,
      ...r
    });
  } catch {
    s = X(e, {
      callToJSON: false,
      maxDepth: t,
      escapeString: false,
      plugins: $n,
      ...r
    });
  }
  return s.length >= o && t > 1 ? pe(e, Math.floor(Math.min(t, Number.MAX_SAFE_INTEGER) / 2), {
    maxLength: n2,
    ...r
  }) : s;
}
i(pe, "stringify");
var ts = /%[sdjifoOc%]/g;
function wn(...e) {
  if (typeof e[0] != "string") {
    let s = [];
    for (let c = 0; c < e.length; c++)
      s.push(Te(e[c], {
        depth: 0,
        colors: false
      }));
    return s.join(" ");
  }
  let t = e.length, n2 = 1, r = e[0], o = String(r).replace(ts, (s) => {
    if (s === "%%")
      return "%";
    if (n2 >= t)
      return s;
    switch (s) {
      case "%s": {
        let c = e[n2++];
        return typeof c == "bigint" ? `${c.toString()}n` : typeof c == "number" && c === 0 && 1 / c < 0 ? "-0" : typeof c == "object" && c !== null ? typeof c.toString == "function" && c.toString !== Object.prototype.toString ? c.toString() : Te(c, {
          depth: 0,
          colors: false
        }) : String(c);
      }
      case "%d": {
        let c = e[n2++];
        return typeof c == "bigint" ? `${c.toString()}n` : Number(c).toString();
      }
      case "%i": {
        let c = e[n2++];
        return typeof c == "bigint" ? `${c.toString()}n` : Number.parseInt(String(c)).toString();
      }
      case "%f":
        return Number.parseFloat(String(e[n2++])).toString();
      case "%o":
        return Te(e[n2++], {
          showHidden: true,
          showProxy: true
        });
      case "%O":
        return Te(e[n2++]);
      case "%c":
        return n2++, "";
      case "%j":
        try {
          return JSON.stringify(e[n2++]);
        } catch (c) {
          let a = c.message;
          if (a.includes("circular structure") || a.includes("cyclic structures") || a.includes("cyclic object"))
            return "[Circular]";
          throw c;
        }
      default:
        return s;
    }
  });
  for (let s = e[n2]; n2 < t; s = e[++n2])
    s === null || typeof s != "object" ? o += ` ${s}` : o += ` ${Te(s)}`;
  return o;
}
i(wn, "format");
function Te(e, t = {}) {
  return t.truncate === 0 && (t.truncate = Number.POSITIVE_INFINITY), ke(e, t);
}
i(Te, "inspect");
function Rn(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
i(Rn, "getDefaultExportFromCjs");
function ns(e) {
  return e === Object.prototype || e === Function.prototype || e === RegExp.prototype;
}
i(ns, "isFinalObj");
function Be(e) {
  return Object.prototype.toString.apply(e).slice(8, -1);
}
i(Be, "getType");
function rs(e, t) {
  let n2 = typeof t == "function" ? t : (r) => t.add(r);
  Object.getOwnPropertyNames(e).forEach(n2), Object.getOwnPropertySymbols(e).forEach(n2);
}
i(rs, "collectOwnProperties");
function bt(e) {
  let t = /* @__PURE__ */ new Set();
  return ns(e) ? [] : (rs(e, t), Array.from(t));
}
i(bt, "getOwnProperties");
var An = { forceWritable: false };
function St(e, t = An) {
  return yt(e, /* @__PURE__ */ new WeakMap(), t);
}
i(St, "deepClone");
function yt(e, t, n2 = An) {
  let r, o;
  if (t.has(e))
    return t.get(e);
  if (Array.isArray(e)) {
    for (o = Array.from({ length: r = e.length }), t.set(e, o); r--; )
      o[r] = yt(e[r], t, n2);
    return o;
  }
  if (Object.prototype.toString.call(e) === "[object Object]") {
    o = Object.create(Object.getPrototypeOf(e)), t.set(e, o);
    let s = bt(e);
    for (let c of s) {
      let a = Object.getOwnPropertyDescriptor(e, c);
      if (!a)
        continue;
      let u = yt(e[c], t, n2);
      n2.forceWritable ? Object.defineProperty(o, c, {
        enumerable: a.enumerable,
        configurable: true,
        writable: true,
        value: u
      }) : "get" in a ? Object.defineProperty(o, c, {
        ...a,
        get() {
          return u;
        }
      }) : Object.defineProperty(o, c, {
        ...a,
        value: u
      });
    }
    return o;
  }
  return e;
}
i(yt, "clone");
var z = -1;
var j = 1;
var M = 0;
var At = class At2 {
  0;
  1;
  constructor(t, n2) {
    this[0] = t, this[1] = n2;
  }
};
i(At, "Diff");
var P = At;
function os(e, t) {
  if (!e || !t || e.charAt(0) !== t.charAt(0))
    return 0;
  let n2 = 0, r = Math.min(e.length, t.length), o = r, s = 0;
  for (; n2 < o; )
    e.substring(s, o) === t.substring(s, o) ? (n2 = o, s = n2) : r = o, o = Math.floor((r - n2) / 2 + n2);
  return o;
}
i(os, "diff_commonPrefix");
function Vn(e, t) {
  if (!e || !t || e.charAt(e.length - 1) !== t.charAt(t.length - 1))
    return 0;
  let n2 = 0, r = Math.min(e.length, t.length), o = r, s = 0;
  for (; n2 < o; )
    e.substring(e.length - o, e.length - s) === t.substring(t.length - o, t.length - s) ? (n2 = o, s = n2) : r = o, o = Math.floor((r - n2) / 2 + n2);
  return o;
}
i(Vn, "diff_commonSuffix");
function Pn(e, t) {
  let n2 = e.length, r = t.length;
  if (n2 === 0 || r === 0)
    return 0;
  n2 > r ? e = e.substring(n2 - r) : n2 < r && (t = t.substring(0, n2));
  let o = Math.min(n2, r);
  if (e === t)
    return o;
  let s = 0, c = 1;
  for (; ; ) {
    let a = e.substring(o - c), u = t.indexOf(a);
    if (u === -1)
      return s;
    c += u, (u === 0 || e.substring(o - c) === t.substring(0, c)) && (s = c, c++);
  }
}
i(Pn, "diff_commonOverlap_");
function ss(e) {
  let t = false, n2 = [], r = 0, o = null, s = 0, c = 0, a = 0, u = 0, m = 0;
  for (; s < e.length; )
    e[s][0] === M ? (n2[r++] = s, c = u, a = m, u = 0, m = 0, o = e[s][1]) : (e[s][0] === j ? u += e[s][1].length : m += e[s][1].length, o && o.length <= Math.max(c, a) && o.length <= Math.max(u, m) && (e.splice(n2[r - 1], 0, new P(z, o)), e[n2[r - 1] + 1][0] = j, r--, r--, s = r > 0 ? n2[r - 1] : -1, c = 0, a = 0, u = 0, m = 0, o = null, t = true)), s++;
  for (t && qn(e), us(e), s = 1; s < e.length; ) {
    if (e[s - 1][0] === z && e[s][0] === j) {
      let p = e[s - 1][1], l = e[s][1], b2 = Pn(p, l), g = Pn(l, p);
      b2 >= g ? (b2 >= p.length / 2 || b2 >= l.length / 2) && (e.splice(s, 0, new P(M, l.substring(0, b2))), e[s - 1][1] = p.substring(0, p.length - b2), e[s + 1][1] = l.substring(b2), s++) : (g >= p.length / 2 || g >= l.length / 2) && (e.splice(s, 0, new P(M, p.substring(0, g))), e[s - 1][0] = j, e[s - 1][1] = l.substring(0, l.length - g), e[s + 1][0] = z, e[s + 1][1] = p.substring(g), s++), s++;
    }
    s++;
  }
}
i(ss, "diff_cleanupSemantic");
var Nn = /[^a-z0-9]/i;
var In = /\s/;
var Mn = /[\r\n]/;
var is = /\n\r?\n$/;
var cs = /^\r?\n\r?\n/;
function us(e) {
  let t = 1;
  for (; t < e.length - 1; ) {
    if (e[t - 1][0] === M && e[t + 1][0] === M) {
      let n2 = e[t - 1][1], r = e[t][1], o = e[t + 1][1], s = Vn(n2, r);
      if (s) {
        let p = r.substring(r.length - s);
        n2 = n2.substring(0, n2.length - s), r = p + r.substring(0, r.length - s), o = p + o;
      }
      let c = n2, a = r, u = o, m = ze(n2, r) + ze(r, o);
      for (; r.charAt(0) === o.charAt(0); ) {
        n2 += r.charAt(0), r = r.substring(1) + o.charAt(0), o = o.substring(1);
        let p = ze(n2, r) + ze(r, o);
        p >= m && (m = p, c = n2, a = r, u = o);
      }
      e[t - 1][1] !== c && (c ? e[t - 1][1] = c : (e.splice(t - 1, 1), t--), e[t][1] = a, u ? e[t + 1][1] = u : (e.splice(t + 1, 1), t--));
    }
    t++;
  }
}
i(us, "diff_cleanupSemanticLossless");
function qn(e) {
  e.push(new P(M, ""));
  let t = 0, n2 = 0, r = 0, o = "", s = "", c;
  for (; t < e.length; )
    switch (e[t][0]) {
      case j:
        r++, s += e[t][1], t++;
        break;
      case z:
        n2++, o += e[t][1], t++;
        break;
      case M:
        n2 + r > 1 ? (n2 !== 0 && r !== 0 && (c = os(s, o), c !== 0 && (t - n2 - r > 0 && e[t - n2 - r - 1][0] === M ? e[t - n2 - r - 1][1] += s.substring(0, c) : (e.splice(0, 0, new P(M, s.substring(0, c))), t++), s = s.substring(c), o = o.substring(c)), c = Vn(s, o), c !== 0 && (e[t][1] = s.substring(s.length - c) + e[t][1], s = s.substring(0, s.length - c), o = o.substring(0, o.length - c))), t -= n2 + r, e.splice(t, n2 + r), o.length && (e.splice(t, 0, new P(z, o)), t++), s.length && (e.splice(t, 0, new P(j, s)), t++), t++) : t !== 0 && e[t - 1][0] === M ? (e[t - 1][1] += e[t][1], e.splice(t, 1)) : t++, r = 0, n2 = 0, o = "", s = "";
        break;
    }
  e[e.length - 1][1] === "" && e.pop();
  let a = false;
  for (t = 1; t < e.length - 1; )
    e[t - 1][0] === M && e[t + 1][0] === M && (e[t][1].substring(e[t][1].length - e[t - 1][1].length) === e[t - 1][1] ? (e[t][1] = e[t - 1][1] + e[t][1].substring(0, e[t][1].length - e[t - 1][1].length), e[t + 1][1] = e[t - 1][1] + e[t + 1][1], e.splice(t - 1, 1), a = true) : e[t][1].substring(0, e[t + 1][1].length) === e[t + 1][1] && (e[t - 1][1] += e[t + 1][1], e[t][1] = e[t][1].substring(e[t + 1][1].length) + e[t + 1][1], e.splice(t + 1, 1), a = true)), t++;
  a && qn(e);
}
i(qn, "diff_cleanupMerge");
function ze(e, t) {
  if (!e || !t)
    return 6;
  let n2 = e.charAt(e.length - 1), r = t.charAt(0), o = n2.match(Nn), s = r.match(Nn), c = o && n2.match(In), a = s && r.match(In), u = c && n2.match(Mn), m = a && r.match(Mn), p = u && e.match(is), l = m && t.match(cs);
  return p || l ? 5 : u || m ? 4 : o && !c && a ? 3 : c || a ? 2 : o || s ? 1 : 0;
}
i(ze, "diff_cleanupSemanticScore_");
var Kn = "Compared values have no visual difference.";
var as = "Compared values serialize to the same structure.\nPrinting internal object structure without calling `toJSON` instead.";
var Ye = {};
var Ln;
function ls() {
  if (Ln) return Ye;
  Ln = 1, Object.defineProperty(Ye, "__esModule", {
    value: true
  }), Ye.default = b2;
  let e = "diff-sequences", t = 0, n2 = i((g, h, f, d, S) => {
    let _2 = 0;
    for (; g < h && f < d && S(g, f); )
      g += 1, f += 1, _2 += 1;
    return _2;
  }, "countCommonItemsF"), r = i((g, h, f, d, S) => {
    let _2 = 0;
    for (; g <= h && f <= d && S(h, d); )
      h -= 1, d -= 1, _2 += 1;
    return _2;
  }, "countCommonItemsR"), o = i((g, h, f, d, S, _2, O) => {
    let y = 0, E = -g, $2 = _2[y], T = $2;
    _2[y] += n2(
      $2 + 1,
      h,
      d + $2 - E + 1,
      f,
      S
    );
    let R = g < O ? g : O;
    for (y += 1, E += 2; y <= R; y += 1, E += 2) {
      if (y !== g && T < _2[y])
        $2 = _2[y];
      else if ($2 = T + 1, h <= $2)
        return y - 1;
      T = _2[y], _2[y] = $2 + n2($2 + 1, h, d + $2 - E + 1, f, S);
    }
    return O;
  }, "extendPathsF"), s = i((g, h, f, d, S, _2, O) => {
    let y = 0, E = g, $2 = _2[y], T = $2;
    _2[y] -= r(
      h,
      $2 - 1,
      f,
      d + $2 - E - 1,
      S
    );
    let R = g < O ? g : O;
    for (y += 1, E -= 2; y <= R; y += 1, E -= 2) {
      if (y !== g && _2[y] < T)
        $2 = _2[y];
      else if ($2 = T - 1, $2 < h)
        return y - 1;
      T = _2[y], _2[y] = $2 - r(
        h,
        $2 - 1,
        f,
        d + $2 - E - 1,
        S
      );
    }
    return O;
  }, "extendPathsR"), c = i((g, h, f, d, S, _2, O, y, E, $2, T) => {
    let R = d - h, K2 = f - h, I = S - d - K2, k = -I - (g - 1), G2 = -I + (g - 1), Y2 = t, N2 = g < y ? g : y;
    for (let L = 0, x = -g; L <= N2; L += 1, x += 2) {
      let H = L === 0 || L !== g && Y2 < O[L], F = H ? O[L] : Y2, W2 = H ? F : F + 1, re2 = R + W2 - x, V2 = n2(
        W2 + 1,
        f,
        re2 + 1,
        S,
        _2
      ), q2 = W2 + V2;
      if (Y2 = O[L], O[L] = q2, k <= x && x <= G2) {
        let se2 = (g - 1 - (x + I)) / 2;
        if (se2 <= $2 && E[se2] - 1 <= q2) {
          let J2 = R + F - (H ? x + 1 : x - 1), U2 = r(
            h,
            F,
            d,
            J2,
            _2
          ), oe2 = F - U2, he2 = J2 - U2, ue2 = oe2 + 1, be2 = he2 + 1;
          T.nChangePreceding = g - 1, g - 1 === ue2 + be2 - h - d ? (T.aEndPreceding = h, T.bEndPreceding = d) : (T.aEndPreceding = ue2, T.bEndPreceding = be2), T.nCommonPreceding = U2, U2 !== 0 && (T.aCommonPreceding = ue2, T.bCommonPreceding = be2), T.nCommonFollowing = V2, V2 !== 0 && (T.aCommonFollowing = W2 + 1, T.bCommonFollowing = re2 + 1);
          let Ce2 = q2 + 1, Oe2 = re2 + V2 + 1;
          return T.nChangeFollowing = g - 1, g - 1 === f + S - Ce2 - Oe2 ? (T.aStartFollowing = f, T.bStartFollowing = S) : (T.aStartFollowing = Ce2, T.bStartFollowing = Oe2), true;
        }
      }
    }
    return false;
  }, "extendOverlappablePathsF"), a = i((g, h, f, d, S, _2, O, y, E, $2, T) => {
    let R = S - f, K2 = f - h, I = S - d - K2, k = I - g, G2 = I + g, Y2 = t, N2 = g < $2 ? g : $2;
    for (let L = 0, x = g; L <= N2; L += 1, x -= 2) {
      let H = L === 0 || L !== g && E[L] < Y2, F = H ? E[L] : Y2, W2 = H ? F : F - 1, re2 = R + W2 - x, V2 = r(
        h,
        W2 - 1,
        d,
        re2 - 1,
        _2
      ), q2 = W2 - V2;
      if (Y2 = E[L], E[L] = q2, k <= x && x <= G2) {
        let se2 = (g + (x - I)) / 2;
        if (se2 <= y && q2 - 1 <= O[se2]) {
          let J2 = re2 - V2;
          if (T.nChangePreceding = g, g === q2 + J2 - h - d ? (T.aEndPreceding = h, T.bEndPreceding = d) : (T.aEndPreceding = q2, T.bEndPreceding = J2), T.nCommonPreceding = V2, V2 !== 0 && (T.aCommonPreceding = q2, T.bCommonPreceding = J2), T.nChangeFollowing = g - 1, g === 1)
            T.nCommonFollowing = 0, T.aStartFollowing = f, T.bStartFollowing = S;
          else {
            let U2 = R + F - (H ? x - 1 : x + 1), oe2 = n2(
              F,
              f,
              U2,
              S,
              _2
            );
            T.nCommonFollowing = oe2, oe2 !== 0 && (T.aCommonFollowing = F, T.bCommonFollowing = U2);
            let he2 = F + oe2, ue2 = U2 + oe2;
            g - 1 === f + S - he2 - ue2 ? (T.aStartFollowing = f, T.bStartFollowing = S) : (T.aStartFollowing = he2, T.bStartFollowing = ue2);
          }
          return true;
        }
      }
    }
    return false;
  }, "extendOverlappablePathsR"), u = i((g, h, f, d, S, _2, O, y, E) => {
    let $2 = d - h, T = S - f, R = f - h, K2 = S - d, Q2 = K2 - R, I = R, k = R;
    if (O[0] = h - 1, y[0] = f, Q2 % 2 === 0) {
      let G2 = (g || Q2) / 2, Y2 = (R + K2) / 2;
      for (let N2 = 1; N2 <= Y2; N2 += 1)
        if (I = o(N2, f, S, $2, _2, O, I), N2 < G2)
          k = s(N2, h, d, T, _2, y, k);
        else if (
          // If a reverse path overlaps a forward path in the same diagonal,
          // return a division of the index intervals at the middle change.
          a(
            N2,
            h,
            f,
            d,
            S,
            _2,
            O,
            I,
            y,
            k,
            E
          )
        )
          return;
    } else {
      let G2 = ((g || Q2) + 1) / 2, Y2 = (R + K2 + 1) / 2, N2 = 1;
      for (I = o(N2, f, S, $2, _2, O, I), N2 += 1; N2 <= Y2; N2 += 1)
        if (k = s(
          N2 - 1,
          h,
          d,
          T,
          _2,
          y,
          k
        ), N2 < G2)
          I = o(N2, f, S, $2, _2, O, I);
        else if (
          // If a forward path overlaps a reverse path in the same diagonal,
          // return a division of the index intervals at the middle change.
          c(
            N2,
            h,
            f,
            d,
            S,
            _2,
            O,
            I,
            y,
            k,
            E
          )
        )
          return;
    }
    throw new Error(
      `${e}: no overlap aStart=${h} aEnd=${f} bStart=${d} bEnd=${S}`
    );
  }, "divide"), m = i((g, h, f, d, S, _2, O, y, E, $2) => {
    if (S - d < f - h) {
      if (_2 = !_2, _2 && O.length === 1) {
        let { foundSubsequence: q2, isCommon: se2 } = O[0];
        O[1] = {
          foundSubsequence: i((J2, U2, oe2) => {
            q2(J2, oe2, U2);
          }, "foundSubsequence"),
          isCommon: i((J2, U2) => se2(U2, J2), "isCommon")
        };
      }
      let re2 = h, V2 = f;
      h = d, f = S, d = re2, S = V2;
    }
    let { foundSubsequence: T, isCommon: R } = O[_2 ? 1 : 0];
    u(
      g,
      h,
      f,
      d,
      S,
      R,
      y,
      E,
      $2
    );
    let {
      nChangePreceding: K2,
      aEndPreceding: Q2,
      bEndPreceding: I,
      nCommonPreceding: k,
      aCommonPreceding: G2,
      bCommonPreceding: Y2,
      nCommonFollowing: N2,
      aCommonFollowing: L,
      bCommonFollowing: x,
      nChangeFollowing: H,
      aStartFollowing: F,
      bStartFollowing: W2
    } = $2;
    h < Q2 && d < I && m(
      K2,
      h,
      Q2,
      d,
      I,
      _2,
      O,
      y,
      E,
      $2
    ), k !== 0 && T(k, G2, Y2), N2 !== 0 && T(N2, L, x), F < f && W2 < S && m(
      H,
      F,
      f,
      W2,
      S,
      _2,
      O,
      y,
      E,
      $2
    );
  }, "findSubsequences"), p = i((g, h) => {
    if (typeof h != "number")
      throw new TypeError(`${e}: ${g} typeof ${typeof h} is not a number`);
    if (!Number.isSafeInteger(h))
      throw new RangeError(`${e}: ${g} value ${h} is not a safe integer`);
    if (h < 0)
      throw new RangeError(`${e}: ${g} value ${h} is a negative integer`);
  }, "validateLength"), l = i((g, h) => {
    let f = typeof h;
    if (f !== "function")
      throw new TypeError(`${e}: ${g} typeof ${f} is not a function`);
  }, "validateCallback");
  function b2(g, h, f, d) {
    p("aLength", g), p("bLength", h), l("isCommon", f), l("foundSubsequence", d);
    let S = n2(0, g, 0, h, f);
    if (S !== 0 && d(S, 0, 0), g !== S || h !== S) {
      let _2 = S, O = S, y = r(
        _2,
        g - 1,
        O,
        h - 1,
        f
      ), E = g - y, $2 = h - y, T = S + y;
      g !== T && h !== T && m(
        0,
        _2,
        E,
        O,
        $2,
        false,
        [
          {
            foundSubsequence: d,
            isCommon: f
          }
        ],
        [t],
        [t],
        {
          aCommonFollowing: t,
          aCommonPreceding: t,
          aEndPreceding: t,
          aStartFollowing: t,
          bCommonFollowing: t,
          bCommonPreceding: t,
          bEndPreceding: t,
          bStartFollowing: t,
          nChangeFollowing: t,
          nChangePreceding: t,
          nCommonFollowing: t,
          nCommonPreceding: t
        }
      ), y !== 0 && d(y, E, $2);
    }
  }
  return i(b2, "diffSequence"), Ye;
}
i(ls, "requireBuild");
var fs = ls();
var Gn = Rn(fs);
function ms(e, t) {
  return e.replace(/\s+$/, (n2) => t(n2));
}
i(ms, "formatTrailingSpaces");
function wt(e, t, n2, r, o, s) {
  return e.length !== 0 ? n2(`${r} ${ms(e, o)}`) : r !== " " ? n2(r) : t && s.length !== 0 ? n2(`${r} ${s}`) : "";
}
i(wt, "printDiffLine");
function Hn(e, t, { aColor: n2, aIndicator: r, changeLineTrailingSpaceColor: o, emptyFirstOrLastLinePlaceholder: s }) {
  return wt(e, t, n2, r, o, s);
}
i(Hn, "printDeleteLine");
function Jn(e, t, { bColor: n2, bIndicator: r, changeLineTrailingSpaceColor: o, emptyFirstOrLastLinePlaceholder: s }) {
  return wt(e, t, n2, r, o, s);
}
i(Jn, "printInsertLine");
function Xn(e, t, { commonColor: n2, commonIndicator: r, commonLineTrailingSpaceColor: o, emptyFirstOrLastLinePlaceholder: s }) {
  return wt(e, t, n2, r, o, s);
}
i(Xn, "printCommonLine");
function xn(e, t, n2, r, { patchColor: o }) {
  return o(`@@ -${e + 1},${t - e} +${n2 + 1},${r - n2} @@`);
}
i(xn, "createPatchMark");
function ps(e, t) {
  let n2 = e.length, r = t.contextLines, o = r + r, s = n2, c = false, a = 0, u = 0;
  for (; u !== n2; ) {
    let y = u;
    for (; u !== n2 && e[u][0] === M; )
      u += 1;
    if (y !== u)
      if (y === 0)
        u > r && (s -= u - r, c = true);
      else if (u === n2) {
        let E = u - y;
        E > r && (s -= E - r, c = true);
      } else {
        let E = u - y;
        E > o && (s -= E - o, a += 1);
      }
    for (; u !== n2 && e[u][0] !== M; )
      u += 1;
  }
  let m = a !== 0 || c;
  a !== 0 ? s += a + 1 : c && (s += 1);
  let p = s - 1, l = [], b2 = 0;
  m && l.push("");
  let g = 0, h = 0, f = 0, d = 0, S = i((y) => {
    let E = l.length;
    l.push(Xn(y, E === 0 || E === p, t)), f += 1, d += 1;
  }, "pushCommonLine"), _2 = i((y) => {
    let E = l.length;
    l.push(Hn(y, E === 0 || E === p, t)), f += 1;
  }, "pushDeleteLine"), O = i((y) => {
    let E = l.length;
    l.push(Jn(y, E === 0 || E === p, t)), d += 1;
  }, "pushInsertLine");
  for (u = 0; u !== n2; ) {
    let y = u;
    for (; u !== n2 && e[u][0] === M; )
      u += 1;
    if (y !== u)
      if (y === 0) {
        u > r && (y = u - r, g = y, h = y, f = g, d = h);
        for (let E = y; E !== u; E += 1)
          S(e[E][1]);
      } else if (u === n2) {
        let E = u - y > r ? y + r : u;
        for (let $2 = y; $2 !== E; $2 += 1)
          S(e[$2][1]);
      } else {
        let E = u - y;
        if (E > o) {
          let $2 = y + r;
          for (let R = y; R !== $2; R += 1)
            S(e[R][1]);
          l[b2] = xn(g, f, h, d, t), b2 = l.length, l.push("");
          let T = E - o;
          g = f + T, h = d + T, f = g, d = h;
          for (let R = u - r; R !== u; R += 1)
            S(e[R][1]);
        } else
          for (let $2 = y; $2 !== u; $2 += 1)
            S(e[$2][1]);
      }
    for (; u !== n2 && e[u][0] === z; )
      _2(e[u][1]), u += 1;
    for (; u !== n2 && e[u][0] === j; )
      O(e[u][1]), u += 1;
  }
  return m && (l[b2] = xn(g, f, h, d, t)), l.join(`
`);
}
i(ps, "joinAlignedDiffsNoExpand");
function gs(e, t) {
  return e.map((n2, r, o) => {
    let s = n2[1], c = r === 0 || r === o.length - 1;
    switch (n2[0]) {
      case z:
        return Hn(s, c, t);
      case j:
        return Jn(s, c, t);
      default:
        return Xn(s, c, t);
    }
  }).join(`
`);
}
i(gs, "joinAlignedDiffsExpand");
var Et = i((e) => e, "noColor");
var Zn = 5;
var hs = 0;
function ds() {
  return {
    aAnnotation: "Expected",
    aColor: v.green,
    aIndicator: "-",
    bAnnotation: "Received",
    bColor: v.red,
    bIndicator: "+",
    changeColor: v.inverse,
    changeLineTrailingSpaceColor: Et,
    commonColor: v.dim,
    commonIndicator: " ",
    commonLineTrailingSpaceColor: Et,
    compareKeys: void 0,
    contextLines: Zn,
    emptyFirstOrLastLinePlaceholder: "",
    expand: false,
    includeChangeCounts: false,
    omitAnnotationLines: false,
    patchColor: v.yellow,
    printBasicPrototype: false,
    truncateThreshold: hs,
    truncateAnnotation: "... Diff result is truncated",
    truncateAnnotationColor: Et
  };
}
i(ds, "getDefaultOptions");
function ys(e) {
  return e && typeof e == "function" ? e : void 0;
}
i(ys, "getCompareKeys");
function bs(e) {
  return typeof e == "number" && Number.isSafeInteger(e) && e >= 0 ? e : Zn;
}
i(bs, "getContextLines");
function ge(e = {}) {
  return {
    ...ds(),
    ...e,
    compareKeys: ys(e.compareKeys),
    contextLines: bs(e.contextLines)
  };
}
i(ge, "normalizeDiffOptions");
function ye(e) {
  return e.length === 1 && e[0].length === 0;
}
i(ye, "isEmptyString");
function Ss(e) {
  let t = 0, n2 = 0;
  return e.forEach((r) => {
    switch (r[0]) {
      case z:
        t += 1;
        break;
      case j:
        n2 += 1;
        break;
    }
  }), {
    a: t,
    b: n2
  };
}
i(Ss, "countChanges");
function Es({ aAnnotation: e, aColor: t, aIndicator: n2, bAnnotation: r, bColor: o, bIndicator: s, includeChangeCounts: c, omitAnnotationLines: a }, u) {
  if (a)
    return "";
  let m = "", p = "";
  if (c) {
    let g = String(u.a), h = String(u.b), f = r.length - e.length, d = " ".repeat(Math.max(0, f)), S = " ".repeat(Math.max(0, -f)), _2 = h.length - g.length, O = " ".repeat(Math.max(0, _2)), y = " ".repeat(Math.max(0, -_2));
    m = `${d}  ${n2} ${O}${g}`, p = `${S}  ${s} ${y}${h}`;
  }
  let l = `${n2} ${e}${m}`, b2 = `${s} ${r}${p}`;
  return `${t(l)}
${o(b2)}

`;
}
i(Es, "printAnnotation");
function Rt(e, t, n2) {
  return Es(n2, Ss(e)) + (n2.expand ? gs(e, n2) : ps(e, n2)) + (t ? n2.truncateAnnotationColor(`
${n2.truncateAnnotation}`) : "");
}
i(Rt, "printDiffLines");
function We(e, t, n2) {
  let r = ge(n2), [o, s] = Qn(ye(e) ? [] : e, ye(t) ? [] : t, r);
  return Rt(o, s, r);
}
i(We, "diffLinesUnified");
function _s(e, t, n2, r, o) {
  if (ye(e) && ye(n2) && (e = [], n2 = []), ye(t) && ye(r) && (t = [], r = []), e.length !== n2.length || t.length !== r.length)
    return We(e, t, o);
  let [s, c] = Qn(n2, r, o), a = 0, u = 0;
  return s.forEach((m) => {
    switch (m[0]) {
      case z:
        m[1] = e[a], a += 1;
        break;
      case j:
        m[1] = t[u], u += 1;
        break;
      default:
        m[1] = t[u], a += 1, u += 1;
    }
  }), Rt(s, c, ge(o));
}
i(_s, "diffLinesUnified2");
function Qn(e, t, n2) {
  let r = n2?.truncateThreshold ?? false, o = Math.max(Math.floor(n2?.truncateThreshold ?? 0), 0), s = r ? Math.min(e.length, o) : e.length, c = r ? Math.min(t.length, o) : t.length, a = s !== e.length || c !== t.length, u = i((g, h) => e[g] === t[h], "isCommon"), m = [], p = 0, l = 0;
  for (Gn(s, c, u, i((g, h, f) => {
    for (; p !== h; p += 1)
      m.push(new P(z, e[p]));
    for (; l !== f; l += 1)
      m.push(new P(j, t[l]));
    for (; g !== 0; g -= 1, p += 1, l += 1)
      m.push(new P(M, t[l]));
  }, "foundSubsequence")); p !== s; p += 1)
    m.push(new P(z, e[p]));
  for (; l !== c; l += 1)
    m.push(new P(j, t[l]));
  return [m, a];
}
i(Qn, "diffLinesRaw");
function Dn(e) {
  if (e === void 0)
    return "undefined";
  if (e === null)
    return "null";
  if (Array.isArray(e))
    return "array";
  if (typeof e == "boolean")
    return "boolean";
  if (typeof e == "function")
    return "function";
  if (typeof e == "number")
    return "number";
  if (typeof e == "string")
    return "string";
  if (typeof e == "bigint")
    return "bigint";
  if (typeof e == "object") {
    if (e != null) {
      if (e.constructor === RegExp)
        return "regexp";
      if (e.constructor === Map)
        return "map";
      if (e.constructor === Set)
        return "set";
      if (e.constructor === Date)
        return "date";
    }
    return "object";
  } else if (typeof e == "symbol")
    return "symbol";
  throw new Error(`value of unknown type: ${e}`);
}
i(Dn, "getType");
function Fn(e) {
  return e.includes(`\r
`) ? `\r
` : `
`;
}
i(Fn, "getNewLineSymbol");
function Ts(e, t, n2) {
  let r = n2?.truncateThreshold ?? false, o = Math.max(Math.floor(n2?.truncateThreshold ?? 0), 0), s = e.length, c = t.length;
  if (r) {
    let g = e.includes(`
`), h = t.includes(`
`), f = Fn(e), d = Fn(t), S = g ? `${e.split(f, o).join(f)}
` : e, _2 = h ? `${t.split(d, o).join(d)}
` : t;
    s = S.length, c = _2.length;
  }
  let a = s !== e.length || c !== t.length, u = i((g, h) => e[g] === t[h], "isCommon"), m = 0, p = 0, l = [];
  return Gn(s, c, u, i((g, h, f) => {
    m !== h && l.push(new P(z, e.slice(m, h))), p !== f && l.push(new P(j, t.slice(p, f))), m = h + g, p = f + g, l.push(new P(M, t.slice(
      f,
      p
    )));
  }, "foundSubsequence")), m !== s && l.push(new P(z, e.slice(m))), p !== c && l.push(new P(j, t.slice(p))), [l, a];
}
i(Ts, "diffStrings");
function Cs(e, t, n2) {
  return t.reduce((r, o) => r + (o[0] === M ? o[1] : o[0] === e && o[1].length !== 0 ? n2(o[1]) : ""), "");
}
i(Cs, "concatenateRelevantDiffs");
var Pt = class Pt2 {
  op;
  line;
  lines;
  changeColor;
  constructor(t, n2) {
    this.op = t, this.line = [], this.lines = [], this.changeColor = n2;
  }
  pushSubstring(t) {
    this.pushDiff(new P(this.op, t));
  }
  pushLine() {
    this.lines.push(this.line.length !== 1 ? new P(this.op, Cs(this.op, this.line, this.changeColor)) : this.line[0][0] === this.op ? this.line[0] : new P(this.op, this.line[0][1])), this.line.length = 0;
  }
  isLineEmpty() {
    return this.line.length === 0;
  }
  // Minor input to buffer.
  pushDiff(t) {
    this.line.push(t);
  }
  // Main input to buffer.
  align(t) {
    let n2 = t[1];
    if (n2.includes(`
`)) {
      let r = n2.split(`
`), o = r.length - 1;
      r.forEach((s, c) => {
        c < o ? (this.pushSubstring(s), this.pushLine()) : s.length !== 0 && this.pushSubstring(s);
      });
    } else
      this.pushDiff(t);
  }
  // Output from buffer.
  moveLinesTo(t) {
    this.isLineEmpty() || this.pushLine(), t.push(...this.lines), this.lines.length = 0;
  }
};
i(Pt, "ChangeBuffer");
var Ue = Pt;
var Nt = class Nt2 {
  deleteBuffer;
  insertBuffer;
  lines;
  constructor(t, n2) {
    this.deleteBuffer = t, this.insertBuffer = n2, this.lines = [];
  }
  pushDiffCommonLine(t) {
    this.lines.push(t);
  }
  pushDiffChangeLines(t) {
    let n2 = t[1].length === 0;
    (!n2 || this.deleteBuffer.isLineEmpty()) && this.deleteBuffer.pushDiff(t), (!n2 || this.insertBuffer.isLineEmpty()) && this.insertBuffer.pushDiff(
      t
    );
  }
  flushChangeLines() {
    this.deleteBuffer.moveLinesTo(this.lines), this.insertBuffer.moveLinesTo(this.lines);
  }
  // Input to buffer.
  align(t) {
    let n2 = t[0], r = t[1];
    if (r.includes(`
`)) {
      let o = r.split(`
`), s = o.length - 1;
      o.forEach((c, a) => {
        if (a === 0) {
          let u = new P(n2, c);
          this.deleteBuffer.isLineEmpty() && this.insertBuffer.isLineEmpty() ? (this.flushChangeLines(), this.pushDiffCommonLine(u)) : (this.pushDiffChangeLines(u), this.flushChangeLines());
        } else a < s ? this.pushDiffCommonLine(new P(n2, c)) : c.length !== 0 && this.pushDiffChangeLines(new P(n2, c));
      });
    } else
      this.pushDiffChangeLines(t);
  }
  // Output from buffer.
  getLines() {
    return this.flushChangeLines(), this.lines;
  }
};
i(Nt, "CommonBuffer");
var Tt = Nt;
function Os(e, t) {
  let n2 = new Ue(z, t), r = new Ue(j, t), o = new Tt(n2, r);
  return e.forEach((s) => {
    switch (s[0]) {
      case z:
        n2.align(s);
        break;
      case j:
        r.align(s);
        break;
      default:
        o.align(s);
    }
  }), o.getLines();
}
i(Os, "getAlignedDiffs");
function $s(e, t) {
  if (t) {
    let n2 = e.length - 1;
    return e.some((r, o) => r[0] === M && (o !== n2 || r[1] !== `
`));
  }
  return e.some((n2) => n2[0] === M);
}
i($s, "hasCommonDiff");
function ws(e, t, n2) {
  if (e !== t && e.length !== 0 && t.length !== 0) {
    let r = e.includes(`
`) || t.includes(`
`), [o, s] = vn(r ? `${e}
` : e, r ? `${t}
` : t, true, n2);
    if ($s(o, r)) {
      let c = ge(n2), a = Os(o, c.changeColor);
      return Rt(a, s, c);
    }
  }
  return We(e.split(`
`), t.split(`
`), n2);
}
i(ws, "diffStringsUnified");
function vn(e, t, n2, r) {
  let [o, s] = Ts(e, t, r);
  return n2 && ss(o), [o, s];
}
i(vn, "diffStringsRaw");
function Ct(e, t) {
  let { commonColor: n2 } = ge(t);
  return n2(e);
}
i(Ct, "getCommonMessage");
var { AsymmetricMatcher: Rs, DOMCollection: As, DOMElement: Ps, Immutable: Ns, ReactElement: Is, ReactTestComponent: Ms } = _e;
var er = [
  Ms,
  Is,
  Ps,
  As,
  Ns,
  Rs,
  _e.Error
];
var Ot = {
  maxDepth: 20,
  plugins: er
};
var tr = {
  callToJSON: false,
  maxDepth: 8,
  plugins: er
};
function Ls(e, t, n2) {
  if (Object.is(e, t))
    return "";
  let r = Dn(e), o = r, s = false;
  if (r === "object" && typeof e.asymmetricMatch == "function") {
    if (e.$$typeof !== Symbol.for("jest.asymmetricMatcher") || typeof e.getExpectedType != "function")
      return;
    o = e.getExpectedType(), s = o === "string";
  }
  if (o !== Dn(t)) {
    let d = function(O) {
      return O.length <= f ? O : `${O.slice(0, f)}...`;
    };
    i(d, "truncate");
    let { aAnnotation: c, aColor: a, aIndicator: u, bAnnotation: m, bColor: p, bIndicator: l } = ge(n2), b2 = $t(tr, n2), g = X(e, b2), h = X(
      t,
      b2
    ), f = 1e5;
    g = d(g), h = d(h);
    let S = `${a(`${u} ${c}:`)} 
${g}`, _2 = `${p(`${l} ${m}:`)} 
${h}`;
    return `${S}

${_2}`;
  }
  if (!s)
    switch (r) {
      case "string":
        return We(e.split(`
`), t.split(`
`), n2);
      case "boolean":
      case "number":
        return xs(e, t, n2);
      case "map":
        return _t(jn(e), jn(t), n2);
      case "set":
        return _t(kn(e), kn(t), n2);
      default:
        return _t(e, t, n2);
    }
}
i(Ls, "diff");
function xs(e, t, n2) {
  let r = X(e, Ot), o = X(t, Ot);
  return r === o ? "" : We(r.split(`
`), o.split(`
`), n2);
}
i(xs, "comparePrimitive");
function jn(e) {
  return new Map(Array.from(e.entries()).sort());
}
i(jn, "sortMap");
function kn(e) {
  return new Set(Array.from(e.values()).sort());
}
i(kn, "sortSet");
function _t(e, t, n2) {
  let r, o = false;
  try {
    let c = $t(Ot, n2);
    r = Bn(e, t, c, n2);
  } catch {
    o = true;
  }
  let s = Ct(Kn, n2);
  if (r === void 0 || r === s) {
    let c = $t(tr, n2);
    r = Bn(e, t, c, n2), r !== s && !o && (r = `${Ct(as, n2)}

${r}`);
  }
  return r;
}
i(_t, "compareObjects");
function $t(e, t) {
  let { compareKeys: n2, printBasicPrototype: r, maxDepth: o } = ge(t);
  return {
    ...e,
    compareKeys: n2,
    printBasicPrototype: r,
    maxDepth: o ?? e.maxDepth
  };
}
i($t, "getFormatOptions");
function Bn(e, t, n2, r) {
  let o = {
    ...n2,
    indent: 0
  }, s = X(e, o), c = X(t, o);
  if (s === c)
    return Ct(Kn, r);
  {
    let a = X(e, n2), u = X(t, n2);
    return _s(a.split(`
`), u.split(`
`), s.split(`
`), c.split(`
`), r);
  }
}
i(Bn, "getObjectsDifference");
var zn = 2e4;
function Yn(e) {
  return Be(e) === "Object" && typeof e.asymmetricMatch == "function";
}
i(Yn, "isAsymmetricMatcher");
function Un(e, t) {
  let n2 = Be(e), r = Be(t);
  return n2 === r && (n2 === "Object" || n2 === "Array");
}
i(Un, "isReplaceable");
function nr(e, t, n2) {
  let { aAnnotation: r, bAnnotation: o } = ge(n2);
  if (typeof t == "string" && typeof e == "string" && t.length > 0 && e.length > 0 && t.length <= zn && e.length <= zn && t !== e) {
    if (t.includes(`
`) || e.includes(`
`))
      return ws(t, e, n2);
    let [p] = vn(t, e, true), l = p.some((f) => f[0] === M), b2 = Ds(r, o), g = b2(r) + ks(Wn(p, z, l)), h = b2(o) + js(Wn(p, j, l));
    return `${g}
${h}`;
  }
  let s = St(t, { forceWritable: true }), c = St(e, { forceWritable: true }), { replacedExpected: a, replacedActual: u } = rr(c, s);
  return Ls(a, u, n2);
}
i(nr, "printDiffOrStringify");
function rr(e, t, n2 = /* @__PURE__ */ new WeakSet(), r = /* @__PURE__ */ new WeakSet()) {
  return e instanceof Error && t instanceof Error && typeof e.cause < "u" && typeof t.cause > "u" ? (delete e.cause, {
    replacedActual: e,
    replacedExpected: t
  }) : Un(e, t) ? n2.has(e) || r.has(t) ? {
    replacedActual: e,
    replacedExpected: t
  } : (n2.add(e), r.add(t), bt(t).forEach((o) => {
    let s = t[o], c = e[o];
    if (Yn(s))
      s.asymmetricMatch(c) && (e[o] = s);
    else if (Yn(c))
      c.asymmetricMatch(s) && (t[o] = c);
    else if (Un(c, s)) {
      let a = rr(c, s, n2, r);
      e[o] = a.replacedActual, t[o] = a.replacedExpected;
    }
  }), {
    replacedActual: e,
    replacedExpected: t
  }) : {
    replacedActual: e,
    replacedExpected: t
  };
}
i(rr, "replaceAsymmetricMatcher");
function Ds(...e) {
  let t = e.reduce((n2, r) => r.length > n2 ? r.length : n2, 0);
  return (n2) => `${n2}: ${" ".repeat(t - n2.length)}`;
}
i(Ds, "getLabelPrinter");
var Fs = "·";
function or(e) {
  return e.replace(/\s+$/gm, (t) => Fs.repeat(t.length));
}
i(or, "replaceTrailingSpaces");
function js(e) {
  return v.red(or(pe(e)));
}
i(js, "printReceived");
function ks(e) {
  return v.green(or(pe(e)));
}
i(ks, "printExpected");
function Wn(e, t, n2) {
  return e.reduce((r, o) => r + (o[0] === M ? o[1] : o[0] === t ? n2 ? v.inverse(o[1]) : o[1] : ""), "");
}
i(Wn, "getCommonAndChangedSubstrings");
var Bs = "@@__IMMUTABLE_RECORD__@@";
var zs = "@@__IMMUTABLE_ITERABLE__@@";
function Ys(e) {
  return e && (e[zs] || e[Bs]);
}
i(Ys, "isImmutable");
var Us = Object.getPrototypeOf({});
function sr(e) {
  return e instanceof Error ? `<unserializable>: ${e.message}` : typeof e == "string" ? `<unserializable>: ${e}` : "<unserializable>";
}
i(sr, "getUnserializableMessage");
function le(e, t = /* @__PURE__ */ new WeakMap()) {
  if (!e || typeof e == "string")
    return e;
  if (e instanceof Error && "toJSON" in e && typeof e.toJSON == "function") {
    let n2 = e.toJSON();
    return n2 && n2 !== e && typeof n2 == "object" && (typeof e.message == "string" && Ve(() => n2.message ?? (n2.message = e.message)), typeof e.stack == "string" && Ve(() => n2.stack ?? (n2.stack = e.stack)), typeof e.name == "string" && Ve(() => n2.name ?? (n2.name = e.name)), e.cause != null && Ve(() => n2.cause ?? (n2.cause = le(e.cause, t)))), le(n2, t);
  }
  if (typeof e == "function")
    return `Function<${e.name || "anonymous"}>`;
  if (typeof e == "symbol")
    return e.toString();
  if (typeof e != "object")
    return e;
  if (typeof Buffer < "u" && e instanceof Buffer)
    return `<Buffer(${e.length}) ...>`;
  if (typeof Uint8Array < "u" && e instanceof Uint8Array)
    return `<Uint8Array(${e.length}) ...>`;
  if (Ys(e))
    return le(e.toJSON(), t);
  if (e instanceof Promise || e.constructor && e.constructor.prototype === "AsyncFunction")
    return "Promise";
  if (typeof Element < "u" && e instanceof Element)
    return e.tagName;
  if (typeof e.asymmetricMatch == "function")
    return `${e.toString()} ${wn(e.sample)}`;
  if (typeof e.toJSON == "function")
    return le(e.toJSON(), t);
  if (t.has(e))
    return t.get(e);
  if (Array.isArray(e)) {
    let n2 = new Array(e.length);
    return t.set(e, n2), e.forEach((r, o) => {
      try {
        n2[o] = le(r, t);
      } catch (s) {
        n2[o] = sr(s);
      }
    }), n2;
  } else {
    let n2 = /* @__PURE__ */ Object.create(null);
    t.set(e, n2);
    let r = e;
    for (; r && r !== Us; )
      Object.getOwnPropertyNames(r).forEach((o) => {
        if (!(o in n2))
          try {
            n2[o] = le(e[o], t);
          } catch (s) {
            delete n2[o], n2[o] = sr(s);
          }
      }), r = Object.getPrototypeOf(r);
    return n2;
  }
}
i(le, "serializeValue");
function Ve(e) {
  try {
    return e();
  } catch {
  }
}
i(Ve, "safe");
function Ws(e) {
  return e.replace(/__(vite_ssr_import|vi_import)_\d+__\./g, "");
}
i(Ws, "normalizeErrorMessage");
function It(e, t, n2 = /* @__PURE__ */ new WeakSet()) {
  if (!e || typeof e != "object")
    return { message: String(e) };
  let r = e;
  (r.showDiff || r.showDiff === void 0 && r.expected !== void 0 && r.actual !== void 0) && (r.diff = nr(r.actual, r.expected, {
    ...t,
    ...r.diffOptions
  })), "expected" in r && typeof r.expected != "string" && (r.expected = pe(r.expected, 10)), "actual" in r && typeof r.actual != "string" && (r.actual = pe(r.actual, 10));
  try {
    typeof r.message == "string" && (r.message = Ws(r.message));
  } catch {
  }
  try {
    !n2.has(r) && typeof r.cause == "object" && (n2.add(r), r.cause = It(r.cause, t, n2));
  } catch {
  }
  try {
    return le(r);
  } catch (o) {
    return le(new Error(`Failed to fully serialize error: ${o?.message}
Inner error message: ${r?.message}`));
  }
}
i(It, "processError");
var ne = {
  CALL: "storybook/instrumenter/call",
  SYNC: "storybook/instrumenter/sync",
  START: "storybook/instrumenter/start",
  BACK: "storybook/instrumenter/back",
  GOTO: "storybook/instrumenter/goto",
  NEXT: "storybook/instrumenter/next",
  END: "storybook/instrumenter/end"
};
var qe = globalThis.__STORYBOOK_ADDONS_PREVIEW;
var Vs = ((o) => (o.DONE = "done", o.ERROR = "error", o.ACTIVE = "active", o.WAITING = "waiting", o))(Vs || {});
var Hs = new Error(
  "This function ran after the play function completed. Did you forget to `await` it?"
);
var cr = i((e) => Object.prototype.toString.call(e) === "[object Object]", "isObject");
var Js = i((e) => Object.prototype.toString.call(e) === "[object Module]", "isModule");
var Xs = i((e) => {
  if (!cr(e) && !Js(e))
    return false;
  if (e.constructor === void 0)
    return true;
  let t = e.constructor.prototype;
  return !!cr(t);
}, "isInstrumentable");
var Zs = i((e) => {
  try {
    return new e.constructor();
  } catch {
    return {};
  }
}, "construct");
var Mt = i(() => ({
  renderPhase: "preparing",
  isDebugging: false,
  isPlaying: false,
  isLocked: false,
  cursor: 0,
  calls: [],
  shadowCalls: [],
  callRefsByResult: /* @__PURE__ */ new Map(),
  chainedCallIds: /* @__PURE__ */ new Set(),
  ancestors: [],
  playUntil: void 0,
  resolvers: {},
  syncTimeout: void 0
}), "getInitialState");
var ur = i((e, t = false) => {
  let n2 = (t ? e.shadowCalls : e.calls).filter((o) => o.retain);
  if (!n2.length)
    return;
  let r = new Map(
    Array.from(e.callRefsByResult.entries()).filter(([, o]) => o.retain)
  );
  return { cursor: n2.length, calls: n2, callRefsByResult: r };
}, "getRetainedState");
var xt = class xt2 {
  constructor() {
    this.detached = false;
    this.initialized = false;
    this.state = {};
    this.loadParentWindowState = i(() => {
      try {
        this.state = import_global.global.window?.parent?.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER_STATE__ || {};
      } catch {
        this.detached = true;
      }
    }, "loadParentWindowState");
    this.updateParentWindowState = i(() => {
      try {
        import_global.global.window.parent.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER_STATE__ = this.state;
      } catch {
        this.detached = true;
      }
    }, "updateParentWindowState");
    this.loadParentWindowState();
    let t = i(({
      storyId: u,
      renderPhase: m,
      isPlaying: p = true,
      isDebugging: l = false
    }) => {
      let b2 = this.getState(u);
      this.setState(u, {
        ...Mt(),
        ...ur(b2, l),
        renderPhase: m || b2.renderPhase,
        shadowCalls: l ? b2.shadowCalls : [],
        chainedCallIds: l ? b2.chainedCallIds : /* @__PURE__ */ new Set(),
        playUntil: l ? b2.playUntil : void 0,
        isPlaying: p,
        isDebugging: l
      }), this.sync(u);
    }, "resetState"), n2 = i((u) => ({ storyId: m, playUntil: p }) => {
      this.getState(m).isDebugging || this.setState(m, ({ calls: b2 }) => ({
        calls: [],
        shadowCalls: b2.map((g) => ({ ...g, status: "waiting" })),
        isDebugging: true
      }));
      let l = this.getLog(m);
      this.setState(m, ({ shadowCalls: b2 }) => {
        if (p || !l.length)
          return { playUntil: p };
        let g = b2.findIndex((h) => h.id === l[0].callId);
        return {
          playUntil: b2.slice(0, g).filter((h) => h.interceptable && !h.ancestors?.length).slice(-1)[0]?.id
        };
      }), u.emit(import_core_events.FORCE_REMOUNT, { storyId: m, isDebugging: true });
    }, "start"), r = i((u) => ({ storyId: m }) => {
      let p = this.getLog(m).filter((b2) => !b2.ancestors?.length), l = p.reduceRight((b2, g, h) => b2 >= 0 || g.status === "waiting" ? b2 : h, -1);
      n2(u)({ storyId: m, playUntil: p[l - 1]?.callId });
    }, "back"), o = i((u) => ({ storyId: m, callId: p }) => {
      let { calls: l, shadowCalls: b2, resolvers: g } = this.getState(m), h = l.find(({ id: d }) => d === p), f = b2.find(({ id: d }) => d === p);
      if (!h && f && Object.values(g).length > 0) {
        let d = this.getLog(m).find((S) => S.status === "waiting")?.callId;
        f.id !== d && this.setState(m, { playUntil: f.id }), Object.values(g).forEach((S) => S());
      } else
        n2(u)({ storyId: m, playUntil: p });
    }, "goto"), s = i((u) => ({ storyId: m }) => {
      let { resolvers: p } = this.getState(m);
      if (Object.values(p).length > 0)
        Object.values(p).forEach((l) => l());
      else {
        let l = this.getLog(m).find((b2) => b2.status === "waiting")?.callId;
        l ? n2(u)({ storyId: m, playUntil: l }) : c({ storyId: m });
      }
    }, "next"), c = i(({ storyId: u }) => {
      this.setState(u, { playUntil: void 0, isDebugging: false }), Object.values(this.getState(u).resolvers).forEach((m) => m());
    }, "end"), a = i(({
      storyId: u,
      newPhase: m
    }) => {
      let { isDebugging: p } = this.getState(u);
      if (m === "preparing" && p)
        return t({ storyId: u, renderPhase: m });
      if (m === "playing")
        return t({ storyId: u, renderPhase: m, isDebugging: p });
      m === "played" ? this.setState(u, {
        renderPhase: m,
        isLocked: false,
        isPlaying: false,
        isDebugging: false
      }) : m === "errored" ? this.setState(u, {
        renderPhase: m,
        isLocked: false,
        isPlaying: false
      }) : m === "aborted" ? this.setState(u, {
        renderPhase: m,
        isLocked: true,
        isPlaying: false
      }) : this.setState(u, {
        renderPhase: m
      }), this.sync(u);
    }, "renderPhaseChanged");
    qe && qe.ready().then(() => {
      this.channel = qe.getChannel(), this.channel.on(import_core_events.FORCE_REMOUNT, t), this.channel.on(import_core_events.STORY_RENDER_PHASE_CHANGED, a), this.channel.on(import_core_events.SET_CURRENT_STORY, () => {
        this.initialized ? this.cleanup() : this.initialized = true;
      }), this.channel.on(ne.START, n2(this.channel)), this.channel.on(ne.BACK, r(this.channel)), this.channel.on(ne.GOTO, o(this.channel)), this.channel.on(ne.NEXT, s(this.channel)), this.channel.on(ne.END, c);
    });
  }
  getState(t) {
    return this.state[t] || Mt();
  }
  setState(t, n2) {
    if (t) {
      let r = this.getState(t), o = typeof n2 == "function" ? n2(r) : n2;
      this.state = { ...this.state, [t]: { ...r, ...o } }, this.updateParentWindowState();
    }
  }
  cleanup() {
    this.state = Object.entries(this.state).reduce(
      (r, [o, s]) => {
        let c = ur(s);
        return c && (r[o] = Object.assign(Mt(), c)), r;
      },
      {}
    );
    let n2 = { controlStates: {
      detached: this.detached,
      start: false,
      back: false,
      goto: false,
      next: false,
      end: false
    }, logItems: [] };
    this.channel?.emit(ne.SYNC, n2), this.updateParentWindowState();
  }
  getLog(t) {
    let { calls: n2, shadowCalls: r } = this.getState(t), o = [...r];
    n2.forEach((c, a) => {
      o[a] = c;
    });
    let s = /* @__PURE__ */ new Set();
    return o.reduceRight((c, a) => (a.args.forEach((u) => {
      u?.__callId__ && s.add(u.__callId__);
    }), a.path.forEach((u) => {
      u.__callId__ && s.add(u.__callId__);
    }), (a.interceptable || a.exception) && !s.has(a.id) && (c.unshift({ callId: a.id, status: a.status, ancestors: a.ancestors }), s.add(a.id)), c), []);
  }
  // Traverses the object structure to recursively patch all function properties.
  // Returns the original object, or a new object with the same constructor,
  // depending on whether it should mutate.
  instrument(t, n2, r = 0) {
    if (!Xs(t))
      return t;
    let { mutate: o = false, path: s = [] } = n2, c = n2.getKeys ? n2.getKeys(t, r) : Object.keys(t);
    return r += 1, c.reduce(
      (a, u) => {
        let m = vs(t, u);
        if (typeof m?.get == "function") {
          if (m.configurable) {
            let l = i(() => m?.get?.bind(t)?.(), "getter");
            Object.defineProperty(a, u, {
              get: i(() => this.instrument(l(), { ...n2, path: s.concat(u) }, r), "get")
            });
          }
          return a;
        }
        let p = t[u];
        return typeof p != "function" ? (a[u] = this.instrument(p, { ...n2, path: s.concat(u) }, r), a) : "__originalFn__" in p && typeof p.__originalFn__ == "function" ? (a[u] = p, a) : (a[u] = (...l) => this.track(u, p, t, l, n2), a[u].__originalFn__ = p, Object.defineProperty(
          a[u],
          "name",
          { value: u, writable: false }
        ), Object.keys(p).length > 0 && Object.assign(
          a[u],
          this.instrument({ ...p }, { ...n2, path: s.concat(u) }, r)
        ), a);
      },
      o ? t : Zs(t)
    );
  }
  // Monkey patch an object method to record calls.
  // Returns a function that invokes the original function, records the invocation ("call") and
  // returns the original result.
  track(t, n2, r, o, s) {
    let c = o?.[0]?.__storyId__ || import_global.global.__STORYBOOK_PREVIEW__?.selectionStore?.selection?.storyId, { cursor: a, ancestors: u } = this.getState(
      c
    );
    this.setState(c, { cursor: a + 1 });
    let m = `${u.slice(-1)[0] || c} [${a}] ${t}`, { path: p = [], intercept: l = false, retain: b2 = false } = s, g = typeof l == "function" ? l(
      t,
      p
    ) : l, h = { id: m, cursor: a, storyId: c, ancestors: u, path: p, method: t, args: o, interceptable: g, retain: b2 }, d = (g && !u.length ? this.intercept : this.invoke).call(this, n2, r, h, s);
    return this.instrument(d, { ...s, mutate: true, path: [{ __callId__: h.id }] });
  }
  intercept(t, n2, r, o) {
    let { chainedCallIds: s, isDebugging: c, playUntil: a } = this.getState(r.storyId), u = s.has(r.id);
    return !c || u || a ? (a === r.id && this.setState(r.storyId, { playUntil: void 0 }), this.invoke(t, n2, r, o)) : new Promise((m) => {
      this.setState(r.storyId, ({ resolvers: p }) => ({
        isLocked: false,
        resolvers: { ...p, [r.id]: m }
      }));
    }).then(() => (this.setState(r.storyId, (m) => {
      let { [r.id]: p, ...l } = m.resolvers;
      return { isLocked: true, resolvers: l };
    }), this.invoke(t, n2, r, o)));
  }
  invoke(t, n2, r, o) {
    let { callRefsByResult: s, renderPhase: c } = this.getState(r.storyId), a = 25, u = i((l, b2, g) => {
      if (g.includes(l))
        return "[Circular]";
      if (g = [...g, l], b2 > a)
        return "...";
      if (s.has(l))
        return s.get(l);
      if (l instanceof Array)
        return l.map((h) => u(h, ++b2, g));
      if (l instanceof Date)
        return { __date__: { value: l.toISOString() } };
      if (l instanceof Error) {
        let { name: h, message: f, stack: d } = l;
        return { __error__: { name: h, message: f, stack: d } };
      }
      if (l instanceof RegExp) {
        let { flags: h, source: f } = l;
        return { __regexp__: { flags: h, source: f } };
      }
      if (l instanceof import_global.global.window?.HTMLElement) {
        let { prefix: h, localName: f, id: d, classList: S, innerText: _2 } = l, O = Array.from(S);
        return { __element__: { prefix: h, localName: f, id: d, classNames: O, innerText: _2 } };
      }
      return typeof l == "function" ? {
        __function__: { name: "getMockName" in l ? l.getMockName() : l.name }
      } : typeof l == "symbol" ? { __symbol__: { description: l.description } } : typeof l == "object" && l?.constructor?.name && l?.constructor?.name !== "Object" ? { __class__: { name: l.constructor.name } } : Object.prototype.toString.call(l) === "[object Object]" ? Object.fromEntries(
        Object.entries(l).map(([h, f]) => [h, u(f, ++b2, g)])
      ) : l;
    }, "serializeValues"), m = {
      ...r,
      args: r.args.map((l) => u(l, 0, []))
    };
    r.path.forEach((l) => {
      l?.__callId__ && this.setState(r.storyId, ({ chainedCallIds: b2 }) => ({
        chainedCallIds: new Set(Array.from(b2).concat(l.__callId__))
      }));
    });
    let p = i((l) => {
      if (l instanceof Error) {
        let { name: b2, message: g, stack: h, callId: f = r.id } = l, {
          showDiff: d = void 0,
          diff: S = void 0,
          actual: _2 = void 0,
          expected: O = void 0
        } = l.name === "AssertionError" ? It(l) : l, y = { name: b2, message: g, stack: h, callId: f, showDiff: d, diff: S, actual: _2, expected: O };
        if (this.update({ ...m, status: "error", exception: y }), this.setState(r.storyId, (E) => ({
          callRefsByResult: new Map([
            ...Array.from(E.callRefsByResult.entries()),
            [l, { __callId__: r.id, retain: r.retain }]
          ])
        })), r.ancestors?.length)
          throw Object.prototype.hasOwnProperty.call(l, "callId") || Object.defineProperty(l, "callId", { value: r.id }), l;
      }
      throw l;
    }, "handleException");
    try {
      if (c === "played" && !r.retain)
        throw Hs;
      let b2 = (o.getArgs ? o.getArgs(r, this.getState(r.storyId)) : r.args).map((h) => typeof h != "function" || ei(h) || Object.keys(h).length ? h : (...f) => {
        let { cursor: d, ancestors: S } = this.getState(r.storyId);
        this.setState(r.storyId, { cursor: 0, ancestors: [...S, r.id] });
        let _2 = i(() => this.setState(r.storyId, { cursor: d, ancestors: S }), "restore"), O = false;
        try {
          let y = h(...f);
          return y instanceof Promise ? (O = true, y.finally(_2)) : y;
        } finally {
          O || _2();
        }
      }), g = t.apply(n2, b2);
      return g && ["object", "function", "symbol"].includes(typeof g) && this.setState(r.storyId, (h) => ({
        callRefsByResult: new Map([
          ...Array.from(h.callRefsByResult.entries()),
          [g, { __callId__: r.id, retain: r.retain }]
        ])
      })), this.update({
        ...m,
        status: g instanceof Promise ? "active" : "done"
      }), g instanceof Promise ? g.then((h) => (this.update({ ...m, status: "done" }), h), p) : g;
    } catch (l) {
      return p(l);
    }
  }
  // Sends the call info to the manager and synchronizes the log.
  update(t) {
    this.channel?.emit(ne.CALL, t), this.setState(t.storyId, ({ calls: n2 }) => {
      let r = n2.concat(t).reduce((o, s) => Object.assign(o, { [s.id]: s }), {});
      return {
        // Calls are sorted to ensure parent calls always come before calls in their callback.
        calls: Object.values(r).sort(
          (o, s) => o.id.localeCompare(s.id, void 0, { numeric: true })
        )
      };
    }), this.sync(t.storyId);
  }
  // Builds a log of interceptable calls and control states and sends it to the manager.
  // Uses a 0ms debounce because this might get called many times in one tick.
  sync(t) {
    let n2 = i(() => {
      let { isLocked: r, isPlaying: o } = this.getState(t), s = this.getLog(t), c = s.filter(({ ancestors: l }) => !l.length).find((l) => l.status === "waiting")?.callId, a = s.some((l) => l.status === "active");
      if (this.detached || r || a || s.length === 0) {
        let b2 = { controlStates: {
          detached: this.detached,
          start: false,
          back: false,
          goto: false,
          next: false,
          end: false
        }, logItems: s };
        this.channel?.emit(ne.SYNC, b2);
        return;
      }
      let u = s.some(
        (l) => l.status === "done" || l.status === "error"
      ), p = { controlStates: {
        detached: this.detached,
        start: u,
        back: u,
        goto: true,
        next: o,
        end: o
      }, logItems: s, pausedAt: c };
      this.channel?.emit(ne.SYNC, p);
    }, "synchronize");
    this.setState(t, ({ syncTimeout: r }) => (clearTimeout(r), { syncTimeout: setTimeout(n2, 0) }));
  }
};
i(xt, "Instrumenter");
var Lt = xt;
function Qs(e, t = {}) {
  try {
    let n2 = false, r = false;
    return import_global.global.window?.location?.search?.includes("instrument=true") ? n2 = true : import_global.global.window?.location?.search?.includes("instrument=false") && (r = true), import_global.global.window?.parent === import_global.global.window && !n2 || r ? e : (import_global.global.window && !import_global.global.window.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER__ && (import_global.global.window.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER__ = new Lt()), (import_global.global.window?.__STORYBOOK_ADDON_INTERACTIONS_INSTRUMENTER__).instrument(e, t));
  } catch (n2) {
    return import_client_logger.once.warn(n2), e;
  }
}
i(Qs, "instrument");
function vs(e, t) {
  let n2 = e;
  for (; n2 != null; ) {
    let r = Object.getOwnPropertyDescriptor(n2, t);
    if (r)
      return r;
    n2 = Object.getPrototypeOf(n2);
  }
}
i(vs, "getPropertyDescriptor");
function ei(e) {
  if (typeof e != "function")
    return false;
  let t = Object.getOwnPropertyDescriptor(e, "prototype");
  return t ? !t.writable : false;
}
i(ei, "isClass");

// node_modules/storybook/dist/csf/index.js
var import_preview_api3 = __toESM(require_preview_api());
var import_core_events3 = __toESM(require_core_events());
var import_preview_api4 = __toESM(require_preview_api());
var import_global8 = __toESM(require_global());
var import_global9 = __toESM(require_global());
var import_global10 = __toESM(require_global());
var import_preview_api5 = __toESM(require_preview_api());
var import_global11 = __toESM(require_global());
var import_test2 = __toESM(require_test());
var Br2 = Object.create;
var ce2 = Object.defineProperty;
var zr2 = Object.getOwnPropertyDescriptor;
var Ur2 = Object.getOwnPropertyNames;
var Gr2 = Object.getPrototypeOf;
var Wr2 = Object.prototype.hasOwnProperty;
var n = (e, t) => ce2(e, "name", { value: t, configurable: true });
var Yr2 = (e, t) => () => (t || e((t = { exports: {} }).exports, t), t.exports);
var xt3 = (e, t) => {
  for (var r in t)
    ce2(e, r, { get: t[r], enumerable: true });
};
var Vr2 = (e, t, r, o) => {
  if (t && typeof t == "object" || typeof t == "function")
    for (let i2 of Ur2(t))
      !Wr2.call(e, i2) && i2 !== r && ce2(e, i2, { get: () => t[i2], enumerable: !(o = zr2(t, i2)) || o.enumerable });
  return e;
};
var Kr2 = (e, t, r) => (r = e != null ? Br2(Gr2(e)) : {}, Vr2(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  t || !e || !e.__esModule ? ce2(r, "default", { value: e, enumerable: true }) : r,
  e
));
var Tt2 = Yr2((Ee2) => {
  Object.defineProperty(Ee2, "__esModule", { value: true }), Ee2.isEqual = /* @__PURE__ */ (function() {
    var e = Object.prototype.toString, t = Object.getPrototypeOf, r = Object.getOwnPropertySymbols ? function(o) {
      return Object.keys(o).concat(Object.getOwnPropertySymbols(o));
    } : Object.keys;
    return function(o, i2) {
      return n(function s(a, p, c) {
        var l, y, u, h = e.call(a), T = e.call(p);
        if (a === p) return true;
        if (a == null || p == null) return false;
        if (c.indexOf(a) > -1 && c.indexOf(p) > -1) return true;
        if (c.push(a, p), h != T || (l = r(a), y = r(p), l.length != y.length || l.some(function(R) {
          return !s(a[R], p[R], c);
        }))) return false;
        switch (h.slice(8, -1)) {
          case "Symbol":
            return a.valueOf() == p.valueOf();
          case "Date":
          case "Number":
            return +a == +p || +a != +a && +p != +p;
          case "RegExp":
          case "Function":
          case "String":
          case "Boolean":
            return "" + a == "" + p;
          case "Set":
          case "Map":
            l = a.entries(), y = p.entries();
            do
              if (!s((u = l.next()).value, y.next().value, c)) return false;
            while (!u.done);
            return true;
          case "ArrayBuffer":
            a = new Uint8Array(a), p = new Uint8Array(p);
          case "DataView":
            a = new Uint8Array(a.buffer), p = new Uint8Array(p.buffer);
          case "Float32Array":
          case "Float64Array":
          case "Int8Array":
          case "Int16Array":
          case "Int32Array":
          case "Uint8Array":
          case "Uint16Array":
          case "Uint32Array":
          case "Uint8ClampedArray":
          case "Arguments":
          case "Array":
            if (a.length != p.length) return false;
            for (u = 0; u < a.length; u++) if ((u in a || u in p) && (u in a != u in p || !s(a[u], p[u], c))) return false;
            return true;
          case "Object":
            return s(t(a), t(p), c);
          default:
            return false;
        }
      }, "n")(o, i2, []);
    };
  })();
});
function bt2(e) {
  return e.replace(/_/g, " ").replace(/-/g, " ").replace(/\./g, " ").replace(/([^\n])([A-Z])([a-z])/g, (t, r, o, i2) => `${r} ${o}${i2}`).replace(
    /([a-z])([A-Z])/g,
    (t, r, o) => `${r} ${o}`
  ).replace(/([a-z])([0-9])/gi, (t, r, o) => `${r} ${o}`).replace(/([0-9])([a-z])/gi, (t, r, o) => `${r} ${o}`).replace(/(\s|^)(\w)/g, (t, r, o) => `${r}${o.toUpperCase()}`).replace(/ +/g, " ").trim();
}
n(bt2, "toStartCaseStr");
var Ce = Kr2(Tt2(), 1);
var St2 = n((e) => e.map((t) => typeof t < "u").filter(Boolean).length, "count");
var qr2 = n((e, t) => {
  let { exists: r, eq: o, neq: i2, truthy: s } = e;
  if (St2([r, o, i2, s]) > 1)
    throw new Error(`Invalid conditional test ${JSON.stringify({ exists: r, eq: o, neq: i2 })}`);
  if (typeof o < "u")
    return (0, Ce.isEqual)(t, o);
  if (typeof i2 < "u")
    return !(0, Ce.isEqual)(t, i2);
  if (typeof r < "u") {
    let p = typeof t < "u";
    return r ? p : !p;
  }
  return (typeof s > "u" ? true : s) ? !!t : !t;
}, "testValue");
var Xr2 = n((e, t, r) => {
  if (!e.if)
    return true;
  let { arg: o, global: i2 } = e.if;
  if (St2([o, i2]) !== 1)
    throw new Error(`Invalid conditional value ${JSON.stringify({ arg: o, global: i2 })}`);
  let s = o ? t[o] : r[i2];
  return qr2(e.if, s);
}, "includeConditionalArg");
function At3() {
  let e = {
    setHandler: n(() => {
    }, "setHandler"),
    send: n(() => {
    }, "send")
  };
  return new import_channels.Channel({ transport: e });
}
n(At3, "mockChannel");
var Me2 = class Me3 {
  constructor() {
    this.getChannel = n(() => {
      if (!this.channel) {
        let t = At3();
        return this.setChannel(t), t;
      }
      return this.channel;
    }, "getChannel");
    this.ready = n(() => this.promise, "ready");
    this.hasChannel = n(() => !!this.channel, "hasChannel");
    this.setChannel = n((t) => {
      this.channel = t, this.resolve();
    }, "setChannel");
    this.promise = new Promise((t) => {
      this.resolve = () => t(this.getChannel());
    });
  }
};
n(Me2, "AddonStore");
var Pe2 = Me2;
var ke2 = "__STORYBOOK_ADDONS_PREVIEW";
function Jr2() {
  return import_global2.global[ke2] || (import_global2.global[ke2] = new Pe2()), import_global2.global[ke2];
}
n(Jr2, "getAddonsStore");
var Oe = Jr2();
var Ie2 = class Ie3 {
  constructor() {
    this.hookListsMap = void 0;
    this.mountedDecorators = void 0;
    this.prevMountedDecorators = void 0;
    this.currentHooks = void 0;
    this.nextHookIndex = void 0;
    this.currentPhase = void 0;
    this.currentEffects = void 0;
    this.prevEffects = void 0;
    this.currentDecoratorName = void 0;
    this.hasUpdates = void 0;
    this.currentContext = void 0;
    this.renderListener = n((t) => {
      t === this.currentContext?.id && (this.triggerEffects(), this.currentContext = null, this.removeRenderListeners());
    }, "renderListener");
    this.init();
  }
  init() {
    this.hookListsMap = /* @__PURE__ */ new WeakMap(), this.mountedDecorators = /* @__PURE__ */ new Set(), this.prevMountedDecorators = /* @__PURE__ */ new Set(), this.currentHooks = [], this.nextHookIndex = 0, this.currentPhase = "NONE", this.currentEffects = [], this.prevEffects = [], this.currentDecoratorName = null, this.hasUpdates = false, this.currentContext = null;
  }
  clean() {
    this.prevEffects.forEach((t) => {
      t.destroy && t.destroy();
    }), this.init(), this.removeRenderListeners();
  }
  getNextHook() {
    let t = this.currentHooks[this.nextHookIndex];
    return this.nextHookIndex += 1, t;
  }
  triggerEffects() {
    this.prevEffects.forEach((t) => {
      !this.currentEffects.includes(t) && t.destroy && t.destroy();
    }), this.currentEffects.forEach((t) => {
      this.prevEffects.includes(t) || (t.destroy = t.create());
    }), this.prevEffects = this.currentEffects, this.currentEffects = [];
  }
  addRenderListeners() {
    this.removeRenderListeners(), Oe.getChannel().on(import_core_events2.STORY_RENDERED, this.renderListener);
  }
  removeRenderListeners() {
    Oe.getChannel().removeListener(import_core_events2.STORY_RENDERED, this.renderListener);
  }
};
n(Ie2, "HooksContext");
var de2 = Ie2;
function wt2(e) {
  let t = n((...r) => {
    let { hooks: o } = typeof r[0] == "function" ? r[1] : r[0], i2 = o.currentPhase, s = o.currentHooks, a = o.nextHookIndex, p = o.currentDecoratorName;
    o.currentDecoratorName = e.name, o.prevMountedDecorators.has(e) ? (o.currentPhase = "UPDATE", o.currentHooks = o.hookListsMap.get(e) || []) : (o.currentPhase = "MOUNT", o.currentHooks = [], o.hookListsMap.set(e, o.currentHooks), o.prevMountedDecorators.add(e)), o.nextHookIndex = 0;
    let c = import_global3.global.STORYBOOK_HOOKS_CONTEXT;
    import_global3.global.STORYBOOK_HOOKS_CONTEXT = o;
    let l = e(...r);
    if (import_global3.global.STORYBOOK_HOOKS_CONTEXT = c, o.currentPhase === "UPDATE" && o.getNextHook() != null)
      throw new Error(
        "Rendered fewer hooks than expected. This may be caused by an accidental early return statement."
      );
    return o.currentPhase = i2, o.currentHooks = s, o.nextHookIndex = a, o.currentDecoratorName = p, l;
  }, "hookified");
  return t.originalFn = e, t;
}
n(wt2, "hookify");
var Fe2 = 0;
var Qr2 = 25;
var Et2 = n((e) => (t, r) => {
  let o = e(
    wt2(t),
    r.map((i2) => wt2(i2))
  );
  return (i2) => {
    let { hooks: s } = i2;
    s.prevMountedDecorators ??= /* @__PURE__ */ new Set(), s.mountedDecorators = /* @__PURE__ */ new Set([t, ...r]), s.currentContext = i2, s.hasUpdates = false;
    let a = o(i2);
    for (Fe2 = 1; s.hasUpdates; )
      if (s.hasUpdates = false, s.currentEffects = [], a = o(i2), Fe2 += 1, Fe2 > Qr2)
        throw new Error(
          "Too many re-renders. Storybook limits the number of renders to prevent an infinite loop."
        );
    return s.addRenderListeners(), a;
  };
}, "applyHooks");
function ee2(e) {
  if (!e || typeof e != "object")
    return false;
  let t = Object.getPrototypeOf(e);
  return t === null || t === Object.prototype || Object.getPrototypeOf(t) === null ? Object.prototype.toString.call(e) === "[object Object]" : false;
}
n(ee2, "isPlainObject");
function U(e, t) {
  let r = {}, o = Object.keys(e);
  for (let i2 = 0; i2 < o.length; i2++) {
    let s = o[i2], a = e[s];
    r[s] = t(a, s, e);
  }
  return r;
}
n(U, "mapValues");
function Le2(e, t) {
  let r = {}, o = Object.keys(e);
  for (let i2 = 0; i2 < o.length; i2++) {
    let s = o[i2], a = e[s];
    t(a, s) && (r[s] = a);
  }
  return r;
}
n(Le2, "pickBy");
function W(e) {
  for (var t = [], r = 1; r < arguments.length; r++)
    t[r - 1] = arguments[r];
  var o = Array.from(typeof e == "string" ? [e] : e);
  o[o.length - 1] = o[o.length - 1].replace(/\r?\n([\t ]*)$/, "");
  var i2 = o.reduce(function(p, c) {
    var l = c.match(/\n([\t ]+|(?!\s).)/g);
    return l ? p.concat(l.map(function(y) {
      var u, h;
      return (h = (u = y.match(/[\t ]/g)) === null || u === void 0 ? void 0 : u.length) !== null && h !== void 0 ? h : 0;
    })) : p;
  }, []);
  if (i2.length) {
    var s = new RegExp(`
[	 ]{` + Math.min.apply(Math, i2) + "}", "g");
    o = o.map(function(p) {
      return p.replace(s, `
`);
    });
  }
  o[0] = o[0].replace(/^\r?\n/, "");
  var a = o[0];
  return t.forEach(function(p, c) {
    var l = a.match(/(?:^|\n)( *)$/), y = l ? l[1] : "", u = p;
    typeof p == "string" && p.includes(`
`) && (u = String(p).split(`
`).map(function(h, T) {
      return T === 0 ? h : "" + y + h;
    }).join(`
`)), a += u + o[c + 1];
  }), a;
}
n(W, "dedent");
var vi = Symbol("incompatible");
var ki = Symbol("Deeply equal");
var De2 = "UNTARGETED";
function Ct2({
  args: e,
  argTypes: t
}) {
  let r = {};
  return Object.entries(e).forEach(([o, i2]) => {
    let { target: s = De2 } = t[o] || {};
    r[s] = r[s] || {}, r[s][o] = i2;
  }), r;
}
n(Ct2, "groupArgsByTarget");
var vt2 = n((e = {}) => Object.entries(e).reduce((t, [r, { defaultValue: o }]) => (typeof o < "u" && (t[r] = o), t), {}), "getValuesFromArgTypes");
var eo2 = n((e) => typeof e == "string" ? { name: e } : e, "normalizeType");
var to2 = n((e) => typeof e == "string" ? { type: e } : e, "normalizeControl");
var ro2 = n((e, t) => {
  let { type: r, control: o, ...i2 } = e, s = {
    name: t,
    ...i2
  };
  return r && (s.type = eo2(r)), o ? s.control = to2(o) : o === false && (s.control = { disable: true }), s;
}, "normalizeInputType");
var K = n((e) => U(e, ro2), "normalizeInputTypes");
var b = n((e) => Array.isArray(e) ? e : e ? [e] : [], "normalizeArrays");
var ao2 = W`
CSF .story annotations deprecated; annotate story functions directly:
- StoryFn.story.name => StoryFn.storyName
- StoryFn.story.(parameters|decorators) => StoryFn.(parameters|decorators)
See https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#hoisted-csf-annotations for details and codemod.
`;
function _e2(e, t, r) {
  let o = t, i2 = typeof t == "function" ? t : null, { story: s } = o;
  s && (import_client_logger4.logger.debug("deprecated story", s), (0, import_client_logger4.deprecate)(ao2));
  let a = cc(e), p = typeof o != "function" && o.name || o.storyName || s?.name || a, c = [
    ...b(o.decorators),
    ...b(s?.decorators)
  ], l = { ...s?.parameters, ...o.parameters }, y = { ...s?.args, ...o.args }, u = { ...s?.argTypes, ...o.argTypes }, h = [...b(o.loaders), ...b(
    s?.loaders
  )], T = [
    ...b(o.beforeEach),
    ...b(s?.beforeEach)
  ], R = [
    ...b(o.afterEach),
    ...b(s?.afterEach)
  ], { render: P2, play: L, tags: O = [], globals: F = {} } = o, A2 = l.__id || lc(r.id, a);
  return {
    moduleExport: t,
    id: A2,
    name: p,
    tags: O,
    decorators: c,
    parameters: l,
    args: y,
    argTypes: K(u),
    loaders: h,
    beforeEach: T,
    afterEach: R,
    globals: F,
    ...P2 && { render: P2 },
    ...i2 && { userStoryFn: i2 },
    ...L && { play: L }
  };
}
n(_e2, "normalizeStory");
function kt2(e, t = e.title, r) {
  let { id: o, argTypes: i2 } = e;
  return {
    id: jn2(o || t),
    ...e,
    title: t,
    ...i2 && { argTypes: K(i2) },
    parameters: {
      fileName: r,
      ...e.parameters
    }
  };
}
n(kt2, "normalizeComponentAnnotations");
function Ot2(e) {
  return e != null && lo2(e).includes("mount");
}
n(Ot2, "mountDestructured");
function lo2(e) {
  let t = e.toString().match(/[^(]*\(([^)]*)/);
  if (!t)
    return [];
  let r = Pt3(t[1]);
  if (!r.length)
    return [];
  let o = r[0];
  return o.startsWith("{") && o.endsWith("}") ? Pt3(o.slice(1, -1).replace(/\s/g, "")).map((s) => s.replace(/:.*|=.*/g, "")) : [];
}
n(lo2, "getUsedProps");
function Pt3(e) {
  let t = [], r = [], o = 0;
  for (let s = 0; s < e.length; s++)
    if (e[s] === "{" || e[s] === "[")
      r.push(e[s] === "{" ? "}" : "]");
    else if (e[s] === r[r.length - 1])
      r.pop();
    else if (!r.length && e[s] === ",") {
      let a = e.substring(o, s).trim();
      a && t.push(a), o = s + 1;
    }
  let i2 = e.substring(o).trim();
  return i2 && t.push(i2), t;
}
n(Pt3, "splitByComma");
function Mt2(e, t, r) {
  let o = r(e);
  return (i2) => t(o, i2);
}
n(Mt2, "decorateStory");
function $t2({
  componentId: e,
  title: t,
  kind: r,
  id: o,
  name: i2,
  story: s,
  parameters: a,
  initialArgs: p,
  argTypes: c,
  ...l
} = {}) {
  return l;
}
n($t2, "sanitizeStoryContextUpdate");
function He2(e, t) {
  let r = {}, o = n((s) => (a) => {
    if (!r.value)
      throw new Error("Decorated function called without init");
    return r.value = {
      ...r.value,
      ...$t2(a)
    }, s(r.value);
  }, "bindWithContext"), i2 = t.reduce(
    (s, a) => Mt2(s, a, o),
    e
  );
  return (s) => (r.value = s, i2(s));
}
n(He2, "defaultDecorateStory");
var D2 = n((...e) => {
  let t = {}, r = e.filter(Boolean), o = r.reduce((i2, s) => (Object.entries(s).forEach(([a, p]) => {
    let c = i2[a];
    Array.isArray(p) || typeof c > "u" ? i2[a] = p : ee2(p) && ee2(c) ? t[a] = true : typeof p < "u" && (i2[a] = p);
  }), i2), {});
  return Object.keys(t).forEach((i2) => {
    let s = r.filter(Boolean).map((a) => a[i2]).filter((a) => typeof a < "u");
    s.every((a) => ee2(a)) ? o[i2] = D2(...s) : o[i2] = s[s.length - 1];
  }), o;
}, "combineParameters");
function Ne2(e, t, r) {
  let { moduleExport: o, id: i2, name: s } = e || {}, a = go2(
    e,
    t,
    r
  ), p = n(async (w2) => {
    let d = {};
    for (let m of [
      b(r.loaders),
      b(t.loaders),
      b(e.loaders)
    ]) {
      if (w2.abortSignal.aborted)
        return d;
      let f = await Promise.all(m.map((x) => x(w2)));
      Object.assign(d, ...f);
    }
    return d;
  }, "applyLoaders"), c = n(async (w2) => {
    let d = new Array();
    for (let m of [
      ...b(r.beforeEach),
      ...b(t.beforeEach),
      ...b(e.beforeEach)
    ]) {
      if (w2.abortSignal.aborted)
        return d;
      let f = await m(w2);
      f && d.push(f);
    }
    return d;
  }, "applyBeforeEach"), l = n(async (w2) => {
    let d = [
      ...b(r.afterEach),
      ...b(t.afterEach),
      ...b(e.afterEach)
    ].reverse();
    for (let m of d) {
      if (w2.abortSignal.aborted)
        return;
      await m(w2);
    }
  }, "applyAfterEach"), y = n((w2) => w2.originalStoryFn(w2.args, w2), "undecoratedStoryFn"), { applyDecorators: u = He2, runStep: h } = r, T = [
    ...b(e?.decorators),
    ...b(t?.decorators),
    ...b(r?.decorators)
  ], R = e?.userStoryFn || e?.render || t.render || r.render, P2 = Et2(u)(y, T), L = n((w2) => P2(w2), "unboundStoryFn"), O = e?.play ?? t?.play, F = Ot2(O);
  if (!R && !F)
    throw new import_preview_errors.NoRenderFunctionError({ id: i2 });
  let A2 = n((w2) => async () => (await w2.renderToCanvas(), w2.canvas), "defaultMount"), S = e.mount ?? t.mount ?? r.mount ?? A2, v2 = r.testingLibraryRender;
  return {
    storyGlobals: {},
    ...a,
    moduleExport: o,
    id: i2,
    name: s,
    story: s,
    originalStoryFn: R,
    undecoratedStoryFn: y,
    unboundStoryFn: L,
    applyLoaders: p,
    applyBeforeEach: c,
    applyAfterEach: l,
    playFunction: O,
    runStep: h,
    mount: S,
    testingLibraryRender: v2,
    renderToCanvas: r.renderToCanvas,
    usesMount: F
  };
}
n(Ne2, "prepareStory");
function go2(e, t, r) {
  let o = ["dev", "test"], i2 = import_global5.global.DOCS_OPTIONS?.autodocs === true ? ["autodocs"] : [], s = uc(
    ...o,
    ...i2,
    ...r.tags ?? [],
    ...t.tags ?? [],
    ...e?.tags ?? []
  ), a = D2(
    r.parameters,
    t.parameters,
    e?.parameters
  ), { argTypesEnhancers: p = [], argsEnhancers: c = [] } = r, l = D2(
    r.argTypes,
    t.argTypes,
    e?.argTypes
  );
  if (e) {
    let O = e?.userStoryFn || e?.render || t.render || r.render;
    a.__isArgsStory = O && O.length > 0;
  }
  let y = {
    ...r.args,
    ...t.args,
    ...e?.args
  }, u = {
    ...t.globals,
    ...e?.globals
  }, h = {
    componentId: t.id,
    title: t.title,
    kind: t.title,
    // Back compat
    id: e?.id || t.id,
    // if there's no story name, we create a fake one since enhancers expect a name
    name: e?.name || "__meta",
    story: e?.name || "__meta",
    // Back compat
    component: t.component,
    subcomponents: t.subcomponents,
    tags: s,
    parameters: a,
    initialArgs: y,
    argTypes: l,
    storyGlobals: u
  };
  h.argTypes = p.reduce(
    (O, F) => F({ ...h, argTypes: O }),
    h.argTypes
  );
  let T = { ...y };
  h.initialArgs = [...c].reduce(
    (O, F) => ({
      ...O,
      ...F({
        ...h,
        initialArgs: O
      })
    }),
    T
  );
  let { name: R, story: P2, ...L } = h;
  return L;
}
n(go2, "preparePartialAnnotations");
function Ft2(e) {
  let { args: t } = e, r = {
    ...e,
    allArgs: void 0,
    argsByTarget: void 0
  };
  if (import_global4.global.FEATURES?.argTypeTargetsV7) {
    let s = Ct2(e);
    r = {
      ...e,
      allArgs: e.args,
      argsByTarget: s,
      args: s[De2] || {}
    };
  }
  let o = Object.entries(r.args).reduce((s, [a, p]) => {
    if (!r.argTypes[a]?.mapping)
      return s[a] = p, s;
    let c = n((l) => {
      let y = r.argTypes[a].mapping;
      return y && l in y ? y[l] : l;
    }, "mappingFn");
    return s[a] = Array.isArray(p) ? p.map(c) : c(p), s;
  }, {}), i2 = Object.entries(o).reduce((s, [a, p]) => {
    let c = r.argTypes[a] || {};
    return Xr2(c, o, r.globals) && (s[a] = p), s;
  }, {});
  return { ...r, unmappedArgs: t, args: i2 };
}
n(Ft2, "prepareContext");
var je2 = n((e, t, r) => {
  let o = typeof e;
  switch (o) {
    case "boolean":
    case "string":
    case "number":
    case "function":
    case "symbol":
      return { name: o };
    default:
      break;
  }
  return e ? r.has(e) ? (import_client_logger5.logger.warn(W`
        We've detected a cycle in arg '${t}'. Args should be JSON-serializable.

        Consider using the mapping feature or fully custom args:
        - Mapping: https://storybook.js.org/docs/writing-stories/args#mapping-to-complex-arg-values
        - Custom args: https://storybook.js.org/docs/essentials/controls#fully-custom-args
      `), { name: "other", value: "cyclic object" }) : (r.add(e), Array.isArray(e) ? { name: "array", value: e.length > 0 ? je2(e[0], t, new Set(
    r
  )) : { name: "other", value: "unknown" } } : { name: "object", value: U(e, (s) => je2(s, t, new Set(r))) }) : { name: "object", value: {} };
}, "inferType");
var Be2 = n((e) => {
  let { id: t, argTypes: r = {}, initialArgs: o = {} } = e, i2 = U(o, (a, p) => ({
    name: p,
    type: je2(a, `${t}.${p}`, /* @__PURE__ */ new Set())
  })), s = U(r, (a, p) => ({
    name: p
  }));
  return D2(i2, s, r);
}, "inferArgTypes");
Be2.secondPass = true;
var It2 = n((e, t) => Array.isArray(t) ? t.includes(e) : e.match(t), "matches");
var ze2 = n((e, t, r) => !t && !r ? e : e && Le2(e, (o, i2) => {
  let s = o.name || i2.toString();
  return !!(!t || It2(s, t)) && (!r || !It2(s, r));
}), "filterArgTypes");
var bo2 = n((e, t, r) => {
  let { type: o, options: i2 } = e;
  if (o) {
    if (r.color && r.color.test(t)) {
      let s = o.name;
      if (s === "string")
        return { control: { type: "color" } };
      s !== "enum" && import_client_logger6.logger.warn(
        `Addon controls: Control of type color only supports string, received "${s}" instead`
      );
    }
    if (r.date && r.date.test(t))
      return { control: { type: "date" } };
    switch (o.name) {
      case "array":
        return { control: { type: "object" } };
      case "boolean":
        return { control: { type: "boolean" } };
      case "string":
        return { control: { type: "text" } };
      case "number":
        return { control: { type: "number" } };
      case "enum": {
        let { value: s } = o;
        return { control: { type: s?.length <= 5 ? "radio" : "select" }, options: s };
      }
      case "function":
      case "symbol":
        return null;
      default:
        return { control: { type: i2 ? "select" : "object" } };
    }
  }
}, "inferControl");
var me2 = n((e) => {
  let {
    argTypes: t,
    parameters: { __isArgsStory: r, controls: { include: o = null, exclude: i2 = null, matchers: s = {} } = {} }
  } = e;
  if (!r)
    return t;
  let a = ze2(t, o, i2), p = U(a, (c, l) => c?.type && bo2(c, l.toString(), s));
  return D2(p, a);
}, "inferControls");
me2.secondPass = true;
function te2({
  argTypes: e,
  globalTypes: t,
  argTypesEnhancers: r,
  decorators: o,
  loaders: i2,
  beforeEach: s,
  afterEach: a,
  initialGlobals: p,
  ...c
}) {
  return {
    ...e && { argTypes: K(e) },
    ...t && { globalTypes: K(t) },
    decorators: b(o),
    loaders: b(i2),
    beforeEach: b(s),
    afterEach: b(a),
    argTypesEnhancers: [
      ...r || [],
      Be2,
      // There's an architectural decision to be made regarding embedded addons in core:
      //
      // Option 1: Keep embedded addons but ensure consistency by moving addon-specific code
      // (like inferControls) to live alongside the addon code itself. This maintains the
      // concept of core addons while improving code organization.
      //
      // Option 2: Fully integrate these addons into core, potentially moving UI components
      // into the manager and treating them as core features rather than addons. This is a
      // bigger architectural change requiring careful consideration.
      //
      // For now, we're keeping inferControls here as we need time to properly evaluate
      // these options and their implications. Some features (like Angular's cleanArgsDecorator)
      // currently rely on this behavior.
      //
      // TODO: Make an architectural decision on the handling of core addons
      me2
    ],
    initialGlobals: p,
    ...c
  };
}
n(te2, "normalizeProjectAnnotations");
var Lt2 = n((e) => async () => {
  let t = [];
  for (let r of e) {
    let o = await r();
    o && t.unshift(o);
  }
  return async () => {
    for (let r of t)
      await r();
  };
}, "composeBeforeAllHooks");
function Ue2(e) {
  return async (t, r, o) => {
    await e.reduceRight(
      (s, a) => async () => a(t, s, o),
      async () => r(o)
    )();
  };
}
n(Ue2, "composeStepRunners");
function oe(e, t) {
  return e.map((r) => r.default?.[t] ?? r[t]).filter(Boolean);
}
n(oe, "getField");
function Y(e, t, r = {}) {
  return oe(e, t).reduce((o, i2) => {
    let s = b(i2);
    return r.reverseFileOrder ? [...s, ...o] : [...o, ...s];
  }, []);
}
n(Y, "getArrayField");
function ue(e, t) {
  return Object.assign({}, ...oe(e, t));
}
n(ue, "getObjectField");
function re(e, t) {
  return oe(e, t).pop();
}
n(re, "getSingletonField");
function ne2(e) {
  let t = Y(e, "argTypesEnhancers"), r = oe(e, "runStep"), o = Y(e, "beforeAll");
  return {
    parameters: D2(...oe(e, "parameters")),
    decorators: Y(e, "decorators", {
      reverseFileOrder: !(import_global6.global.FEATURES?.legacyDecoratorFileOrder ?? false)
    }),
    args: ue(e, "args"),
    argsEnhancers: Y(e, "argsEnhancers"),
    argTypes: ue(e, "argTypes"),
    argTypesEnhancers: [
      ...t.filter((i2) => !i2.secondPass),
      ...t.filter((i2) => i2.secondPass)
    ],
    initialGlobals: ue(e, "initialGlobals"),
    globalTypes: ue(e, "globalTypes"),
    loaders: Y(e, "loaders"),
    beforeAll: Lt2(o),
    beforeEach: Y(e, "beforeEach"),
    afterEach: Y(e, "afterEach"),
    render: re(e, "render"),
    renderToCanvas: re(e, "renderToCanvas"),
    applyDecorators: re(e, "applyDecorators"),
    runStep: Ue2(r),
    tags: Y(e, "tags"),
    mount: re(e, "mount"),
    testingLibraryRender: re(e, "testingLibraryRender")
  };
}
n(ne2, "composeConfigs");
function Dt() {
  try {
    return (
      // @ts-expect-error This property exists in Vitest browser mode
      !!globalThis.__vitest_browser__ || !!globalThis.window?.navigator?.userAgent?.match(/StorybookTestRunner/)
    );
  } catch {
    return false;
  }
}
n(Dt, "isTestEnvironment");
function _t2(e = true) {
  if (!("document" in globalThis && "createElement" in globalThis.document))
    return () => {
    };
  let t = document.createElement("style");
  t.textContent = `*, *:before, *:after {
    animation: none !important;
  }`, document.head.appendChild(t);
  let r = document.createElement("style");
  return r.textContent = `*, *:before, *:after {
    animation-delay: 0s !important;
    animation-direction: ${e ? "reverse" : "normal"} !important;
    animation-play-state: paused !important;
    transition: none !important;
  }`, document.head.appendChild(r), document.body.clientHeight, document.head.removeChild(t), () => {
    r.parentNode?.removeChild(r);
  };
}
n(_t2, "pauseAnimations");
async function Ht2(e) {
  if (!("document" in globalThis && "getAnimations" in globalThis.document && "querySelectorAll" in globalThis.document))
    return;
  let t = false;
  await Promise.race([
    // After 50ms, retrieve any running animations and wait for them to finish
    // If new animations are created while waiting, we'll wait for them too
    new Promise((r) => {
      setTimeout(() => {
        let o = [globalThis.document, ...Nt3(globalThis.document)], i2 = n(async () => {
          if (t || e?.aborted)
            return;
          let s = o.flatMap((a) => a?.getAnimations?.() || []).filter((a) => a.playState === "running" && !So2(a));
          s.length > 0 && (await Promise.all(s.map((a) => a.finished)), await i2());
        }, "checkAnimationsFinished");
        i2().then(r);
      }, 100);
    }),
    // If animations don't finish within the timeout, continue without waiting
    new Promise(
      (r) => setTimeout(() => {
        t = true, r(void 0);
      }, 5e3)
    )
  ]);
}
n(Ht2, "waitForAnimations");
function Nt3(e) {
  return [e, ...e.querySelectorAll("*")].reduce(
    (t, r) => ("shadowRoot" in r && r.shadowRoot && t.push(r.shadowRoot, ...Nt3(r.shadowRoot)), t),
    []
  );
}
n(Nt3, "getShadowRoots");
function So2(e) {
  if (e instanceof CSSAnimation && e.effect instanceof KeyframeEffect && e.effect.target) {
    let t = getComputedStyle(e.effect.target, e.effect.pseudoElement), r = t.animationName?.split(", ").indexOf(e.animationName);
    return t.animationIterationCount.split(", ")[r] === "infinite";
  }
  return false;
}
n(So2, "isInfiniteAnimation");
var Ge2 = class Ge3 {
  constructor() {
    this.reports = [];
  }
  async addReport(t) {
    this.reports.push(t);
  }
};
n(Ge2, "ReporterAPI");
var fe2 = Ge2;
var Ro2 = "ComposedStory";
var wo2 = "Unnamed Story";
var V = [];
function We2(e, t, r, o, i2) {
  if (e === void 0)
    throw new Error("Expected a story but received undefined.");
  t.title = t.title ?? Ro2;
  let s = kt2(t), a = i2 || e.storyName || e.story?.name || e.name || wo2, p = _e2(
    a,
    e,
    s
  ), c = te2(
    ne2([
      o ?? globalThis.globalProjectAnnotations ?? {},
      r ?? {}
    ])
  ), l = Ne2(
    p,
    s,
    c
  ), u = {
    ...vt2(c.globalTypes),
    ...c.initialGlobals,
    ...l.storyGlobals
  }, h = new fe2(), T = n(() => {
    let A2 = Ft2({
      hooks: new de2(),
      globals: u,
      args: { ...l.initialArgs },
      viewMode: "story",
      reporting: h,
      loaded: {},
      abortSignal: new AbortController().signal,
      step: n((S, v2) => l.runStep(S, v2, A2), "step"),
      canvasElement: null,
      canvas: {},
      userEvent: {},
      globalTypes: c.globalTypes,
      ...l,
      context: null,
      mount: null
    });
    return A2.parameters.__isPortableStory = true, A2.context = A2, l.renderToCanvas && (A2.renderToCanvas = async () => {
      let S = await l.renderToCanvas?.(
        {
          componentId: l.componentId,
          title: l.title,
          id: l.id,
          name: l.name,
          tags: l.tags,
          showMain: n(() => {
          }, "showMain"),
          showError: n((v2) => {
            throw new Error(`${v2.title}
${v2.description}`);
          }, "showError"),
          showException: n((v2) => {
            throw v2;
          }, "showException"),
          forceRemount: true,
          storyContext: A2,
          storyFn: n(() => l.unboundStoryFn(A2), "storyFn"),
          unboundStoryFn: l.unboundStoryFn
        },
        A2.canvasElement
      );
      S && V.push(S);
    }), A2.mount = l.mount(A2), A2;
  }, "initializeContext"), R, P2 = n(async (A2) => {
    let S = T();
    return S.canvasElement ??= globalThis?.document?.body, R && (S.loaded = R.loaded), Object.assign(S, A2), l.playFunction(S);
  }, "play"), L = n((A2) => {
    let S = T();
    return Object.assign(S, A2), Eo2(l, S);
  }, "run"), O = l.playFunction ? P2 : void 0;
  return Object.assign(
    n(function(S) {
      let v2 = T();
      return R && (v2.loaded = R.loaded), v2.args = {
        ...v2.initialArgs,
        ...S
      }, l.unboundStoryFn(v2);
    }, "storyFn"),
    {
      id: l.id,
      storyName: a,
      load: n(async () => {
        for (let S of [...V].reverse())
          await S();
        V.length = 0;
        let A2 = T();
        A2.loaded = await l.applyLoaders(A2), V.push(...(await l.applyBeforeEach(A2)).filter(Boolean)), R = A2;
      }, "load"),
      globals: u,
      args: l.initialArgs,
      parameters: l.parameters,
      argTypes: l.argTypes,
      play: O,
      run: L,
      reporting: h,
      tags: l.tags
    }
  );
}
n(We2, "composeStory");
async function Eo2(e, t) {
  for (let s of [...V].reverse())
    await s();
  if (V.length = 0, !t.canvasElement) {
    let s = document.createElement("div");
    globalThis?.document?.body?.appendChild(s), t.canvasElement = s, V.push(() => {
      globalThis?.document?.body?.contains(s) && globalThis?.document?.body?.removeChild(s);
    });
  }
  if (t.loaded = await e.applyLoaders(t), t.abortSignal.aborted)
    return;
  V.push(...(await e.applyBeforeEach(t)).filter(Boolean));
  let r = e.playFunction, o = e.usesMount;
  if (o || await t.mount(), t.abortSignal.aborted)
    return;
  r && (o || (t.mount = async () => {
    throw new import_preview_errors2.MountMustBeDestructuredError({ playFunction: r.toString() });
  }), await r(t));
  let i2;
  Dt() ? i2 = _t2() : await Ht2(t.abortSignal), await e.applyAfterEach(t), await i2?.();
}
n(Eo2, "runStory");
var Co2 = false;
var Ye2 = "Invariant failed";
function ye2(e, t) {
  if (!e) {
    if (Co2)
      throw new Error(Ye2);
    var r = typeof t == "function" ? t() : t, o = r ? "".concat(Ye2, ": ").concat(r) : Ye2;
    throw new Error(o);
  }
}
n(ye2, "invariant");
var Ke = {};
xt3(Ke, {
  argsEnhancers: () => Mo2
});
var Ve2 = "storybook/actions";
var Ia = `${Ve2}/panel`;
var jt2 = `${Ve2}/action-event`;
var La = `${Ve2}/action-clear`;
var Bt2 = {
  depth: 10,
  clearOnStoryChange: true,
  limit: 50
};
var Ut2 = n((e, t) => {
  let r = Object.getPrototypeOf(e);
  return !r || t(r) ? r : Ut2(r, t);
}, "findProto");
var Po2 = n((e) => !!(typeof e == "object" && e && Ut2(e, (t) => /^Synthetic(?:Base)?Event$/.test(t.constructor.name)) && typeof e.persist == "function"), "isReactSyntheticEvent");
var Oo2 = n((e) => {
  if (Po2(e)) {
    let t = Object.create(
      e.constructor.prototype,
      Object.getOwnPropertyDescriptors(e)
    );
    t.persist();
    let r = Object.getOwnPropertyDescriptor(t, "view"), o = r?.value;
    return typeof o == "object" && o?.constructor.name === "Window" && Object.defineProperty(t, "view", {
      ...r,
      value: Object.create(o.constructor.prototype)
    }), t;
  }
  return e;
}, "serializeArg");
function ie2(e, t = {}) {
  let r = {
    ...Bt2,
    ...t
  }, o = n(function(...s) {
    if (t.implicit) {
      let T = ("__STORYBOOK_PREVIEW__" in import_global7.global ? import_global7.global.__STORYBOOK_PREVIEW__ : void 0)?.storyRenders.find(
        (R) => R.phase === "playing" || R.phase === "rendering"
      );
      if (T) {
        let R = !globalThis?.FEATURES?.disallowImplicitActionsInRenderV8, P2 = new import_preview_errors3.ImplicitActionsDuringRendering({
          phase: T.phase,
          name: e,
          deprecated: R
        });
        if (R)
          console.warn(P2);
        else
          throw P2;
      }
    }
    let a = import_preview_api.addons.getChannel(), p = Date.now().toString(36) + Math.random().toString(36).substring(2), c = 5, l = s.map(Oo2), y = s.length > 1 ? l : l[0], u = {
      id: p,
      count: 0,
      data: { name: e, args: y },
      options: {
        ...r,
        maxDepth: c + (r.depth || 3)
      }
    };
    a.emit(jt2, u);
  }, "actionHandler");
  return o.isAction = true, o.implicit = t.implicit, o;
}
n(ie2, "action");
var Gt2 = n((e, t) => typeof t[e] > "u" && !(e in t), "isInInitialArgs");
var Wt2 = n((e) => {
  let {
    initialArgs: t,
    argTypes: r,
    id: o,
    parameters: { actions: i2 }
  } = e;
  if (!i2 || i2.disable || !i2.argTypesRegex || !r)
    return {};
  let s = new RegExp(i2.argTypesRegex);
  return Object.entries(r).filter(
    ([p]) => !!s.test(p)
  ).reduce((p, [c, l]) => (Gt2(c, t) && (p[c] = ie2(c, { implicit: true, id: o })), p), {});
}, "inferActionsFromArgTypesRegex");
var Yt2 = n((e) => {
  let {
    initialArgs: t,
    argTypes: r,
    parameters: { actions: o }
  } = e;
  return o?.disable || !r ? {} : Object.entries(r).filter(([s, a]) => !!a.action).reduce((s, [a, p]) => (Gt2(a, t) && (s[a] = ie2(typeof p.action == "string" ? p.action : a)), s), {});
}, "addActionsFromArgTypes");
var Mo2 = [
  Yt2,
  Wt2
];
var qe2 = {};
xt3(qe2, {
  loaders: () => Io2
});
var Vt2 = false;
var Fo2 = n((e) => {
  let { parameters: t } = e;
  t?.actions?.disable || Vt2 || ((0, import_test.onMockCall)((r, o) => {
    let i2 = r.getMockName();
    i2 !== "spy" && (!/^next\/.*::/.test(i2) || [
      "next/router::useRouter()",
      "next/navigation::useRouter()",
      "next/navigation::redirect",
      "next/cache::",
      "next/headers::cookies().set",
      "next/headers::cookies().delete",
      "next/headers::headers().set",
      "next/headers::headers().delete"
    ].some((s) => i2.startsWith(s))) && ie2(i2)(o);
  }), Vt2 = true);
}, "logActionsWhenMockCalled");
var Io2 = [Fo2];
var Xe2 = n(() => rc({
  ...Ke,
  ...qe2
}), "default");
var Do2 = "storybook/background";
var Z2 = "backgrounds";
var rp = {
  UPDATE: `${Do2}/update`
};
var Kt2 = {
  light: { name: "light", value: "#F8F8F8" },
  dark: { name: "dark", value: "#333" }
};
var { document: N } = globalThis;
var qt2 = n(() => globalThis?.matchMedia ? !!globalThis.matchMedia("(prefers-reduced-motion: reduce)")?.matches : false, "isReduceMotionEnabled");
var Ze2 = n((e) => {
  (Array.isArray(e) ? e : [e]).forEach(_o2);
}, "clearStyles");
var _o2 = n((e) => {
  if (!N)
    return;
  let t = N.getElementById(e);
  t && t.parentElement && t.parentElement.removeChild(t);
}, "clearStyle");
var Xt2 = n((e, t) => {
  if (!N)
    return;
  let r = N.getElementById(e);
  if (r)
    r.innerHTML !== t && (r.innerHTML = t);
  else {
    let o = N.createElement("style");
    o.setAttribute("id", e), o.innerHTML = t, N.head.appendChild(o);
  }
}, "addGridStyle");
var Zt2 = n((e, t, r) => {
  if (!N)
    return;
  let o = N.getElementById(e);
  if (o)
    o.innerHTML !== t && (o.innerHTML = t);
  else {
    let i2 = N.createElement("style");
    i2.setAttribute("id", e), i2.innerHTML = t;
    let s = `addon-backgrounds-grid${r ? `-docs-${r}` : ""}`, a = N.getElementById(s);
    a ? a.parentElement?.insertBefore(i2, a) : N.head.appendChild(i2);
  }
}, "addBackgroundStyle");
var Ho = {
  cellSize: 100,
  cellAmount: 10,
  opacity: 0.8
};
var Qt2 = "addon-backgrounds";
var er2 = "addon-backgrounds-grid";
var No2 = qt2() ? "" : "transition: background-color 0.3s;";
var tr2 = n((e, t) => {
  let { globals: r = {}, parameters: o = {}, viewMode: i2, id: s } = t, {
    options: a = Kt2,
    disable: p,
    grid: c = Ho
  } = o[Z2] || {}, l = r[Z2] || {}, y = typeof l == "string" ? l : l?.value, u = y ? a[y] : void 0, h = typeof u == "string" ? u : u?.value || "transparent", T = typeof l == "string" ? false : l.grid || false, R = !!u && !p, P2 = i2 === "docs" ? `#anchor--${s} .docs-story` : ".sb-show-main", L = i2 === "docs" ? `#anchor--${s} .docs-story` : ".sb-show-main", O = o.layout === void 0 || o.layout === "padded", F = i2 === "docs" ? 20 : O ? 16 : 0, { cellAmount: A2, cellSize: S, opacity: v2, offsetX: w2 = F, offsetY: d = F } = c, m = i2 === "docs" ? `${Qt2}-docs-${s}` : `${Qt2}-color`, f = i2 === "docs" ? s : null;
  (0, import_preview_api2.useEffect)(() => {
    let g = `
    ${P2} {
      background: ${h} !important;
      ${No2}
      }`;
    if (!R) {
      Ze2(m);
      return;
    }
    Zt2(m, g, f);
  }, [P2, m, f, R, h]);
  let x = i2 === "docs" ? `${er2}-docs-${s}` : `${er2}`;
  return (0, import_preview_api2.useEffect)(() => {
    if (!T) {
      Ze2(x);
      return;
    }
    let g = [
      `${S * A2}px ${S * A2}px`,
      `${S * A2}px ${S * A2}px`,
      `${S}px ${S}px`,
      `${S}px ${S}px`
    ].join(", "), E = `
        ${L} {
          background-size: ${g} !important;
          background-position: ${w2}px ${d}px, ${w2}px ${d}px, ${w2}px ${d}px, ${w2}px ${d}px !important;
          background-blend-mode: difference !important;
          background-image: linear-gradient(rgba(130, 130, 130, ${v2}) 1px, transparent 1px),
           linear-gradient(90deg, rgba(130, 130, 130, ${v2}) 1px, transparent 1px),
           linear-gradient(rgba(130, 130, 130, ${v2 / 2}) 1px, transparent 1px),
           linear-gradient(90deg, rgba(130, 130, 130, ${v2 / 2}) 1px, transparent 1px) !important;
        }
      `;
    Xt2(x, E);
  }, [A2, S, L, x, T, w2, d, v2]), e();
}, "withBackgroundAndGrid");
var Bo2 = globalThis.FEATURES?.backgrounds ? [tr2] : [];
var zo2 = {
  [Z2]: {
    grid: {
      cellSize: 20,
      opacity: 0.5,
      cellAmount: 5
    },
    disable: false
  }
};
var Uo2 = {
  [Z2]: { value: void 0, grid: false }
};
var Je2 = n(() => rc({
  decorators: Bo2,
  parameters: zo2,
  initialGlobals: Uo2
}), "default");
var { step: Yo2 } = Qs(
  {
    // It seems like the label is unused, but the instrumenter has access to it
    // The context will be bounded later in StoryRender, so that the user can write just:
    // await step("label", (context) => {
    //   // labeled step
    // });
    step: n(async (e, t, r) => t(r), "step")
  },
  { intercept: true }
);
var Qe2 = n(() => rc({
  parameters: {
    throwPlayFunctionExceptions: false
  },
  runStep: Yo2
}), "default");
var ge2 = "storybook/highlight";
var rr2 = `${ge2}/add`;
var or2 = `${ge2}/remove`;
var nr2 = `${ge2}/reset`;
var ir2 = `${ge2}/scroll-into-view`;
var et2 = 2147483647;
var B2 = 28;
var tt2 = {
  chevronLeft: [
    "M9.10355 10.1464C9.29882 10.3417 9.29882 10.6583 9.10355 10.8536C8.90829 11.0488 8.59171 11.0488 8.39645 10.8536L4.89645 7.35355C4.70118 7.15829 4.70118 6.84171 4.89645 6.64645L8.39645 3.14645C8.59171 2.95118 8.90829 2.95118 9.10355 3.14645C9.29882 3.34171 9.29882 3.65829 9.10355 3.85355L5.95711 7L9.10355 10.1464Z"
  ],
  chevronRight: [
    "M4.89645 10.1464C4.70118 10.3417 4.70118 10.6583 4.89645 10.8536C5.09171 11.0488 5.40829 11.0488 5.60355 10.8536L9.10355 7.35355C9.29882 7.15829 9.29882 6.84171 9.10355 6.64645L5.60355 3.14645C5.40829 2.95118 5.09171 2.95118 4.89645 3.14645C4.70118 3.34171 4.70118 3.65829 4.89645 3.85355L8.04289 7L4.89645 10.1464Z"
  ],
  info: [
    "M7 5.5a.5.5 0 01.5.5v4a.5.5 0 01-1 0V6a.5.5 0 01.5-.5zM7 4.5A.75.75 0 107 3a.75.75 0 000 1.5z",
    "M7 14A7 7 0 107 0a7 7 0 000 14zm0-1A6 6 0 107 1a6 6 0 000 12z"
  ],
  shareAlt: [
    "M2 1.004a1 1 0 00-1 1v10a1 1 0 001 1h10a1 1 0 001-1v-4.5a.5.5 0 00-1 0v4.5H2v-10h4.5a.5.5 0 000-1H2z",
    "M7.354 7.357L12 2.711v1.793a.5.5 0 001 0v-3a.5.5 0 00-.5-.5h-3a.5.5 0 100 1h1.793L6.646 6.65a.5.5 0 10.708.707z"
  ]
};
var Vo2 = "svg,path,rect,circle,line,polyline,polygon,ellipse,text".split(",");
var M2 = n((e, t = {}, r) => {
  let o = Vo2.includes(e) ? document.createElementNS("http://www.w3.org/2000/svg", e) : document.createElement(e);
  return Object.entries(t).forEach(([i2, s]) => {
    /[A-Z]/.test(i2) ? (i2 === "onClick" && (o.addEventListener("click", s), o.addEventListener("keydown", (a) => {
      (a.key === "Enter" || a.key === " ") && (a.preventDefault(), s());
    })), i2 === "onMouseEnter" && o.addEventListener("mouseenter", s), i2 === "onMouseLeave" && o.addEventListener("mouseleave", s)) : o.setAttribute(
      i2,
      s
    );
  }), r?.forEach((i2) => {
    if (!(i2 == null || i2 === false))
      try {
        o.appendChild(i2);
      } catch {
        o.appendChild(document.createTextNode(String(i2)));
      }
  }), o;
}, "createElement");
var ae2 = n((e) => tt2[e] && M2(
  "svg",
  { width: "14", height: "14", viewBox: "0 0 14 14", xmlns: "http://www.w3.org/2000/svg" },
  tt2[e].map(
    (t) => M2("path", {
      fill: "currentColor",
      "fill-rule": "evenodd",
      "clip-rule": "evenodd",
      d: t
    })
  )
), "createIcon");
var sr2 = n((e) => {
  if ("elements" in e) {
    let { elements: o, color: i2, style: s } = e;
    return {
      id: void 0,
      priority: 0,
      selectors: o,
      styles: {
        outline: `2px ${s} ${i2}`,
        outlineOffset: "2px",
        boxShadow: "0 0 0 6px rgba(255,255,255,0.6)"
      },
      menu: void 0
    };
  }
  let { menu: t, ...r } = e;
  return {
    id: void 0,
    priority: 0,
    styles: {
      outline: "2px dashed #029cfd"
    },
    ...r,
    menu: Array.isArray(t) ? t.every(Array.isArray) ? t : [t] : void 0
  };
}, "normalizeOptions");
var Ko2 = n((e) => e instanceof Function, "isFunction");
var se = /* @__PURE__ */ new Map();
var q = /* @__PURE__ */ new Map();
var he = /* @__PURE__ */ new Map();
var z2 = n((e) => {
  let t = Symbol();
  return q.set(t, []), se.set(t, e), { get: n(() => se.get(t), "get"), set: n((a) => {
    let p = se.get(t), c = Ko2(a) ? a(p) : a;
    c !== p && (se.set(t, c), q.get(t)?.forEach((l) => {
      he.get(l)?.(), he.set(l, l(c));
    }));
  }, "set"), subscribe: n((a) => (q.get(t)?.push(a), () => {
    let p = q.get(t);
    p && q.set(
      t,
      p.filter((c) => c !== a)
    );
  }), "subscribe"), teardown: n(() => {
    q.get(t)?.forEach((a) => {
      he.get(a)?.(), he.delete(a);
    }), q.delete(t), se.delete(t);
  }, "teardown") };
}, "useStore");
var rt2 = n((e) => {
  let t = document.getElementById("storybook-root"), r = /* @__PURE__ */ new Map();
  for (let o of e) {
    let { priority: i2 = 0 } = o;
    for (let s of o.selectors) {
      let a = [
        ...document.querySelectorAll(
          // Elements matching the selector, excluding storybook elements and their descendants.
          // Necessary to find portaled elements (e.g. children of `body`).
          `:is(${s}):not([id^="storybook-"], [id^="storybook-"] *, [class^="sb-"], [class^="sb-"] *)`
        ),
        // Elements matching the selector inside the storybook root, as these were excluded above.
        ...t?.querySelectorAll(s) || []
      ];
      for (let p of a) {
        let c = r.get(p);
        (!c || c.priority <= i2) && r.set(p, {
          ...o,
          priority: i2,
          selectors: Array.from(new Set((c?.selectors || []).concat(s)))
        });
      }
    }
  }
  return r;
}, "mapElements");
var ar = n((e) => Array.from(e.entries()).map(([t, {
  selectors: r,
  styles: o,
  hoverStyles: i2,
  focusStyles: s,
  menu: a
}]) => {
  let { top: p, left: c, width: l, height: y } = t.getBoundingClientRect(), { position: u } = getComputedStyle(t);
  return {
    element: t,
    selectors: r,
    styles: o,
    hoverStyles: i2,
    focusStyles: s,
    menu: a,
    top: u === "fixed" ? p : p + window.scrollY,
    left: u === "fixed" ? c : c + window.scrollX,
    width: l,
    height: y
  };
}).sort((t, r) => r.width * r.height - t.width * t.height), "mapBoxes");
var ot2 = n((e, t) => {
  let r = e.getBoundingClientRect(), { x: o, y: i2 } = t;
  return r?.top && r?.left && o >= r.left && o <= r.left + r.width && i2 >= r.top && i2 <= r.top + r.height;
}, "isOverMenu");
var nt2 = n((e, t, r) => {
  if (!t || !r)
    return false;
  let { left: o, top: i2, width: s, height: a } = e;
  a < B2 && (i2 = i2 - Math.round((B2 - a) / 2), a = B2), s < B2 && (o = o - Math.round((B2 - s) / 2), s = B2), t.style.position === "fixed" && (o += window.scrollX, i2 += window.scrollY);
  let { x: p, y: c } = r;
  return p >= o && p <= o + s && c >= i2 && c <= i2 + a;
}, "isTargeted");
var pr2 = n((e, t, r = {}) => {
  let { x: o, y: i2 } = t, { margin: s = 5, topOffset: a = 0, centered: p = false } = r, { scrollX: c, scrollY: l, innerHeight: y, innerWidth: u } = window, h = Math.min(
    e.style.position === "fixed" ? i2 - l : i2,
    y - e.clientHeight - s - a + l
  ), T = p ? e.clientWidth / 2 : 0, R = e.style.position === "fixed" ? Math.max(Math.min(o - c, u - T - s), T + s) : Math.max(
    Math.min(o, u - T - s + c),
    T + s + c
  );
  Object.assign(e.style, {
    ...R !== o && { left: `${R}px` },
    ...h !== i2 && { top: `${h}px` }
  });
}, "keepInViewport");
var it2 = n((e) => {
  window.HTMLElement.prototype.hasOwnProperty("showPopover") && e.showPopover();
}, "showPopover");
var lr2 = n((e) => {
  window.HTMLElement.prototype.hasOwnProperty("showPopover") && e.hidePopover();
}, "hidePopover");
var cr2 = n((e) => ({
  top: e.top,
  left: e.left,
  width: e.width,
  height: e.height,
  selectors: e.selectors,
  element: {
    attributes: Object.fromEntries(
      Array.from(e.element.attributes).map((t) => [t.name, t.value])
    ),
    localName: e.element.localName,
    tagName: e.element.tagName,
    outerHTML: e.element.outerHTML
  }
}), "getEventDetails");
var C = "storybook-highlights-menu";
var dr2 = "storybook-highlights-root";
var Xo2 = "storybook-root";
var mr2 = n((e) => {
  if (globalThis.__STORYBOOK_HIGHLIGHT_INITIALIZED)
    return;
  globalThis.__STORYBOOK_HIGHLIGHT_INITIALIZED = true;
  let { document: t } = globalThis, r = z2([]), o = z2(/* @__PURE__ */ new Map()), i2 = z2([]), s = z2(), a = z2(), p = z2([]), c = z2([]), l = z2(), y = z2(), u = t.getElementById(dr2);
  r.subscribe(() => {
    u || (u = M2("div", { id: dr2 }), t.body.appendChild(u));
  }), r.subscribe((d) => {
    let m = t.getElementById(Xo2);
    if (!m)
      return;
    o.set(rt2(d));
    let f = new MutationObserver(() => o.set(rt2(d)));
    return f.observe(m, { subtree: true, childList: true }), () => {
      f.disconnect();
    };
  }), o.subscribe((d) => {
    let m = n(() => requestAnimationFrame(() => i2.set(ar(d))), "updateBoxes"), f = new ResizeObserver(m);
    f.observe(t.body), Array.from(d.keys()).forEach((g) => f.observe(g));
    let x = Array.from(t.body.querySelectorAll("*")).filter((g) => {
      let { overflow: E, overflowX: I, overflowY: k } = window.getComputedStyle(g);
      return ["auto", "scroll"].some((H) => [E, I, k].includes(H));
    });
    return x.forEach((g) => g.addEventListener("scroll", m)), () => {
      f.disconnect(), x.forEach((g) => g.removeEventListener("scroll", m));
    };
  }), o.subscribe((d) => {
    let m = Array.from(d.keys()).filter(({ style: x }) => x.position === "sticky"), f = n(() => requestAnimationFrame(() => {
      i2.set(
        (x) => x.map((g) => {
          if (m.includes(g.element)) {
            let { top: E, left: I } = g.element.getBoundingClientRect();
            return { ...g, top: E + window.scrollY, left: I + window.scrollX };
          }
          return g;
        })
      );
    }), "updateBoxes");
    return t.addEventListener("scroll", f), () => t.removeEventListener("scroll", f);
  }), o.subscribe((d) => {
    p.set((m) => m.filter(({ element: f }) => d.has(f)));
  }), p.subscribe((d) => {
    d.length ? (y.set((m) => d.some((f) => f.element === m?.element) ? m : void 0), l.set((m) => d.some((f) => f.element === m?.element) ? m : void 0)) : (y.set(void 0), l.set(void 0), s.set(void 0));
  });
  let h = new Map(/* @__PURE__ */ new Map());
  r.subscribe((d) => {
    d.forEach(({ keyframes: m }) => {
      if (m) {
        let f = h.get(m);
        f || (f = t.createElement("style"), f.setAttribute("data-highlight", "keyframes"), h.set(m, f), t.head.appendChild(f)), f.innerHTML = m;
      }
    }), h.forEach((m, f) => {
      d.some((x) => x.keyframes === f) || (m.remove(), h.delete(f));
    });
  });
  let T = new Map(/* @__PURE__ */ new Map());
  i2.subscribe((d) => {
    d.forEach((m) => {
      let f = T.get(m.element);
      if (u && !f) {
        let x = {
          popover: "manual",
          "data-highlight-dimensions": `w${m.width.toFixed(0)}h${m.height.toFixed(0)}`,
          "data-highlight-coordinates": `x${m.left.toFixed(0)}y${m.top.toFixed(0)}`
        };
        f = u.appendChild(
          M2("div", x, [M2("div")])
        ), T.set(m.element, f);
      }
    }), T.forEach((m, f) => {
      d.some(({ element: x }) => x === f) || (m.remove(), T.delete(f));
    });
  }), i2.subscribe((d) => {
    let m = d.filter((x) => x.menu);
    if (!m.length)
      return;
    let f = n((x) => {
      requestAnimationFrame(() => {
        let g = t.getElementById(C), E = { x: x.pageX, y: x.pageY };
        if (g && !ot2(g, E)) {
          let I = m.filter((k) => {
            let H = T.get(k.element);
            return nt2(k, H, E);
          });
          s.set(I.length ? E : void 0), p.set(I);
        }
      });
    }, "onClick");
    return t.addEventListener("click", f), () => t.removeEventListener("click", f);
  });
  let R = n(() => {
    let d = t.getElementById(C), m = a.get();
    !m || d && ot2(d, m) || c.set((f) => {
      let x = i2.get().filter((k) => {
        let H = T.get(k.element);
        return nt2(k, H, m);
      }), g = f.filter((k) => x.includes(k)), E = x.filter((k) => !f.includes(k)), I = f.length - g.length;
      return E.length || I ? [...g, ...E] : f;
    });
  }, "updateHovered");
  a.subscribe(R), i2.subscribe(R);
  let P2 = n(() => {
    let d = y.get(), m = d ? [d] : p.get(), f = m.length === 1 ? m[0] : l.get(), x = s.get() !== void 0;
    i2.get().forEach((g) => {
      let E = T.get(g.element);
      if (E) {
        let I = f === g, k = x ? f ? I : m.includes(g) : c.get()?.includes(g);
        Object.assign(E.style, {
          animation: "none",
          background: "transparent",
          border: "none",
          boxSizing: "border-box",
          outline: "none",
          outlineOffset: "0px",
          ...g.styles,
          ...k ? g.hoverStyles : {},
          ...I ? g.focusStyles : {},
          position: getComputedStyle(g.element).position === "fixed" ? "fixed" : "absolute",
          zIndex: et2 - 10,
          top: `${g.top}px`,
          left: `${g.left}px`,
          width: `${g.width}px`,
          height: `${g.height}px`,
          margin: 0,
          padding: 0,
          cursor: g.menu && k ? "pointer" : "default",
          pointerEvents: g.menu ? "auto" : "none",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          overflow: "visible"
        }), Object.assign(E.children[0].style, {
          width: "100%",
          height: "100%",
          minHeight: `${B2}px`,
          minWidth: `${B2}px`,
          boxSizing: "content-box",
          padding: E.style.outlineWidth || "0px"
        }), it2(E);
      }
    });
  }, "updateBoxStyles");
  i2.subscribe(P2), p.subscribe(P2), c.subscribe(P2), l.subscribe(P2), y.subscribe(P2);
  let L = n(() => {
    if (!u)
      return;
    let d = t.getElementById(C);
    if (d)
      d.innerHTML = "";
    else {
      let g = { id: C, popover: "manual" };
      d = u.appendChild(M2("div", g)), u.appendChild(
        M2("style", {}, [
          `
            #${C} {
              position: absolute;
              z-index: ${et2};
              width: 300px;
              padding: 0px;
              margin: 15px 0 0 0;
              transform: translateX(-50%);
              font-family: "Nunito Sans", -apple-system, ".SFNSText-Regular", "San Francisco", BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif;
              font-size: 12px;
              background: white;
              border: none;
              border-radius: 6px;
              box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.05), 0 5px 15px 0 rgba(0, 0, 0, 0.1);
              color: #2E3438;
            }
            #${C} ul {
              list-style: none;
              margin: 0;
              padding: 0;
            }
            #${C} > ul {
              max-height: 300px;
              overflow-y: auto;
              padding: 4px 0;
            }
            #${C} li {
              padding: 0 4px;
              margin: 0;
            }
            #${C} li > :not(ul) {
              display: flex;
              padding: 8px;
              margin: 0;
              align-items: center;
              gap: 8px;
              border-radius: 4px;
            }
            #${C} button {
              width: 100%;
              border: 0;
              background: transparent;
              color: inherit;
              text-align: left;
              font-family: inherit;
              font-size: inherit;
            }
            #${C} button:focus-visible {
              outline-color: #029CFD;
            }
            #${C} button:hover {
              background: rgba(2, 156, 253, 0.07);
              color: #029CFD;
              cursor: pointer;
            }
            #${C} li code {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              line-height: 16px;
              font-size: 11px;
            }
            #${C} li svg {
              flex-shrink: 0;
              margin: 1px;
              color: #73828C;
            }
            #${C} li > button:hover svg, #${C} li > button:focus-visible svg {
              color: #029CFD;
            }
            #${C} .element-list li svg {
              display: none;
            }
            #${C} li.selectable svg, #${C} li.selected svg {
              display: block;
            }
            #${C} .menu-list {
              border-top: 1px solid rgba(38, 85, 115, 0.15);
            }
            #${C} .menu-list > li:not(:last-child) {
              padding-bottom: 4px;
              margin-bottom: 4px;
              border-bottom: 1px solid rgba(38, 85, 115, 0.15);
            }
            #${C} .menu-items, #${C} .menu-items li {
              padding: 0;
            }
            #${C} .menu-item {
              display: flex;
            }
            #${C} .menu-item-content {
              display: flex;
              flex-direction: column;
              flex-grow: 1;
            }
          `
        ])
      );
    }
    let m = y.get(), f = m ? [m] : p.get();
    if (f.length && (d.style.position = getComputedStyle(f[0].element).position === "fixed" ? "fixed" : "absolute", d.appendChild(
      M2(
        "ul",
        { class: "element-list" },
        f.map((g) => {
          let E = f.length > 1 && !!g.menu?.some(
            (H) => H.some(
              (X2) => !X2.selectors || X2.selectors.some((le2) => g.selectors.includes(le2))
            )
          ), I = E ? {
            class: "selectable",
            onClick: n(() => y.set(g), "onClick"),
            onMouseEnter: n(() => l.set(g), "onMouseEnter"),
            onMouseLeave: n(() => l.set(void 0), "onMouseLeave")
          } : m ? { class: "selected", onClick: n(() => y.set(void 0), "onClick") } : {}, k = E || m;
          return M2("li", I, [
            M2(k ? "button" : "div", k ? { type: "button" } : {}, [
              m ? ae2("chevronLeft") : null,
              M2("code", {}, [g.element.outerHTML]),
              E ? ae2("chevronRight") : null
            ])
          ]);
        })
      )
    )), y.get() || p.get().length === 1) {
      let g = y.get() || p.get()[0], E = g.menu?.filter(
        (I) => I.some(
          (k) => !k.selectors || k.selectors.some((H) => g.selectors.includes(H))
        )
      );
      E?.length && d.appendChild(
        M2(
          "ul",
          { class: "menu-list" },
          E.map(
            (I) => M2("li", {}, [
              M2(
                "ul",
                { class: "menu-items" },
                I.map(
                  ({ id: k, title: H, description: X2, iconLeft: le2, iconRight: gt2, clickEvent: ht2 }) => {
                    let we2 = ht2 && (() => e.emit(ht2, k, cr2(g)));
                    return M2("li", {}, [
                      M2(
                        we2 ? "button" : "div",
                        we2 ? { class: "menu-item", type: "button", onClick: we2 } : { class: "menu-item" },
                        [
                          le2 ? ae2(le2) : null,
                          M2("div", { class: "menu-item-content" }, [
                            M2(X2 ? "strong" : "span", {}, [H]),
                            X2 && M2("span", {}, [X2])
                          ]),
                          gt2 ? ae2(gt2) : null
                        ]
                      )
                    ]);
                  }
                )
              )
            ])
          )
        )
      );
    }
    let x = s.get();
    x ? (Object.assign(d.style, {
      display: "block",
      left: `${d.style.position === "fixed" ? x.x - window.scrollX : x.x}px`,
      top: `${d.style.position === "fixed" ? x.y - window.scrollY : x.y}px`
    }), it2(d), requestAnimationFrame(() => pr2(d, x, { topOffset: 15, centered: true }))) : (lr2(d), Object.assign(d.style, { display: "none" }));
  }, "renderMenu");
  p.subscribe(L), y.subscribe(L);
  let O = n((d) => {
    let m = sr2(d);
    r.set((f) => {
      let x = m.id ? f.filter((g) => g.id !== m.id) : f;
      return m.selectors?.length ? [...x, m] : x;
    });
  }, "addHighlight"), F = n((d) => {
    d && r.set((m) => m.filter((f) => f.id !== d));
  }, "removeHighlight"), A2 = n(() => {
    r.set([]), o.set(/* @__PURE__ */ new Map()), i2.set([]), s.set(void 0), a.set(void 0), p.set([]), c.set([]), l.set(void 0), y.set(void 0);
  }, "resetState"), S, v2 = n((d, m) => {
    let f = "scrollIntoView-highlight";
    clearTimeout(S), F(f);
    let x = t.querySelector(d);
    if (!x) {
      console.warn(`Cannot scroll into view: ${d} not found`);
      return;
    }
    x.scrollIntoView({ behavior: "smooth", block: "center", ...m });
    let g = `kf-${Math.random().toString(36).substring(2, 15)}`;
    r.set((E) => [
      ...E,
      {
        id: f,
        priority: 1e3,
        selectors: [d],
        styles: {
          outline: "2px solid #1EA7FD",
          outlineOffset: "-1px",
          animation: `${g} 3s linear forwards`
        },
        keyframes: `@keyframes ${g} {
          0% { outline: 2px solid #1EA7FD; }
          20% { outline: 2px solid #1EA7FD00; }
          40% { outline: 2px solid #1EA7FD; }
          60% { outline: 2px solid #1EA7FD00; }
          80% { outline: 2px solid #1EA7FD; }
          100% { outline: 2px solid #1EA7FD00; }
        }`
      }
    ]), S = setTimeout(() => F(f), 3500);
  }, "scrollIntoView"), w2 = n((d) => {
    requestAnimationFrame(() => a.set({ x: d.pageX, y: d.pageY }));
  }, "onMouseMove");
  t.body.addEventListener("mousemove", w2), e.on(rr2, O), e.on(or2, F), e.on(nr2, A2), e.on(ir2, v2), e.on(import_core_events3.STORY_RENDER_PHASE_CHANGED, ({ newPhase: d }) => {
    d === "loading" && A2();
  });
}, "useHighlights");
globalThis?.FEATURES?.highlight && import_preview_api3.addons?.ready && import_preview_api3.addons.ready().then(mr2);
var st3 = n(() => rc({}), "default");
var xe2 = "storybook/measure-addon";
var Hp = `${xe2}/tool`;
var fr2 = "measureEnabled";
var Np = {
  RESULT: `${xe2}/result`,
  REQUEST: `${xe2}/request`,
  CLEAR: `${xe2}/clear`
};
function yr2() {
  let e = import_global8.global.document.documentElement, t = Math.max(e.scrollHeight, e.offsetHeight);
  return { width: Math.max(e.scrollWidth, e.offsetWidth), height: t };
}
n(yr2, "getDocumentWidthAndHeight");
function Jo2() {
  let e = import_global8.global.document.createElement("canvas");
  e.id = "storybook-addon-measure";
  let t = e.getContext("2d");
  ye2(t != null);
  let { width: r, height: o } = yr2();
  return at2(e, t, { width: r, height: o }), e.style.position = "absolute", e.style.left = "0", e.style.top = "0", e.style.zIndex = "2147483647", e.style.pointerEvents = "none", import_global8.global.document.body.appendChild(e), { canvas: e, context: t, width: r, height: o };
}
n(Jo2, "createCanvas");
function at2(e, t, { width: r, height: o }) {
  e.style.width = `${r}px`, e.style.height = `${o}px`;
  let i2 = import_global8.global.window.devicePixelRatio;
  e.width = Math.floor(r * i2), e.height = Math.floor(o * i2), t.scale(i2, i2);
}
n(at2, "setCanvasWidthAndHeight");
var $ = {};
function gr2() {
  $.canvas || ($ = Jo2());
}
n(gr2, "init");
function hr2() {
  $.context && $.context.clearRect(0, 0, $.width ?? 0, $.height ?? 0);
}
n(hr2, "clear");
function xr2(e) {
  hr2(), e($.context);
}
n(xr2, "draw");
function br2() {
  ye2($.canvas, "Canvas should exist in the state."), ye2($.context, "Context should exist in the state."), at2($.canvas, $.context, {
    width: 0,
    height: 0
  });
  let { width: e, height: t } = yr2();
  at2($.canvas, $.context, { width: e, height: t }), $.width = e, $.height = t;
}
n(br2, "rescale");
function Tr2() {
  $.canvas && (hr2(), $.canvas.parentNode?.removeChild($.canvas), $ = {});
}
n(Tr2, "destroy");
var J = {
  margin: "#f6b26b",
  border: "#ffe599",
  padding: "#93c47d",
  content: "#6fa8dc",
  text: "#232020"
};
var G = 6;
function Sr2(e, { x: t, y: r, w: o, h: i2, r: s }) {
  t = t - o / 2, r = r - i2 / 2, o < 2 * s && (s = o / 2), i2 < 2 * s && (s = i2 / 2), e.beginPath(), e.moveTo(t + s, r), e.arcTo(t + o, r, t + o, r + i2, s), e.arcTo(t + o, r + i2, t, r + i2, s), e.arcTo(t, r + i2, t, r, s), e.arcTo(t, r, t + o, r, s), e.closePath();
}
n(Sr2, "roundedRect");
function Qo2(e, { padding: t, border: r, width: o, height: i2, top: s, left: a }) {
  let p = o - r.left - r.right - t.left - t.right, c = i2 - t.top - t.bottom - r.top - r.bottom, l = a + r.left + t.left, y = s + r.top + t.top;
  return e === "top" ? l += p / 2 : e === "right" ? (l += p, y += c / 2) : e === "bottom" ? (l += p / 2, y += c) : e === "left" ? y += c / 2 : e === "center" && (l += p / 2, y += c / 2), { x: l, y };
}
n(Qo2, "positionCoordinate");
function en2(e, t, { margin: r, border: o, padding: i2 }, s, a) {
  let p = n((h) => 0, "shift"), c = 0, l = 0, y = a ? 1 : 0.5, u = a ? s * 2 : 0;
  return e === "padding" ? p = n((h) => i2[h] * y + u, "shift") : e === "border" ? p = n((h) => i2[h] + o[h] * y + u, "shift") : e === "margin" && (p = n((h) => i2[h] + o[h] + r[h] * y + u, "shift")), t === "top" ? l = -p("top") : t === "right" ? c = p("right") : t === "bottom" ? l = p("bottom") : t === "left" && (c = -p("left")), { offsetX: c, offsetY: l };
}
n(en2, "offset");
function tn2(e, t) {
  return Math.abs(e.x - t.x) < Math.abs(e.w + t.w) / 2 && Math.abs(e.y - t.y) < Math.abs(e.h + t.h) / 2;
}
n(tn2, "collide");
function rn2(e, t, r) {
  return e === "top" ? t.y = r.y - r.h - G : e === "right" ? t.x = r.x + r.w / 2 + G + t.w / 2 : e === "bottom" ? t.y = r.y + r.h + G : e === "left" && (t.x = r.x - r.w / 2 - G - t.w / 2), { x: t.x, y: t.y };
}
n(rn2, "overlapAdjustment");
function Ar2(e, t, { x: r, y: o, w: i2, h: s }, a) {
  return Sr2(e, { x: r, y: o, w: i2, h: s, r: 3 }), e.fillStyle = `${J[t]}dd`, e.fill(), e.strokeStyle = J[t], e.stroke(), e.fillStyle = J.text, e.fillText(a, r, o), Sr2(e, { x: r, y: o, w: i2, h: s, r: 3 }), e.fillStyle = `${J[t]}dd`, e.fill(), e.strokeStyle = J[t], e.stroke(), e.fillStyle = J.text, e.fillText(a, r, o), { x: r, y: o, w: i2, h: s };
}
n(Ar2, "textWithRect");
function Rr2(e, t) {
  e.font = "600 12px monospace", e.textBaseline = "middle", e.textAlign = "center";
  let r = e.measureText(t), o = r.actualBoundingBoxAscent + r.actualBoundingBoxDescent, i2 = r.width + G * 2, s = o + G * 2;
  return { w: i2, h: s };
}
n(Rr2, "configureText");
function on2(e, t, { type: r, position: o = "center", text: i2 }, s, a = false) {
  let { x: p, y: c } = Qo2(o, t), { offsetX: l, offsetY: y } = en2(r, o, t, G + 1, a);
  p += l, c += y;
  let { w: u, h } = Rr2(e, i2);
  if (s && tn2({ x: p, y: c, w: u, h }, s)) {
    let T = rn2(o, { x: p, y: c, w: u, h }, s);
    p = T.x, c = T.y;
  }
  return Ar2(e, r, { x: p, y: c, w: u, h }, i2);
}
n(on2, "drawLabel");
function nn2(e, { w: t, h: r }) {
  let o = t * 0.5 + G, i2 = r * 0.5 + G;
  return {
    offsetX: (e.x === "left" ? -1 : 1) * o,
    offsetY: (e.y === "top" ? -1 : 1) * i2
  };
}
n(nn2, "floatingOffset");
function sn2(e, t, { type: r, text: o }) {
  let { floatingAlignment: i2, extremities: s } = t, a = s[i2.x], p = s[i2.y], { w: c, h: l } = Rr2(e, o), { offsetX: y, offsetY: u } = nn2(i2, {
    w: c,
    h: l
  });
  return a += y, p += u, Ar2(e, r, { x: a, y: p, w: c, h: l }, o);
}
n(sn2, "drawFloatingLabel");
function pe2(e, t, r, o) {
  let i2 = [];
  r.forEach((s, a) => {
    let p = o && s.position === "center" ? sn2(e, t, s) : on2(e, t, s, i2[a - 1], o);
    i2[a] = p;
  });
}
n(pe2, "drawStack");
function wr2(e, t, r, o) {
  let i2 = r.reduce((s, a) => (Object.prototype.hasOwnProperty.call(s, a.position) || (s[a.position] = []), s[a.position]?.push(a), s), {});
  i2.top && pe2(e, t, i2.top, o), i2.right && pe2(e, t, i2.right, o), i2.bottom && pe2(e, t, i2.bottom, o), i2.left && pe2(e, t, i2.left, o), i2.center && pe2(e, t, i2.center, o);
}
n(wr2, "labelStacks");
var Te2 = {
  margin: "#f6b26ba8",
  border: "#ffe599a8",
  padding: "#93c47d8c",
  content: "#6fa8dca8"
};
var Er2 = 30;
function _(e) {
  return parseInt(e.replace("px", ""), 10);
}
n(_, "pxToNumber");
function Q(e) {
  return Number.isInteger(e) ? e : e.toFixed(2);
}
n(Q, "round");
function pt2(e) {
  return e.filter((t) => t.text !== 0 && t.text !== "0");
}
n(pt2, "filterZeroValues");
function an2(e) {
  let t = {
    top: import_global9.global.window.scrollY,
    bottom: import_global9.global.window.scrollY + import_global9.global.window.innerHeight,
    left: import_global9.global.window.scrollX,
    right: import_global9.global.window.scrollX + import_global9.global.window.innerWidth
  }, r = {
    top: Math.abs(t.top - e.top),
    bottom: Math.abs(t.bottom - e.bottom),
    left: Math.abs(t.left - e.left),
    right: Math.abs(t.right - e.right)
  };
  return {
    x: r.left > r.right ? "left" : "right",
    y: r.top > r.bottom ? "top" : "bottom"
  };
}
n(an2, "floatingAlignment");
function pn2(e) {
  let t = import_global9.global.getComputedStyle(e), { top: r, left: o, right: i2, bottom: s, width: a, height: p } = e.getBoundingClientRect(), {
    marginTop: c,
    marginBottom: l,
    marginLeft: y,
    marginRight: u,
    paddingTop: h,
    paddingBottom: T,
    paddingLeft: R,
    paddingRight: P2,
    borderBottomWidth: L,
    borderTopWidth: O,
    borderLeftWidth: F,
    borderRightWidth: A2
  } = t;
  r = r + import_global9.global.window.scrollY, o = o + import_global9.global.window.scrollX, s = s + import_global9.global.window.scrollY, i2 = i2 + import_global9.global.window.scrollX;
  let S = {
    top: _(c),
    bottom: _(l),
    left: _(y),
    right: _(u)
  }, v2 = {
    top: _(h),
    bottom: _(T),
    left: _(R),
    right: _(P2)
  }, w2 = {
    top: _(O),
    bottom: _(L),
    left: _(F),
    right: _(A2)
  }, d = {
    top: r - S.top,
    bottom: s + S.bottom,
    left: o - S.left,
    right: i2 + S.right
  };
  return {
    margin: S,
    padding: v2,
    border: w2,
    top: r,
    left: o,
    bottom: s,
    right: i2,
    width: a,
    height: p,
    extremities: d,
    floatingAlignment: an2(d)
  };
}
n(pn2, "measureElement");
function ln2(e, { margin: t, width: r, height: o, top: i2, left: s, bottom: a, right: p }) {
  let c = o + t.bottom + t.top;
  e.fillStyle = Te2.margin, e.fillRect(s, i2 - t.top, r, t.top), e.fillRect(p, i2 - t.top, t.right, c), e.fillRect(s, a, r, t.bottom), e.fillRect(
    s - t.left,
    i2 - t.top,
    t.left,
    c
  );
  let l = [
    {
      type: "margin",
      text: Q(t.top),
      position: "top"
    },
    {
      type: "margin",
      text: Q(t.right),
      position: "right"
    },
    {
      type: "margin",
      text: Q(t.bottom),
      position: "bottom"
    },
    {
      type: "margin",
      text: Q(t.left),
      position: "left"
    }
  ];
  return pt2(l);
}
n(ln2, "drawMargin");
function cn2(e, { padding: t, border: r, width: o, height: i2, top: s, left: a, bottom: p, right: c }) {
  let l = o - r.left - r.right, y = i2 - t.top - t.bottom - r.top - r.bottom;
  e.fillStyle = Te2.padding, e.fillRect(a + r.left, s + r.top, l, t.top), e.fillRect(
    c - t.right - r.right,
    s + t.top + r.top,
    t.right,
    y
  ), e.fillRect(
    a + r.left,
    p - t.bottom - r.bottom,
    l,
    t.bottom
  ), e.fillRect(a + r.left, s + t.top + r.top, t.left, y);
  let u = [
    {
      type: "padding",
      text: t.top,
      position: "top"
    },
    {
      type: "padding",
      text: t.right,
      position: "right"
    },
    {
      type: "padding",
      text: t.bottom,
      position: "bottom"
    },
    {
      type: "padding",
      text: t.left,
      position: "left"
    }
  ];
  return pt2(u);
}
n(cn2, "drawPadding");
function dn2(e, { border: t, width: r, height: o, top: i2, left: s, bottom: a, right: p }) {
  let c = o - t.top - t.bottom;
  e.fillStyle = Te2.border, e.fillRect(s, i2, r, t.top), e.fillRect(s, a - t.bottom, r, t.bottom), e.fillRect(s, i2 + t.top, t.left, c), e.fillRect(
    p - t.right,
    i2 + t.top,
    t.right,
    c
  );
  let l = [
    {
      type: "border",
      text: t.top,
      position: "top"
    },
    {
      type: "border",
      text: t.right,
      position: "right"
    },
    {
      type: "border",
      text: t.bottom,
      position: "bottom"
    },
    {
      type: "border",
      text: t.left,
      position: "left"
    }
  ];
  return pt2(l);
}
n(dn2, "drawBorder");
function mn2(e, { padding: t, border: r, width: o, height: i2, top: s, left: a }) {
  let p = o - r.left - r.right - t.left - t.right, c = i2 - t.top - t.bottom - r.top - r.bottom;
  return e.fillStyle = Te2.content, e.fillRect(
    a + r.left + t.left,
    s + r.top + t.top,
    p,
    c
  ), [
    {
      type: "content",
      position: "center",
      text: `${Q(p)} x ${Q(c)}`
    }
  ];
}
n(mn2, "drawContent");
function un2(e) {
  return (t) => {
    if (e && t) {
      let r = pn2(e), o = ln2(t, r), i2 = cn2(t, r), s = dn2(t, r), a = mn2(t, r), p = r.width <= Er2 * 3 || r.height <= Er2;
      wr2(
        t,
        r,
        [...a, ...i2, ...s, ...o],
        p
      );
    }
  };
}
n(un2, "drawBoxModel");
function Cr2(e) {
  xr2(un2(e));
}
n(Cr2, "drawSelectedElement");
var vr2 = n((e, t) => {
  let r = import_global10.global.document.elementFromPoint(e, t), o = n((s) => {
    if (s && s.shadowRoot) {
      let a = s.shadowRoot.elementFromPoint(e, t);
      return s.isEqualNode(a) ? s : a.shadowRoot ? o(a) : a;
    }
    return s;
  }, "crawlShadows");
  return o(r) || r;
}, "deepElementFromPoint");
var Pr2;
var Se2 = { x: 0, y: 0 };
function Or2(e, t) {
  Pr2 = vr2(e, t), Cr2(Pr2);
}
n(Or2, "findAndDrawElement");
var Mr2 = n((e, t) => {
  let { measureEnabled: r } = t.globals || {};
  return (0, import_preview_api4.useEffect)(() => {
    if (typeof globalThis.document > "u")
      return;
    let o = n((i2) => {
      window.requestAnimationFrame(() => {
        i2.stopPropagation(), Se2.x = i2.clientX, Se2.y = i2.clientY;
      });
    }, "onPointerMove");
    return globalThis.document.addEventListener("pointermove", o), () => {
      globalThis.document.removeEventListener("pointermove", o);
    };
  }, []), (0, import_preview_api4.useEffect)(() => {
    let o = n((s) => {
      window.requestAnimationFrame(() => {
        s.stopPropagation(), Or2(s.clientX, s.clientY);
      });
    }, "onPointerOver"), i2 = n(() => {
      window.requestAnimationFrame(() => {
        br2();
      });
    }, "onResize");
    return t.viewMode === "story" && r && (globalThis.document.addEventListener("pointerover", o), gr2(), globalThis.window.addEventListener(
      "resize",
      i2
    ), Or2(Se2.x, Se2.y)), () => {
      globalThis.window.removeEventListener("resize", i2), Tr2();
    };
  }, [r, t.viewMode]), e();
}, "withMeasure");
var gn2 = globalThis.FEATURES?.measure ? [Mr2] : [];
var hn2 = {
  [fr2]: false
};
var lt2 = n(() => rc({
  decorators: gn2,
  initialGlobals: hn2
}), "default");
var Ae2 = "outline";
var ct2 = n((e) => {
  (Array.isArray(e) ? e : [e]).forEach(xn2);
}, "clearStyles");
var xn2 = n((e) => {
  let t = typeof e == "string" ? e : e.join(""), r = import_global11.global.document.getElementById(t);
  r && r.parentElement && r.parentElement.removeChild(r);
}, "clearStyle");
var $r2 = n((e, t) => {
  let r = import_global11.global.document.getElementById(e);
  if (r)
    r.innerHTML !== t && (r.innerHTML = t);
  else {
    let o = import_global11.global.document.createElement("style");
    o.setAttribute("id", e), o.innerHTML = t, import_global11.global.document.head.appendChild(o);
  }
}, "addOutlineStyles");
function dt2(e) {
  return W`
    ${e} body {
      outline: 1px solid #2980b9 !important;
    }

    ${e} article {
      outline: 1px solid #3498db !important;
    }

    ${e} nav {
      outline: 1px solid #0088c3 !important;
    }

    ${e} aside {
      outline: 1px solid #33a0ce !important;
    }

    ${e} section {
      outline: 1px solid #66b8da !important;
    }

    ${e} header {
      outline: 1px solid #99cfe7 !important;
    }

    ${e} footer {
      outline: 1px solid #cce7f3 !important;
    }

    ${e} h1 {
      outline: 1px solid #162544 !important;
    }

    ${e} h2 {
      outline: 1px solid #314e6e !important;
    }

    ${e} h3 {
      outline: 1px solid #3e5e85 !important;
    }

    ${e} h4 {
      outline: 1px solid #449baf !important;
    }

    ${e} h5 {
      outline: 1px solid #c7d1cb !important;
    }

    ${e} h6 {
      outline: 1px solid #4371d0 !important;
    }

    ${e} main {
      outline: 1px solid #2f4f90 !important;
    }

    ${e} address {
      outline: 1px solid #1a2c51 !important;
    }

    ${e} div {
      outline: 1px solid #036cdb !important;
    }

    ${e} p {
      outline: 1px solid #ac050b !important;
    }

    ${e} hr {
      outline: 1px solid #ff063f !important;
    }

    ${e} pre {
      outline: 1px solid #850440 !important;
    }

    ${e} blockquote {
      outline: 1px solid #f1b8e7 !important;
    }

    ${e} ol {
      outline: 1px solid #ff050c !important;
    }

    ${e} ul {
      outline: 1px solid #d90416 !important;
    }

    ${e} li {
      outline: 1px solid #d90416 !important;
    }

    ${e} dl {
      outline: 1px solid #fd3427 !important;
    }

    ${e} dt {
      outline: 1px solid #ff0043 !important;
    }

    ${e} dd {
      outline: 1px solid #e80174 !important;
    }

    ${e} figure {
      outline: 1px solid #ff00bb !important;
    }

    ${e} figcaption {
      outline: 1px solid #bf0032 !important;
    }

    ${e} table {
      outline: 1px solid #00cc99 !important;
    }

    ${e} caption {
      outline: 1px solid #37ffc4 !important;
    }

    ${e} thead {
      outline: 1px solid #98daca !important;
    }

    ${e} tbody {
      outline: 1px solid #64a7a0 !important;
    }

    ${e} tfoot {
      outline: 1px solid #22746b !important;
    }

    ${e} tr {
      outline: 1px solid #86c0b2 !important;
    }

    ${e} th {
      outline: 1px solid #a1e7d6 !important;
    }

    ${e} td {
      outline: 1px solid #3f5a54 !important;
    }

    ${e} col {
      outline: 1px solid #6c9a8f !important;
    }

    ${e} colgroup {
      outline: 1px solid #6c9a9d !important;
    }

    ${e} button {
      outline: 1px solid #da8301 !important;
    }

    ${e} datalist {
      outline: 1px solid #c06000 !important;
    }

    ${e} fieldset {
      outline: 1px solid #d95100 !important;
    }

    ${e} form {
      outline: 1px solid #d23600 !important;
    }

    ${e} input {
      outline: 1px solid #fca600 !important;
    }

    ${e} keygen {
      outline: 1px solid #b31e00 !important;
    }

    ${e} label {
      outline: 1px solid #ee8900 !important;
    }

    ${e} legend {
      outline: 1px solid #de6d00 !important;
    }

    ${e} meter {
      outline: 1px solid #e8630c !important;
    }

    ${e} optgroup {
      outline: 1px solid #b33600 !important;
    }

    ${e} option {
      outline: 1px solid #ff8a00 !important;
    }

    ${e} output {
      outline: 1px solid #ff9619 !important;
    }

    ${e} progress {
      outline: 1px solid #e57c00 !important;
    }

    ${e} select {
      outline: 1px solid #e26e0f !important;
    }

    ${e} textarea {
      outline: 1px solid #cc5400 !important;
    }

    ${e} details {
      outline: 1px solid #33848f !important;
    }

    ${e} summary {
      outline: 1px solid #60a1a6 !important;
    }

    ${e} command {
      outline: 1px solid #438da1 !important;
    }

    ${e} menu {
      outline: 1px solid #449da6 !important;
    }

    ${e} del {
      outline: 1px solid #bf0000 !important;
    }

    ${e} ins {
      outline: 1px solid #400000 !important;
    }

    ${e} img {
      outline: 1px solid #22746b !important;
    }

    ${e} iframe {
      outline: 1px solid #64a7a0 !important;
    }

    ${e} embed {
      outline: 1px solid #98daca !important;
    }

    ${e} object {
      outline: 1px solid #00cc99 !important;
    }

    ${e} param {
      outline: 1px solid #37ffc4 !important;
    }

    ${e} video {
      outline: 1px solid #6ee866 !important;
    }

    ${e} audio {
      outline: 1px solid #027353 !important;
    }

    ${e} source {
      outline: 1px solid #012426 !important;
    }

    ${e} canvas {
      outline: 1px solid #a2f570 !important;
    }

    ${e} track {
      outline: 1px solid #59a600 !important;
    }

    ${e} map {
      outline: 1px solid #7be500 !important;
    }

    ${e} area {
      outline: 1px solid #305900 !important;
    }

    ${e} a {
      outline: 1px solid #ff62ab !important;
    }

    ${e} em {
      outline: 1px solid #800b41 !important;
    }

    ${e} strong {
      outline: 1px solid #ff1583 !important;
    }

    ${e} i {
      outline: 1px solid #803156 !important;
    }

    ${e} b {
      outline: 1px solid #cc1169 !important;
    }

    ${e} u {
      outline: 1px solid #ff0430 !important;
    }

    ${e} s {
      outline: 1px solid #f805e3 !important;
    }

    ${e} small {
      outline: 1px solid #d107b2 !important;
    }

    ${e} abbr {
      outline: 1px solid #4a0263 !important;
    }

    ${e} q {
      outline: 1px solid #240018 !important;
    }

    ${e} cite {
      outline: 1px solid #64003c !important;
    }

    ${e} dfn {
      outline: 1px solid #b4005a !important;
    }

    ${e} sub {
      outline: 1px solid #dba0c8 !important;
    }

    ${e} sup {
      outline: 1px solid #cc0256 !important;
    }

    ${e} time {
      outline: 1px solid #d6606d !important;
    }

    ${e} code {
      outline: 1px solid #e04251 !important;
    }

    ${e} kbd {
      outline: 1px solid #5e001f !important;
    }

    ${e} samp {
      outline: 1px solid #9c0033 !important;
    }

    ${e} var {
      outline: 1px solid #d90047 !important;
    }

    ${e} mark {
      outline: 1px solid #ff0053 !important;
    }

    ${e} bdi {
      outline: 1px solid #bf3668 !important;
    }

    ${e} bdo {
      outline: 1px solid #6f1400 !important;
    }

    ${e} ruby {
      outline: 1px solid #ff7b93 !important;
    }

    ${e} rt {
      outline: 1px solid #ff2f54 !important;
    }

    ${e} rp {
      outline: 1px solid #803e49 !important;
    }

    ${e} span {
      outline: 1px solid #cc2643 !important;
    }

    ${e} br {
      outline: 1px solid #db687d !important;
    }

    ${e} wbr {
      outline: 1px solid #db175b !important;
    }`;
}
n(dt2, "outlineCSS");
var Fr2 = n((e, t) => {
  let r = t.globals || {}, o = [true, "true"].includes(r[Ae2]), i2 = t.viewMode === "docs", s = (0, import_preview_api5.useMemo)(() => dt2(i2 ? '[data-story-block="true"]' : ".sb-show-main"), [t]);
  return (0, import_preview_api5.useEffect)(() => {
    let a = i2 ? `addon-outline-docs-${t.id}` : "addon-outline";
    return o ? $r2(a, s) : ct2(a), () => {
      ct2(a);
    };
  }, [o, s, t]), e();
}, "withOutline");
var An2 = globalThis.FEATURES?.outline ? [Fr2] : [];
var Rn2 = {
  [Ae2]: false
};
var mt2 = n(() => rc({ decorators: An2, initialGlobals: Rn2 }), "default");
var Fn2 = n(({ parameters: e }) => {
  e?.test?.mockReset === true ? (0, import_test2.resetAllMocks)() : e?.test?.clearMocks === true ? (0, import_test2.clearAllMocks)() : e?.test?.restoreMocks !== false && (0, import_test2.restoreAllMocks)();
}, "resetAllMocksLoader");
var ut2 = n((e, t = 0, r) => {
  if (t > 5 || e == null)
    return e;
  if ((0, import_test2.isMockFunction)(e))
    return r && e.mockName(r), e;
  if (typeof e == "function" && "isAction" in e && e.isAction && !("implicit" in e && e.implicit)) {
    let o = (0, import_test2.fn)(e);
    return r && o.mockName(r), o;
  }
  if (Array.isArray(e)) {
    t++;
    for (let o = 0; o < e.length; o++)
      Object.getOwnPropertyDescriptor(e, o)?.writable && (e[o] = ut2(e[o], t));
    return e;
  }
  if (typeof e == "object" && e.constructor === Object) {
    t++;
    for (let [o, i2] of Object.entries(e))
      Object.getOwnPropertyDescriptor(e, o)?.writable && (e[o] = ut2(i2, t, o));
    return e;
  }
  return e;
}, "traverseArgs");
var In2 = n(({ initialArgs: e }) => {
  ut2(e);
}, "nameSpiesAndWrapActionsInSpies");
var Ir2 = false;
var Ln2 = n(async (e) => {
  globalThis.HTMLElement && e.canvasElement instanceof globalThis.HTMLElement && (e.canvas = (0, import_test2.within)(e.canvasElement));
  let t = globalThis.window?.navigator?.clipboard;
  if (t) {
    e.userEvent = Qs(
      { userEvent: import_test2.uninstrumentedUserEvent.setup() },
      { intercept: true }
    ).userEvent, Object.defineProperty(globalThis.window.navigator, "clipboard", {
      get: n(() => t, "get"),
      configurable: true
    });
    let r = HTMLElement.prototype.focus;
    Ir2 || Object.defineProperties(HTMLElement.prototype, {
      focus: {
        configurable: true,
        set: n((o) => {
          r = o, Ir2 = true;
        }, "set"),
        get: n(() => r, "get")
      }
    });
  }
}, "enhanceContext");
var ft2 = n(() => rc({
  loaders: [Fn2, In2, Ln2]
}), "default");
var Lr2 = "storybook/viewport";
var Dr2 = "viewport";
var Ll = `${Lr2}/panel`;
var Dl = `${Lr2}/tool`;
var _n2 = {
  [Dr2]: { value: void 0, isRotated: false }
};
var yt2 = n(() => rc({
  initialGlobals: _n2
}), "default");
function _r2() {
  return [
    // @ts-expect-error CJS fallback
    (lt2.default ?? lt2)(),
    // @ts-expect-error CJS fallback
    (Je2.default ?? Je2)(),
    // @ts-expect-error CJS fallback
    (st3.default ?? st3)(),
    // @ts-expect-error CJS fallback
    (mt2.default ?? mt2)(),
    // @ts-expect-error CJS fallback
    (yt2.default ?? yt2)(),
    // @ts-expect-error CJS fallback
    (Xe2.default ?? Xe2)(),
    // @ts-expect-error CJS fallback
    (Qe2.default ?? Qe2)(),
    // @ts-expect-error CJS fallback
    (ft2.default ?? ft2)()
  ];
}
n(_r2, "getCoreAnnotations");
function tc(e) {
  let t, r = {
    _tag: "Preview",
    input: e,
    get composed() {
      if (t)
        return t;
      let { addons: o, ...i2 } = e;
      return t = te2(
        ne2([..._r2(), ...o ?? [], i2])
      ), t;
    },
    meta(o) {
      return Nn2(o, this);
    }
  };
  return globalThis.globalProjectAnnotations = r.composed, r;
}
n(tc, "definePreview");
function rc(e) {
  return e;
}
n(rc, "definePreviewAddon");
function oc(e) {
  return e != null && typeof e == "object" && "_tag" in e && e?._tag === "Preview";
}
n(oc, "isPreview");
function nc(e) {
  return e != null && typeof e == "object" && "_tag" in e && e?._tag === "Meta";
}
n(nc, "isMeta");
function Nn2(e, t) {
  return {
    _tag: "Meta",
    input: e,
    preview: t,
    get composed() {
      throw new Error("Not implemented");
    },
    // @ts-expect-error hard
    story(r = {}) {
      return Hr2(typeof r == "function" ? { render: r } : r, this);
    }
  };
}
n(Nn2, "defineMeta");
function ic(e) {
  return e != null && typeof e == "object" && "_tag" in e && e?._tag === "Story";
}
n(ic, "isStory");
function Hr2(e, t) {
  let r, o = n(() => (r || (r = We2(
    e,
    t.input,
    void 0,
    t.preview.composed
  )), r), "compose");
  return {
    _tag: "Story",
    input: e,
    meta: t,
    __compose: o,
    get composed() {
      let i2 = o(), { args: s, argTypes: a, parameters: p, id: c, tags: l, globals: y, storyName: u } = i2;
      return { args: s, argTypes: a, parameters: p, id: c, tags: l, name: u, globals: y };
    },
    get play() {
      return e.play ?? t.input?.play ?? (async () => {
      });
    },
    get run() {
      return o().run ?? (async () => {
      });
    },
    extend(i2) {
      return Hr2(
        {
          ...this.input,
          ...i2,
          args: { ...this.input.args, ...i2.args },
          argTypes: D2(this.input.argTypes, i2.argTypes),
          afterEach: [
            ...b(this.input?.afterEach ?? []),
            ...b(i2.afterEach ?? [])
          ],
          beforeEach: [
            ...b(this.input?.beforeEach ?? []),
            ...b(i2.beforeEach ?? [])
          ],
          decorators: [
            ...b(this.input?.decorators ?? []),
            ...b(i2.decorators ?? [])
          ],
          globals: { ...this.input.globals, ...i2.globals },
          loaders: [
            ...b(this.input?.loaders ?? []),
            ...b(i2.loaders ?? [])
          ],
          parameters: D2(this.input.parameters, i2.parameters),
          tags: uc(...this.input.tags ?? [], ...i2.tags ?? [])
        },
        this.meta
      );
    }
  };
}
n(Hr2, "defineStory");
var jn2 = n((e) => e.toLowerCase().replace(/[ ’–—―′¿'`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi, "-").replace(
  /-+/g,
  "-"
).replace(/^-+/, "").replace(/-+$/, ""), "sanitize");
var Nr2 = n((e, t) => {
  let r = jn2(e);
  if (r === "")
    throw new Error(`Invalid ${t} '${e}', must include alphanumeric characters`);
  return r;
}, "sanitizeSafe");
var lc = n((e, t) => `${Nr2(e, "kind")}${t ? `--${Nr2(t, "name")}` : ""}`, "toId");
var cc = n((e) => bt2(
  e
), "storyNameFromExport");
function jr2(e, t) {
  return Array.isArray(t) ? t.includes(e) : e.match(t);
}
n(jr2, "matches");
function dc(e, { includeStories: t, excludeStories: r }) {
  return (
    // https://babeljs.io/docs/en/babel-plugin-transform-modules-commonjs
    e !== "__esModule" && (!t || jr2(e, t)) && (!r || !jr2(e, r))
  );
}
n(dc, "isExportStory");
var mc = n((e, { rootSeparator: t, groupSeparator: r }) => {
  let [o, i2] = e.split(t, 2), s = (i2 || e).split(r).filter((a) => !!a);
  return {
    root: i2 ? o : null,
    groups: s
  };
}, "parseKind");
var uc = n((...e) => {
  let t = e.reduce((r, o) => (o.startsWith("!") ? r.delete(o.slice(1)) : r.add(o), r), /* @__PURE__ */ new Set());
  return Array.from(t);
}, "combineTags");

export {
  require_channels,
  Xr2 as Xr,
  _r2 as _r,
  tc,
  rc,
  oc,
  nc,
  ic,
  jn2 as jn,
  lc,
  cc,
  dc,
  mc,
  uc
};
//# sourceMappingURL=chunk-VUQGWFAF.js.map
