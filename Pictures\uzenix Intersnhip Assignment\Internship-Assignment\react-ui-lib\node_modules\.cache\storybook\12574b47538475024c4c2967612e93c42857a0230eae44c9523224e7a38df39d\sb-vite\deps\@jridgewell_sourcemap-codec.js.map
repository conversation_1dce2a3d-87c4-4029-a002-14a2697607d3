{"version": 3, "sources": ["../../../../../@jridgewell/sourcemap-codec/src/vlq.ts", "../../../../../@jridgewell/sourcemap-codec/src/strings.ts", "../../../../../@jridgewell/sourcemap-codec/src/scopes.ts", "../../../../../@jridgewell/sourcemap-codec/src/sourcemap-codec.ts"], "sourcesContent": ["import type { StringReader, StringWriter } from './strings';\n\nexport const comma = ','.charCodeAt(0);\nexport const semicolon = ';'.charCodeAt(0);\n\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nconst intToChar = new Uint8Array(64); // 64 possible chars.\nconst charToInt = new Uint8Array(128); // z is 122 in ASCII\n\nfor (let i = 0; i < chars.length; i++) {\n  const c = chars.charCodeAt(i);\n  intToChar[i] = c;\n  charToInt[c] = i;\n}\n\nexport function decodeInteger(reader: StringReader, relative: number): number {\n  let value = 0;\n  let shift = 0;\n  let integer = 0;\n\n  do {\n    const c = reader.next();\n    integer = charToInt[c];\n    value |= (integer & 31) << shift;\n    shift += 5;\n  } while (integer & 32);\n\n  const shouldNegate = value & 1;\n  value >>>= 1;\n\n  if (shouldNegate) {\n    value = -0x80000000 | -value;\n  }\n\n  return relative + value;\n}\n\nexport function encodeInteger(builder: StringWriter, num: number, relative: number): number {\n  let delta = num - relative;\n\n  delta = delta < 0 ? (-delta << 1) | 1 : delta << 1;\n  do {\n    let clamped = delta & 0b011111;\n    delta >>>= 5;\n    if (delta > 0) clamped |= 0b100000;\n    builder.write(intToChar[clamped]);\n  } while (delta > 0);\n\n  return num;\n}\n\nexport function hasMoreVlq(reader: StringReader, max: number) {\n  if (reader.pos >= max) return false;\n  return reader.peek() !== comma;\n}\n", "const bufLength = 1024 * 16;\n\n// Provide a fallback for older environments.\nconst td =\n  typeof TextDecoder !== 'undefined'\n    ? /* #__PURE__ */ new TextDecoder()\n    : typeof Buffer !== 'undefined'\n      ? {\n          decode(buf: Uint8Array): string {\n            const out = Buffer.from(buf.buffer, buf.byteOffset, buf.byteLength);\n            return out.toString();\n          },\n        }\n      : {\n          decode(buf: Uint8Array): string {\n            let out = '';\n            for (let i = 0; i < buf.length; i++) {\n              out += String.fromCharCode(buf[i]);\n            }\n            return out;\n          },\n        };\n\nexport class StringWriter {\n  pos = 0;\n  private out = '';\n  private buffer = new Uint8Array(bufLength);\n\n  write(v: number): void {\n    const { buffer } = this;\n    buffer[this.pos++] = v;\n    if (this.pos === bufLength) {\n      this.out += td.decode(buffer);\n      this.pos = 0;\n    }\n  }\n\n  flush(): string {\n    const { buffer, out, pos } = this;\n    return pos > 0 ? out + td.decode(buffer.subarray(0, pos)) : out;\n  }\n}\n\nexport class StringReader {\n  pos = 0;\n  declare private buffer: string;\n\n  constructor(buffer: string) {\n    this.buffer = buffer;\n  }\n\n  next(): number {\n    return this.buffer.charCodeAt(this.pos++);\n  }\n\n  peek(): number {\n    return this.buffer.charCodeAt(this.pos);\n  }\n\n  indexOf(char: string): number {\n    const { buffer, pos } = this;\n    const idx = buffer.indexOf(char, pos);\n    return idx === -1 ? buffer.length : idx;\n  }\n}\n", "import { String<PERSON>ead<PERSON>, StringWriter } from './strings';\nimport { comma, decodeInteger, encodeInteger, hasMoreVlq, semicolon } from './vlq';\n\nconst EMPTY: any[] = [];\n\ntype Line = number;\ntype Column = number;\ntype Kind = number;\ntype Name = number;\ntype Var = number;\ntype SourcesIndex = number;\ntype ScopesIndex = number;\n\ntype Mix<A, B, O> = (A & O) | (B & O);\n\nexport type OriginalScope = Mix<\n  [Line, Column, Line, Column, Kind],\n  [Line, Column, Line, Column, Kind, Name],\n  { vars: Var[] }\n>;\n\nexport type GeneratedRange = Mix<\n  [Line, Column, Line, Column],\n  [Line, Column, Line, Column, SourcesIndex, ScopesIndex],\n  {\n    callsite: CallSite | null;\n    bindings: Binding[];\n    isScope: boolean;\n  }\n>;\nexport type CallSite = [SourcesIndex, Line, Column];\ntype Binding = BindingExpressionRange[];\nexport type BindingExpressionRange = [Name] | [Name, Line, Column];\n\nexport function decodeOriginalScopes(input: string): OriginalScope[] {\n  const { length } = input;\n  const reader = new StringReader(input);\n  const scopes: OriginalScope[] = [];\n  const stack: OriginalScope[] = [];\n  let line = 0;\n\n  for (; reader.pos < length; reader.pos++) {\n    line = decodeInteger(reader, line);\n    const column = decodeInteger(reader, 0);\n\n    if (!hasMoreVlq(reader, length)) {\n      const last = stack.pop()!;\n      last[2] = line;\n      last[3] = column;\n      continue;\n    }\n\n    const kind = decodeInteger(reader, 0);\n    const fields = decodeInteger(reader, 0);\n    const hasName = fields & 0b0001;\n\n    const scope: OriginalScope = (\n      hasName ? [line, column, 0, 0, kind, decodeInteger(reader, 0)] : [line, column, 0, 0, kind]\n    ) as OriginalScope;\n\n    let vars: Var[] = EMPTY;\n    if (hasMoreVlq(reader, length)) {\n      vars = [];\n      do {\n        const varsIndex = decodeInteger(reader, 0);\n        vars.push(varsIndex);\n      } while (hasMoreVlq(reader, length));\n    }\n    scope.vars = vars;\n\n    scopes.push(scope);\n    stack.push(scope);\n  }\n\n  return scopes;\n}\n\nexport function encodeOriginalScopes(scopes: OriginalScope[]): string {\n  const writer = new StringWriter();\n\n  for (let i = 0; i < scopes.length; ) {\n    i = _encodeOriginalScopes(scopes, i, writer, [0]);\n  }\n\n  return writer.flush();\n}\n\nfunction _encodeOriginalScopes(\n  scopes: OriginalScope[],\n  index: number,\n  writer: StringWriter,\n  state: [\n    number, // GenColumn\n  ],\n): number {\n  const scope = scopes[index];\n  const { 0: startLine, 1: startColumn, 2: endLine, 3: endColumn, 4: kind, vars } = scope;\n\n  if (index > 0) writer.write(comma);\n\n  state[0] = encodeInteger(writer, startLine, state[0]);\n  encodeInteger(writer, startColumn, 0);\n  encodeInteger(writer, kind, 0);\n\n  const fields = scope.length === 6 ? 0b0001 : 0;\n  encodeInteger(writer, fields, 0);\n  if (scope.length === 6) encodeInteger(writer, scope[5], 0);\n\n  for (const v of vars) {\n    encodeInteger(writer, v, 0);\n  }\n\n  for (index++; index < scopes.length; ) {\n    const next = scopes[index];\n    const { 0: l, 1: c } = next;\n    if (l > endLine || (l === endLine && c >= endColumn)) {\n      break;\n    }\n    index = _encodeOriginalScopes(scopes, index, writer, state);\n  }\n\n  writer.write(comma);\n  state[0] = encodeInteger(writer, endLine, state[0]);\n  encodeInteger(writer, endColumn, 0);\n\n  return index;\n}\n\nexport function decodeGeneratedRanges(input: string): GeneratedRange[] {\n  const { length } = input;\n  const reader = new StringReader(input);\n  const ranges: GeneratedRange[] = [];\n  const stack: GeneratedRange[] = [];\n\n  let genLine = 0;\n  let definitionSourcesIndex = 0;\n  let definitionScopeIndex = 0;\n  let callsiteSourcesIndex = 0;\n  let callsiteLine = 0;\n  let callsiteColumn = 0;\n  let bindingLine = 0;\n  let bindingColumn = 0;\n\n  do {\n    const semi = reader.indexOf(';');\n    let genColumn = 0;\n\n    for (; reader.pos < semi; reader.pos++) {\n      genColumn = decodeInteger(reader, genColumn);\n\n      if (!hasMoreVlq(reader, semi)) {\n        const last = stack.pop()!;\n        last[2] = genLine;\n        last[3] = genColumn;\n        continue;\n      }\n\n      const fields = decodeInteger(reader, 0);\n      const hasDefinition = fields & 0b0001;\n      const hasCallsite = fields & 0b0010;\n      const hasScope = fields & 0b0100;\n\n      let callsite: CallSite | null = null;\n      let bindings: Binding[] = EMPTY;\n      let range: GeneratedRange;\n      if (hasDefinition) {\n        const defSourcesIndex = decodeInteger(reader, definitionSourcesIndex);\n        definitionScopeIndex = decodeInteger(\n          reader,\n          definitionSourcesIndex === defSourcesIndex ? definitionScopeIndex : 0,\n        );\n\n        definitionSourcesIndex = defSourcesIndex;\n        range = [genLine, genColumn, 0, 0, defSourcesIndex, definitionScopeIndex] as GeneratedRange;\n      } else {\n        range = [genLine, genColumn, 0, 0] as GeneratedRange;\n      }\n\n      range.isScope = !!hasScope;\n\n      if (hasCallsite) {\n        const prevCsi = callsiteSourcesIndex;\n        const prevLine = callsiteLine;\n        callsiteSourcesIndex = decodeInteger(reader, callsiteSourcesIndex);\n        const sameSource = prevCsi === callsiteSourcesIndex;\n        callsiteLine = decodeInteger(reader, sameSource ? callsiteLine : 0);\n        callsiteColumn = decodeInteger(\n          reader,\n          sameSource && prevLine === callsiteLine ? callsiteColumn : 0,\n        );\n\n        callsite = [callsiteSourcesIndex, callsiteLine, callsiteColumn];\n      }\n      range.callsite = callsite;\n\n      if (hasMoreVlq(reader, semi)) {\n        bindings = [];\n        do {\n          bindingLine = genLine;\n          bindingColumn = genColumn;\n          const expressionsCount = decodeInteger(reader, 0);\n          let expressionRanges: BindingExpressionRange[];\n          if (expressionsCount < -1) {\n            expressionRanges = [[decodeInteger(reader, 0)]];\n            for (let i = -1; i > expressionsCount; i--) {\n              const prevBl = bindingLine;\n              bindingLine = decodeInteger(reader, bindingLine);\n              bindingColumn = decodeInteger(reader, bindingLine === prevBl ? bindingColumn : 0);\n              const expression = decodeInteger(reader, 0);\n              expressionRanges.push([expression, bindingLine, bindingColumn]);\n            }\n          } else {\n            expressionRanges = [[expressionsCount]];\n          }\n          bindings.push(expressionRanges);\n        } while (hasMoreVlq(reader, semi));\n      }\n      range.bindings = bindings;\n\n      ranges.push(range);\n      stack.push(range);\n    }\n\n    genLine++;\n    reader.pos = semi + 1;\n  } while (reader.pos < length);\n\n  return ranges;\n}\n\nexport function encodeGeneratedRanges(ranges: GeneratedRange[]): string {\n  if (ranges.length === 0) return '';\n\n  const writer = new StringWriter();\n\n  for (let i = 0; i < ranges.length; ) {\n    i = _encodeGeneratedRanges(ranges, i, writer, [0, 0, 0, 0, 0, 0, 0]);\n  }\n\n  return writer.flush();\n}\n\nfunction _encodeGeneratedRanges(\n  ranges: GeneratedRange[],\n  index: number,\n  writer: StringWriter,\n  state: [\n    number, // GenLine\n    number, // GenColumn\n    number, // DefSourcesIndex\n    number, // DefScopesIndex\n    number, // CallSourcesIndex\n    number, // CallLine\n    number, // CallColumn\n  ],\n): number {\n  const range = ranges[index];\n  const {\n    0: startLine,\n    1: startColumn,\n    2: endLine,\n    3: endColumn,\n    isScope,\n    callsite,\n    bindings,\n  } = range;\n\n  if (state[0] < startLine) {\n    catchupLine(writer, state[0], startLine);\n    state[0] = startLine;\n    state[1] = 0;\n  } else if (index > 0) {\n    writer.write(comma);\n  }\n\n  state[1] = encodeInteger(writer, range[1], state[1]);\n\n  const fields =\n    (range.length === 6 ? 0b0001 : 0) | (callsite ? 0b0010 : 0) | (isScope ? 0b0100 : 0);\n  encodeInteger(writer, fields, 0);\n\n  if (range.length === 6) {\n    const { 4: sourcesIndex, 5: scopesIndex } = range;\n    if (sourcesIndex !== state[2]) {\n      state[3] = 0;\n    }\n    state[2] = encodeInteger(writer, sourcesIndex, state[2]);\n    state[3] = encodeInteger(writer, scopesIndex, state[3]);\n  }\n\n  if (callsite) {\n    const { 0: sourcesIndex, 1: callLine, 2: callColumn } = range.callsite!;\n    if (sourcesIndex !== state[4]) {\n      state[5] = 0;\n      state[6] = 0;\n    } else if (callLine !== state[5]) {\n      state[6] = 0;\n    }\n    state[4] = encodeInteger(writer, sourcesIndex, state[4]);\n    state[5] = encodeInteger(writer, callLine, state[5]);\n    state[6] = encodeInteger(writer, callColumn, state[6]);\n  }\n\n  if (bindings) {\n    for (const binding of bindings) {\n      if (binding.length > 1) encodeInteger(writer, -binding.length, 0);\n      const expression = binding[0][0];\n      encodeInteger(writer, expression, 0);\n      let bindingStartLine = startLine;\n      let bindingStartColumn = startColumn;\n      for (let i = 1; i < binding.length; i++) {\n        const expRange = binding[i];\n        bindingStartLine = encodeInteger(writer, expRange[1]!, bindingStartLine);\n        bindingStartColumn = encodeInteger(writer, expRange[2]!, bindingStartColumn);\n        encodeInteger(writer, expRange[0]!, 0);\n      }\n    }\n  }\n\n  for (index++; index < ranges.length; ) {\n    const next = ranges[index];\n    const { 0: l, 1: c } = next;\n    if (l > endLine || (l === endLine && c >= endColumn)) {\n      break;\n    }\n    index = _encodeGeneratedRanges(ranges, index, writer, state);\n  }\n\n  if (state[0] < endLine) {\n    catchupLine(writer, state[0], endLine);\n    state[0] = endLine;\n    state[1] = 0;\n  } else {\n    writer.write(comma);\n  }\n  state[1] = encodeInteger(writer, endColumn, state[1]);\n\n  return index;\n}\n\nfunction catchupLine(writer: StringWriter, lastLine: number, line: number) {\n  do {\n    writer.write(semicolon);\n  } while (++lastLine < line);\n}\n", "import { comma, decodeInteger, encodeInteger, hasMoreVlq, semicolon } from './vlq';\nimport { StringWriter, StringReader } from './strings';\n\nexport {\n  decodeOriginalScopes,\n  encodeOriginalScopes,\n  decodeGeneratedRanges,\n  encodeGeneratedRanges,\n} from './scopes';\nexport type { OriginalScope, GeneratedRange, CallSite, BindingExpressionRange } from './scopes';\n\nexport type SourceMapSegment =\n  | [number]\n  | [number, number, number, number]\n  | [number, number, number, number, number];\nexport type SourceMapLine = SourceMapSegment[];\nexport type SourceMapMappings = SourceMapLine[];\n\nexport function decode(mappings: string): SourceMapMappings {\n  const { length } = mappings;\n  const reader = new StringReader(mappings);\n  const decoded: SourceMapMappings = [];\n  let genColumn = 0;\n  let sourcesIndex = 0;\n  let sourceLine = 0;\n  let sourceColumn = 0;\n  let namesIndex = 0;\n\n  do {\n    const semi = reader.indexOf(';');\n    const line: SourceMapLine = [];\n    let sorted = true;\n    let lastCol = 0;\n    genColumn = 0;\n\n    while (reader.pos < semi) {\n      let seg: SourceMapSegment;\n\n      genColumn = decodeInteger(reader, genColumn);\n      if (genColumn < lastCol) sorted = false;\n      lastCol = genColumn;\n\n      if (hasMoreVlq(reader, semi)) {\n        sourcesIndex = decodeInteger(reader, sourcesIndex);\n        sourceLine = decodeInteger(reader, sourceLine);\n        sourceColumn = decodeInteger(reader, sourceColumn);\n\n        if (hasMoreVlq(reader, semi)) {\n          namesIndex = decodeInteger(reader, namesIndex);\n          seg = [genColumn, sourcesIndex, sourceLine, sourceColumn, namesIndex];\n        } else {\n          seg = [genColumn, sourcesIndex, sourceLine, sourceColumn];\n        }\n      } else {\n        seg = [genColumn];\n      }\n\n      line.push(seg);\n      reader.pos++;\n    }\n\n    if (!sorted) sort(line);\n    decoded.push(line);\n    reader.pos = semi + 1;\n  } while (reader.pos <= length);\n\n  return decoded;\n}\n\nfunction sort(line: SourceMapSegment[]) {\n  line.sort(sortComparator);\n}\n\nfunction sortComparator(a: SourceMapSegment, b: SourceMapSegment): number {\n  return a[0] - b[0];\n}\n\nexport function encode(decoded: SourceMapMappings): string;\nexport function encode(decoded: Readonly<SourceMapMappings>): string;\nexport function encode(decoded: Readonly<SourceMapMappings>): string {\n  const writer = new StringWriter();\n  let sourcesIndex = 0;\n  let sourceLine = 0;\n  let sourceColumn = 0;\n  let namesIndex = 0;\n\n  for (let i = 0; i < decoded.length; i++) {\n    const line = decoded[i];\n    if (i > 0) writer.write(semicolon);\n    if (line.length === 0) continue;\n\n    let genColumn = 0;\n\n    for (let j = 0; j < line.length; j++) {\n      const segment = line[j];\n      if (j > 0) writer.write(comma);\n\n      genColumn = encodeInteger(writer, segment[0], genColumn);\n\n      if (segment.length === 1) continue;\n      sourcesIndex = encodeInteger(writer, segment[1], sourcesIndex);\n      sourceLine = encodeInteger(writer, segment[2], sourceLine);\n      sourceColumn = encodeInteger(writer, segment[3], sourceColumn);\n\n      if (segment.length === 4) continue;\n      namesIndex = encodeInteger(writer, segment[4], namesIndex);\n    }\n  }\n\n  return writer.flush();\n}\n"], "mappings": ";;;AAEO,IAAM,QAAQ,IAAI,WAAW,CAAC;AAC9B,IAAM,YAAY,IAAI,WAAW,CAAC;AAEzC,IAAM,QAAQ;AACd,IAAM,YAAY,IAAI,WAAW,EAAE;AACnC,IAAM,YAAY,IAAI,WAAW,GAAG;AAEpC,SAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAM,IAAI,MAAM,WAAW,CAAC;AAC5B,YAAU,CAAC,IAAI;AACf,YAAU,CAAC,IAAI;AACjB;AAEO,SAAS,cAAc,QAAsB,UAA0B;AAC5E,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,UAAU;AAEd,KAAG;AACD,UAAM,IAAI,OAAO,KAAK;AACtB,cAAU,UAAU,CAAC;AACrB,cAAU,UAAU,OAAO;AAC3B,aAAS;EACX,SAAS,UAAU;AAEnB,QAAM,eAAe,QAAQ;AAC7B,aAAW;AAEX,MAAI,cAAc;AAChB,YAAQ,cAAc,CAAC;EACzB;AAEA,SAAO,WAAW;AACpB;AAEO,SAAS,cAAc,SAAuB,KAAa,UAA0B;AAC1F,MAAI,QAAQ,MAAM;AAElB,UAAQ,QAAQ,IAAK,CAAC,SAAS,IAAK,IAAI,SAAS;AACjD,KAAG;AACD,QAAI,UAAU,QAAQ;AACtB,eAAW;AACX,QAAI,QAAQ,EAAG,YAAW;AAC1B,YAAQ,MAAM,UAAU,OAAO,CAAC;EAClC,SAAS,QAAQ;AAEjB,SAAO;AACT;AAEO,SAAS,WAAW,QAAsB,KAAa;AAC5D,MAAI,OAAO,OAAO,IAAK,QAAO;AAC9B,SAAO,OAAO,KAAK,MAAM;AAC3B;ACtDA,IAAM,YAAY,OAAO;AAGzB,IAAM,KACJ,OAAO,gBAAgB,cACH,IAAI,YAAY,IAChC,OAAO,WAAW,cAChB;EACE,OAAO,KAAyB;AAC9B,UAAM,MAAM,OAAO,KAAK,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AAClE,WAAO,IAAI,SAAS;EACtB;AACF,IACA;EACE,OAAO,KAAyB;AAC9B,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,aAAO,OAAO,aAAa,IAAI,CAAC,CAAC;IACnC;AACA,WAAO;EACT;AACF;AAED,IAAM,eAAN,MAAmB;EAAnB,cAAA;AACL,SAAA,MAAM;AACN,SAAQ,MAAM;AACd,SAAQ,SAAS,IAAI,WAAW,SAAS;EAAA;EAEzC,MAAM,GAAiB;AACrB,UAAM,EAAE,OAAO,IAAI;AACnB,WAAO,KAAK,KAAK,IAAI;AACrB,QAAI,KAAK,QAAQ,WAAW;AAC1B,WAAK,OAAO,GAAG,OAAO,MAAM;AAC5B,WAAK,MAAM;IACb;EACF;EAEA,QAAgB;AACd,UAAM,EAAE,QAAQ,KAAK,IAAI,IAAI;AAC7B,WAAO,MAAM,IAAI,MAAM,GAAG,OAAO,OAAO,SAAS,GAAG,GAAG,CAAC,IAAI;EAC9D;AACF;AAEO,IAAM,eAAN,MAAmB;EAIxB,YAAY,QAAgB;AAH5B,SAAA,MAAM;AAIJ,SAAK,SAAS;EAChB;EAEA,OAAe;AACb,WAAO,KAAK,OAAO,WAAW,KAAK,KAAK;EAC1C;EAEA,OAAe;AACb,WAAO,KAAK,OAAO,WAAW,KAAK,GAAG;EACxC;EAEA,QAAQ,MAAsB;AAC5B,UAAM,EAAE,QAAQ,IAAI,IAAI;AACxB,UAAM,MAAM,OAAO,QAAQ,MAAM,GAAG;AACpC,WAAO,QAAQ,KAAK,OAAO,SAAS;EACtC;AACF;AC7DA,IAAM,QAAe,CAAC;AA+Bf,SAAS,qBAAqB,OAAgC;AACnE,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,SAAS,IAAI,aAAa,KAAK;AACrC,QAAM,SAA0B,CAAC;AACjC,QAAM,QAAyB,CAAC;AAChC,MAAI,OAAO;AAEX,SAAO,OAAO,MAAM,QAAQ,OAAO,OAAO;AACxC,WAAO,cAAc,QAAQ,IAAI;AACjC,UAAM,SAAS,cAAc,QAAQ,CAAC;AAEtC,QAAI,CAAC,WAAW,QAAQ,MAAM,GAAG;AAC/B,YAAM,OAAO,MAAM,IAAI;AACvB,WAAK,CAAC,IAAI;AACV,WAAK,CAAC,IAAI;AACV;IACF;AAEA,UAAM,OAAO,cAAc,QAAQ,CAAC;AACpC,UAAM,SAAS,cAAc,QAAQ,CAAC;AACtC,UAAM,UAAU,SAAS;AAEzB,UAAM,QACJ,UAAU,CAAC,MAAM,QAAQ,GAAG,GAAG,MAAM,cAAc,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,QAAQ,GAAG,GAAG,IAAI;AAG5F,QAAI,OAAc;AAClB,QAAI,WAAW,QAAQ,MAAM,GAAG;AAC9B,aAAO,CAAC;AACR,SAAG;AACD,cAAM,YAAY,cAAc,QAAQ,CAAC;AACzC,aAAK,KAAK,SAAS;MACrB,SAAS,WAAW,QAAQ,MAAM;IACpC;AACA,UAAM,OAAO;AAEb,WAAO,KAAK,KAAK;AACjB,UAAM,KAAK,KAAK;EAClB;AAEA,SAAO;AACT;AAEO,SAAS,qBAAqB,QAAiC;AACpE,QAAM,SAAS,IAAI,aAAa;AAEhC,WAAS,IAAI,GAAG,IAAI,OAAO,UAAU;AACnC,QAAI,sBAAsB,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC;EAClD;AAEA,SAAO,OAAO,MAAM;AACtB;AAEA,SAAS,sBACP,QACA,OACA,QACA,OAGQ;AACR,QAAM,QAAQ,OAAO,KAAK;AAC1B,QAAM,EAAE,GAAG,WAAW,GAAG,aAAa,GAAG,SAAS,GAAG,WAAW,GAAG,MAAM,KAAK,IAAI;AAElF,MAAI,QAAQ,EAAG,QAAO,MAAM,KAAK;AAEjC,QAAM,CAAC,IAAI,cAAc,QAAQ,WAAW,MAAM,CAAC,CAAC;AACpD,gBAAc,QAAQ,aAAa,CAAC;AACpC,gBAAc,QAAQ,MAAM,CAAC;AAE7B,QAAM,SAAS,MAAM,WAAW,IAAI,IAAS;AAC7C,gBAAc,QAAQ,QAAQ,CAAC;AAC/B,MAAI,MAAM,WAAW,EAAG,eAAc,QAAQ,MAAM,CAAC,GAAG,CAAC;AAEzD,aAAW,KAAK,MAAM;AACpB,kBAAc,QAAQ,GAAG,CAAC;EAC5B;AAEA,OAAK,SAAS,QAAQ,OAAO,UAAU;AACrC,UAAM,OAAO,OAAO,KAAK;AACzB,UAAM,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI;AACvB,QAAI,IAAI,WAAY,MAAM,WAAW,KAAK,WAAY;AACpD;IACF;AACA,YAAQ,sBAAsB,QAAQ,OAAO,QAAQ,KAAK;EAC5D;AAEA,SAAO,MAAM,KAAK;AAClB,QAAM,CAAC,IAAI,cAAc,QAAQ,SAAS,MAAM,CAAC,CAAC;AAClD,gBAAc,QAAQ,WAAW,CAAC;AAElC,SAAO;AACT;AAEO,SAAS,sBAAsB,OAAiC;AACrE,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,SAAS,IAAI,aAAa,KAAK;AACrC,QAAM,SAA2B,CAAC;AAClC,QAAM,QAA0B,CAAC;AAEjC,MAAI,UAAU;AACd,MAAI,yBAAyB;AAC7B,MAAI,uBAAuB;AAC3B,MAAI,uBAAuB;AAC3B,MAAI,eAAe;AACnB,MAAI,iBAAiB;AACrB,MAAI,cAAc;AAClB,MAAI,gBAAgB;AAEpB,KAAG;AACD,UAAM,OAAO,OAAO,QAAQ,GAAG;AAC/B,QAAI,YAAY;AAEhB,WAAO,OAAO,MAAM,MAAM,OAAO,OAAO;AACtC,kBAAY,cAAc,QAAQ,SAAS;AAE3C,UAAI,CAAC,WAAW,QAAQ,IAAI,GAAG;AAC7B,cAAM,OAAO,MAAM,IAAI;AACvB,aAAK,CAAC,IAAI;AACV,aAAK,CAAC,IAAI;AACV;MACF;AAEA,YAAM,SAAS,cAAc,QAAQ,CAAC;AACtC,YAAM,gBAAgB,SAAS;AAC/B,YAAM,cAAc,SAAS;AAC7B,YAAM,WAAW,SAAS;AAE1B,UAAI,WAA4B;AAChC,UAAI,WAAsB;AAC1B,UAAI;AACJ,UAAI,eAAe;AACjB,cAAM,kBAAkB,cAAc,QAAQ,sBAAsB;AACpE,+BAAuB;UACrB;UACA,2BAA2B,kBAAkB,uBAAuB;QACtE;AAEA,iCAAyB;AACzB,gBAAQ,CAAC,SAAS,WAAW,GAAG,GAAG,iBAAiB,oBAAoB;MAC1E,OAAO;AACL,gBAAQ,CAAC,SAAS,WAAW,GAAG,CAAC;MACnC;AAEA,YAAM,UAAU,CAAC,CAAC;AAElB,UAAI,aAAa;AACf,cAAM,UAAU;AAChB,cAAM,WAAW;AACjB,+BAAuB,cAAc,QAAQ,oBAAoB;AACjE,cAAM,aAAa,YAAY;AAC/B,uBAAe,cAAc,QAAQ,aAAa,eAAe,CAAC;AAClE,yBAAiB;UACf;UACA,cAAc,aAAa,eAAe,iBAAiB;QAC7D;AAEA,mBAAW,CAAC,sBAAsB,cAAc,cAAc;MAChE;AACA,YAAM,WAAW;AAEjB,UAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,mBAAW,CAAC;AACZ,WAAG;AACD,wBAAc;AACd,0BAAgB;AAChB,gBAAM,mBAAmB,cAAc,QAAQ,CAAC;AAChD,cAAI;AACJ,cAAI,mBAAmB,IAAI;AACzB,+BAAmB,CAAC,CAAC,cAAc,QAAQ,CAAC,CAAC,CAAC;AAC9C,qBAAS,IAAI,IAAI,IAAI,kBAAkB,KAAK;AAC1C,oBAAM,SAAS;AACf,4BAAc,cAAc,QAAQ,WAAW;AAC/C,8BAAgB,cAAc,QAAQ,gBAAgB,SAAS,gBAAgB,CAAC;AAChF,oBAAM,aAAa,cAAc,QAAQ,CAAC;AAC1C,+BAAiB,KAAK,CAAC,YAAY,aAAa,aAAa,CAAC;YAChE;UACF,OAAO;AACL,+BAAmB,CAAC,CAAC,gBAAgB,CAAC;UACxC;AACA,mBAAS,KAAK,gBAAgB;QAChC,SAAS,WAAW,QAAQ,IAAI;MAClC;AACA,YAAM,WAAW;AAEjB,aAAO,KAAK,KAAK;AACjB,YAAM,KAAK,KAAK;IAClB;AAEA;AACA,WAAO,MAAM,OAAO;EACtB,SAAS,OAAO,MAAM;AAEtB,SAAO;AACT;AAEO,SAAS,sBAAsB,QAAkC;AACtE,MAAI,OAAO,WAAW,EAAG,QAAO;AAEhC,QAAM,SAAS,IAAI,aAAa;AAEhC,WAAS,IAAI,GAAG,IAAI,OAAO,UAAU;AACnC,QAAI,uBAAuB,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;EACrE;AAEA,SAAO,OAAO,MAAM;AACtB;AAEA,SAAS,uBACP,QACA,OACA,QACA,OASQ;AACR,QAAM,QAAQ,OAAO,KAAK;AAC1B,QAAM;IACJ,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH;IACA;IACA;EACF,IAAI;AAEJ,MAAI,MAAM,CAAC,IAAI,WAAW;AACxB,gBAAY,QAAQ,MAAM,CAAC,GAAG,SAAS;AACvC,UAAM,CAAC,IAAI;AACX,UAAM,CAAC,IAAI;EACb,WAAW,QAAQ,GAAG;AACpB,WAAO,MAAM,KAAK;EACpB;AAEA,QAAM,CAAC,IAAI,cAAc,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAEnD,QAAM,UACH,MAAM,WAAW,IAAI,IAAS,MAAM,WAAW,IAAS,MAAM,UAAU,IAAS;AACpF,gBAAc,QAAQ,QAAQ,CAAC;AAE/B,MAAI,MAAM,WAAW,GAAG;AACtB,UAAM,EAAE,GAAG,cAAc,GAAG,YAAY,IAAI;AAC5C,QAAI,iBAAiB,MAAM,CAAC,GAAG;AAC7B,YAAM,CAAC,IAAI;IACb;AACA,UAAM,CAAC,IAAI,cAAc,QAAQ,cAAc,MAAM,CAAC,CAAC;AACvD,UAAM,CAAC,IAAI,cAAc,QAAQ,aAAa,MAAM,CAAC,CAAC;EACxD;AAEA,MAAI,UAAU;AACZ,UAAM,EAAE,GAAG,cAAc,GAAG,UAAU,GAAG,WAAW,IAAI,MAAM;AAC9D,QAAI,iBAAiB,MAAM,CAAC,GAAG;AAC7B,YAAM,CAAC,IAAI;AACX,YAAM,CAAC,IAAI;IACb,WAAW,aAAa,MAAM,CAAC,GAAG;AAChC,YAAM,CAAC,IAAI;IACb;AACA,UAAM,CAAC,IAAI,cAAc,QAAQ,cAAc,MAAM,CAAC,CAAC;AACvD,UAAM,CAAC,IAAI,cAAc,QAAQ,UAAU,MAAM,CAAC,CAAC;AACnD,UAAM,CAAC,IAAI,cAAc,QAAQ,YAAY,MAAM,CAAC,CAAC;EACvD;AAEA,MAAI,UAAU;AACZ,eAAW,WAAW,UAAU;AAC9B,UAAI,QAAQ,SAAS,EAAG,eAAc,QAAQ,CAAC,QAAQ,QAAQ,CAAC;AAChE,YAAM,aAAa,QAAQ,CAAC,EAAE,CAAC;AAC/B,oBAAc,QAAQ,YAAY,CAAC;AACnC,UAAI,mBAAmB;AACvB,UAAI,qBAAqB;AACzB,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAM,WAAW,QAAQ,CAAC;AAC1B,2BAAmB,cAAc,QAAQ,SAAS,CAAC,GAAI,gBAAgB;AACvE,6BAAqB,cAAc,QAAQ,SAAS,CAAC,GAAI,kBAAkB;AAC3E,sBAAc,QAAQ,SAAS,CAAC,GAAI,CAAC;MACvC;IACF;EACF;AAEA,OAAK,SAAS,QAAQ,OAAO,UAAU;AACrC,UAAM,OAAO,OAAO,KAAK;AACzB,UAAM,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI;AACvB,QAAI,IAAI,WAAY,MAAM,WAAW,KAAK,WAAY;AACpD;IACF;AACA,YAAQ,uBAAuB,QAAQ,OAAO,QAAQ,KAAK;EAC7D;AAEA,MAAI,MAAM,CAAC,IAAI,SAAS;AACtB,gBAAY,QAAQ,MAAM,CAAC,GAAG,OAAO;AACrC,UAAM,CAAC,IAAI;AACX,UAAM,CAAC,IAAI;EACb,OAAO;AACL,WAAO,MAAM,KAAK;EACpB;AACA,QAAM,CAAC,IAAI,cAAc,QAAQ,WAAW,MAAM,CAAC,CAAC;AAEpD,SAAO;AACT;AAEA,SAAS,YAAY,QAAsB,UAAkB,MAAc;AACzE,KAAG;AACD,WAAO,MAAM,SAAS;EACxB,SAAS,EAAE,WAAW;AACxB;ACtUO,SAAS,OAAO,UAAqC;AAC1D,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,SAAS,IAAI,aAAa,QAAQ;AACxC,QAAM,UAA6B,CAAC;AACpC,MAAI,YAAY;AAChB,MAAI,eAAe;AACnB,MAAI,aAAa;AACjB,MAAI,eAAe;AACnB,MAAI,aAAa;AAEjB,KAAG;AACD,UAAM,OAAO,OAAO,QAAQ,GAAG;AAC/B,UAAM,OAAsB,CAAC;AAC7B,QAAI,SAAS;AACb,QAAI,UAAU;AACd,gBAAY;AAEZ,WAAO,OAAO,MAAM,MAAM;AACxB,UAAI;AAEJ,kBAAY,cAAc,QAAQ,SAAS;AAC3C,UAAI,YAAY,QAAS,UAAS;AAClC,gBAAU;AAEV,UAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,uBAAe,cAAc,QAAQ,YAAY;AACjD,qBAAa,cAAc,QAAQ,UAAU;AAC7C,uBAAe,cAAc,QAAQ,YAAY;AAEjD,YAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,uBAAa,cAAc,QAAQ,UAAU;AAC7C,gBAAM,CAAC,WAAW,cAAc,YAAY,cAAc,UAAU;QACtE,OAAO;AACL,gBAAM,CAAC,WAAW,cAAc,YAAY,YAAY;QAC1D;MACF,OAAO;AACL,cAAM,CAAC,SAAS;MAClB;AAEA,WAAK,KAAK,GAAG;AACb,aAAO;IACT;AAEA,QAAI,CAAC,OAAQ,MAAK,IAAI;AACtB,YAAQ,KAAK,IAAI;AACjB,WAAO,MAAM,OAAO;EACtB,SAAS,OAAO,OAAO;AAEvB,SAAO;AACT;AAEA,SAAS,KAAK,MAA0B;AACtC,OAAK,KAAK,cAAc;AAC1B;AAEA,SAAS,eAAe,GAAqB,GAA6B;AACxE,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB;AAIO,SAAS,OAAO,SAA8C;AACnE,QAAM,SAAS,IAAI,aAAa;AAChC,MAAI,eAAe;AACnB,MAAI,aAAa;AACjB,MAAI,eAAe;AACnB,MAAI,aAAa;AAEjB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,OAAO,QAAQ,CAAC;AACtB,QAAI,IAAI,EAAG,QAAO,MAAM,SAAS;AACjC,QAAI,KAAK,WAAW,EAAG;AAEvB,QAAI,YAAY;AAEhB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAM,UAAU,KAAK,CAAC;AACtB,UAAI,IAAI,EAAG,QAAO,MAAM,KAAK;AAE7B,kBAAY,cAAc,QAAQ,QAAQ,CAAC,GAAG,SAAS;AAEvD,UAAI,QAAQ,WAAW,EAAG;AAC1B,qBAAe,cAAc,QAAQ,QAAQ,CAAC,GAAG,YAAY;AAC7D,mBAAa,cAAc,QAAQ,QAAQ,CAAC,GAAG,UAAU;AACzD,qBAAe,cAAc,QAAQ,QAAQ,CAAC,GAAG,YAAY;AAE7D,UAAI,QAAQ,WAAW,EAAG;AAC1B,mBAAa,cAAc,QAAQ,QAAQ,CAAC,GAAG,UAAU;IAC3D;EACF;AAEA,SAAO,OAAO,MAAM;AACtB;", "names": []}