{"name": "std-env", "version": "3.9.0", "description": "Runtime agnostic JS utils", "repository": "unjs/std-env", "license": "MIT", "sideEffects": false, "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint . && prettier -c src test", "lint:fix": "eslint --fix . && prettier -w src test", "prepack": "unbuild", "play:bun": "bun playground/bun.ts", "play:deno": "pnpm build && deno run -A playground/deno.ts", "play:node": "pnpm build && node playground/node.mjs", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && pnpm typecheck && vitest run --coverage", "typecheck": "tsc --noEmit"}, "devDependencies": {"@types/node": "^22.14.0", "@vitest/coverage-v8": "^3.1.1", "changelogen": "^0.6.1", "esbuild": "^0.25.2", "eslint": "^9.23.0", "eslint-config-unjs": "^0.4.2", "jiti": "^2.4.2", "prettier": "^3.5.3", "rollup": "^4.39.0", "typescript": "^5.8.2", "unbuild": "^3.5.0", "vitest": "^3.1.1"}, "packageManager": "pnpm@10.7.1"}