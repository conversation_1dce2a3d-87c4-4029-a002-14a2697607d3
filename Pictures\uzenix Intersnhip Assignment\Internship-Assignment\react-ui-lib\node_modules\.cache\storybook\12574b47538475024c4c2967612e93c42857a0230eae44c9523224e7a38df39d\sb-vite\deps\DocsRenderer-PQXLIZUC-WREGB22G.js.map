{"version": 3, "sources": ["../../../../../@storybook/addon-docs/dist/DocsRenderer-PQXLIZUC.mjs"], "sourcesContent": ["import React, { Component } from 'react';\nimport { renderElement, unmountElement } from '@storybook/react-dom-shim';\nimport { CodeOrSourceMdx, AnchorMdx, HeadersMdx, Docs } from '@storybook/addon-docs/blocks';\n\nvar defaultComponents={code:CodeOrSourceMdx,a:AnchorMdx,...HeadersMdx},ErrorBoundary=class extends Component{constructor(){super(...arguments);this.state={hasError:!1};}static getDerivedStateFromError(){return {hasError:!0}}componentDidCatch(err){let{showException}=this.props;showException(err);}render(){let{hasError}=this.state,{children}=this.props;return hasError?null:React.createElement(React.Fragment,null,children)}},DocsRenderer=class{constructor(){this.render=async(context,docsParameter,element)=>{let components={...defaultComponents,...docsParameter?.components},TDocs=Docs;return new Promise((resolve,reject)=>{import('@mdx-js/react').then(({MDXProvider})=>renderElement(React.createElement(ErrorBoundary,{showException:reject,key:Math.random()},React.createElement(MDXProvider,{components},React.createElement(TDocs,{context,docsParameter}))),element)).then(()=>resolve());})},this.unmount=element=>{unmountElement(element);};}};\n\nexport { DocsRenderer, defaultComponents };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mBAAiC;AAIjC,IAAI,oBAAkB,EAAC,MAAK,iBAAgB,GAAE,WAAU,GAAG,WAAU;AAArE,IAAuE,gBAAc,cAAc,uBAAS;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS;AAAE,SAAK,QAAM,EAAC,UAAS,MAAE;AAAA,EAAE;AAAA,EAAC,OAAO,2BAA0B;AAAC,WAAO,EAAC,UAAS,KAAE;AAAA,EAAC;AAAA,EAAC,kBAAkB,KAAI;AAAC,QAAG,EAAC,cAAa,IAAE,KAAK;AAAM,kBAAc,GAAG;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,QAAG,EAAC,SAAQ,IAAE,KAAK,OAAM,EAAC,SAAQ,IAAE,KAAK;AAAM,WAAO,WAAS,OAAK,aAAAA,QAAM,cAAc,aAAAA,QAAM,UAAS,MAAK,QAAQ;AAAA,EAAC;AAAC;AAAxa,IAA0a,eAAa,MAAK;AAAA,EAAC,cAAa;AAAC,SAAK,SAAO,OAAM,SAAQ,eAAc,YAAU;AAAC,UAAI,aAAW,EAAC,GAAG,mBAAkB,GAAG,eAAe,WAAU,GAAE,QAAM;AAAK,aAAO,IAAI,QAAQ,CAAC,SAAQ,WAAS;AAAC,eAAO,oBAAe,EAAE,KAAK,CAAC,EAAC,YAAW,MAAI,cAAc,aAAAA,QAAM,cAAc,eAAc,EAAC,eAAc,QAAO,KAAI,KAAK,OAAO,EAAC,GAAE,aAAAA,QAAM,cAAc,aAAY,EAAC,WAAU,GAAE,aAAAA,QAAM,cAAc,OAAM,EAAC,SAAQ,cAAa,CAAC,CAAC,CAAC,GAAE,OAAO,CAAC,EAAE,KAAK,MAAI,QAAQ,CAAC;AAAA,MAAE,CAAC;AAAA,IAAC,GAAE,KAAK,UAAQ,aAAS;AAAC,qBAAe,OAAO;AAAA,IAAE;AAAA,EAAE;AAAC;", "names": ["React"]}