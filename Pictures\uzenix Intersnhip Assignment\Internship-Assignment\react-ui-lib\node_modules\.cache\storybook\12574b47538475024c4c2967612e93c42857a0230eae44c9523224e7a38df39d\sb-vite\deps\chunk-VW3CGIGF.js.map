{"version": 3, "sources": ["../../../../../@vitest/mocker/dist/chunk-registry.js", "../../../../../@vitest/mocker/dist/index.js"], "sourcesContent": ["class MockerRegistry {\n\tregistryByUrl = new Map();\n\tregistryById = new Map();\n\tclear() {\n\t\tthis.registryByUrl.clear();\n\t\tthis.registryById.clear();\n\t}\n\tkeys() {\n\t\treturn this.registryByUrl.keys();\n\t}\n\tadd(mock) {\n\t\tthis.registryByUrl.set(mock.url, mock);\n\t\tthis.registryById.set(mock.id, mock);\n\t}\n\tregister(typeOrEvent, raw, id, url, factoryOrRedirect) {\n\t\tconst type = typeof typeOrEvent === \"object\" ? typeOrEvent.type : typeOrEvent;\n\t\tif (typeof typeOrEvent === \"object\") {\n\t\t\tconst event = typeOrEvent;\n\t\t\tif (event instanceof AutomockedModule || event instanceof AutospiedModule || event instanceof ManualMockedModule || event instanceof RedirectedModule) {\n\t\t\t\tthrow new TypeError(`[vitest] Cannot register a mock that is already defined. ` + `Expected a JSON representation from \\`MockedModule.toJSON\\`, instead got \"${event.type}\". ` + `Use \"registry.add()\" to update a mock instead.`);\n\t\t\t}\n\t\t\tif (event.type === \"automock\") {\n\t\t\t\tconst module = AutomockedModule.fromJSON(event);\n\t\t\t\tthis.add(module);\n\t\t\t\treturn module;\n\t\t\t} else if (event.type === \"autospy\") {\n\t\t\t\tconst module = AutospiedModule.fromJSON(event);\n\t\t\t\tthis.add(module);\n\t\t\t\treturn module;\n\t\t\t} else if (event.type === \"redirect\") {\n\t\t\t\tconst module = RedirectedModule.fromJSON(event);\n\t\t\t\tthis.add(module);\n\t\t\t\treturn module;\n\t\t\t} else if (event.type === \"manual\") {\n\t\t\t\tthrow new Error(`Cannot set serialized manual mock. Define a factory function manually with \\`ManualMockedModule.fromJSON()\\`.`);\n\t\t\t} else {\n\t\t\t\tthrow new Error(`Unknown mock type: ${event.type}`);\n\t\t\t}\n\t\t}\n\t\tif (typeof raw !== \"string\") {\n\t\t\tthrow new TypeError(\"[vitest] Mocks require a raw string.\");\n\t\t}\n\t\tif (typeof url !== \"string\") {\n\t\t\tthrow new TypeError(\"[vitest] Mocks require a url string.\");\n\t\t}\n\t\tif (typeof id !== \"string\") {\n\t\t\tthrow new TypeError(\"[vitest] Mocks require an id string.\");\n\t\t}\n\t\tif (type === \"manual\") {\n\t\t\tif (typeof factoryOrRedirect !== \"function\") {\n\t\t\t\tthrow new TypeError(\"[vitest] Manual mocks require a factory function.\");\n\t\t\t}\n\t\t\tconst mock = new ManualMockedModule(raw, id, url, factoryOrRedirect);\n\t\t\tthis.add(mock);\n\t\t\treturn mock;\n\t\t} else if (type === \"automock\" || type === \"autospy\") {\n\t\t\tconst mock = type === \"automock\" ? new AutomockedModule(raw, id, url) : new AutospiedModule(raw, id, url);\n\t\t\tthis.add(mock);\n\t\t\treturn mock;\n\t\t} else if (type === \"redirect\") {\n\t\t\tif (typeof factoryOrRedirect !== \"string\") {\n\t\t\t\tthrow new TypeError(\"[vitest] Redirect mocks require a redirect string.\");\n\t\t\t}\n\t\t\tconst mock = new RedirectedModule(raw, id, url, factoryOrRedirect);\n\t\t\tthis.add(mock);\n\t\t\treturn mock;\n\t\t} else {\n\t\t\tthrow new Error(`[vitest] Unknown mock type: ${type}`);\n\t\t}\n\t}\n\tdelete(id) {\n\t\tthis.registryByUrl.delete(id);\n\t}\n\tget(id) {\n\t\treturn this.registryByUrl.get(id);\n\t}\n\tgetById(id) {\n\t\treturn this.registryById.get(id);\n\t}\n\thas(id) {\n\t\treturn this.registryByUrl.has(id);\n\t}\n}\nclass AutomockedModule {\n\ttype = \"automock\";\n\tconstructor(raw, id, url) {\n\t\tthis.raw = raw;\n\t\tthis.id = id;\n\t\tthis.url = url;\n\t}\n\tstatic fromJSON(data) {\n\t\treturn new AutospiedModule(data.raw, data.id, data.url);\n\t}\n\ttoJSON() {\n\t\treturn {\n\t\t\ttype: this.type,\n\t\t\turl: this.url,\n\t\t\traw: this.raw,\n\t\t\tid: this.id\n\t\t};\n\t}\n}\nclass AutospiedModule {\n\ttype = \"autospy\";\n\tconstructor(raw, id, url) {\n\t\tthis.raw = raw;\n\t\tthis.id = id;\n\t\tthis.url = url;\n\t}\n\tstatic fromJSON(data) {\n\t\treturn new AutospiedModule(data.raw, data.id, data.url);\n\t}\n\ttoJSON() {\n\t\treturn {\n\t\t\ttype: this.type,\n\t\t\turl: this.url,\n\t\t\tid: this.id,\n\t\t\traw: this.raw\n\t\t};\n\t}\n}\nclass RedirectedModule {\n\ttype = \"redirect\";\n\tconstructor(raw, id, url, redirect) {\n\t\tthis.raw = raw;\n\t\tthis.id = id;\n\t\tthis.url = url;\n\t\tthis.redirect = redirect;\n\t}\n\tstatic fromJSON(data) {\n\t\treturn new RedirectedModule(data.raw, data.id, data.url, data.redirect);\n\t}\n\ttoJSON() {\n\t\treturn {\n\t\t\ttype: this.type,\n\t\t\turl: this.url,\n\t\t\traw: this.raw,\n\t\t\tid: this.id,\n\t\t\tredirect: this.redirect\n\t\t};\n\t}\n}\nclass ManualMockedModule {\n\tcache;\n\ttype = \"manual\";\n\tconstructor(raw, id, url, factory) {\n\t\tthis.raw = raw;\n\t\tthis.id = id;\n\t\tthis.url = url;\n\t\tthis.factory = factory;\n\t}\n\tasync resolve() {\n\t\tif (this.cache) {\n\t\t\treturn this.cache;\n\t\t}\n\t\tlet exports;\n\t\ttry {\n\t\t\texports = await this.factory();\n\t\t} catch (err) {\n\t\t\tconst vitestError = new Error(\"[vitest] There was an error when mocking a module. \" + \"If you are using \\\"vi.mock\\\" factory, make sure there are no top level variables inside, since this call is hoisted to top of the file. \" + \"Read more: https://vitest.dev/api/vi.html#vi-mock\");\n\t\t\tvitestError.cause = err;\n\t\t\tthrow vitestError;\n\t\t}\n\t\tif (exports === null || typeof exports !== \"object\" || Array.isArray(exports)) {\n\t\t\tthrow new TypeError(`[vitest] vi.mock(\"${this.raw}\", factory?: () => unknown) is not returning an object. Did you mean to return an object with a \"default\" key?`);\n\t\t}\n\t\treturn this.cache = exports;\n\t}\n\tstatic fromJSON(data, factory) {\n\t\treturn new ManualMockedModule(data.raw, data.id, data.url, factory);\n\t}\n\ttoJSON() {\n\t\treturn {\n\t\t\ttype: this.type,\n\t\t\turl: this.url,\n\t\t\tid: this.id,\n\t\t\traw: this.raw\n\t\t};\n\t}\n}\n\nexport { AutomockedModule as A, MockerRegistry as M, RedirectedModule as R, ManualMockedModule as a, AutospiedModule as b };\n", "export { A as AutomockedModule, b as AutospiedModule, a as ManualMockedModule, M as MockerRegistry, R as RedirectedModule } from './chunk-registry.js';\n\nfunction mockObject(options, object, mockExports = {}) {\n\tconst finalizers = new Array();\n\tconst refs = new RefTracker();\n\tconst define = (container, key, value) => {\n\t\ttry {\n\t\t\tcontainer[key] = value;\n\t\t\treturn true;\n\t\t} catch {\n\t\t\treturn false;\n\t\t}\n\t};\n\tconst mockPropertiesOf = (container, newContainer) => {\n\t\tconst containerType = getType(container);\n\t\tconst isModule = containerType === \"Module\" || !!container.__esModule;\n\t\tfor (const { key: property, descriptor } of getAllMockableProperties(container, isModule, options.globalConstructors)) {\n\t\t\t// Modules define their exports as getters. We want to process those.\n\t\t\tif (!isModule && descriptor.get) {\n\t\t\t\ttry {\n\t\t\t\t\tObject.defineProperty(newContainer, property, descriptor);\n\t\t\t\t} catch {}\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\t// Skip special read-only props, we don't want to mess with those.\n\t\t\tif (isSpecialProp(property, containerType)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tconst value = container[property];\n\t\t\t// Special handling of references we've seen before to prevent infinite\n\t\t\t// recursion in circular objects.\n\t\t\tconst refId = refs.getId(value);\n\t\t\tif (refId !== undefined) {\n\t\t\t\tfinalizers.push(() => define(newContainer, property, refs.getMockedValue(refId)));\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tconst type = getType(value);\n\t\t\tif (Array.isArray(value)) {\n\t\t\t\tdefine(newContainer, property, []);\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tconst isFunction = type.includes(\"Function\") && typeof value === \"function\";\n\t\t\tif ((!isFunction || value._isMockFunction) && type !== \"Object\" && type !== \"Module\") {\n\t\t\t\tdefine(newContainer, property, value);\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\t// Sometimes this assignment fails for some unknown reason. If it does,\n\t\t\t// just move along.\n\t\t\tif (!define(newContainer, property, isFunction ? value : {})) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tif (isFunction) {\n\t\t\t\tif (!options.spyOn) {\n\t\t\t\t\tthrow new Error(\"[@vitest/mocker] `spyOn` is not defined. This is a Vitest error. Please open a new issue with reproduction.\");\n\t\t\t\t}\n\t\t\t\tconst spyOn = options.spyOn;\n\t\t\t\tfunction mockFunction() {\n\t\t\t\t\t// detect constructor call and mock each instance's methods\n\t\t\t\t\t// so that mock states between prototype/instances don't affect each other\n\t\t\t\t\t// (jest reference https://github.com/jestjs/jest/blob/2c3d2409879952157433de215ae0eee5188a4384/packages/jest-mock/src/index.ts#L678-L691)\n\t\t\t\t\tif (this instanceof newContainer[property]) {\n\t\t\t\t\t\tfor (const { key, descriptor } of getAllMockableProperties(this, false, options.globalConstructors)) {\n\t\t\t\t\t\t\t// skip getter since it's not mocked on prototype as well\n\t\t\t\t\t\t\tif (descriptor.get) {\n\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tconst value = this[key];\n\t\t\t\t\t\t\tconst type = getType(value);\n\t\t\t\t\t\t\tconst isFunction = type.includes(\"Function\") && typeof value === \"function\";\n\t\t\t\t\t\t\tif (isFunction) {\n\t\t\t\t\t\t\t\t// mock and delegate calls to original prototype method, which should be also mocked already\n\t\t\t\t\t\t\t\tconst original = this[key];\n\t\t\t\t\t\t\t\tconst mock = spyOn(this, key).mockImplementation(original);\n\t\t\t\t\t\t\t\tconst origMockReset = mock.mockReset;\n\t\t\t\t\t\t\t\tmock.mockRestore = mock.mockReset = () => {\n\t\t\t\t\t\t\t\t\torigMockReset.call(mock);\n\t\t\t\t\t\t\t\t\tmock.mockImplementation(original);\n\t\t\t\t\t\t\t\t\treturn mock;\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst mock = spyOn(newContainer, property);\n\t\t\t\tif (options.type === \"automock\") {\n\t\t\t\t\tmock.mockImplementation(mockFunction);\n\t\t\t\t\tconst origMockReset = mock.mockReset;\n\t\t\t\t\tmock.mockRestore = mock.mockReset = () => {\n\t\t\t\t\t\torigMockReset.call(mock);\n\t\t\t\t\t\tmock.mockImplementation(mockFunction);\n\t\t\t\t\t\treturn mock;\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\t// tinyspy retains length, but jest doesn't.\n\t\t\t\tObject.defineProperty(newContainer[property], \"length\", { value: 0 });\n\t\t\t}\n\t\t\trefs.track(value, newContainer[property]);\n\t\t\tmockPropertiesOf(value, newContainer[property]);\n\t\t}\n\t};\n\tconst mockedObject = mockExports;\n\tmockPropertiesOf(object, mockedObject);\n\t// Plug together refs\n\tfor (const finalizer of finalizers) {\n\t\tfinalizer();\n\t}\n\treturn mockedObject;\n}\nclass RefTracker {\n\tidMap = new Map();\n\tmockedValueMap = new Map();\n\tgetId(value) {\n\t\treturn this.idMap.get(value);\n\t}\n\tgetMockedValue(id) {\n\t\treturn this.mockedValueMap.get(id);\n\t}\n\ttrack(originalValue, mockedValue) {\n\t\tconst newId = this.idMap.size;\n\t\tthis.idMap.set(originalValue, newId);\n\t\tthis.mockedValueMap.set(newId, mockedValue);\n\t\treturn newId;\n\t}\n}\nfunction getType(value) {\n\treturn Object.prototype.toString.apply(value).slice(8, -1);\n}\nfunction isSpecialProp(prop, parentType) {\n\treturn parentType.includes(\"Function\") && typeof prop === \"string\" && [\n\t\t\"arguments\",\n\t\t\"callee\",\n\t\t\"caller\",\n\t\t\"length\",\n\t\t\"name\"\n\t].includes(prop);\n}\nfunction getAllMockableProperties(obj, isModule, constructors) {\n\tconst { Map, Object, Function, RegExp, Array } = constructors;\n\tconst allProps = new Map();\n\tlet curr = obj;\n\tdo {\n\t\t// we don't need properties from these\n\t\tif (curr === Object.prototype || curr === Function.prototype || curr === RegExp.prototype) {\n\t\t\tbreak;\n\t\t}\n\t\tcollectOwnProperties(curr, (key) => {\n\t\t\tconst descriptor = Object.getOwnPropertyDescriptor(curr, key);\n\t\t\tif (descriptor) {\n\t\t\t\tallProps.set(key, {\n\t\t\t\t\tkey,\n\t\t\t\t\tdescriptor\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t} while (curr = Object.getPrototypeOf(curr));\n\t// default is not specified in ownKeys, if module is interoped\n\tif (isModule && !allProps.has(\"default\") && \"default\" in obj) {\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(obj, \"default\");\n\t\tif (descriptor) {\n\t\t\tallProps.set(\"default\", {\n\t\t\t\tkey: \"default\",\n\t\t\t\tdescriptor\n\t\t\t});\n\t\t}\n\t}\n\treturn Array.from(allProps.values());\n}\nfunction collectOwnProperties(obj, collector) {\n\tconst collect = typeof collector === \"function\" ? collector : (key) => collector.add(key);\n\tObject.getOwnPropertyNames(obj).forEach(collect);\n\tObject.getOwnPropertySymbols(obj).forEach(collect);\n}\n\nexport { mockObject };\n"], "mappings": ";AAAA,IAAM,iBAAN,MAAqB;AAAA,EACpB,gBAAgB,oBAAI,IAAI;AAAA,EACxB,eAAe,oBAAI,IAAI;AAAA,EACvB,QAAQ;AACP,SAAK,cAAc,MAAM;AACzB,SAAK,aAAa,MAAM;AAAA,EACzB;AAAA,EACA,OAAO;AACN,WAAO,KAAK,cAAc,KAAK;AAAA,EAChC;AAAA,EACA,IAAI,MAAM;AACT,SAAK,cAAc,IAAI,KAAK,KAAK,IAAI;AACrC,SAAK,aAAa,IAAI,KAAK,IAAI,IAAI;AAAA,EACpC;AAAA,EACA,SAAS,aAAa,KAAK,IAAI,KAAK,mBAAmB;AACtD,UAAM,OAAO,OAAO,gBAAgB,WAAW,YAAY,OAAO;AAClE,QAAI,OAAO,gBAAgB,UAAU;AACpC,YAAM,QAAQ;AACd,UAAI,iBAAiB,oBAAoB,iBAAiB,mBAAmB,iBAAiB,sBAAsB,iBAAiB,kBAAkB;AACtJ,cAAM,IAAI,UAAU,sIAA2I,MAAM,IAAI,mDAAwD;AAAA,MAClO;AACA,UAAI,MAAM,SAAS,YAAY;AAC9B,cAAM,SAAS,iBAAiB,SAAS,KAAK;AAC9C,aAAK,IAAI,MAAM;AACf,eAAO;AAAA,MACR,WAAW,MAAM,SAAS,WAAW;AACpC,cAAM,SAAS,gBAAgB,SAAS,KAAK;AAC7C,aAAK,IAAI,MAAM;AACf,eAAO;AAAA,MACR,WAAW,MAAM,SAAS,YAAY;AACrC,cAAM,SAAS,iBAAiB,SAAS,KAAK;AAC9C,aAAK,IAAI,MAAM;AACf,eAAO;AAAA,MACR,WAAW,MAAM,SAAS,UAAU;AACnC,cAAM,IAAI,MAAM,+GAA+G;AAAA,MAChI,OAAO;AACN,cAAM,IAAI,MAAM,sBAAsB,MAAM,IAAI,EAAE;AAAA,MACnD;AAAA,IACD;AACA,QAAI,OAAO,QAAQ,UAAU;AAC5B,YAAM,IAAI,UAAU,sCAAsC;AAAA,IAC3D;AACA,QAAI,OAAO,QAAQ,UAAU;AAC5B,YAAM,IAAI,UAAU,sCAAsC;AAAA,IAC3D;AACA,QAAI,OAAO,OAAO,UAAU;AAC3B,YAAM,IAAI,UAAU,sCAAsC;AAAA,IAC3D;AACA,QAAI,SAAS,UAAU;AACtB,UAAI,OAAO,sBAAsB,YAAY;AAC5C,cAAM,IAAI,UAAU,mDAAmD;AAAA,MACxE;AACA,YAAM,OAAO,IAAI,mBAAmB,KAAK,IAAI,KAAK,iBAAiB;AACnE,WAAK,IAAI,IAAI;AACb,aAAO;AAAA,IACR,WAAW,SAAS,cAAc,SAAS,WAAW;AACrD,YAAM,OAAO,SAAS,aAAa,IAAI,iBAAiB,KAAK,IAAI,GAAG,IAAI,IAAI,gBAAgB,KAAK,IAAI,GAAG;AACxG,WAAK,IAAI,IAAI;AACb,aAAO;AAAA,IACR,WAAW,SAAS,YAAY;AAC/B,UAAI,OAAO,sBAAsB,UAAU;AAC1C,cAAM,IAAI,UAAU,oDAAoD;AAAA,MACzE;AACA,YAAM,OAAO,IAAI,iBAAiB,KAAK,IAAI,KAAK,iBAAiB;AACjE,WAAK,IAAI,IAAI;AACb,aAAO;AAAA,IACR,OAAO;AACN,YAAM,IAAI,MAAM,+BAA+B,IAAI,EAAE;AAAA,IACtD;AAAA,EACD;AAAA,EACA,OAAO,IAAI;AACV,SAAK,cAAc,OAAO,EAAE;AAAA,EAC7B;AAAA,EACA,IAAI,IAAI;AACP,WAAO,KAAK,cAAc,IAAI,EAAE;AAAA,EACjC;AAAA,EACA,QAAQ,IAAI;AACX,WAAO,KAAK,aAAa,IAAI,EAAE;AAAA,EAChC;AAAA,EACA,IAAI,IAAI;AACP,WAAO,KAAK,cAAc,IAAI,EAAE;AAAA,EACjC;AACD;AACA,IAAM,mBAAN,MAAuB;AAAA,EACtB,OAAO;AAAA,EACP,YAAY,KAAK,IAAI,KAAK;AACzB,SAAK,MAAM;AACX,SAAK,KAAK;AACV,SAAK,MAAM;AAAA,EACZ;AAAA,EACA,OAAO,SAAS,MAAM;AACrB,WAAO,IAAI,gBAAgB,KAAK,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,EACvD;AAAA,EACA,SAAS;AACR,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,KAAK,KAAK;AAAA,MACV,KAAK,KAAK;AAAA,MACV,IAAI,KAAK;AAAA,IACV;AAAA,EACD;AACD;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACrB,OAAO;AAAA,EACP,YAAY,KAAK,IAAI,KAAK;AACzB,SAAK,MAAM;AACX,SAAK,KAAK;AACV,SAAK,MAAM;AAAA,EACZ;AAAA,EACA,OAAO,SAAS,MAAM;AACrB,WAAO,IAAI,iBAAgB,KAAK,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,EACvD;AAAA,EACA,SAAS;AACR,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,KAAK,KAAK;AAAA,MACV,IAAI,KAAK;AAAA,MACT,KAAK,KAAK;AAAA,IACX;AAAA,EACD;AACD;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACtB,OAAO;AAAA,EACP,YAAY,KAAK,IAAI,KAAK,UAAU;AACnC,SAAK,MAAM;AACX,SAAK,KAAK;AACV,SAAK,MAAM;AACX,SAAK,WAAW;AAAA,EACjB;AAAA,EACA,OAAO,SAAS,MAAM;AACrB,WAAO,IAAI,kBAAiB,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,QAAQ;AAAA,EACvE;AAAA,EACA,SAAS;AACR,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,KAAK,KAAK;AAAA,MACV,KAAK,KAAK;AAAA,MACV,IAAI,KAAK;AAAA,MACT,UAAU,KAAK;AAAA,IAChB;AAAA,EACD;AACD;AACA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACxB;AAAA,EACA,OAAO;AAAA,EACP,YAAY,KAAK,IAAI,KAAK,SAAS;AAClC,SAAK,MAAM;AACX,SAAK,KAAK;AACV,SAAK,MAAM;AACX,SAAK,UAAU;AAAA,EAChB;AAAA,EACA,MAAM,UAAU;AACf,QAAI,KAAK,OAAO;AACf,aAAO,KAAK;AAAA,IACb;AACA,QAAI;AACJ,QAAI;AACH,gBAAU,MAAM,KAAK,QAAQ;AAAA,IAC9B,SAAS,KAAK;AACb,YAAM,cAAc,IAAI,MAAM,4OAAwP;AACtR,kBAAY,QAAQ;AACpB,YAAM;AAAA,IACP;AACA,QAAI,YAAY,QAAQ,OAAO,YAAY,YAAY,MAAM,QAAQ,OAAO,GAAG;AAC9E,YAAM,IAAI,UAAU,qBAAqB,KAAK,GAAG,gHAAgH;AAAA,IAClK;AACA,WAAO,KAAK,QAAQ;AAAA,EACrB;AAAA,EACA,OAAO,SAAS,MAAM,SAAS;AAC9B,WAAO,IAAI,oBAAmB,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,OAAO;AAAA,EACnE;AAAA,EACA,SAAS;AACR,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,KAAK,KAAK;AAAA,MACV,IAAI,KAAK;AAAA,MACT,KAAK,KAAK;AAAA,IACX;AAAA,EACD;AACD;;;ACjLA,SAAS,WAAW,SAAS,QAAQ,cAAc,CAAC,GAAG;AACtD,QAAM,aAAa,IAAI,MAAM;AAC7B,QAAM,OAAO,IAAI,WAAW;AAC5B,QAAM,SAAS,CAAC,WAAW,KAAK,UAAU;AACzC,QAAI;AACH,gBAAU,GAAG,IAAI;AACjB,aAAO;AAAA,IACR,QAAQ;AACP,aAAO;AAAA,IACR;AAAA,EACD;AACA,QAAM,mBAAmB,CAAC,WAAW,iBAAiB;AACrD,UAAM,gBAAgB,QAAQ,SAAS;AACvC,UAAM,WAAW,kBAAkB,YAAY,CAAC,CAAC,UAAU;AAC3D,eAAW,EAAE,KAAK,UAAU,WAAW,KAAK,yBAAyB,WAAW,UAAU,QAAQ,kBAAkB,GAAG;AAEtH,UAAI,CAAC,YAAY,WAAW,KAAK;AAChC,YAAI;AACH,iBAAO,eAAe,cAAc,UAAU,UAAU;AAAA,QACzD,QAAQ;AAAA,QAAC;AACT;AAAA,MACD;AAEA,UAAI,cAAc,UAAU,aAAa,GAAG;AAC3C;AAAA,MACD;AACA,YAAM,QAAQ,UAAU,QAAQ;AAGhC,YAAM,QAAQ,KAAK,MAAM,KAAK;AAC9B,UAAI,UAAU,QAAW;AACxB,mBAAW,KAAK,MAAM,OAAO,cAAc,UAAU,KAAK,eAAe,KAAK,CAAC,CAAC;AAChF;AAAA,MACD;AACA,YAAM,OAAO,QAAQ,KAAK;AAC1B,UAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,eAAO,cAAc,UAAU,CAAC,CAAC;AACjC;AAAA,MACD;AACA,YAAM,aAAa,KAAK,SAAS,UAAU,KAAK,OAAO,UAAU;AACjE,WAAK,CAAC,cAAc,MAAM,oBAAoB,SAAS,YAAY,SAAS,UAAU;AACrF,eAAO,cAAc,UAAU,KAAK;AACpC;AAAA,MACD;AAGA,UAAI,CAAC,OAAO,cAAc,UAAU,aAAa,QAAQ,CAAC,CAAC,GAAG;AAC7D;AAAA,MACD;AACA,UAAI,YAAY;AAKf,YAAS,eAAT,WAAwB;AAIvB,cAAI,gBAAgB,aAAa,QAAQ,GAAG;AAC3C,uBAAW,EAAE,KAAK,YAAAA,YAAW,KAAK,yBAAyB,MAAM,OAAO,QAAQ,kBAAkB,GAAG;AAEpG,kBAAIA,YAAW,KAAK;AACnB;AAAA,cACD;AACA,oBAAMC,SAAQ,KAAK,GAAG;AACtB,oBAAMC,QAAO,QAAQD,MAAK;AAC1B,oBAAME,cAAaD,MAAK,SAAS,UAAU,KAAK,OAAOD,WAAU;AACjE,kBAAIE,aAAY;AAEf,sBAAM,WAAW,KAAK,GAAG;AACzB,sBAAMC,QAAO,MAAM,MAAM,GAAG,EAAE,mBAAmB,QAAQ;AACzD,sBAAM,gBAAgBA,MAAK;AAC3B,gBAAAA,MAAK,cAAcA,MAAK,YAAY,MAAM;AACzC,gCAAc,KAAKA,KAAI;AACvB,kBAAAA,MAAK,mBAAmB,QAAQ;AAChC,yBAAOA;AAAA,gBACR;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AA9BA,YAAI,CAAC,QAAQ,OAAO;AACnB,gBAAM,IAAI,MAAM,6GAA6G;AAAA,QAC9H;AACA,cAAM,QAAQ,QAAQ;AA4BtB,cAAM,OAAO,MAAM,cAAc,QAAQ;AACzC,YAAI,QAAQ,SAAS,YAAY;AAChC,eAAK,mBAAmB,YAAY;AACpC,gBAAM,gBAAgB,KAAK;AAC3B,eAAK,cAAc,KAAK,YAAY,MAAM;AACzC,0BAAc,KAAK,IAAI;AACvB,iBAAK,mBAAmB,YAAY;AACpC,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,eAAO,eAAe,aAAa,QAAQ,GAAG,UAAU,EAAE,OAAO,EAAE,CAAC;AAAA,MACrE;AACA,WAAK,MAAM,OAAO,aAAa,QAAQ,CAAC;AACxC,uBAAiB,OAAO,aAAa,QAAQ,CAAC;AAAA,IAC/C;AAAA,EACD;AACA,QAAM,eAAe;AACrB,mBAAiB,QAAQ,YAAY;AAErC,aAAW,aAAa,YAAY;AACnC,cAAU;AAAA,EACX;AACA,SAAO;AACR;AACA,IAAM,aAAN,MAAiB;AAAA,EAChB,QAAQ,oBAAI,IAAI;AAAA,EAChB,iBAAiB,oBAAI,IAAI;AAAA,EACzB,MAAM,OAAO;AACZ,WAAO,KAAK,MAAM,IAAI,KAAK;AAAA,EAC5B;AAAA,EACA,eAAe,IAAI;AAClB,WAAO,KAAK,eAAe,IAAI,EAAE;AAAA,EAClC;AAAA,EACA,MAAM,eAAe,aAAa;AACjC,UAAM,QAAQ,KAAK,MAAM;AACzB,SAAK,MAAM,IAAI,eAAe,KAAK;AACnC,SAAK,eAAe,IAAI,OAAO,WAAW;AAC1C,WAAO;AAAA,EACR;AACD;AACA,SAAS,QAAQ,OAAO;AACvB,SAAO,OAAO,UAAU,SAAS,MAAM,KAAK,EAAE,MAAM,GAAG,EAAE;AAC1D;AACA,SAAS,cAAc,MAAM,YAAY;AACxC,SAAO,WAAW,SAAS,UAAU,KAAK,OAAO,SAAS,YAAY;AAAA,IACrE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,EAAE,SAAS,IAAI;AAChB;AACA,SAAS,yBAAyB,KAAK,UAAU,cAAc;AAC9D,QAAM,EAAE,KAAAC,MAAK,QAAAC,SAAQ,UAAU,QAAQ,OAAAC,OAAM,IAAI;AACjD,QAAM,WAAW,IAAIF,KAAI;AACzB,MAAI,OAAO;AACX,KAAG;AAEF,QAAI,SAASC,QAAO,aAAa,SAAS,SAAS,aAAa,SAAS,OAAO,WAAW;AAC1F;AAAA,IACD;AACA,yBAAqB,MAAM,CAAC,QAAQ;AACnC,YAAM,aAAaA,QAAO,yBAAyB,MAAM,GAAG;AAC5D,UAAI,YAAY;AACf,iBAAS,IAAI,KAAK;AAAA,UACjB;AAAA,UACA;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD,CAAC;AAAA,EACF,SAAS,OAAOA,QAAO,eAAe,IAAI;AAE1C,MAAI,YAAY,CAAC,SAAS,IAAI,SAAS,KAAK,aAAa,KAAK;AAC7D,UAAM,aAAaA,QAAO,yBAAyB,KAAK,SAAS;AACjE,QAAI,YAAY;AACf,eAAS,IAAI,WAAW;AAAA,QACvB,KAAK;AAAA,QACL;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AACA,SAAOC,OAAM,KAAK,SAAS,OAAO,CAAC;AACpC;AACA,SAAS,qBAAqB,KAAK,WAAW;AAC7C,QAAM,UAAU,OAAO,cAAc,aAAa,YAAY,CAAC,QAAQ,UAAU,IAAI,GAAG;AACxF,SAAO,oBAAoB,GAAG,EAAE,QAAQ,OAAO;AAC/C,SAAO,sBAAsB,GAAG,EAAE,QAAQ,OAAO;AAClD;", "names": ["descriptor", "value", "type", "isFunction", "mock", "Map", "Object", "Array"]}