{"version": 3, "sources": ["../../../../../@jridgewell/resolve-uri/src/resolve-uri.ts", "../../../../../@jridgewell/sourcemap-codec/src/sourcemap-codec.ts", "../../../../../@jridgewell/sourcemap-codec/src/vlq.ts", "../../../../../@jridgewell/sourcemap-codec/src/strings.ts", "../../../../../@jridgewell/sourcemap-codec/src/scopes.ts", "umd:@jridgewell/sourcemap-codec", "umd:@j<PERSON>well/resolve-uri", "../../../../../@jridgewell/trace-mapping/src/trace-mapping.ts", "../../../../../@jridgewell/trace-mapping/src/resolve.ts", "../../../../../@jridgewell/trace-mapping/src/strip-filename.ts", "../../../../../@jridgewell/trace-mapping/src/sourcemap-segment.ts", "../../../../../@jridgewell/trace-mapping/src/sort.ts", "../../../../../@jridgewell/trace-mapping/src/binary-search.ts", "../../../../../@jridgewell/trace-mapping/src/by-source.ts", "../../../../../@jridgewell/trace-mapping/src/types.ts", "../../../../../@jridgewell/trace-mapping/src/flatten-map.ts", "umd:@jridgewell/sourcemap-codec", "umd:@jridgewell/trace-mapping", "../../../../../@jridgewell/gen-mapping/src/gen-mapping.ts", "../../../../../@jridgewell/gen-mapping/src/set-array.ts", "../../../../../@jridgewell/gen-mapping/src/sourcemap-segment.ts", "../../../../../@ampproject/remapping/src/source-map-tree.ts", "../../../../../@ampproject/remapping/src/build-source-map-tree.ts", "../../../../../@ampproject/remapping/src/source-map.ts", "../../../../../@ampproject/remapping/src/remapping.ts"], "sourcesContent": ["// Matches the scheme of a URL, eg \"http://\"\nconst schemeRegex = /^[\\w+.-]+:\\/\\//;\n\n/**\n * Matches the parts of a URL:\n * 1. Scheme, including \":\", guaranteed.\n * 2. User/password, including \"@\", optional.\n * 3. Host, guaranteed.\n * 4. Port, including \":\", optional.\n * 5. Path, including \"/\", optional.\n * 6. Query, including \"?\", optional.\n * 7. Hash, including \"#\", optional.\n */\nconst urlRegex = /^([\\w+.-]+:)\\/\\/([^@/#?]*@)?([^:/#?]*)(:\\d+)?(\\/[^#?]*)?(\\?[^#]*)?(#.*)?/;\n\n/**\n * File URLs are weird. They dont' need the regular `//` in the scheme, they may or may not start\n * with a leading `/`, they can have a domain (but only if they don't start with a Windows drive).\n *\n * 1. Host, optional.\n * 2. Path, which may include \"/\", guaranteed.\n * 3. Query, including \"?\", optional.\n * 4. Hash, including \"#\", optional.\n */\nconst fileRegex = /^file:(?:\\/\\/((?![a-z]:)[^/#?]*)?)?(\\/?[^#?]*)(\\?[^#]*)?(#.*)?/i;\n\ntype Url = {\n  scheme: string;\n  user: string;\n  host: string;\n  port: string;\n  path: string;\n  query: string;\n  hash: string;\n  type: UrlType;\n};\n\nconst enum UrlType {\n  Empty = 1,\n  Hash = 2,\n  Query = 3,\n  RelativePath = 4,\n  AbsolutePath = 5,\n  SchemeRelative = 6,\n  Absolute = 7,\n}\n\nfunction isAbsoluteUrl(input: string): boolean {\n  return schemeRegex.test(input);\n}\n\nfunction isSchemeRelativeUrl(input: string): boolean {\n  return input.startsWith('//');\n}\n\nfunction isAbsolutePath(input: string): boolean {\n  return input.startsWith('/');\n}\n\nfunction isFileUrl(input: string): boolean {\n  return input.startsWith('file:');\n}\n\nfunction isRelative(input: string): boolean {\n  return /^[.?#]/.test(input);\n}\n\nfunction parseAbsoluteUrl(input: string): Url {\n  const match = urlRegex.exec(input)!;\n  return makeUrl(\n    match[1],\n    match[2] || '',\n    match[3],\n    match[4] || '',\n    match[5] || '/',\n    match[6] || '',\n    match[7] || '',\n  );\n}\n\nfunction parseFileUrl(input: string): Url {\n  const match = fileRegex.exec(input)!;\n  const path = match[2];\n  return makeUrl(\n    'file:',\n    '',\n    match[1] || '',\n    '',\n    isAbsolutePath(path) ? path : '/' + path,\n    match[3] || '',\n    match[4] || '',\n  );\n}\n\nfunction makeUrl(\n  scheme: string,\n  user: string,\n  host: string,\n  port: string,\n  path: string,\n  query: string,\n  hash: string,\n): Url {\n  return {\n    scheme,\n    user,\n    host,\n    port,\n    path,\n    query,\n    hash,\n    type: UrlType.Absolute,\n  };\n}\n\nfunction parseUrl(input: string): Url {\n  if (isSchemeRelativeUrl(input)) {\n    const url = parseAbsoluteUrl('http:' + input);\n    url.scheme = '';\n    url.type = UrlType.SchemeRelative;\n    return url;\n  }\n\n  if (isAbsolutePath(input)) {\n    const url = parseAbsoluteUrl('http://foo.com' + input);\n    url.scheme = '';\n    url.host = '';\n    url.type = UrlType.AbsolutePath;\n    return url;\n  }\n\n  if (isFileUrl(input)) return parseFileUrl(input);\n\n  if (isAbsoluteUrl(input)) return parseAbsoluteUrl(input);\n\n  const url = parseAbsoluteUrl('http://foo.com/' + input);\n  url.scheme = '';\n  url.host = '';\n  url.type = input\n    ? input.startsWith('?')\n      ? UrlType.Query\n      : input.startsWith('#')\n      ? UrlType.Hash\n      : UrlType.RelativePath\n    : UrlType.Empty;\n  return url;\n}\n\nfunction stripPathFilename(path: string): string {\n  // If a path ends with a parent directory \"..\", then it's a relative path with excess parent\n  // paths. It's not a file, so we can't strip it.\n  if (path.endsWith('/..')) return path;\n  const index = path.lastIndexOf('/');\n  return path.slice(0, index + 1);\n}\n\nfunction mergePaths(url: Url, base: Url) {\n  normalizePath(base, base.type);\n\n  // If the path is just a \"/\", then it was an empty path to begin with (remember, we're a relative\n  // path).\n  if (url.path === '/') {\n    url.path = base.path;\n  } else {\n    // Resolution happens relative to the base path's directory, not the file.\n    url.path = stripPathFilename(base.path) + url.path;\n  }\n}\n\n/**\n * The path can have empty directories \"//\", unneeded parents \"foo/..\", or current directory\n * \"foo/.\". We need to normalize to a standard representation.\n */\nfunction normalizePath(url: Url, type: UrlType) {\n  const rel = type <= UrlType.RelativePath;\n  const pieces = url.path.split('/');\n\n  // We need to preserve the first piece always, so that we output a leading slash. The item at\n  // pieces[0] is an empty string.\n  let pointer = 1;\n\n  // Positive is the number of real directories we've output, used for popping a parent directory.\n  // Eg, \"foo/bar/..\" will have a positive 2, and we can decrement to be left with just \"foo\".\n  let positive = 0;\n\n  // We need to keep a trailing slash if we encounter an empty directory (eg, splitting \"foo/\" will\n  // generate `[\"foo\", \"\"]` pieces). And, if we pop a parent directory. But once we encounter a\n  // real directory, we won't need to append, unless the other conditions happen again.\n  let addTrailingSlash = false;\n\n  for (let i = 1; i < pieces.length; i++) {\n    const piece = pieces[i];\n\n    // An empty directory, could be a trailing slash, or just a double \"//\" in the path.\n    if (!piece) {\n      addTrailingSlash = true;\n      continue;\n    }\n\n    // If we encounter a real directory, then we don't need to append anymore.\n    addTrailingSlash = false;\n\n    // A current directory, which we can always drop.\n    if (piece === '.') continue;\n\n    // A parent directory, we need to see if there are any real directories we can pop. Else, we\n    // have an excess of parents, and we'll need to keep the \"..\".\n    if (piece === '..') {\n      if (positive) {\n        addTrailingSlash = true;\n        positive--;\n        pointer--;\n      } else if (rel) {\n        // If we're in a relativePath, then we need to keep the excess parents. Else, in an absolute\n        // URL, protocol relative URL, or an absolute path, we don't need to keep excess.\n        pieces[pointer++] = piece;\n      }\n      continue;\n    }\n\n    // We've encountered a real directory. Move it to the next insertion pointer, which accounts for\n    // any popped or dropped directories.\n    pieces[pointer++] = piece;\n    positive++;\n  }\n\n  let path = '';\n  for (let i = 1; i < pointer; i++) {\n    path += '/' + pieces[i];\n  }\n  if (!path || (addTrailingSlash && !path.endsWith('/..'))) {\n    path += '/';\n  }\n  url.path = path;\n}\n\n/**\n * Attempts to resolve `input` URL/path relative to `base`.\n */\nexport default function resolve(input: string, base: string | undefined): string {\n  if (!input && !base) return '';\n\n  const url = parseUrl(input);\n  let inputType = url.type;\n\n  if (base && inputType !== UrlType.Absolute) {\n    const baseUrl = parseUrl(base);\n    const baseType = baseUrl.type;\n\n    switch (inputType) {\n      case UrlType.Empty:\n        url.hash = baseUrl.hash;\n      // fall through\n\n      case UrlType.Hash:\n        url.query = baseUrl.query;\n      // fall through\n\n      case UrlType.Query:\n      case UrlType.RelativePath:\n        mergePaths(url, baseUrl);\n      // fall through\n\n      case UrlType.AbsolutePath:\n        // The host, user, and port are joined, you can't copy one without the others.\n        url.user = baseUrl.user;\n        url.host = baseUrl.host;\n        url.port = baseUrl.port;\n      // fall through\n\n      case UrlType.SchemeRelative:\n        // The input doesn't have a schema at least, so we need to copy at least that over.\n        url.scheme = baseUrl.scheme;\n    }\n    if (baseType > inputType) inputType = baseType;\n  }\n\n  normalizePath(url, inputType);\n\n  const queryHash = url.query + url.hash;\n  switch (inputType) {\n    // This is impossible, because of the empty checks at the start of the function.\n    // case UrlType.Empty:\n\n    case UrlType.Hash:\n    case UrlType.Query:\n      return queryHash;\n\n    case UrlType.RelativePath: {\n      // The first char is always a \"/\", and we need it to be relative.\n      const path = url.path.slice(1);\n\n      if (!path) return queryHash || '.';\n\n      if (isRelative(base || input) && !isRelative(path)) {\n        // If base started with a leading \".\", or there is no base and input started with a \".\",\n        // then we need to ensure that the relative path starts with a \".\". We don't know if\n        // relative starts with a \"..\", though, so check before prepending.\n        return './' + path + queryHash;\n      }\n\n      return path + queryHash;\n    }\n\n    case UrlType.AbsolutePath:\n      return url.path + queryHash;\n\n    default:\n      return url.scheme + '//' + url.user + url.host + url.port + url.path + queryHash;\n  }\n}\n", "import { comma, decodeInteger, encodeInteger, hasMoreVlq, semicolon } from './vlq';\nimport { StringWriter, StringReader } from './strings';\n\nexport {\n  decodeOriginalScopes,\n  encodeOriginalScopes,\n  decodeGeneratedRanges,\n  encodeGeneratedRanges,\n} from './scopes';\nexport type { OriginalScope, GeneratedRange, CallSite, BindingExpressionRange } from './scopes';\n\nexport type SourceMapSegment =\n  | [number]\n  | [number, number, number, number]\n  | [number, number, number, number, number];\nexport type SourceMapLine = SourceMapSegment[];\nexport type SourceMapMappings = SourceMapLine[];\n\nexport function decode(mappings: string): SourceMapMappings {\n  const { length } = mappings;\n  const reader = new StringReader(mappings);\n  const decoded: SourceMapMappings = [];\n  let genColumn = 0;\n  let sourcesIndex = 0;\n  let sourceLine = 0;\n  let sourceColumn = 0;\n  let namesIndex = 0;\n\n  do {\n    const semi = reader.indexOf(';');\n    const line: SourceMapLine = [];\n    let sorted = true;\n    let lastCol = 0;\n    genColumn = 0;\n\n    while (reader.pos < semi) {\n      let seg: SourceMapSegment;\n\n      genColumn = decodeInteger(reader, genColumn);\n      if (genColumn < lastCol) sorted = false;\n      lastCol = genColumn;\n\n      if (hasMoreVlq(reader, semi)) {\n        sourcesIndex = decodeInteger(reader, sourcesIndex);\n        sourceLine = decodeInteger(reader, sourceLine);\n        sourceColumn = decodeInteger(reader, sourceColumn);\n\n        if (hasMoreVlq(reader, semi)) {\n          namesIndex = decodeInteger(reader, namesIndex);\n          seg = [genColumn, sourcesIndex, sourceLine, sourceColumn, namesIndex];\n        } else {\n          seg = [genColumn, sourcesIndex, sourceLine, sourceColumn];\n        }\n      } else {\n        seg = [genColumn];\n      }\n\n      line.push(seg);\n      reader.pos++;\n    }\n\n    if (!sorted) sort(line);\n    decoded.push(line);\n    reader.pos = semi + 1;\n  } while (reader.pos <= length);\n\n  return decoded;\n}\n\nfunction sort(line: SourceMapSegment[]) {\n  line.sort(sortComparator);\n}\n\nfunction sortComparator(a: SourceMapSegment, b: SourceMapSegment): number {\n  return a[0] - b[0];\n}\n\nexport function encode(decoded: SourceMapMappings): string;\nexport function encode(decoded: Readonly<SourceMapMappings>): string;\nexport function encode(decoded: Readonly<SourceMapMappings>): string {\n  const writer = new StringWriter();\n  let sourcesIndex = 0;\n  let sourceLine = 0;\n  let sourceColumn = 0;\n  let namesIndex = 0;\n\n  for (let i = 0; i < decoded.length; i++) {\n    const line = decoded[i];\n    if (i > 0) writer.write(semicolon);\n    if (line.length === 0) continue;\n\n    let genColumn = 0;\n\n    for (let j = 0; j < line.length; j++) {\n      const segment = line[j];\n      if (j > 0) writer.write(comma);\n\n      genColumn = encodeInteger(writer, segment[0], genColumn);\n\n      if (segment.length === 1) continue;\n      sourcesIndex = encodeInteger(writer, segment[1], sourcesIndex);\n      sourceLine = encodeInteger(writer, segment[2], sourceLine);\n      sourceColumn = encodeInteger(writer, segment[3], sourceColumn);\n\n      if (segment.length === 4) continue;\n      namesIndex = encodeInteger(writer, segment[4], namesIndex);\n    }\n  }\n\n  return writer.flush();\n}\n", "import type { StringReader, StringWriter } from './strings';\n\nexport const comma = ','.charCodeAt(0);\nexport const semicolon = ';'.charCodeAt(0);\n\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nconst intToChar = new Uint8Array(64); // 64 possible chars.\nconst charToInt = new Uint8Array(128); // z is 122 in ASCII\n\nfor (let i = 0; i < chars.length; i++) {\n  const c = chars.charCodeAt(i);\n  intToChar[i] = c;\n  charToInt[c] = i;\n}\n\nexport function decodeInteger(reader: StringReader, relative: number): number {\n  let value = 0;\n  let shift = 0;\n  let integer = 0;\n\n  do {\n    const c = reader.next();\n    integer = charToInt[c];\n    value |= (integer & 31) << shift;\n    shift += 5;\n  } while (integer & 32);\n\n  const shouldNegate = value & 1;\n  value >>>= 1;\n\n  if (shouldNegate) {\n    value = -0x80000000 | -value;\n  }\n\n  return relative + value;\n}\n\nexport function encodeInteger(builder: StringWriter, num: number, relative: number): number {\n  let delta = num - relative;\n\n  delta = delta < 0 ? (-delta << 1) | 1 : delta << 1;\n  do {\n    let clamped = delta & 0b011111;\n    delta >>>= 5;\n    if (delta > 0) clamped |= 0b100000;\n    builder.write(intToChar[clamped]);\n  } while (delta > 0);\n\n  return num;\n}\n\nexport function hasMoreVlq(reader: StringReader, max: number) {\n  if (reader.pos >= max) return false;\n  return reader.peek() !== comma;\n}\n", "const bufLength = 1024 * 16;\n\n// Provide a fallback for older environments.\nconst td =\n  typeof TextDecoder !== 'undefined'\n    ? /* #__PURE__ */ new TextDecoder()\n    : typeof Buffer !== 'undefined'\n      ? {\n          decode(buf: Uint8Array): string {\n            const out = Buffer.from(buf.buffer, buf.byteOffset, buf.byteLength);\n            return out.toString();\n          },\n        }\n      : {\n          decode(buf: Uint8Array): string {\n            let out = '';\n            for (let i = 0; i < buf.length; i++) {\n              out += String.fromCharCode(buf[i]);\n            }\n            return out;\n          },\n        };\n\nexport class StringWriter {\n  pos = 0;\n  private out = '';\n  private buffer = new Uint8Array(bufLength);\n\n  write(v: number): void {\n    const { buffer } = this;\n    buffer[this.pos++] = v;\n    if (this.pos === bufLength) {\n      this.out += td.decode(buffer);\n      this.pos = 0;\n    }\n  }\n\n  flush(): string {\n    const { buffer, out, pos } = this;\n    return pos > 0 ? out + td.decode(buffer.subarray(0, pos)) : out;\n  }\n}\n\nexport class StringReader {\n  pos = 0;\n  declare private buffer: string;\n\n  constructor(buffer: string) {\n    this.buffer = buffer;\n  }\n\n  next(): number {\n    return this.buffer.charCodeAt(this.pos++);\n  }\n\n  peek(): number {\n    return this.buffer.charCodeAt(this.pos);\n  }\n\n  indexOf(char: string): number {\n    const { buffer, pos } = this;\n    const idx = buffer.indexOf(char, pos);\n    return idx === -1 ? buffer.length : idx;\n  }\n}\n", "import { String<PERSON>ead<PERSON>, StringWriter } from './strings';\nimport { comma, decodeInteger, encodeInteger, hasMoreVlq, semicolon } from './vlq';\n\nconst EMPTY: any[] = [];\n\ntype Line = number;\ntype Column = number;\ntype Kind = number;\ntype Name = number;\ntype Var = number;\ntype SourcesIndex = number;\ntype ScopesIndex = number;\n\ntype Mix<A, B, O> = (A & O) | (B & O);\n\nexport type OriginalScope = Mix<\n  [Line, Column, Line, Column, Kind],\n  [Line, Column, Line, Column, Kind, Name],\n  { vars: Var[] }\n>;\n\nexport type GeneratedRange = Mix<\n  [Line, Column, Line, Column],\n  [Line, Column, Line, Column, SourcesIndex, ScopesIndex],\n  {\n    callsite: CallSite | null;\n    bindings: Binding[];\n    isScope: boolean;\n  }\n>;\nexport type CallSite = [SourcesIndex, Line, Column];\ntype Binding = BindingExpressionRange[];\nexport type BindingExpressionRange = [Name] | [Name, Line, Column];\n\nexport function decodeOriginalScopes(input: string): OriginalScope[] {\n  const { length } = input;\n  const reader = new StringReader(input);\n  const scopes: OriginalScope[] = [];\n  const stack: OriginalScope[] = [];\n  let line = 0;\n\n  for (; reader.pos < length; reader.pos++) {\n    line = decodeInteger(reader, line);\n    const column = decodeInteger(reader, 0);\n\n    if (!hasMoreVlq(reader, length)) {\n      const last = stack.pop()!;\n      last[2] = line;\n      last[3] = column;\n      continue;\n    }\n\n    const kind = decodeInteger(reader, 0);\n    const fields = decodeInteger(reader, 0);\n    const hasName = fields & 0b0001;\n\n    const scope: OriginalScope = (\n      hasName ? [line, column, 0, 0, kind, decodeInteger(reader, 0)] : [line, column, 0, 0, kind]\n    ) as OriginalScope;\n\n    let vars: Var[] = EMPTY;\n    if (hasMoreVlq(reader, length)) {\n      vars = [];\n      do {\n        const varsIndex = decodeInteger(reader, 0);\n        vars.push(varsIndex);\n      } while (hasMoreVlq(reader, length));\n    }\n    scope.vars = vars;\n\n    scopes.push(scope);\n    stack.push(scope);\n  }\n\n  return scopes;\n}\n\nexport function encodeOriginalScopes(scopes: OriginalScope[]): string {\n  const writer = new StringWriter();\n\n  for (let i = 0; i < scopes.length; ) {\n    i = _encodeOriginalScopes(scopes, i, writer, [0]);\n  }\n\n  return writer.flush();\n}\n\nfunction _encodeOriginalScopes(\n  scopes: OriginalScope[],\n  index: number,\n  writer: StringWriter,\n  state: [\n    number, // GenColumn\n  ],\n): number {\n  const scope = scopes[index];\n  const { 0: startLine, 1: startColumn, 2: endLine, 3: endColumn, 4: kind, vars } = scope;\n\n  if (index > 0) writer.write(comma);\n\n  state[0] = encodeInteger(writer, startLine, state[0]);\n  encodeInteger(writer, startColumn, 0);\n  encodeInteger(writer, kind, 0);\n\n  const fields = scope.length === 6 ? 0b0001 : 0;\n  encodeInteger(writer, fields, 0);\n  if (scope.length === 6) encodeInteger(writer, scope[5], 0);\n\n  for (const v of vars) {\n    encodeInteger(writer, v, 0);\n  }\n\n  for (index++; index < scopes.length; ) {\n    const next = scopes[index];\n    const { 0: l, 1: c } = next;\n    if (l > endLine || (l === endLine && c >= endColumn)) {\n      break;\n    }\n    index = _encodeOriginalScopes(scopes, index, writer, state);\n  }\n\n  writer.write(comma);\n  state[0] = encodeInteger(writer, endLine, state[0]);\n  encodeInteger(writer, endColumn, 0);\n\n  return index;\n}\n\nexport function decodeGeneratedRanges(input: string): GeneratedRange[] {\n  const { length } = input;\n  const reader = new StringReader(input);\n  const ranges: GeneratedRange[] = [];\n  const stack: GeneratedRange[] = [];\n\n  let genLine = 0;\n  let definitionSourcesIndex = 0;\n  let definitionScopeIndex = 0;\n  let callsiteSourcesIndex = 0;\n  let callsiteLine = 0;\n  let callsiteColumn = 0;\n  let bindingLine = 0;\n  let bindingColumn = 0;\n\n  do {\n    const semi = reader.indexOf(';');\n    let genColumn = 0;\n\n    for (; reader.pos < semi; reader.pos++) {\n      genColumn = decodeInteger(reader, genColumn);\n\n      if (!hasMoreVlq(reader, semi)) {\n        const last = stack.pop()!;\n        last[2] = genLine;\n        last[3] = genColumn;\n        continue;\n      }\n\n      const fields = decodeInteger(reader, 0);\n      const hasDefinition = fields & 0b0001;\n      const hasCallsite = fields & 0b0010;\n      const hasScope = fields & 0b0100;\n\n      let callsite: CallSite | null = null;\n      let bindings: Binding[] = EMPTY;\n      let range: GeneratedRange;\n      if (hasDefinition) {\n        const defSourcesIndex = decodeInteger(reader, definitionSourcesIndex);\n        definitionScopeIndex = decodeInteger(\n          reader,\n          definitionSourcesIndex === defSourcesIndex ? definitionScopeIndex : 0,\n        );\n\n        definitionSourcesIndex = defSourcesIndex;\n        range = [genLine, genColumn, 0, 0, defSourcesIndex, definitionScopeIndex] as GeneratedRange;\n      } else {\n        range = [genLine, genColumn, 0, 0] as GeneratedRange;\n      }\n\n      range.isScope = !!hasScope;\n\n      if (hasCallsite) {\n        const prevCsi = callsiteSourcesIndex;\n        const prevLine = callsiteLine;\n        callsiteSourcesIndex = decodeInteger(reader, callsiteSourcesIndex);\n        const sameSource = prevCsi === callsiteSourcesIndex;\n        callsiteLine = decodeInteger(reader, sameSource ? callsiteLine : 0);\n        callsiteColumn = decodeInteger(\n          reader,\n          sameSource && prevLine === callsiteLine ? callsiteColumn : 0,\n        );\n\n        callsite = [callsiteSourcesIndex, callsiteLine, callsiteColumn];\n      }\n      range.callsite = callsite;\n\n      if (hasMoreVlq(reader, semi)) {\n        bindings = [];\n        do {\n          bindingLine = genLine;\n          bindingColumn = genColumn;\n          const expressionsCount = decodeInteger(reader, 0);\n          let expressionRanges: BindingExpressionRange[];\n          if (expressionsCount < -1) {\n            expressionRanges = [[decodeInteger(reader, 0)]];\n            for (let i = -1; i > expressionsCount; i--) {\n              const prevBl = bindingLine;\n              bindingLine = decodeInteger(reader, bindingLine);\n              bindingColumn = decodeInteger(reader, bindingLine === prevBl ? bindingColumn : 0);\n              const expression = decodeInteger(reader, 0);\n              expressionRanges.push([expression, bindingLine, bindingColumn]);\n            }\n          } else {\n            expressionRanges = [[expressionsCount]];\n          }\n          bindings.push(expressionRanges);\n        } while (hasMoreVlq(reader, semi));\n      }\n      range.bindings = bindings;\n\n      ranges.push(range);\n      stack.push(range);\n    }\n\n    genLine++;\n    reader.pos = semi + 1;\n  } while (reader.pos < length);\n\n  return ranges;\n}\n\nexport function encodeGeneratedRanges(ranges: GeneratedRange[]): string {\n  if (ranges.length === 0) return '';\n\n  const writer = new StringWriter();\n\n  for (let i = 0; i < ranges.length; ) {\n    i = _encodeGeneratedRanges(ranges, i, writer, [0, 0, 0, 0, 0, 0, 0]);\n  }\n\n  return writer.flush();\n}\n\nfunction _encodeGeneratedRanges(\n  ranges: GeneratedRange[],\n  index: number,\n  writer: StringWriter,\n  state: [\n    number, // GenLine\n    number, // GenColumn\n    number, // DefSourcesIndex\n    number, // DefScopesIndex\n    number, // CallSourcesIndex\n    number, // CallLine\n    number, // CallColumn\n  ],\n): number {\n  const range = ranges[index];\n  const {\n    0: startLine,\n    1: startColumn,\n    2: endLine,\n    3: endColumn,\n    isScope,\n    callsite,\n    bindings,\n  } = range;\n\n  if (state[0] < startLine) {\n    catchupLine(writer, state[0], startLine);\n    state[0] = startLine;\n    state[1] = 0;\n  } else if (index > 0) {\n    writer.write(comma);\n  }\n\n  state[1] = encodeInteger(writer, range[1], state[1]);\n\n  const fields =\n    (range.length === 6 ? 0b0001 : 0) | (callsite ? 0b0010 : 0) | (isScope ? 0b0100 : 0);\n  encodeInteger(writer, fields, 0);\n\n  if (range.length === 6) {\n    const { 4: sourcesIndex, 5: scopesIndex } = range;\n    if (sourcesIndex !== state[2]) {\n      state[3] = 0;\n    }\n    state[2] = encodeInteger(writer, sourcesIndex, state[2]);\n    state[3] = encodeInteger(writer, scopesIndex, state[3]);\n  }\n\n  if (callsite) {\n    const { 0: sourcesIndex, 1: callLine, 2: callColumn } = range.callsite!;\n    if (sourcesIndex !== state[4]) {\n      state[5] = 0;\n      state[6] = 0;\n    } else if (callLine !== state[5]) {\n      state[6] = 0;\n    }\n    state[4] = encodeInteger(writer, sourcesIndex, state[4]);\n    state[5] = encodeInteger(writer, callLine, state[5]);\n    state[6] = encodeInteger(writer, callColumn, state[6]);\n  }\n\n  if (bindings) {\n    for (const binding of bindings) {\n      if (binding.length > 1) encodeInteger(writer, -binding.length, 0);\n      const expression = binding[0][0];\n      encodeInteger(writer, expression, 0);\n      let bindingStartLine = startLine;\n      let bindingStartColumn = startColumn;\n      for (let i = 1; i < binding.length; i++) {\n        const expRange = binding[i];\n        bindingStartLine = encodeInteger(writer, expRange[1]!, bindingStartLine);\n        bindingStartColumn = encodeInteger(writer, expRange[2]!, bindingStartColumn);\n        encodeInteger(writer, expRange[0]!, 0);\n      }\n    }\n  }\n\n  for (index++; index < ranges.length; ) {\n    const next = ranges[index];\n    const { 0: l, 1: c } = next;\n    if (l > endLine || (l === endLine && c >= endColumn)) {\n      break;\n    }\n    index = _encodeGeneratedRanges(ranges, index, writer, state);\n  }\n\n  if (state[0] < endLine) {\n    catchupLine(writer, state[0], endLine);\n    state[0] = endLine;\n    state[1] = 0;\n  } else {\n    writer.write(comma);\n  }\n  state[1] = encodeInteger(writer, endColumn, state[1]);\n\n  return index;\n}\n\nfunction catchupLine(writer: StringWriter, lastLine: number, line: number) {\n  do {\n    writer.write(semicolon);\n  } while (++lastLine < line);\n}\n", null, null, "import { encode, decode } from '@jridgewell/sourcemap-codec';\n\nimport resolver from './resolve';\nimport maybeSort from './sort';\nimport buildBySources from './by-source';\nimport {\n  memoizedState,\n  memoizedBinarySearch,\n  upperBound,\n  lowerBound,\n  found as bsFound,\n} from './binary-search';\nimport {\n  COLUMN,\n  SOURCES_INDEX,\n  SOURCE_LINE,\n  SOURCE_COLUMN,\n  NAMES_INDEX,\n  REV_GENERATED_LINE,\n  REV_GENERATED_COLUMN,\n} from './sourcemap-segment';\nimport { parse } from './types';\n\nimport type { SourceMapSegment, ReverseSegment } from './sourcemap-segment';\nimport type {\n  SourceMapV3,\n  DecodedSourceMap,\n  EncodedSourceMap,\n  InvalidOriginalMapping,\n  OriginalMapping,\n  InvalidGeneratedMapping,\n  GeneratedMapping,\n  SourceMapInput,\n  Needle,\n  SourceNeedle,\n  SourceMap,\n  EachMapping,\n  Bias,\n  XInput,\n  SectionedSourceMap,\n  Ro,\n} from './types';\nimport type { Source } from './by-source';\nimport type { MemoState } from './binary-search';\n\nexport type { SourceMapSegment } from './sourcemap-segment';\nexport type {\n  SourceMap,\n  DecodedSourceMap,\n  EncodedSourceMap,\n  Section,\n  SectionedSourceMap,\n  SourceMapV3,\n  Bias,\n  EachMapping,\n  GeneratedMapping,\n  InvalidGeneratedMapping,\n  InvalidOriginalMapping,\n  Needle,\n  OriginalMapping,\n  OriginalMapping as Mapping,\n  SectionedSourceMapInput,\n  SourceMapInput,\n  SourceNeedle,\n  XInput,\n  EncodedSourceMapXInput,\n  DecodedSourceMapXInput,\n  SectionedSourceMapXInput,\n  SectionXInput,\n} from './types';\n\ninterface PublicMap {\n  _encoded: TraceMap['_encoded'];\n  _decoded: TraceMap['_decoded'];\n  _decodedMemo: TraceMap['_decodedMemo'];\n  _bySources: TraceMap['_bySources'];\n  _bySourceMemos: TraceMap['_bySourceMemos'];\n}\n\nconst LINE_GTR_ZERO = '`line` must be greater than 0 (lines start at line 1)';\nconst COL_GTR_EQ_ZERO = '`column` must be greater than or equal to 0 (columns start at column 0)';\n\nexport const LEAST_UPPER_BOUND = -1;\nexport const GREATEST_LOWER_BOUND = 1;\n\nexport { FlattenMap, FlattenMap as AnyMap } from './flatten-map';\n\nexport class TraceMap implements SourceMap {\n  declare version: SourceMapV3['version'];\n  declare file: SourceMapV3['file'];\n  declare names: SourceMapV3['names'];\n  declare sourceRoot: SourceMapV3['sourceRoot'];\n  declare sources: SourceMapV3['sources'];\n  declare sourcesContent: SourceMapV3['sourcesContent'];\n  declare ignoreList: SourceMapV3['ignoreList'];\n\n  declare resolvedSources: string[];\n  declare private _encoded: string | undefined;\n\n  declare private _decoded: SourceMapSegment[][] | undefined;\n  declare private _decodedMemo: MemoState;\n\n  declare private _bySources: Source[] | undefined;\n  declare private _bySourceMemos: MemoState[] | undefined;\n\n  constructor(map: Ro<SourceMapInput>, mapUrl?: string | null) {\n    const isString = typeof map === 'string';\n    if (!isString && (map as unknown as { _decodedMemo: any })._decodedMemo) return map as TraceMap;\n\n    const parsed = parse(map as Exclude<SourceMapInput, TraceMap>);\n\n    const { version, file, names, sourceRoot, sources, sourcesContent } = parsed;\n    this.version = version;\n    this.file = file;\n    this.names = names || [];\n    this.sourceRoot = sourceRoot;\n    this.sources = sources;\n    this.sourcesContent = sourcesContent;\n    this.ignoreList = parsed.ignoreList || (parsed as XInput).x_google_ignoreList || undefined;\n\n    const resolve = resolver(mapUrl, sourceRoot);\n    this.resolvedSources = sources.map(resolve);\n\n    const { mappings } = parsed;\n    if (typeof mappings === 'string') {\n      this._encoded = mappings;\n      this._decoded = undefined;\n    } else if (Array.isArray(mappings)) {\n      this._encoded = undefined;\n      this._decoded = maybeSort(mappings, isString);\n    } else if ((parsed as unknown as SectionedSourceMap).sections) {\n      throw new Error(`TraceMap passed sectioned source map, please use FlattenMap export instead`);\n    } else {\n      throw new Error(`invalid source map: ${JSON.stringify(parsed)}`);\n    }\n\n    this._decodedMemo = memoizedState();\n    this._bySources = undefined;\n    this._bySourceMemos = undefined;\n  }\n}\n\n/**\n * Typescript doesn't allow friend access to private fields, so this just casts the map into a type\n * with public access modifiers.\n */\nfunction cast(map: unknown): PublicMap {\n  return map as any;\n}\n\n/**\n * Returns the encoded (VLQ string) form of the SourceMap's mappings field.\n */\nexport function encodedMappings(map: TraceMap): EncodedSourceMap['mappings'] {\n  return (cast(map)._encoded ??= encode(cast(map)._decoded!));\n}\n\n/**\n * Returns the decoded (array of lines of segments) form of the SourceMap's mappings field.\n */\nexport function decodedMappings(map: TraceMap): Readonly<DecodedSourceMap['mappings']> {\n  return (cast(map)._decoded ||= decode(cast(map)._encoded!));\n}\n\n/**\n * A low-level API to find the segment associated with a generated line/column (think, from a\n * stack trace). Line and column here are 0-based, unlike `originalPositionFor`.\n */\nexport function traceSegment(\n  map: TraceMap,\n  line: number,\n  column: number,\n): Readonly<SourceMapSegment> | null {\n  const decoded = decodedMappings(map);\n\n  // It's common for parent source maps to have pointers to lines that have no\n  // mapping (like a \"//# sourceMappingURL=\") at the end of the child file.\n  if (line >= decoded.length) return null;\n\n  const segments = decoded[line];\n  const index = traceSegmentInternal(\n    segments,\n    cast(map)._decodedMemo,\n    line,\n    column,\n    GREATEST_LOWER_BOUND,\n  );\n\n  return index === -1 ? null : segments[index];\n}\n\n/**\n * A higher-level API to find the source/line/column associated with a generated line/column\n * (think, from a stack trace). Line is 1-based, but column is 0-based, due to legacy behavior in\n * `source-map` library.\n */\nexport function originalPositionFor(\n  map: TraceMap,\n  needle: Needle,\n): OriginalMapping | InvalidOriginalMapping {\n  let { line, column, bias } = needle;\n  line--;\n  if (line < 0) throw new Error(LINE_GTR_ZERO);\n  if (column < 0) throw new Error(COL_GTR_EQ_ZERO);\n\n  const decoded = decodedMappings(map);\n\n  // It's common for parent source maps to have pointers to lines that have no\n  // mapping (like a \"//# sourceMappingURL=\") at the end of the child file.\n  if (line >= decoded.length) return OMapping(null, null, null, null);\n\n  const segments = decoded[line];\n  const index = traceSegmentInternal(\n    segments,\n    cast(map)._decodedMemo,\n    line,\n    column,\n    bias || GREATEST_LOWER_BOUND,\n  );\n\n  if (index === -1) return OMapping(null, null, null, null);\n\n  const segment = segments[index];\n  if (segment.length === 1) return OMapping(null, null, null, null);\n\n  const { names, resolvedSources } = map;\n  return OMapping(\n    resolvedSources[segment[SOURCES_INDEX]],\n    segment[SOURCE_LINE] + 1,\n    segment[SOURCE_COLUMN],\n    segment.length === 5 ? names[segment[NAMES_INDEX]] : null,\n  );\n}\n\n/**\n * Finds the generated line/column position of the provided source/line/column source position.\n */\nexport function generatedPositionFor(\n  map: TraceMap,\n  needle: SourceNeedle,\n): GeneratedMapping | InvalidGeneratedMapping {\n  const { source, line, column, bias } = needle;\n  return generatedPosition(map, source, line, column, bias || GREATEST_LOWER_BOUND, false);\n}\n\n/**\n * Finds all generated line/column positions of the provided source/line/column source position.\n */\nexport function allGeneratedPositionsFor(map: TraceMap, needle: SourceNeedle): GeneratedMapping[] {\n  const { source, line, column, bias } = needle;\n  // SourceMapConsumer uses LEAST_UPPER_BOUND for some reason, so we follow suit.\n  return generatedPosition(map, source, line, column, bias || LEAST_UPPER_BOUND, true);\n}\n\n/**\n * Iterates each mapping in generated position order.\n */\nexport function eachMapping(map: TraceMap, cb: (mapping: EachMapping) => void): void {\n  const decoded = decodedMappings(map);\n  const { names, resolvedSources } = map;\n\n  for (let i = 0; i < decoded.length; i++) {\n    const line = decoded[i];\n    for (let j = 0; j < line.length; j++) {\n      const seg = line[j];\n\n      const generatedLine = i + 1;\n      const generatedColumn = seg[0];\n      let source = null;\n      let originalLine = null;\n      let originalColumn = null;\n      let name = null;\n      if (seg.length !== 1) {\n        source = resolvedSources[seg[1]];\n        originalLine = seg[2] + 1;\n        originalColumn = seg[3];\n      }\n      if (seg.length === 5) name = names[seg[4]];\n\n      cb({\n        generatedLine,\n        generatedColumn,\n        source,\n        originalLine,\n        originalColumn,\n        name,\n      } as EachMapping);\n    }\n  }\n}\n\nfunction sourceIndex(map: TraceMap, source: string): number {\n  const { sources, resolvedSources } = map;\n  let index = sources.indexOf(source);\n  if (index === -1) index = resolvedSources.indexOf(source);\n  return index;\n}\n\n/**\n * Retrieves the source content for a particular source, if its found. Returns null if not.\n */\nexport function sourceContentFor(map: TraceMap, source: string): string | null {\n  const { sourcesContent } = map;\n  if (sourcesContent == null) return null;\n  const index = sourceIndex(map, source);\n  return index === -1 ? null : sourcesContent[index];\n}\n\n/**\n * Determines if the source is marked to ignore by the source map.\n */\nexport function isIgnored(map: TraceMap, source: string): boolean {\n  const { ignoreList } = map;\n  if (ignoreList == null) return false;\n  const index = sourceIndex(map, source);\n  return index === -1 ? false : ignoreList.includes(index);\n}\n\n/**\n * A helper that skips sorting of the input map's mappings array, which can be expensive for larger\n * maps.\n */\nexport function presortedDecodedMap(map: DecodedSourceMap, mapUrl?: string): TraceMap {\n  const tracer = new TraceMap(clone(map, []), mapUrl);\n  cast(tracer)._decoded = map.mappings;\n  return tracer;\n}\n\n/**\n * Returns a sourcemap object (with decoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function decodedMap(\n  map: TraceMap,\n): Omit<DecodedSourceMap, 'mappings'> & { mappings: readonly SourceMapSegment[][] } {\n  return clone(map, decodedMappings(map));\n}\n\n/**\n * Returns a sourcemap object (with encoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function encodedMap(map: TraceMap): EncodedSourceMap {\n  return clone(map, encodedMappings(map));\n}\n\nfunction clone<T extends string | readonly SourceMapSegment[][]>(\n  map: TraceMap | DecodedSourceMap,\n  mappings: T,\n): T extends string ? EncodedSourceMap : DecodedSourceMap {\n  return {\n    version: map.version,\n    file: map.file,\n    names: map.names,\n    sourceRoot: map.sourceRoot,\n    sources: map.sources,\n    sourcesContent: map.sourcesContent,\n    mappings,\n    ignoreList: map.ignoreList || (map as XInput).x_google_ignoreList,\n  } as any;\n}\n\nfunction OMapping(source: null, line: null, column: null, name: null): InvalidOriginalMapping;\nfunction OMapping(\n  source: string,\n  line: number,\n  column: number,\n  name: string | null,\n): OriginalMapping;\nfunction OMapping(\n  source: string | null,\n  line: number | null,\n  column: number | null,\n  name: string | null,\n): OriginalMapping | InvalidOriginalMapping {\n  return { source, line, column, name } as any;\n}\n\nfunction GMapping(line: null, column: null): InvalidGeneratedMapping;\nfunction GMapping(line: number, column: number): GeneratedMapping;\nfunction GMapping(\n  line: number | null,\n  column: number | null,\n): GeneratedMapping | InvalidGeneratedMapping {\n  return { line, column } as any;\n}\n\nfunction traceSegmentInternal(\n  segments: SourceMapSegment[],\n  memo: MemoState,\n  line: number,\n  column: number,\n  bias: Bias,\n): number;\nfunction traceSegmentInternal(\n  segments: ReverseSegment[],\n  memo: MemoState,\n  line: number,\n  column: number,\n  bias: Bias,\n): number;\nfunction traceSegmentInternal(\n  segments: SourceMapSegment[] | ReverseSegment[],\n  memo: MemoState,\n  line: number,\n  column: number,\n  bias: Bias,\n): number {\n  let index = memoizedBinarySearch(segments, column, memo, line);\n  if (bsFound) {\n    index = (bias === LEAST_UPPER_BOUND ? upperBound : lowerBound)(segments, column, index);\n  } else if (bias === LEAST_UPPER_BOUND) index++;\n\n  if (index === -1 || index === segments.length) return -1;\n  return index;\n}\n\nfunction sliceGeneratedPositions(\n  segments: ReverseSegment[],\n  memo: MemoState,\n  line: number,\n  column: number,\n  bias: Bias,\n): GeneratedMapping[] {\n  let min = traceSegmentInternal(segments, memo, line, column, GREATEST_LOWER_BOUND);\n\n  // We ignored the bias when tracing the segment so that we're guarnateed to find the first (in\n  // insertion order) segment that matched. Even if we did respect the bias when tracing, we would\n  // still need to call `lowerBound()` to find the first segment, which is slower than just looking\n  // for the GREATEST_LOWER_BOUND to begin with. The only difference that matters for us is when the\n  // binary search didn't match, in which case GREATEST_LOWER_BOUND just needs to increment to\n  // match LEAST_UPPER_BOUND.\n  if (!bsFound && bias === LEAST_UPPER_BOUND) min++;\n\n  if (min === -1 || min === segments.length) return [];\n\n  // We may have found the segment that started at an earlier column. If this is the case, then we\n  // need to slice all generated segments that match _that_ column, because all such segments span\n  // to our desired column.\n  const matchedColumn = bsFound ? column : segments[min][COLUMN];\n\n  // The binary search is not guaranteed to find the lower bound when a match wasn't found.\n  if (!bsFound) min = lowerBound(segments, matchedColumn, min);\n  const max = upperBound(segments, matchedColumn, min);\n\n  const result = [];\n  for (; min <= max; min++) {\n    const segment = segments[min];\n    result.push(GMapping(segment[REV_GENERATED_LINE] + 1, segment[REV_GENERATED_COLUMN]));\n  }\n  return result;\n}\n\nfunction generatedPosition(\n  map: TraceMap,\n  source: string,\n  line: number,\n  column: number,\n  bias: Bias,\n  all: false,\n): GeneratedMapping | InvalidGeneratedMapping;\nfunction generatedPosition(\n  map: TraceMap,\n  source: string,\n  line: number,\n  column: number,\n  bias: Bias,\n  all: true,\n): GeneratedMapping[];\nfunction generatedPosition(\n  map: TraceMap,\n  source: string,\n  line: number,\n  column: number,\n  bias: Bias,\n  all: boolean,\n): GeneratedMapping | InvalidGeneratedMapping | GeneratedMapping[] {\n  line--;\n  if (line < 0) throw new Error(LINE_GTR_ZERO);\n  if (column < 0) throw new Error(COL_GTR_EQ_ZERO);\n\n  const { sources, resolvedSources } = map;\n  let sourceIndex = sources.indexOf(source);\n  if (sourceIndex === -1) sourceIndex = resolvedSources.indexOf(source);\n  if (sourceIndex === -1) return all ? [] : GMapping(null, null);\n\n  const generated = (cast(map)._bySources ||= buildBySources(\n    decodedMappings(map),\n    (cast(map)._bySourceMemos = sources.map(memoizedState)),\n  ));\n\n  const segments = generated[sourceIndex][line];\n  if (segments == null) return all ? [] : GMapping(null, null);\n\n  const memo = cast(map)._bySourceMemos![sourceIndex];\n\n  if (all) return sliceGeneratedPositions(segments, memo, line, column, bias);\n\n  const index = traceSegmentInternal(segments, memo, line, column, bias);\n  if (index === -1) return GMapping(null, null);\n\n  const segment = segments[index];\n  return GMapping(segment[REV_GENERATED_LINE] + 1, segment[REV_GENERATED_COLUMN]);\n}\n", "import resolveUri from '@jridgewell/resolve-uri';\nimport stripFilename from './strip-filename';\n\ntype Resolve = (source: string | null) => string;\nexport default function resolver(\n  mapUrl: string | null | undefined,\n  sourceRoot: string | undefined,\n): Resolve {\n  const from = stripFilename(mapUrl);\n  // The sourceRoot is always treated as a directory, if it's not empty.\n  // https://github.com/mozilla/source-map/blob/8cb3ee57/lib/util.js#L327\n  // https://github.com/chromium/chromium/blob/da4adbb3/third_party/blink/renderer/devtools/front_end/sdk/SourceMap.js#L400-L401\n  const prefix = sourceRoot ? sourceRoot + '/' : '';\n\n  return (source) => resolveUri(prefix + (source || ''), from);\n}\n", "/**\n * Removes everything after the last \"/\", but leaves the slash.\n */\nexport default function stripFilename(path: string | undefined | null): string {\n  if (!path) return '';\n  const index = path.lastIndexOf('/');\n  return path.slice(0, index + 1);\n}\n", "type GeneratedColumn = number;\ntype SourcesIndex = number;\ntype SourceLine = number;\ntype SourceColumn = number;\ntype NamesIndex = number;\n\ntype GeneratedLine = number;\n\nexport type SourceMapSegment =\n  | [GeneratedColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn, NamesIndex];\n\nexport type ReverseSegment = [SourceColumn, GeneratedLine, GeneratedColumn];\n\nexport const COLUMN = 0;\nexport const SOURCES_INDEX = 1;\nexport const SOURCE_LINE = 2;\nexport const SOURCE_COLUMN = 3;\nexport const NAMES_INDEX = 4;\n\nexport const REV_GENERATED_LINE = 1;\nexport const REV_GENERATED_COLUMN = 2;\n", "import { COLUMN } from './sourcemap-segment';\n\nimport type { SourceMapSegment } from './sourcemap-segment';\n\nexport default function maybeSort(\n  mappings: SourceMapSegment[][],\n  owned: boolean,\n): SourceMapSegment[][] {\n  const unsortedIndex = nextUnsortedSegmentLine(mappings, 0);\n  if (unsortedIndex === mappings.length) return mappings;\n\n  // If we own the array (meaning we parsed it from JSON), then we're free to directly mutate it. If\n  // not, we do not want to modify the consumer's input array.\n  if (!owned) mappings = mappings.slice();\n\n  for (let i = unsortedIndex; i < mappings.length; i = nextUnsortedSegmentLine(mappings, i + 1)) {\n    mappings[i] = sortSegments(mappings[i], owned);\n  }\n  return mappings;\n}\n\nfunction nextUnsortedSegmentLine(mappings: SourceMapSegment[][], start: number): number {\n  for (let i = start; i < mappings.length; i++) {\n    if (!isSorted(mappings[i])) return i;\n  }\n  return mappings.length;\n}\n\nfunction isSorted(line: SourceMapSegment[]): boolean {\n  for (let j = 1; j < line.length; j++) {\n    if (line[j][COLUMN] < line[j - 1][COLUMN]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction sortSegments(line: SourceMapSegment[], owned: boolean): SourceMapSegment[] {\n  if (!owned) line = line.slice();\n  return line.sort(sortComparator);\n}\n\nfunction sortComparator(a: SourceMapSegment, b: SourceMapSegment): number {\n  return a[COLUMN] - b[COLUMN];\n}\n", "import type { SourceMapSegment, ReverseSegment } from './sourcemap-segment';\nimport { COLUMN } from './sourcemap-segment';\n\nexport type MemoState = {\n  lastKey: number;\n  lastNeedle: number;\n  lastIndex: number;\n};\n\nexport let found = false;\n\n/**\n * A binary search implementation that returns the index if a match is found.\n * If no match is found, then the left-index (the index associated with the item that comes just\n * before the desired index) is returned. To maintain proper sort order, a splice would happen at\n * the next index:\n *\n * ```js\n * const array = [1, 3];\n * const needle = 2;\n * const index = binarySearch(array, needle, (item, needle) => item - needle);\n *\n * assert.equal(index, 0);\n * array.splice(index + 1, 0, needle);\n * assert.deepEqual(array, [1, 2, 3]);\n * ```\n */\nexport function binarySearch(\n  haystack: SourceMapSegment[] | ReverseSegment[],\n  needle: number,\n  low: number,\n  high: number,\n): number {\n  while (low <= high) {\n    const mid = low + ((high - low) >> 1);\n    const cmp = haystack[mid][COLUMN] - needle;\n\n    if (cmp === 0) {\n      found = true;\n      return mid;\n    }\n\n    if (cmp < 0) {\n      low = mid + 1;\n    } else {\n      high = mid - 1;\n    }\n  }\n\n  found = false;\n  return low - 1;\n}\n\nexport function upperBound(\n  haystack: SourceMapSegment[] | ReverseSegment[],\n  needle: number,\n  index: number,\n): number {\n  for (let i = index + 1; i < haystack.length; index = i++) {\n    if (haystack[i][COLUMN] !== needle) break;\n  }\n  return index;\n}\n\nexport function lowerBound(\n  haystack: SourceMapSegment[] | ReverseSegment[],\n  needle: number,\n  index: number,\n): number {\n  for (let i = index - 1; i >= 0; index = i--) {\n    if (haystack[i][COLUMN] !== needle) break;\n  }\n  return index;\n}\n\nexport function memoizedState(): MemoState {\n  return {\n    lastKey: -1,\n    lastNeedle: -1,\n    lastIndex: -1,\n  };\n}\n\n/**\n * This overly complicated beast is just to record the last tested line/column and the resulting\n * index, allowing us to skip a few tests if mappings are monotonically increasing.\n */\nexport function memoizedBinarySearch(\n  haystack: SourceMapSegment[] | ReverseSegment[],\n  needle: number,\n  state: MemoState,\n  key: number,\n): number {\n  const { lastKey, lastNeedle, lastIndex } = state;\n\n  let low = 0;\n  let high = haystack.length - 1;\n  if (key === lastKey) {\n    if (needle === lastNeedle) {\n      found = lastIndex !== -1 && haystack[lastIndex][COLUMN] === needle;\n      return lastIndex;\n    }\n\n    if (needle >= lastNeedle) {\n      // lastIndex may be -1 if the previous needle was not found.\n      low = lastIndex === -1 ? 0 : lastIndex;\n    } else {\n      high = lastIndex;\n    }\n  }\n  state.lastKey = key;\n  state.lastNeedle = needle;\n\n  return (state.lastIndex = binarySearch(haystack, needle, low, high));\n}\n", "import { COLUMN, SOURCES_INDEX, SOURCE_LINE, SOURCE_COLUMN } from './sourcemap-segment';\nimport { memoizedBinarySearch, upperBound } from './binary-search';\n\nimport type { ReverseSegment, SourceMapSegment } from './sourcemap-segment';\nimport type { MemoState } from './binary-search';\n\nexport type Source = {\n  __proto__: null;\n  [line: number]: Exclude<ReverseSegment, [number]>[];\n};\n\n// Rebuilds the original source files, with mappings that are ordered by source line/column instead\n// of generated line/column.\nexport default function buildBySources(\n  decoded: readonly SourceMapSegment[][],\n  memos: MemoState[],\n): Source[] {\n  const sources: Source[] = memos.map(buildNullArray);\n\n  for (let i = 0; i < decoded.length; i++) {\n    const line = decoded[i];\n    for (let j = 0; j < line.length; j++) {\n      const seg = line[j];\n      if (seg.length === 1) continue;\n\n      const sourceIndex = seg[SOURCES_INDEX];\n      const sourceLine = seg[SOURCE_LINE];\n      const sourceColumn = seg[SOURCE_COLUMN];\n      const originalSource = sources[sourceIndex];\n      const originalLine = (originalSource[sourceLine] ||= []);\n      const memo = memos[sourceIndex];\n\n      // The binary search either found a match, or it found the left-index just before where the\n      // segment should go. Either way, we want to insert after that. And there may be multiple\n      // generated segments associated with an original location, so there may need to move several\n      // indexes before we find where we need to insert.\n      let index = upperBound(\n        originalLine,\n        sourceColumn,\n        memoizedBinarySearch(originalLine, sourceColumn, memo, sourceLine),\n      );\n\n      memo.lastIndex = ++index;\n      insert(originalLine, index, [sourceColumn, i, seg[COLUMN]]);\n    }\n  }\n\n  return sources;\n}\n\nfunction insert<T>(array: T[], index: number, value: T) {\n  for (let i = array.length; i > index; i--) {\n    array[i] = array[i - 1];\n  }\n  array[index] = value;\n}\n\n// Null arrays allow us to use ordered index keys without actually allocating contiguous memory like\n// a real array. We use a null-prototype object to avoid prototype pollution and deoptimizations.\n// Numeric properties on objects are magically sorted in ascending order by the engine regardless of\n// the insertion order. So, by setting any numeric keys, even out of order, we'll get ascending\n// order when iterating with for-in.\nfunction buildNullArray<T extends { __proto__: null }>(): T {\n  return { __proto__: null } as T;\n}\n", "import type { SourceMapSegment } from './sourcemap-segment';\nimport type { GREATEST_LOWER_BOUND, LEAST_UPPER_BOUND, TraceMap } from './trace-mapping';\n\nexport interface SourceMapV3 {\n  file?: string | null;\n  names: string[];\n  sourceRoot?: string;\n  sources: (string | null)[];\n  sourcesContent?: (string | null)[];\n  version: 3;\n  ignoreList?: number[];\n}\n\nexport interface EncodedSourceMap extends SourceMapV3 {\n  mappings: string;\n}\n\nexport interface DecodedSourceMap extends SourceMapV3 {\n  mappings: SourceMapSegment[][];\n}\n\nexport interface Section {\n  offset: { line: number; column: number };\n  map: EncodedSourceMap | DecodedSourceMap | SectionedSourceMap;\n}\n\nexport interface SectionedSourceMap {\n  file?: string | null;\n  sections: Section[];\n  version: 3;\n}\n\nexport type OriginalMapping = {\n  source: string | null;\n  line: number;\n  column: number;\n  name: string | null;\n};\n\nexport type InvalidOriginalMapping = {\n  source: null;\n  line: null;\n  column: null;\n  name: null;\n};\n\nexport type GeneratedMapping = {\n  line: number;\n  column: number;\n};\nexport type InvalidGeneratedMapping = {\n  line: null;\n  column: null;\n};\n\nexport type Bias = typeof GREATEST_LOWER_BOUND | typeof LEAST_UPPER_BOUND;\n\nexport type XInput = { x_google_ignoreList?: SourceMapV3['ignoreList'] };\nexport type EncodedSourceMapXInput = EncodedSourceMap & XInput;\nexport type DecodedSourceMapXInput = DecodedSourceMap & XInput;\nexport type SectionedSourceMapXInput = Omit<SectionedSourceMap, 'sections'> & {\n  sections: SectionXInput[];\n};\nexport type SectionXInput = Omit<Section, 'map'> & {\n  map: SectionedSourceMapInput;\n};\n\nexport type SourceMapInput = string | EncodedSourceMapXInput | DecodedSourceMapXInput | TraceMap;\nexport type SectionedSourceMapInput = SourceMapInput | SectionedSourceMapXInput;\n\nexport type Needle = { line: number; column: number; bias?: Bias };\nexport type SourceNeedle = { source: string; line: number; column: number; bias?: Bias };\n\nexport type EachMapping =\n  | {\n      generatedLine: number;\n      generatedColumn: number;\n      source: null;\n      originalLine: null;\n      originalColumn: null;\n      name: null;\n    }\n  | {\n      generatedLine: number;\n      generatedColumn: number;\n      source: string | null;\n      originalLine: number;\n      originalColumn: number;\n      name: string | null;\n    };\n\nexport abstract class SourceMap {\n  declare version: SourceMapV3['version'];\n  declare file: SourceMapV3['file'];\n  declare names: SourceMapV3['names'];\n  declare sourceRoot: SourceMapV3['sourceRoot'];\n  declare sources: SourceMapV3['sources'];\n  declare sourcesContent: SourceMapV3['sourcesContent'];\n  declare resolvedSources: SourceMapV3['sources'];\n  declare ignoreList: SourceMapV3['ignoreList'];\n}\n\nexport type Ro<T> =\n  T extends Array<infer V>\n    ? V[] | Readonly<V[]> | RoArray<V> | Readonly<RoArray<V>>\n    : T extends object\n      ? T | Readonly<T> | RoObject<T> | Readonly<RoObject<T>>\n      : T;\ntype RoArray<T> = Ro<T>[];\ntype RoObject<T> = { [K in keyof T]: T[K] | Ro<T[K]> };\n\nexport function parse<T>(map: T): Exclude<T, string> {\n  return typeof map === 'string' ? JSON.parse(map) : (map as Exclude<T, string>);\n}\n", "import { TraceMap, presortedDecodedMap, decodedMappings } from './trace-mapping';\nimport {\n  COLUMN,\n  SOURCES_INDEX,\n  SOURCE_LINE,\n  SOURCE_COLUMN,\n  NAMES_INDEX,\n} from './sourcemap-segment';\nimport { parse } from './types';\n\nimport type {\n  DecodedSourceMap,\n  DecodedSourceMapXInput,\n  EncodedSourceMapXInput,\n  SectionedSourceMapXInput,\n  SectionedSourceMapInput,\n  SectionXInput,\n  Ro,\n} from './types';\nimport type { SourceMapSegment } from './sourcemap-segment';\n\ntype FlattenMap = {\n  new (map: Ro<SectionedSourceMapInput>, mapUrl?: string | null): TraceMap;\n  (map: Ro<SectionedSourceMapInput>, mapUrl?: string | null): TraceMap;\n};\n\nexport const FlattenMap: FlattenMap = function (map, mapUrl) {\n  const parsed = parse(map as SectionedSourceMapInput);\n\n  if (!('sections' in parsed)) {\n    return new TraceMap(parsed as DecodedSourceMapXInput | EncodedSourceMapXInput, mapUrl);\n  }\n\n  const mappings: SourceMapSegment[][] = [];\n  const sources: string[] = [];\n  const sourcesContent: (string | null)[] = [];\n  const names: string[] = [];\n  const ignoreList: number[] = [];\n\n  recurse(\n    parsed,\n    mapUrl,\n    mappings,\n    sources,\n    sourcesContent,\n    names,\n    ignoreList,\n    0,\n    0,\n    Infinity,\n    Infinity,\n  );\n\n  const joined: DecodedSourceMap = {\n    version: 3,\n    file: parsed.file,\n    names,\n    sources,\n    sourcesContent,\n    mappings,\n    ignoreList,\n  };\n\n  return presortedDecodedMap(joined);\n} as FlattenMap;\n\nfunction recurse(\n  input: SectionedSourceMapXInput,\n  mapUrl: string | null | undefined,\n  mappings: SourceMapSegment[][],\n  sources: string[],\n  sourcesContent: (string | null)[],\n  names: string[],\n  ignoreList: number[],\n  lineOffset: number,\n  columnOffset: number,\n  stopLine: number,\n  stopColumn: number,\n) {\n  const { sections } = input;\n  for (let i = 0; i < sections.length; i++) {\n    const { map, offset } = sections[i];\n\n    let sl = stopLine;\n    let sc = stopColumn;\n    if (i + 1 < sections.length) {\n      const nextOffset = sections[i + 1].offset;\n      sl = Math.min(stopLine, lineOffset + nextOffset.line);\n\n      if (sl === stopLine) {\n        sc = Math.min(stopColumn, columnOffset + nextOffset.column);\n      } else if (sl < stopLine) {\n        sc = columnOffset + nextOffset.column;\n      }\n    }\n\n    addSection(\n      map,\n      mapUrl,\n      mappings,\n      sources,\n      sourcesContent,\n      names,\n      ignoreList,\n      lineOffset + offset.line,\n      columnOffset + offset.column,\n      sl,\n      sc,\n    );\n  }\n}\n\nfunction addSection(\n  input: SectionXInput['map'],\n  mapUrl: string | null | undefined,\n  mappings: SourceMapSegment[][],\n  sources: string[],\n  sourcesContent: (string | null)[],\n  names: string[],\n  ignoreList: number[],\n  lineOffset: number,\n  columnOffset: number,\n  stopLine: number,\n  stopColumn: number,\n) {\n  const parsed = parse(input);\n  if ('sections' in parsed) return recurse(...(arguments as unknown as Parameters<typeof recurse>));\n\n  const map = new TraceMap(parsed, mapUrl);\n  const sourcesOffset = sources.length;\n  const namesOffset = names.length;\n  const decoded = decodedMappings(map);\n  const { resolvedSources, sourcesContent: contents, ignoreList: ignores } = map;\n\n  append(sources, resolvedSources);\n  append(names, map.names);\n\n  if (contents) append(sourcesContent, contents);\n  else for (let i = 0; i < resolvedSources.length; i++) sourcesContent.push(null);\n\n  if (ignores) for (let i = 0; i < ignores.length; i++) ignoreList.push(ignores[i] + sourcesOffset);\n\n  for (let i = 0; i < decoded.length; i++) {\n    const lineI = lineOffset + i;\n\n    // We can only add so many lines before we step into the range that the next section's map\n    // controls. When we get to the last line, then we'll start checking the segments to see if\n    // they've crossed into the column range. But it may not have any columns that overstep, so we\n    // still need to check that we don't overstep lines, too.\n    if (lineI > stopLine) return;\n\n    // The out line may already exist in mappings (if we're continuing the line started by a\n    // previous section). Or, we may have jumped ahead several lines to start this section.\n    const out = getLine(mappings, lineI);\n    // On the 0th loop, the section's column offset shifts us forward. On all other lines (since the\n    // map can be multiple lines), it doesn't.\n    const cOffset = i === 0 ? columnOffset : 0;\n\n    const line = decoded[i];\n    for (let j = 0; j < line.length; j++) {\n      const seg = line[j];\n      const column = cOffset + seg[COLUMN];\n\n      // If this segment steps into the column range that the next section's map controls, we need\n      // to stop early.\n      if (lineI === stopLine && column >= stopColumn) return;\n\n      if (seg.length === 1) {\n        out.push([column]);\n        continue;\n      }\n\n      const sourcesIndex = sourcesOffset + seg[SOURCES_INDEX];\n      const sourceLine = seg[SOURCE_LINE];\n      const sourceColumn = seg[SOURCE_COLUMN];\n      out.push(\n        seg.length === 4\n          ? [column, sourcesIndex, sourceLine, sourceColumn]\n          : [column, sourcesIndex, sourceLine, sourceColumn, namesOffset + seg[NAMES_INDEX]],\n      );\n    }\n  }\n}\n\nfunction append<T>(arr: T[], other: T[]) {\n  for (let i = 0; i < other.length; i++) arr.push(other[i]);\n}\n\nfunction getLine<T>(arr: T[][], index: number): T[] {\n  for (let i = arr.length; i <= index; i++) arr[i] = [];\n  return arr[index];\n}\n", null, null, "import { SetArray, put, remove } from './set-array';\nimport {\n  encode,\n  // encodeGeneratedRanges,\n  // encodeOriginalScopes\n} from '@jridgewell/sourcemap-codec';\nimport { TraceMap, decodedMappings } from '@jridgewell/trace-mapping';\n\nimport {\n  COLUMN,\n  SOURCES_INDEX,\n  SOURCE_LINE,\n  SOURCE_COLUMN,\n  NAMES_INDEX,\n} from './sourcemap-segment';\n\nimport type { SourceMapInput } from '@jridgewell/trace-mapping';\n// import type { OriginalScope, GeneratedRange } from '@jridgewell/sourcemap-codec';\nimport type { SourceMapSegment } from './sourcemap-segment';\nimport type {\n  DecodedSourceMap,\n  EncodedSourceMap,\n  Pos,\n  Mapping,\n  // BindingExpressionRange,\n  // OriginalPos,\n  // OriginalScopeInfo,\n  // GeneratedRangeInfo,\n} from './types';\n\nexport type { DecodedSourceMap, EncodedSourceMap, Mapping };\n\nexport type Options = {\n  file?: string | null;\n  sourceRoot?: string | null;\n};\n\nconst NO_NAME = -1;\n\n/**\n * Provides the state to generate a sourcemap.\n */\nexport class GenMapping {\n  declare private _names: SetArray<string>;\n  declare private _sources: SetArray<string>;\n  declare private _sourcesContent: (string | null)[];\n  declare private _mappings: SourceMapSegment[][];\n  // private declare _originalScopes: OriginalScope[][];\n  // private declare _generatedRanges: GeneratedRange[];\n  declare private _ignoreList: SetArray<number>;\n  declare file: string | null | undefined;\n  declare sourceRoot: string | null | undefined;\n\n  constructor({ file, sourceRoot }: Options = {}) {\n    this._names = new SetArray();\n    this._sources = new SetArray();\n    this._sourcesContent = [];\n    this._mappings = [];\n    // this._originalScopes = [];\n    // this._generatedRanges = [];\n    this.file = file;\n    this.sourceRoot = sourceRoot;\n    this._ignoreList = new SetArray();\n  }\n}\n\ninterface PublicMap {\n  _names: GenMapping['_names'];\n  _sources: GenMapping['_sources'];\n  _sourcesContent: GenMapping['_sourcesContent'];\n  _mappings: GenMapping['_mappings'];\n  // _originalScopes: GenMapping['_originalScopes'];\n  // _generatedRanges: GenMapping['_generatedRanges'];\n  _ignoreList: GenMapping['_ignoreList'];\n}\n\n/**\n * Typescript doesn't allow friend access to private fields, so this just casts the map into a type\n * with public access modifiers.\n */\nfunction cast(map: unknown): PublicMap {\n  return map as any;\n}\n\n/**\n * A low-level API to associate a generated position with an original source position. Line and\n * column here are 0-based, unlike `addMapping`.\n */\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source?: null,\n  sourceLine?: null,\n  sourceColumn?: null,\n  name?: null,\n  content?: null,\n): void;\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: string,\n  sourceLine: number,\n  sourceColumn: number,\n  name?: null,\n  content?: string | null,\n): void;\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: string,\n  sourceLine: number,\n  sourceColumn: number,\n  name: string,\n  content?: string | null,\n): void;\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source?: string | null,\n  sourceLine?: number | null,\n  sourceColumn?: number | null,\n  name?: string | null,\n  content?: string | null,\n): void {\n  return addSegmentInternal(\n    false,\n    map,\n    genLine,\n    genColumn,\n    source,\n    sourceLine,\n    sourceColumn,\n    name,\n    content,\n  );\n}\n\n/**\n * A high-level API to associate a generated position with an original source position. Line is\n * 1-based, but column is 0-based, due to legacy behavior in `source-map` library.\n */\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source?: null;\n    original?: null;\n    name?: null;\n    content?: null;\n  },\n): void;\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: string;\n    original: Pos;\n    name?: null;\n    content?: string | null;\n  },\n): void;\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: string;\n    original: Pos;\n    name: string;\n    content?: string | null;\n  },\n): void;\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source?: string | null;\n    original?: Pos | null;\n    name?: string | null;\n    content?: string | null;\n  },\n): void {\n  return addMappingInternal(false, map, mapping as Parameters<typeof addMappingInternal>[2]);\n}\n\n/**\n * Same as `addSegment`, but will only add the segment if it generates useful information in the\n * resulting map. This only works correctly if segments are added **in order**, meaning you should\n * not add a segment with a lower generated line/column than one that came before.\n */\nexport const maybeAddSegment: typeof addSegment = (\n  map,\n  genLine,\n  genColumn,\n  source,\n  sourceLine,\n  sourceColumn,\n  name,\n  content,\n) => {\n  return addSegmentInternal(\n    true,\n    map,\n    genLine,\n    genColumn,\n    source,\n    sourceLine,\n    sourceColumn,\n    name,\n    content,\n  );\n};\n\n/**\n * Same as `addMapping`, but will only add the mapping if it generates useful information in the\n * resulting map. This only works correctly if mappings are added **in order**, meaning you should\n * not add a mapping with a lower generated line/column than one that came before.\n */\nexport const maybeAddMapping: typeof addMapping = (map, mapping) => {\n  return addMappingInternal(true, map, mapping as Parameters<typeof addMappingInternal>[2]);\n};\n\n/**\n * Adds/removes the content of the source file to the source map.\n */\nexport function setSourceContent(map: GenMapping, source: string, content: string | null): void {\n  const {\n    _sources: sources,\n    _sourcesContent: sourcesContent,\n    // _originalScopes: originalScopes,\n  } = cast(map);\n  const index = put(sources, source);\n  sourcesContent[index] = content;\n  // if (index === originalScopes.length) originalScopes[index] = [];\n}\n\nexport function setIgnore(map: GenMapping, source: string, ignore = true) {\n  const {\n    _sources: sources,\n    _sourcesContent: sourcesContent,\n    _ignoreList: ignoreList,\n    // _originalScopes: originalScopes,\n  } = cast(map);\n  const index = put(sources, source);\n  if (index === sourcesContent.length) sourcesContent[index] = null;\n  // if (index === originalScopes.length) originalScopes[index] = [];\n  if (ignore) put(ignoreList, index);\n  else remove(ignoreList, index);\n}\n\n/**\n * Returns a sourcemap object (with decoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function toDecodedMap(map: GenMapping): DecodedSourceMap {\n  const {\n    _mappings: mappings,\n    _sources: sources,\n    _sourcesContent: sourcesContent,\n    _names: names,\n    _ignoreList: ignoreList,\n    // _originalScopes: originalScopes,\n    // _generatedRanges: generatedRanges,\n  } = cast(map);\n  removeEmptyFinalLines(mappings);\n\n  return {\n    version: 3,\n    file: map.file || undefined,\n    names: names.array,\n    sourceRoot: map.sourceRoot || undefined,\n    sources: sources.array,\n    sourcesContent,\n    mappings,\n    // originalScopes,\n    // generatedRanges,\n    ignoreList: ignoreList.array,\n  };\n}\n\n/**\n * Returns a sourcemap object (with encoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function toEncodedMap(map: GenMapping): EncodedSourceMap {\n  const decoded = toDecodedMap(map);\n  return Object.assign({}, decoded, {\n    // originalScopes: decoded.originalScopes.map((os) => encodeOriginalScopes(os)),\n    // generatedRanges: encodeGeneratedRanges(decoded.generatedRanges as GeneratedRange[]),\n    mappings: encode(decoded.mappings as SourceMapSegment[][]),\n  });\n}\n\n/**\n * Constructs a new GenMapping, using the already present mappings of the input.\n */\nexport function fromMap(input: SourceMapInput): GenMapping {\n  const map = new TraceMap(input);\n  const gen = new GenMapping({ file: map.file, sourceRoot: map.sourceRoot });\n\n  putAll(cast(gen)._names, map.names);\n  putAll(cast(gen)._sources, map.sources as string[]);\n  cast(gen)._sourcesContent = map.sourcesContent || map.sources.map(() => null);\n  cast(gen)._mappings = decodedMappings(map) as GenMapping['_mappings'];\n  // TODO: implement originalScopes/generatedRanges\n  if (map.ignoreList) putAll(cast(gen)._ignoreList, map.ignoreList);\n\n  return gen;\n}\n\n/**\n * Returns an array of high-level mapping objects for every recorded segment, which could then be\n * passed to the `source-map` library.\n */\nexport function allMappings(map: GenMapping): Mapping[] {\n  const out: Mapping[] = [];\n  const { _mappings: mappings, _sources: sources, _names: names } = cast(map);\n\n  for (let i = 0; i < mappings.length; i++) {\n    const line = mappings[i];\n    for (let j = 0; j < line.length; j++) {\n      const seg = line[j];\n\n      const generated = { line: i + 1, column: seg[COLUMN] };\n      let source: string | undefined = undefined;\n      let original: Pos | undefined = undefined;\n      let name: string | undefined = undefined;\n\n      if (seg.length !== 1) {\n        source = sources.array[seg[SOURCES_INDEX]];\n        original = { line: seg[SOURCE_LINE] + 1, column: seg[SOURCE_COLUMN] };\n\n        if (seg.length === 5) name = names.array[seg[NAMES_INDEX]];\n      }\n\n      out.push({ generated, source, original, name } as Mapping);\n    }\n  }\n\n  return out;\n}\n\n// This split declaration is only so that terser can elminiate the static initialization block.\nfunction addSegmentInternal<S extends string | null | undefined>(\n  skipable: boolean,\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: S,\n  sourceLine: S extends string ? number : null | undefined,\n  sourceColumn: S extends string ? number : null | undefined,\n  name: S extends string ? string | null | undefined : null | undefined,\n  content: S extends string ? string | null | undefined : null | undefined,\n): void {\n  const {\n    _mappings: mappings,\n    _sources: sources,\n    _sourcesContent: sourcesContent,\n    _names: names,\n    // _originalScopes: originalScopes,\n  } = cast(map);\n  const line = getIndex(mappings, genLine);\n  const index = getColumnIndex(line, genColumn);\n\n  if (!source) {\n    if (skipable && skipSourceless(line, index)) return;\n    return insert(line, index, [genColumn]);\n  }\n\n  // Sigh, TypeScript can't figure out sourceLine and sourceColumn aren't nullish if source\n  // isn't nullish.\n  assert<number>(sourceLine);\n  assert<number>(sourceColumn);\n\n  const sourcesIndex = put(sources, source);\n  const namesIndex = name ? put(names, name) : NO_NAME;\n  if (sourcesIndex === sourcesContent.length) sourcesContent[sourcesIndex] = content ?? null;\n  // if (sourcesIndex === originalScopes.length) originalScopes[sourcesIndex] = [];\n\n  if (skipable && skipSource(line, index, sourcesIndex, sourceLine, sourceColumn, namesIndex)) {\n    return;\n  }\n\n  return insert(\n    line,\n    index,\n    name\n      ? [genColumn, sourcesIndex, sourceLine, sourceColumn, namesIndex]\n      : [genColumn, sourcesIndex, sourceLine, sourceColumn],\n  );\n}\n\nfunction assert<T>(_val: unknown): asserts _val is T {\n  // noop.\n}\n\nfunction getIndex<T>(arr: T[][], index: number): T[] {\n  for (let i = arr.length; i <= index; i++) {\n    arr[i] = [];\n  }\n  return arr[index];\n}\n\nfunction getColumnIndex(line: SourceMapSegment[], genColumn: number): number {\n  let index = line.length;\n  for (let i = index - 1; i >= 0; index = i--) {\n    const current = line[i];\n    if (genColumn >= current[COLUMN]) break;\n  }\n  return index;\n}\n\nfunction insert<T>(array: T[], index: number, value: T) {\n  for (let i = array.length; i > index; i--) {\n    array[i] = array[i - 1];\n  }\n  array[index] = value;\n}\n\nfunction removeEmptyFinalLines(mappings: SourceMapSegment[][]) {\n  const { length } = mappings;\n  let len = length;\n  for (let i = len - 1; i >= 0; len = i, i--) {\n    if (mappings[i].length > 0) break;\n  }\n  if (len < length) mappings.length = len;\n}\n\nfunction putAll<T extends string | number>(setarr: SetArray<T>, array: T[]) {\n  for (let i = 0; i < array.length; i++) put(setarr, array[i]);\n}\n\nfunction skipSourceless(line: SourceMapSegment[], index: number): boolean {\n  // The start of a line is already sourceless, so adding a sourceless segment to the beginning\n  // doesn't generate any useful information.\n  if (index === 0) return true;\n\n  const prev = line[index - 1];\n  // If the previous segment is also sourceless, then adding another sourceless segment doesn't\n  // genrate any new information. Else, this segment will end the source/named segment and point to\n  // a sourceless position, which is useful.\n  return prev.length === 1;\n}\n\nfunction skipSource(\n  line: SourceMapSegment[],\n  index: number,\n  sourcesIndex: number,\n  sourceLine: number,\n  sourceColumn: number,\n  namesIndex: number,\n): boolean {\n  // A source/named segment at the start of a line gives position at that genColumn\n  if (index === 0) return false;\n\n  const prev = line[index - 1];\n\n  // If the previous segment is sourceless, then we're transitioning to a source.\n  if (prev.length === 1) return false;\n\n  // If the previous segment maps to the exact same source position, then this segment doesn't\n  // provide any new position information.\n  return (\n    sourcesIndex === prev[SOURCES_INDEX] &&\n    sourceLine === prev[SOURCE_LINE] &&\n    sourceColumn === prev[SOURCE_COLUMN] &&\n    namesIndex === (prev.length === 5 ? prev[NAMES_INDEX] : NO_NAME)\n  );\n}\n\nfunction addMappingInternal<S extends string | null | undefined>(\n  skipable: boolean,\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: S;\n    original: S extends string ? Pos : null | undefined;\n    name: S extends string ? string | null | undefined : null | undefined;\n    content: S extends string ? string | null | undefined : null | undefined;\n  },\n) {\n  const { generated, source, original, name, content } = mapping;\n  if (!source) {\n    return addSegmentInternal(\n      skipable,\n      map,\n      generated.line - 1,\n      generated.column,\n      null,\n      null,\n      null,\n      null,\n      null,\n    );\n  }\n  assert<Pos>(original);\n  return addSegmentInternal(\n    skipable,\n    map,\n    generated.line - 1,\n    generated.column,\n    source as string,\n    original.line - 1,\n    original.column,\n    name,\n    content,\n  );\n}\n\n/*\nexport function addOriginalScope(\n  map: GenMapping,\n  data: {\n    start: Pos;\n    end: Pos;\n    source: string;\n    kind: string;\n    name?: string;\n    variables?: string[];\n  },\n): OriginalScopeInfo {\n  const { start, end, source, kind, name, variables } = data;\n  const {\n    _sources: sources,\n    _sourcesContent: sourcesContent,\n    _originalScopes: originalScopes,\n    _names: names,\n  } = cast(map);\n  const index = put(sources, source);\n  if (index === sourcesContent.length) sourcesContent[index] = null;\n  if (index === originalScopes.length) originalScopes[index] = [];\n\n  const kindIndex = put(names, kind);\n  const scope: OriginalScope = name\n    ? [start.line - 1, start.column, end.line - 1, end.column, kindIndex, put(names, name)]\n    : [start.line - 1, start.column, end.line - 1, end.column, kindIndex];\n  if (variables) {\n    scope.vars = variables.map((v) => put(names, v));\n  }\n  const len = originalScopes[index].push(scope);\n  return [index, len - 1, variables];\n}\n*/\n\n// Generated Ranges\n/*\nexport function addGeneratedRange(\n  map: GenMapping,\n  data: {\n    start: Pos;\n    isScope: boolean;\n    originalScope?: OriginalScopeInfo;\n    callsite?: OriginalPos;\n  },\n): GeneratedRangeInfo {\n  const { start, isScope, originalScope, callsite } = data;\n  const {\n    _originalScopes: originalScopes,\n    _sources: sources,\n    _sourcesContent: sourcesContent,\n    _generatedRanges: generatedRanges,\n  } = cast(map);\n\n  const range: GeneratedRange = [\n    start.line - 1,\n    start.column,\n    0,\n    0,\n    originalScope ? originalScope[0] : -1,\n    originalScope ? originalScope[1] : -1,\n  ];\n  if (originalScope?.[2]) {\n    range.bindings = originalScope[2].map(() => [[-1]]);\n  }\n  if (callsite) {\n    const index = put(sources, callsite.source);\n    if (index === sourcesContent.length) sourcesContent[index] = null;\n    if (index === originalScopes.length) originalScopes[index] = [];\n    range.callsite = [index, callsite.line - 1, callsite.column];\n  }\n  if (isScope) range.isScope = true;\n  generatedRanges.push(range);\n\n  return [range, originalScope?.[2]];\n}\n\nexport function setEndPosition(range: GeneratedRangeInfo, pos: Pos) {\n  range[0][2] = pos.line - 1;\n  range[0][3] = pos.column;\n}\n\nexport function addBinding(\n  map: GenMapping,\n  range: GeneratedRangeInfo,\n  variable: string,\n  expression: string | BindingExpressionRange,\n) {\n  const { _names: names } = cast(map);\n  const bindings = (range[0].bindings ||= []);\n  const vars = range[1];\n\n  const index = vars!.indexOf(variable);\n  const binding = getIndex(bindings, index);\n\n  if (typeof expression === 'string') binding[0] = [put(names, expression)];\n  else {\n    const { start } = expression;\n    binding.push([put(names, expression.expression), start.line - 1, start.column]);\n  }\n}\n*/\n", "type Key = string | number | symbol;\n\n/**\n * SetArray acts like a `Set` (allowing only one occurrence of a string `key`), but provides the\n * index of the `key` in the backing array.\n *\n * This is designed to allow synchronizing a second array with the contents of the backing array,\n * like how in a sourcemap `sourcesContent[i]` is the source content associated with `source[i]`,\n * and there are never duplicates.\n */\nexport class SetArray<T extends Key = Key> {\n  declare private _indexes: Record<T, number | undefined>;\n  declare array: readonly T[];\n\n  constructor() {\n    this._indexes = { __proto__: null } as any;\n    this.array = [];\n  }\n}\n\ninterface PublicSet<T extends Key> {\n  array: T[];\n  _indexes: SetArray<T>['_indexes'];\n}\n\n/**\n * Typescript doesn't allow friend access to private fields, so this just casts the set into a type\n * with public access modifiers.\n */\nfunction cast<T extends Key>(set: SetArray<T>): PublicSet<T> {\n  return set as any;\n}\n\n/**\n * Gets the index associated with `key` in the backing array, if it is already present.\n */\nexport function get<T extends Key>(setarr: SetArray<T>, key: T): number | undefined {\n  return cast(setarr)._indexes[key];\n}\n\n/**\n * Puts `key` into the backing array, if it is not already present. Returns\n * the index of the `key` in the backing array.\n */\nexport function put<T extends Key>(setarr: SetArray<T>, key: T): number {\n  // The key may or may not be present. If it is present, it's a number.\n  const index = get(setarr, key);\n  if (index !== undefined) return index;\n\n  const { array, _indexes: indexes } = cast(setarr);\n\n  const length = array.push(key);\n  return (indexes[key] = length - 1);\n}\n\n/**\n * Pops the last added item out of the SetArray.\n */\nexport function pop<T extends Key>(setarr: SetArray<T>): void {\n  const { array, _indexes: indexes } = cast(setarr);\n  if (array.length === 0) return;\n\n  const last = array.pop()!;\n  indexes[last] = undefined;\n}\n\n/**\n * Removes the key, if it exists in the set.\n */\nexport function remove<T extends Key>(setarr: SetArray<T>, key: T): void {\n  const index = get(setarr, key);\n  if (index === undefined) return;\n\n  const { array, _indexes: indexes } = cast(setarr);\n  for (let i = index + 1; i < array.length; i++) {\n    const k = array[i];\n    array[i - 1] = k;\n    indexes[k]!--;\n  }\n  indexes[key] = undefined;\n  array.pop();\n}\n", "type GeneratedColumn = number;\ntype SourcesIndex = number;\ntype SourceLine = number;\ntype SourceColumn = number;\ntype NamesIndex = number;\n\nexport type SourceMapSegment =\n  | [GeneratedColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn, NamesIndex];\n\nexport const COLUMN = 0;\nexport const SOURCES_INDEX = 1;\nexport const SOURCE_LINE = 2;\nexport const SOURCE_COLUMN = 3;\nexport const NAMES_INDEX = 4;\n", "import { GenMapping, maybeAddSegment, setIgnore, setSourceContent } from '@jridgewell/gen-mapping';\nimport { traceSegment, decodedMappings } from '@jridgewell/trace-mapping';\n\nimport type { TraceMap } from '@jridgewell/trace-mapping';\n\nexport type SourceMapSegmentObject = {\n  column: number;\n  line: number;\n  name: string;\n  source: string;\n  content: string | null;\n  ignore: boolean;\n};\n\nexport type OriginalSource = {\n  map: null;\n  sources: Sources[];\n  source: string;\n  content: string | null;\n  ignore: boolean;\n};\n\nexport type MapSource = {\n  map: TraceMap;\n  sources: Sources[];\n  source: string;\n  content: null;\n  ignore: false;\n};\n\nexport type Sources = OriginalSource | MapSource;\n\nconst SOURCELESS_MAPPING = /* #__PURE__ */ SegmentObject('', -1, -1, '', null, false);\nconst EMPTY_SOURCES: Sources[] = [];\n\nfunction SegmentObject(\n  source: string,\n  line: number,\n  column: number,\n  name: string,\n  content: string | null,\n  ignore: boolean\n): SourceMapSegmentObject {\n  return { source, line, column, name, content, ignore };\n}\n\nfunction Source(\n  map: TraceMap,\n  sources: Sources[],\n  source: '',\n  content: null,\n  ignore: false\n): MapSource;\nfunction Source(\n  map: null,\n  sources: Sources[],\n  source: string,\n  content: string | null,\n  ignore: boolean\n): OriginalSource;\nfunction Source(\n  map: TraceMap | null,\n  sources: Sources[],\n  source: string | '',\n  content: string | null,\n  ignore: boolean\n): Sources {\n  return {\n    map,\n    sources,\n    source,\n    content,\n    ignore,\n  } as any;\n}\n\n/**\n * MapSource represents a single sourcemap, with the ability to trace mappings into its child nodes\n * (which may themselves be SourceMapTrees).\n */\nexport function MapSource(map: TraceMap, sources: Sources[]): MapSource {\n  return Source(map, sources, '', null, false);\n}\n\n/**\n * A \"leaf\" node in the sourcemap tree, representing an original, unmodified source file. Recursive\n * segment tracing ends at the `OriginalSource`.\n */\nexport function OriginalSource(\n  source: string,\n  content: string | null,\n  ignore: boolean\n): OriginalSource {\n  return Source(null, EMPTY_SOURCES, source, content, ignore);\n}\n\n/**\n * traceMappings is only called on the root level SourceMapTree, and begins the process of\n * resolving each mapping in terms of the original source files.\n */\nexport function traceMappings(tree: MapSource): GenMapping {\n  // TODO: Eventually support sourceRoot, which has to be removed because the sources are already\n  // fully resolved. We'll need to make sources relative to the sourceRoot before adding them.\n  const gen = new GenMapping({ file: tree.map.file });\n  const { sources: rootSources, map } = tree;\n  const rootNames = map.names;\n  const rootMappings = decodedMappings(map);\n\n  for (let i = 0; i < rootMappings.length; i++) {\n    const segments = rootMappings[i];\n\n    for (let j = 0; j < segments.length; j++) {\n      const segment = segments[j];\n      const genCol = segment[0];\n      let traced: SourceMapSegmentObject | null = SOURCELESS_MAPPING;\n\n      // 1-length segments only move the current generated column, there's no source information\n      // to gather from it.\n      if (segment.length !== 1) {\n        const source = rootSources[segment[1]];\n        traced = originalPositionFor(\n          source,\n          segment[2],\n          segment[3],\n          segment.length === 5 ? rootNames[segment[4]] : ''\n        );\n\n        // If the trace is invalid, then the trace ran into a sourcemap that doesn't contain a\n        // respective segment into an original source.\n        if (traced == null) continue;\n      }\n\n      const { column, line, name, content, source, ignore } = traced;\n\n      maybeAddSegment(gen, i, genCol, source, line, column, name);\n      if (source && content != null) setSourceContent(gen, source, content);\n      if (ignore) setIgnore(gen, source, true);\n    }\n  }\n\n  return gen;\n}\n\n/**\n * originalPositionFor is only called on children SourceMapTrees. It recurses down into its own\n * child SourceMapTrees, until we find the original source map.\n */\nexport function originalPositionFor(\n  source: Sources,\n  line: number,\n  column: number,\n  name: string\n): SourceMapSegmentObject | null {\n  if (!source.map) {\n    return SegmentObject(source.source, line, column, name, source.content, source.ignore);\n  }\n\n  const segment = traceSegment(source.map, line, column);\n\n  // If we couldn't find a segment, then this doesn't exist in the sourcemap.\n  if (segment == null) return null;\n  // 1-length segments only move the current generated column, there's no source information\n  // to gather from it.\n  if (segment.length === 1) return SOURCELESS_MAPPING;\n\n  return originalPositionFor(\n    source.sources[segment[1]],\n    segment[2],\n    segment[3],\n    segment.length === 5 ? source.map.names[segment[4]] : name\n  );\n}\n", "import { TraceMap } from '@jridgewell/trace-mapping';\n\nimport { OriginalSource, MapSource } from './source-map-tree';\n\nimport type { Sources, MapSource as MapSourceType } from './source-map-tree';\nimport type { SourceMapInput, SourceMapLoader, LoaderContext } from './types';\n\nfunction asArray<T>(value: T | T[]): T[] {\n  if (Array.isArray(value)) return value;\n  return [value];\n}\n\n/**\n * Recursively builds a tree structure out of sourcemap files, with each node\n * being either an `OriginalSource` \"leaf\" or a `SourceMapTree` composed of\n * `OriginalSource`s and `SourceMapTree`s.\n *\n * Every sourcemap is composed of a collection of source files and mappings\n * into locations of those source files. When we generate a `SourceMapTree` for\n * the sourcemap, we attempt to load each source file's own sourcemap. If it\n * does not have an associated sourcemap, it is considered an original,\n * unmodified source file.\n */\nexport default function buildSourceMapTree(\n  input: SourceMapInput | SourceMapInput[],\n  loader: SourceMapLoader\n): MapSourceType {\n  const maps = asArray(input).map((m) => new TraceMap(m, ''));\n  const map = maps.pop()!;\n\n  for (let i = 0; i < maps.length; i++) {\n    if (maps[i].sources.length > 1) {\n      throw new Error(\n        `Transformation map ${i} must have exactly one source file.\\n` +\n          'Did you specify these with the most recent transformation maps first?'\n      );\n    }\n  }\n\n  let tree = build(map, loader, '', 0);\n  for (let i = maps.length - 1; i >= 0; i--) {\n    tree = MapSource(maps[i], [tree]);\n  }\n  return tree;\n}\n\nfunction build(\n  map: TraceMap,\n  loader: SourceMapLoader,\n  importer: string,\n  importerDepth: number\n): MapSourceType {\n  const { resolvedSources, sourcesContent, ignoreList } = map;\n\n  const depth = importerDepth + 1;\n  const children = resolvedSources.map((sourceFile: string | null, i: number): Sources => {\n    // The loading context gives the loader more information about why this file is being loaded\n    // (eg, from which importer). It also allows the loader to override the location of the loaded\n    // sourcemap/original source, or to override the content in the sourcesContent field if it's\n    // an unmodified source file.\n    const ctx: LoaderContext = {\n      importer,\n      depth,\n      source: sourceFile || '',\n      content: undefined,\n      ignore: undefined,\n    };\n\n    // Use the provided loader callback to retrieve the file's sourcemap.\n    // TODO: We should eventually support async loading of sourcemap files.\n    const sourceMap = loader(ctx.source, ctx);\n\n    const { source, content, ignore } = ctx;\n\n    // If there is a sourcemap, then we need to recurse into it to load its source files.\n    if (sourceMap) return build(new TraceMap(sourceMap, source), loader, source, depth);\n\n    // Else, it's an unmodified source file.\n    // The contents of this unmodified source file can be overridden via the loader context,\n    // allowing it to be explicitly null or a string. If it remains undefined, we fall back to\n    // the importing sourcemap's `sourcesContent` field.\n    const sourceContent =\n      content !== undefined ? content : sourcesContent ? sourcesContent[i] : null;\n    const ignored = ignore !== undefined ? ignore : ignoreList ? ignoreList.includes(i) : false;\n    return OriginalSource(source, sourceContent, ignored);\n  });\n\n  return MapSource(map, children);\n}\n", "import { toDecodedMap, toEncodedMap } from '@jridgewell/gen-mapping';\n\nimport type { GenMapping } from '@jridgewell/gen-mapping';\nimport type { DecodedSourceMap, EncodedSourceMap, Options } from './types';\n\n/**\n * A SourceMap v3 compatible sourcemap, which only includes fields that were\n * provided to it.\n */\nexport default class SourceMap {\n  declare file?: string | null;\n  declare mappings: EncodedSourceMap['mappings'] | DecodedSourceMap['mappings'];\n  declare sourceRoot?: string;\n  declare names: string[];\n  declare sources: (string | null)[];\n  declare sourcesContent?: (string | null)[];\n  declare version: 3;\n  declare ignoreList: number[] | undefined;\n\n  constructor(map: GenMapping, options: Options) {\n    const out = options.decodedMappings ? toDecodedMap(map) : toEncodedMap(map);\n    this.version = out.version; // SourceMap spec says this should be first.\n    this.file = out.file;\n    this.mappings = out.mappings as SourceMap['mappings'];\n    this.names = out.names as SourceMap['names'];\n    this.ignoreList = out.ignoreList as SourceMap['ignoreList'];\n    this.sourceRoot = out.sourceRoot;\n\n    this.sources = out.sources as SourceMap['sources'];\n    if (!options.excludeContent) {\n      this.sourcesContent = out.sourcesContent as SourceMap['sourcesContent'];\n    }\n  }\n\n  toString(): string {\n    return JSON.stringify(this);\n  }\n}\n", "import buildSourceMapTree from './build-source-map-tree';\nimport { traceMappings } from './source-map-tree';\nimport SourceMap from './source-map';\n\nimport type { SourceMapInput, SourceMapLoader, Options } from './types';\nexport type {\n  SourceMapSegment,\n  EncodedSourceMap,\n  EncodedSourceMap as RawSourceMap,\n  DecodedSourceMap,\n  SourceMapInput,\n  SourceMapLoader,\n  LoaderContext,\n  Options,\n} from './types';\nexport type { SourceMap };\n\n/**\n * Traces through all the mappings in the root sourcemap, through the sources\n * (and their sourcemaps), all the way back to the original source location.\n *\n * `loader` will be called every time we encounter a source file. If it returns\n * a sourcemap, we will recurse into that sourcemap to continue the trace. If\n * it returns a falsey value, that source file is treated as an original,\n * unmodified source file.\n *\n * Pass `excludeContent` to exclude any self-containing source file content\n * from the output sourcemap.\n *\n * Pass `decodedMappings` to receive a SourceMap with decoded (instead of\n * VLQ encoded) mappings.\n */\nexport default function remapping(\n  input: SourceMapInput | SourceMapInput[],\n  loader: SourceMapLoader,\n  options?: boolean | Options\n): SourceMap {\n  const opts =\n    typeof options === 'object' ? options : { excludeContent: !!options, decodedMappings: false };\n  const tree = buildSourceMapTree(input, loader);\n  return new SourceMap(traceMappings(tree), opts);\n}\n"], "mappings": ";;;;;;;;;;;AACA,YAAM,cAAc;AAYpB,YAAM,WAAW;AAWjB,YAAM,YAAY;AAuBlB,eAAS,cAAc,OAAa;AAClC,eAAO,YAAY,KAAK,KAAK;MAC/B;AAEA,eAAS,oBAAoB,OAAa;AACxC,eAAO,MAAM,WAAW,IAAI;MAC9B;AAEA,eAAS,eAAe,OAAa;AACnC,eAAO,MAAM,WAAW,GAAG;MAC7B;AAEA,eAAS,UAAU,OAAa;AAC9B,eAAO,MAAM,WAAW,OAAO;MACjC;AAEA,eAAS,WAAW,OAAa;AAC/B,eAAO,SAAS,KAAK,KAAK;MAC5B;AAEA,eAAS,iBAAiB,OAAa;AACrC,cAAM,QAAQ,SAAS,KAAK,KAAK;AACjC,eAAO,QACL,MAAM,CAAC,GACP,MAAM,CAAC,KAAK,IACZ,MAAM,CAAC,GACP,MAAM,CAAC,KAAK,IACZ,MAAM,CAAC,KAAK,KACZ,MAAM,CAAC,KAAK,IACZ,MAAM,CAAC,KAAK,EAAE;MAElB;AAEA,eAAS,aAAa,OAAa;AACjC,cAAM,QAAQ,UAAU,KAAK,KAAK;AAClC,cAAM,OAAO,MAAM,CAAC;AACpB,eAAO,QACL,SACA,IACA,MAAM,CAAC,KAAK,IACZ,IACA,eAAe,IAAI,IAAI,OAAO,MAAM,MACpC,MAAM,CAAC,KAAK,IACZ,MAAM,CAAC,KAAK,EAAE;MAElB;AAEA,eAAS,QACP,QACA,MACA,MACA,MACA,MACA,OACA,MAAY;AAEZ,eAAO;UACL;UACA;UACA;UACA;UACA;UACA;UACA;UACA,MAAI;;MAER;AAEA,eAAS,SAAS,OAAa;AAC7B,YAAI,oBAAoB,KAAK,GAAG;AAC9B,gBAAMA,OAAM,iBAAiB,UAAU,KAAK;AAC5C,UAAAA,KAAI,SAAS;AACb,UAAAA,KAAI,OAAI;AACR,iBAAOA;;AAGT,YAAI,eAAe,KAAK,GAAG;AACzB,gBAAMA,OAAM,iBAAiB,mBAAmB,KAAK;AACrD,UAAAA,KAAI,SAAS;AACb,UAAAA,KAAI,OAAO;AACX,UAAAA,KAAI,OAAI;AACR,iBAAOA;;AAGT,YAAI,UAAU,KAAK;AAAG,iBAAO,aAAa,KAAK;AAE/C,YAAI,cAAc,KAAK;AAAG,iBAAO,iBAAiB,KAAK;AAEvD,cAAM,MAAM,iBAAiB,oBAAoB,KAAK;AACtD,YAAI,SAAS;AACb,YAAI,OAAO;AACX,YAAI,OAAO,QACP,MAAM,WAAW,GAAG,QAElB,MAAM,WAAW,GAAG;AAI1B,eAAO;MACT;AAEA,eAAS,kBAAkB,MAAY;AAGrC,YAAI,KAAK,SAAS,KAAK;AAAG,iBAAO;AACjC,cAAM,QAAQ,KAAK,YAAY,GAAG;AAClC,eAAO,KAAK,MAAM,GAAG,QAAQ,CAAC;MAChC;AAEA,eAAS,WAAW,KAAU,MAAS;AACrC,sBAAc,MAAM,KAAK,IAAI;AAI7B,YAAI,IAAI,SAAS,KAAK;AACpB,cAAI,OAAO,KAAK;eACX;AAEL,cAAI,OAAO,kBAAkB,KAAK,IAAI,IAAI,IAAI;;MAElD;AAMA,eAAS,cAAc,KAAU,MAAa;AAC5C,cAAM,MAAM,QAAI;AAChB,cAAM,SAAS,IAAI,KAAK,MAAM,GAAG;AAIjC,YAAI,UAAU;AAId,YAAI,WAAW;AAKf,YAAI,mBAAmB;AAEvB,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,gBAAM,QAAQ,OAAO,CAAC;AAGtB,cAAI,CAAC,OAAO;AACV,+BAAmB;AACnB;;AAIF,6BAAmB;AAGnB,cAAI,UAAU;AAAK;AAInB,cAAI,UAAU,MAAM;AAClB,gBAAI,UAAU;AACZ,iCAAmB;AACnB;AACA;uBACS,KAAK;AAGd,qBAAO,SAAS,IAAI;;AAEtB;;AAKF,iBAAO,SAAS,IAAI;AACpB;;AAGF,YAAI,OAAO;AACX,iBAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,kBAAQ,MAAM,OAAO,CAAC;;AAExB,YAAI,CAAC,QAAS,oBAAoB,CAAC,KAAK,SAAS,KAAK,GAAI;AACxD,kBAAQ;;AAEV,YAAI,OAAO;MACb;eAKwB,QAAQ,OAAe,MAAwB;AACrE,YAAI,CAAC,SAAS,CAAC;AAAM,iBAAO;AAE5B,cAAM,MAAM,SAAS,KAAK;AAC1B,YAAI,YAAY,IAAI;AAEpB,YAAI,QAAQ,cAAS,GAAuB;AAC1C,gBAAM,UAAU,SAAS,IAAI;AAC7B,gBAAM,WAAW,QAAQ;AAEzB,kBAAQ,WAAS;YACf,KAAA;AACE,kBAAI,OAAO,QAAQ;;YAGrB,KAAA;AACE,kBAAI,QAAQ,QAAQ;;YAGtB,KAAA;YACA,KAAA;AACE,yBAAW,KAAK,OAAO;;YAGzB,KAAA;AAEE,kBAAI,OAAO,QAAQ;AACnB,kBAAI,OAAO,QAAQ;AACnB,kBAAI,OAAO,QAAQ;;YAGrB,KAAA;AAEE,kBAAI,SAAS,QAAQ;;AAEzB,cAAI,WAAW;AAAW,wBAAY;;AAGxC,sBAAc,KAAK,SAAS;AAE5B,cAAM,YAAY,IAAI,QAAQ,IAAI;AAClC,gBAAQ,WAAS;;;UAIf,KAAA;UACA,KAAA;AACE,mBAAO;UAET,KAAA,GAA2B;AAEzB,kBAAM,OAAO,IAAI,KAAK,MAAM,CAAC;AAE7B,gBAAI,CAAC;AAAM,qBAAO,aAAa;AAE/B,gBAAI,WAAW,QAAQ,KAAK,KAAK,CAAC,WAAW,IAAI,GAAG;AAIlD,qBAAO,OAAO,OAAO;;AAGvB,mBAAO,OAAO;;UAGhB,KAAA;AACE,mBAAO,IAAI,OAAO;UAEpB;AACE,mBAAO,IAAI,SAAS,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO;;MAE7E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtTA,UAAA,0BAAA,CAAA;AAAA,eAAA,yBAAA;QAAA,QAAA,MAAA;QAAA,uBAAA,MAAA;QAAA,sBAAA,MAAA;QAAA,QAAA,MAAA;QAAA,uBAAA,MAAA;QAAA,sBAAA,MAAA;MAAA,CAAA;AAAA,MAAAC,QAAA,UAAA,aAAA,uBAAA;ACEO,UAAM,QAAQ,IAAI,WAAW,CAAC;AAC9B,UAAM,YAAY,IAAI,WAAW,CAAC;AAEzC,UAAM,QAAQ;AACd,UAAM,YAAY,IAAI,WAAW,EAAE;AACnC,UAAM,YAAY,IAAI,WAAW,GAAG;AAEpC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,IAAI,MAAM,WAAW,CAAC;AAC5B,kBAAU,CAAC,IAAI;AACf,kBAAU,CAAC,IAAI;MACjB;AAEO,eAAS,cAAc,QAAsB,UAA0B;AAC5E,YAAI,QAAQ;AACZ,YAAI,QAAQ;AACZ,YAAI,UAAU;AAEd,WAAG;AACD,gBAAM,IAAI,OAAO,KAAK;AACtB,oBAAU,UAAU,CAAC;AACrB,oBAAU,UAAU,OAAO;AAC3B,mBAAS;QACX,SAAS,UAAU;AAEnB,cAAM,eAAe,QAAQ;AAC7B,mBAAW;AAEX,YAAI,cAAc;AAChB,kBAAQ,cAAc,CAAC;QACzB;AAEA,eAAO,WAAW;MACpB;AAEO,eAAS,cAAc,SAAuB,KAAa,UAA0B;AAC1F,YAAI,QAAQ,MAAM;AAElB,gBAAQ,QAAQ,IAAK,CAAC,SAAS,IAAK,IAAI,SAAS;AACjD,WAAG;AACD,cAAI,UAAU,QAAQ;AACtB,qBAAW;AACX,cAAI,QAAQ,EAAG,YAAW;AAC1B,kBAAQ,MAAM,UAAU,OAAO,CAAC;QAClC,SAAS,QAAQ;AAEjB,eAAO;MACT;AAEO,eAAS,WAAW,QAAsB,KAAa;AAC5D,YAAI,OAAO,OAAO,IAAK,QAAO;AAC9B,eAAO,OAAO,KAAK,MAAM;MAC3B;ACtDA,UAAM,YAAY,OAAO;AAGzB,UAAM,KACJ,OAAO,gBAAgB,cACH,IAAI,YAAY,IAChC,OAAO,WAAW,cAChB;QACE,OAAO,KAAyB;AAC9B,gBAAM,MAAM,OAAO,KAAK,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AAClE,iBAAO,IAAI,SAAS;QACtB;MACF,IACA;QACE,OAAO,KAAyB;AAC9B,cAAI,MAAM;AACV,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,mBAAO,OAAO,aAAa,IAAI,CAAC,CAAC;UACnC;AACA,iBAAO;QACT;MACF;AAED,UAAM,eAAN,MAAmB;QAAnB,cAAA;AACL,eAAA,MAAM;AACN,eAAQ,MAAM;AACd,eAAQ,SAAS,IAAI,WAAW,SAAS;QAAA;QAEzC,MAAM,GAAiB;AACrB,gBAAM,EAAE,OAAO,IAAI;AACnB,iBAAO,KAAK,KAAK,IAAI;AACrB,cAAI,KAAK,QAAQ,WAAW;AAC1B,iBAAK,OAAO,GAAG,OAAO,MAAM;AAC5B,iBAAK,MAAM;UACb;QACF;QAEA,QAAgB;AACd,gBAAM,EAAE,QAAQ,KAAK,IAAI,IAAI;AAC7B,iBAAO,MAAM,IAAI,MAAM,GAAG,OAAO,OAAO,SAAS,GAAG,GAAG,CAAC,IAAI;QAC9D;MACF;AAEO,UAAM,eAAN,MAAmB;QAIxB,YAAY,QAAgB;AAH5B,eAAA,MAAM;AAIJ,eAAK,SAAS;QAChB;QAEA,OAAe;AACb,iBAAO,KAAK,OAAO,WAAW,KAAK,KAAK;QAC1C;QAEA,OAAe;AACb,iBAAO,KAAK,OAAO,WAAW,KAAK,GAAG;QACxC;QAEA,QAAQ,MAAsB;AAC5B,gBAAM,EAAE,QAAQ,IAAI,IAAI;AACxB,gBAAM,MAAM,OAAO,QAAQ,MAAM,GAAG;AACpC,iBAAO,QAAQ,KAAK,OAAO,SAAS;QACtC;MACF;AC7DA,UAAM,QAAe,CAAC;AA+Bf,eAAS,qBAAqB,OAAgC;AACnE,cAAM,EAAE,OAAO,IAAI;AACnB,cAAM,SAAS,IAAI,aAAa,KAAK;AACrC,cAAM,SAA0B,CAAC;AACjC,cAAM,QAAyB,CAAC;AAChC,YAAI,OAAO;AAEX,eAAO,OAAO,MAAM,QAAQ,OAAO,OAAO;AACxC,iBAAO,cAAc,QAAQ,IAAI;AACjC,gBAAM,SAAS,cAAc,QAAQ,CAAC;AAEtC,cAAI,CAAC,WAAW,QAAQ,MAAM,GAAG;AAC/B,kBAAM,OAAO,MAAM,IAAI;AACvB,iBAAK,CAAC,IAAI;AACV,iBAAK,CAAC,IAAI;AACV;UACF;AAEA,gBAAM,OAAO,cAAc,QAAQ,CAAC;AACpC,gBAAM,SAAS,cAAc,QAAQ,CAAC;AACtC,gBAAM,UAAU,SAAS;AAEzB,gBAAM,QACJ,UAAU,CAAC,MAAM,QAAQ,GAAG,GAAG,MAAM,cAAc,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,QAAQ,GAAG,GAAG,IAAI;AAG5F,cAAI,OAAc;AAClB,cAAI,WAAW,QAAQ,MAAM,GAAG;AAC9B,mBAAO,CAAC;AACR,eAAG;AACD,oBAAM,YAAY,cAAc,QAAQ,CAAC;AACzC,mBAAK,KAAK,SAAS;YACrB,SAAS,WAAW,QAAQ,MAAM;UACpC;AACA,gBAAM,OAAO;AAEb,iBAAO,KAAK,KAAK;AACjB,gBAAM,KAAK,KAAK;QAClB;AAEA,eAAO;MACT;AAEO,eAAS,qBAAqB,QAAiC;AACpE,cAAM,SAAS,IAAI,aAAa;AAEhC,iBAAS,IAAI,GAAG,IAAI,OAAO,UAAU;AACnC,cAAI,sBAAsB,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC;QAClD;AAEA,eAAO,OAAO,MAAM;MACtB;AAEA,eAAS,sBACP,QACA,OACA,QACA,OAGQ;AACR,cAAM,QAAQ,OAAO,KAAK;AAC1B,cAAM,EAAE,GAAG,WAAW,GAAG,aAAa,GAAG,SAAS,GAAG,WAAW,GAAG,MAAM,KAAK,IAAI;AAElF,YAAI,QAAQ,EAAG,QAAO,MAAM,KAAK;AAEjC,cAAM,CAAC,IAAI,cAAc,QAAQ,WAAW,MAAM,CAAC,CAAC;AACpD,sBAAc,QAAQ,aAAa,CAAC;AACpC,sBAAc,QAAQ,MAAM,CAAC;AAE7B,cAAM,SAAS,MAAM,WAAW,IAAI,IAAS;AAC7C,sBAAc,QAAQ,QAAQ,CAAC;AAC/B,YAAI,MAAM,WAAW,EAAG,eAAc,QAAQ,MAAM,CAAC,GAAG,CAAC;AAEzD,mBAAW,KAAK,MAAM;AACpB,wBAAc,QAAQ,GAAG,CAAC;QAC5B;AAEA,aAAK,SAAS,QAAQ,OAAO,UAAU;AACrC,gBAAM,OAAO,OAAO,KAAK;AACzB,gBAAM,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI;AACvB,cAAI,IAAI,WAAY,MAAM,WAAW,KAAK,WAAY;AACpD;UACF;AACA,kBAAQ,sBAAsB,QAAQ,OAAO,QAAQ,KAAK;QAC5D;AAEA,eAAO,MAAM,KAAK;AAClB,cAAM,CAAC,IAAI,cAAc,QAAQ,SAAS,MAAM,CAAC,CAAC;AAClD,sBAAc,QAAQ,WAAW,CAAC;AAElC,eAAO;MACT;AAEO,eAAS,sBAAsB,OAAiC;AACrE,cAAM,EAAE,OAAO,IAAI;AACnB,cAAM,SAAS,IAAI,aAAa,KAAK;AACrC,cAAM,SAA2B,CAAC;AAClC,cAAM,QAA0B,CAAC;AAEjC,YAAI,UAAU;AACd,YAAI,yBAAyB;AAC7B,YAAI,uBAAuB;AAC3B,YAAI,uBAAuB;AAC3B,YAAI,eAAe;AACnB,YAAI,iBAAiB;AACrB,YAAI,cAAc;AAClB,YAAI,gBAAgB;AAEpB,WAAG;AACD,gBAAM,OAAO,OAAO,QAAQ,GAAG;AAC/B,cAAI,YAAY;AAEhB,iBAAO,OAAO,MAAM,MAAM,OAAO,OAAO;AACtC,wBAAY,cAAc,QAAQ,SAAS;AAE3C,gBAAI,CAAC,WAAW,QAAQ,IAAI,GAAG;AAC7B,oBAAM,OAAO,MAAM,IAAI;AACvB,mBAAK,CAAC,IAAI;AACV,mBAAK,CAAC,IAAI;AACV;YACF;AAEA,kBAAM,SAAS,cAAc,QAAQ,CAAC;AACtC,kBAAM,gBAAgB,SAAS;AAC/B,kBAAM,cAAc,SAAS;AAC7B,kBAAM,WAAW,SAAS;AAE1B,gBAAI,WAA4B;AAChC,gBAAI,WAAsB;AAC1B,gBAAI;AACJ,gBAAI,eAAe;AACjB,oBAAM,kBAAkB,cAAc,QAAQ,sBAAsB;AACpE,qCAAuB;gBACrB;gBACA,2BAA2B,kBAAkB,uBAAuB;cACtE;AAEA,uCAAyB;AACzB,sBAAQ,CAAC,SAAS,WAAW,GAAG,GAAG,iBAAiB,oBAAoB;YAC1E,OAAO;AACL,sBAAQ,CAAC,SAAS,WAAW,GAAG,CAAC;YACnC;AAEA,kBAAM,UAAU,CAAC,CAAC;AAElB,gBAAI,aAAa;AACf,oBAAM,UAAU;AAChB,oBAAM,WAAW;AACjB,qCAAuB,cAAc,QAAQ,oBAAoB;AACjE,oBAAM,aAAa,YAAY;AAC/B,6BAAe,cAAc,QAAQ,aAAa,eAAe,CAAC;AAClE,+BAAiB;gBACf;gBACA,cAAc,aAAa,eAAe,iBAAiB;cAC7D;AAEA,yBAAW,CAAC,sBAAsB,cAAc,cAAc;YAChE;AACA,kBAAM,WAAW;AAEjB,gBAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,yBAAW,CAAC;AACZ,iBAAG;AACD,8BAAc;AACd,gCAAgB;AAChB,sBAAM,mBAAmB,cAAc,QAAQ,CAAC;AAChD,oBAAI;AACJ,oBAAI,mBAAmB,IAAI;AACzB,qCAAmB,CAAC,CAAC,cAAc,QAAQ,CAAC,CAAC,CAAC;AAC9C,2BAAS,IAAI,IAAI,IAAI,kBAAkB,KAAK;AAC1C,0BAAM,SAAS;AACf,kCAAc,cAAc,QAAQ,WAAW;AAC/C,oCAAgB,cAAc,QAAQ,gBAAgB,SAAS,gBAAgB,CAAC;AAChF,0BAAM,aAAa,cAAc,QAAQ,CAAC;AAC1C,qCAAiB,KAAK,CAAC,YAAY,aAAa,aAAa,CAAC;kBAChE;gBACF,OAAO;AACL,qCAAmB,CAAC,CAAC,gBAAgB,CAAC;gBACxC;AACA,yBAAS,KAAK,gBAAgB;cAChC,SAAS,WAAW,QAAQ,IAAI;YAClC;AACA,kBAAM,WAAW;AAEjB,mBAAO,KAAK,KAAK;AACjB,kBAAM,KAAK,KAAK;UAClB;AAEA;AACA,iBAAO,MAAM,OAAO;QACtB,SAAS,OAAO,MAAM;AAEtB,eAAO;MACT;AAEO,eAAS,sBAAsB,QAAkC;AACtE,YAAI,OAAO,WAAW,EAAG,QAAO;AAEhC,cAAM,SAAS,IAAI,aAAa;AAEhC,iBAAS,IAAI,GAAG,IAAI,OAAO,UAAU;AACnC,cAAI,uBAAuB,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QACrE;AAEA,eAAO,OAAO,MAAM;MACtB;AAEA,eAAS,uBACP,QACA,OACA,QACA,OASQ;AACR,cAAM,QAAQ,OAAO,KAAK;AAC1B,cAAM;UACJ,GAAG;UACH,GAAG;UACH,GAAG;UACH,GAAG;UACH;UACA;UACA;QACF,IAAI;AAEJ,YAAI,MAAM,CAAC,IAAI,WAAW;AACxB,sBAAY,QAAQ,MAAM,CAAC,GAAG,SAAS;AACvC,gBAAM,CAAC,IAAI;AACX,gBAAM,CAAC,IAAI;QACb,WAAW,QAAQ,GAAG;AACpB,iBAAO,MAAM,KAAK;QACpB;AAEA,cAAM,CAAC,IAAI,cAAc,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAEnD,cAAM,UACH,MAAM,WAAW,IAAI,IAAS,MAAM,WAAW,IAAS,MAAM,UAAU,IAAS;AACpF,sBAAc,QAAQ,QAAQ,CAAC;AAE/B,YAAI,MAAM,WAAW,GAAG;AACtB,gBAAM,EAAE,GAAG,cAAc,GAAG,YAAY,IAAI;AAC5C,cAAI,iBAAiB,MAAM,CAAC,GAAG;AAC7B,kBAAM,CAAC,IAAI;UACb;AACA,gBAAM,CAAC,IAAI,cAAc,QAAQ,cAAc,MAAM,CAAC,CAAC;AACvD,gBAAM,CAAC,IAAI,cAAc,QAAQ,aAAa,MAAM,CAAC,CAAC;QACxD;AAEA,YAAI,UAAU;AACZ,gBAAM,EAAE,GAAG,cAAc,GAAG,UAAU,GAAG,WAAW,IAAI,MAAM;AAC9D,cAAI,iBAAiB,MAAM,CAAC,GAAG;AAC7B,kBAAM,CAAC,IAAI;AACX,kBAAM,CAAC,IAAI;UACb,WAAW,aAAa,MAAM,CAAC,GAAG;AAChC,kBAAM,CAAC,IAAI;UACb;AACA,gBAAM,CAAC,IAAI,cAAc,QAAQ,cAAc,MAAM,CAAC,CAAC;AACvD,gBAAM,CAAC,IAAI,cAAc,QAAQ,UAAU,MAAM,CAAC,CAAC;AACnD,gBAAM,CAAC,IAAI,cAAc,QAAQ,YAAY,MAAM,CAAC,CAAC;QACvD;AAEA,YAAI,UAAU;AACZ,qBAAW,WAAW,UAAU;AAC9B,gBAAI,QAAQ,SAAS,EAAG,eAAc,QAAQ,CAAC,QAAQ,QAAQ,CAAC;AAChE,kBAAM,aAAa,QAAQ,CAAC,EAAE,CAAC;AAC/B,0BAAc,QAAQ,YAAY,CAAC;AACnC,gBAAI,mBAAmB;AACvB,gBAAI,qBAAqB;AACzB,qBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,oBAAM,WAAW,QAAQ,CAAC;AAC1B,iCAAmB,cAAc,QAAQ,SAAS,CAAC,GAAI,gBAAgB;AACvE,mCAAqB,cAAc,QAAQ,SAAS,CAAC,GAAI,kBAAkB;AAC3E,4BAAc,QAAQ,SAAS,CAAC,GAAI,CAAC;YACvC;UACF;QACF;AAEA,aAAK,SAAS,QAAQ,OAAO,UAAU;AACrC,gBAAM,OAAO,OAAO,KAAK;AACzB,gBAAM,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI;AACvB,cAAI,IAAI,WAAY,MAAM,WAAW,KAAK,WAAY;AACpD;UACF;AACA,kBAAQ,uBAAuB,QAAQ,OAAO,QAAQ,KAAK;QAC7D;AAEA,YAAI,MAAM,CAAC,IAAI,SAAS;AACtB,sBAAY,QAAQ,MAAM,CAAC,GAAG,OAAO;AACrC,gBAAM,CAAC,IAAI;AACX,gBAAM,CAAC,IAAI;QACb,OAAO;AACL,iBAAO,MAAM,KAAK;QACpB;AACA,cAAM,CAAC,IAAI,cAAc,QAAQ,WAAW,MAAM,CAAC,CAAC;AAEpD,eAAO;MACT;AAEA,eAAS,YAAY,QAAsB,UAAkB,MAAc;AACzE,WAAG;AACD,iBAAO,MAAM,SAAS;QACxB,SAAS,EAAE,WAAW;MACxB;AHtUO,eAAS,OAAO,UAAqC;AAC1D,cAAM,EAAE,OAAO,IAAI;AACnB,cAAM,SAAS,IAAI,aAAa,QAAQ;AACxC,cAAM,UAA6B,CAAC;AACpC,YAAI,YAAY;AAChB,YAAI,eAAe;AACnB,YAAI,aAAa;AACjB,YAAI,eAAe;AACnB,YAAI,aAAa;AAEjB,WAAG;AACD,gBAAM,OAAO,OAAO,QAAQ,GAAG;AAC/B,gBAAM,OAAsB,CAAC;AAC7B,cAAI,SAAS;AACb,cAAI,UAAU;AACd,sBAAY;AAEZ,iBAAO,OAAO,MAAM,MAAM;AACxB,gBAAI;AAEJ,wBAAY,cAAc,QAAQ,SAAS;AAC3C,gBAAI,YAAY,QAAS,UAAS;AAClC,sBAAU;AAEV,gBAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,6BAAe,cAAc,QAAQ,YAAY;AACjD,2BAAa,cAAc,QAAQ,UAAU;AAC7C,6BAAe,cAAc,QAAQ,YAAY;AAEjD,kBAAI,WAAW,QAAQ,IAAI,GAAG;AAC5B,6BAAa,cAAc,QAAQ,UAAU;AAC7C,sBAAM,CAAC,WAAW,cAAc,YAAY,cAAc,UAAU;cACtE,OAAO;AACL,sBAAM,CAAC,WAAW,cAAc,YAAY,YAAY;cAC1D;YACF,OAAO;AACL,oBAAM,CAAC,SAAS;YAClB;AAEA,iBAAK,KAAK,GAAG;AACb,mBAAO;UACT;AAEA,cAAI,CAAC,OAAQ,MAAK,IAAI;AACtB,kBAAQ,KAAK,IAAI;AACjB,iBAAO,MAAM,OAAO;QACtB,SAAS,OAAO,OAAO;AAEvB,eAAO;MACT;AAEA,eAAS,KAAK,MAA0B;AACtC,aAAK,KAAK,cAAc;MAC1B;AAEA,eAAS,eAAe,GAAqB,GAA6B;AACxE,eAAO,EAAE,CAAC,IAAI,EAAE,CAAC;MACnB;AAIO,eAAS,OAAO,SAA8C;AACnE,cAAM,SAAS,IAAI,aAAa;AAChC,YAAI,eAAe;AACnB,YAAI,aAAa;AACjB,YAAI,eAAe;AACnB,YAAI,aAAa;AAEjB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAM,OAAO,QAAQ,CAAC;AACtB,cAAI,IAAI,EAAG,QAAO,MAAM,SAAS;AACjC,cAAI,KAAK,WAAW,EAAG;AAEvB,cAAI,YAAY;AAEhB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAM,UAAU,KAAK,CAAC;AACtB,gBAAI,IAAI,EAAG,QAAO,MAAM,KAAK;AAE7B,wBAAY,cAAc,QAAQ,QAAQ,CAAC,GAAG,SAAS;AAEvD,gBAAI,QAAQ,WAAW,EAAG;AAC1B,2BAAe,cAAc,QAAQ,QAAQ,CAAC,GAAG,YAAY;AAC7D,yBAAa,cAAc,QAAQ,QAAQ,CAAC,GAAG,UAAU;AACzD,2BAAe,cAAc,QAAQ,QAAQ,CAAC,GAAG,YAAY;AAE7D,gBAAI,QAAQ,WAAW,EAAG;AAC1B,yBAAa,cAAc,QAAQ,QAAQ,CAAC,GAAG,UAAU;UAC3D;QACF;AAEA,eAAO,OAAO,MAAM;MACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AI9GA,UAAA,0BAAAC,YAAA;QAAA,kCAAAC,UAAAC,UAAA;AAAAA,UAAAA,SAAO,UAAU;QAAA;MAAA,CAAA;ACAjB,UAAA,sBAAAF,YAAA;QAAA,8BAAAC,UAAAC,UAAA;AAAAA,UAAAA,SAAO,UAAU;QAAA;MAAA,CAAA;ACAjB,UAAA,wBAAA,CAAA;AAAA,eAAA,uBAAA;QAAA,QAAA,MAAA;QAAA,YAAA,MAAA;QAAA,sBAAA,MAAA;QAAA,mBAAA,MAAA;QAAA,UAAA,MAAA;QAAA,0BAAA,MAAA;QAAA,YAAA,MAAA;QAAA,iBAAA,MAAA;QAAA,aAAA,MAAA;QAAA,YAAA,MAAA;QAAA,iBAAA,MAAA;QAAA,sBAAA,MAAA;QAAA,WAAA,MAAA;QAAA,qBAAA,MAAA;QAAA,qBAAA,MAAA;QAAA,kBAAA,MAAA;QAAA,cAAA,MAAA;MAAA,CAAA;AAAA,MAAAA,QAAA,UAAA,aAAA,qBAAA;AAAA,UAAA,yBAA+B,QAAA,wBAAA,CAAA;ACA/B,UAAA,qBAAuB,QAAA,oBAAA,CAAA;ACGR,eAAR,cAA+B,MAAyC;AAC7E,YAAI,CAAC,KAAM,QAAO;AAClB,cAAM,QAAQ,KAAK,YAAY,GAAG;AAClC,eAAO,KAAK,MAAM,GAAG,QAAQ,CAAC;MAChC;ADHe,eAAR,SACL,QACA,YACS;AACT,cAAM,OAAO,cAAc,MAAM;AAIjC,cAAM,SAAS,aAAa,aAAa,MAAM;AAE/C,eAAO,CAAC,YAAA,GAAW,mBAAAC,SAAW,UAAU,UAAU,KAAK,IAAI;MAC7D;AEAO,UAAM,SAAS;AACf,UAAM,gBAAgB;AACtB,UAAM,cAAc;AACpB,UAAM,gBAAgB;AACtB,UAAM,cAAc;AAEpB,UAAM,qBAAqB;AAC3B,UAAM,uBAAuB;AClBrB,eAAR,UACL,UACA,OACsB;AACtB,cAAM,gBAAgB,wBAAwB,UAAU,CAAC;AACzD,YAAI,kBAAkB,SAAS,OAAQ,QAAO;AAI9C,YAAI,CAAC,MAAO,YAAW,SAAS,MAAM;AAEtC,iBAAS,IAAI,eAAe,IAAI,SAAS,QAAQ,IAAI,wBAAwB,UAAU,IAAI,CAAC,GAAG;AAC7F,mBAAS,CAAC,IAAI,aAAa,SAAS,CAAC,GAAG,KAAK;QAC/C;AACA,eAAO;MACT;AAEA,eAAS,wBAAwB,UAAgC,OAAuB;AACtF,iBAAS,IAAI,OAAO,IAAI,SAAS,QAAQ,KAAK;AAC5C,cAAI,CAAC,SAAS,SAAS,CAAC,CAAC,EAAG,QAAO;QACrC;AACA,eAAO,SAAS;MAClB;AAEA,eAAS,SAAS,MAAmC;AACnD,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,KAAK,CAAC,EAAE,MAAM,IAAI,KAAK,IAAI,CAAC,EAAE,MAAM,GAAG;AACzC,mBAAO;UACT;QACF;AACA,eAAO;MACT;AAEA,eAAS,aAAa,MAA0B,OAAoC;AAClF,YAAI,CAAC,MAAO,QAAO,KAAK,MAAM;AAC9B,eAAO,KAAK,KAAK,cAAc;MACjC;AAEA,eAAS,eAAe,GAAqB,GAA6B;AACxE,eAAO,EAAE,MAAM,IAAI,EAAE,MAAM;MAC7B;ACnCO,UAAI,QAAQ;AAkBZ,eAAS,aACd,UACA,QACA,KACA,MACQ;AACR,eAAO,OAAO,MAAM;AAClB,gBAAM,MAAM,OAAQ,OAAO,OAAQ;AACnC,gBAAM,MAAM,SAAS,GAAG,EAAE,MAAM,IAAI;AAEpC,cAAI,QAAQ,GAAG;AACb,oBAAQ;AACR,mBAAO;UACT;AAEA,cAAI,MAAM,GAAG;AACX,kBAAM,MAAM;UACd,OAAO;AACL,mBAAO,MAAM;UACf;QACF;AAEA,gBAAQ;AACR,eAAO,MAAM;MACf;AAEO,eAAS,WACd,UACA,QACA,OACQ;AACR,iBAAS,IAAI,QAAQ,GAAG,IAAI,SAAS,QAAQ,QAAQ,KAAK;AACxD,cAAI,SAAS,CAAC,EAAE,MAAM,MAAM,OAAQ;QACtC;AACA,eAAO;MACT;AAEO,eAAS,WACd,UACA,QACA,OACQ;AACR,iBAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,QAAQ,KAAK;AAC3C,cAAI,SAAS,CAAC,EAAE,MAAM,MAAM,OAAQ;QACtC;AACA,eAAO;MACT;AAEO,eAAS,gBAA2B;AACzC,eAAO;UACL,SAAS;UACT,YAAY;UACZ,WAAW;QACb;MACF;AAMO,eAAS,qBACd,UACA,QACA,OACA,KACQ;AACR,cAAM,EAAE,SAAS,YAAY,UAAU,IAAI;AAE3C,YAAI,MAAM;AACV,YAAI,OAAO,SAAS,SAAS;AAC7B,YAAI,QAAQ,SAAS;AACnB,cAAI,WAAW,YAAY;AACzB,oBAAQ,cAAc,MAAM,SAAS,SAAS,EAAE,MAAM,MAAM;AAC5D,mBAAO;UACT;AAEA,cAAI,UAAU,YAAY;AAExB,kBAAM,cAAc,KAAK,IAAI;UAC/B,OAAO;AACL,mBAAO;UACT;QACF;AACA,cAAM,UAAU;AAChB,cAAM,aAAa;AAEnB,eAAQ,MAAM,YAAY,aAAa,UAAU,QAAQ,KAAK,IAAI;MACpE;ACrGe,eAAR,eACL,SACA,OACU;AACV,cAAM,UAAoB,MAAM,IAAI,cAAc;AAElD,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAM,OAAO,QAAQ,CAAC;AACtB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAM,MAAM,KAAK,CAAC;AAClB,gBAAI,IAAI,WAAW,EAAG;AAEtB,kBAAMC,eAAc,IAAI,aAAa;AACrC,kBAAM,aAAa,IAAI,WAAW;AAClC,kBAAM,eAAe,IAAI,aAAa;AACtC,kBAAM,iBAAiB,QAAQA,YAAW;AAC1C,kBAAM,eAAgB,eAAA,UAAA,MAAA,eAAA,UAAA,IAA+B,CAAC;AACtD,kBAAM,OAAO,MAAMA,YAAW;AAM9B,gBAAI,QAAQ;cACV;cACA;cACA,qBAAqB,cAAc,cAAc,MAAM,UAAU;YACnE;AAEA,iBAAK,YAAY,EAAE;AACnB,mBAAO,cAAc,OAAO,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,CAAC;UAC5D;QACF;AAEA,eAAO;MACT;AAEA,eAAS,OAAU,OAAY,OAAe,OAAU;AACtD,iBAAS,IAAI,MAAM,QAAQ,IAAI,OAAO,KAAK;AACzC,gBAAM,CAAC,IAAI,MAAM,IAAI,CAAC;QACxB;AACA,cAAM,KAAK,IAAI;MACjB;AAOA,eAAS,iBAAmD;AAC1D,eAAO,EAAE,WAAW,KAAK;MAC3B;AC+CO,eAAS,MAAS,KAA4B;AACnD,eAAO,OAAO,QAAQ,WAAW,KAAK,MAAM,GAAG,IAAK;MACtD;ACvFO,UAAM,aAAyB,SAAU,KAAK,QAAQ;AAC3D,cAAM,SAAS,MAAM,GAA8B;AAEnD,YAAI,EAAE,cAAc,SAAS;AAC3B,iBAAO,IAAI,SAAS,QAA2D,MAAM;QACvF;AAEA,cAAM,WAAiC,CAAC;AACxC,cAAM,UAAoB,CAAC;AAC3B,cAAM,iBAAoC,CAAC;AAC3C,cAAM,QAAkB,CAAC;AACzB,cAAM,aAAuB,CAAC;AAE9B;UACE;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACF;AAEA,cAAM,SAA2B;UAC/B,SAAS;UACT,MAAM,OAAO;UACb;UACA;UACA;UACA;UACA;QACF;AAEA,eAAO,oBAAoB,MAAM;MACnC;AAEA,eAAS,QACP,OACA,QACA,UACA,SACA,gBACA,OACA,YACA,YACA,cACA,UACA,YACA;AACA,cAAM,EAAE,SAAS,IAAI;AACrB,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAM,EAAE,KAAK,OAAO,IAAI,SAAS,CAAC;AAElC,cAAI,KAAK;AACT,cAAI,KAAK;AACT,cAAI,IAAI,IAAI,SAAS,QAAQ;AAC3B,kBAAM,aAAa,SAAS,IAAI,CAAC,EAAE;AACnC,iBAAK,KAAK,IAAI,UAAU,aAAa,WAAW,IAAI;AAEpD,gBAAI,OAAO,UAAU;AACnB,mBAAK,KAAK,IAAI,YAAY,eAAe,WAAW,MAAM;YAC5D,WAAW,KAAK,UAAU;AACxB,mBAAK,eAAe,WAAW;YACjC;UACF;AAEA;YACE;YACA;YACA;YACA;YACA;YACA;YACA;YACA,aAAa,OAAO;YACpB,eAAe,OAAO;YACtB;YACA;UACF;QACF;MACF;AAEA,eAAS,WACP,OACA,QACA,UACA,SACA,gBACA,OACA,YACA,YACA,cACA,UACA,YACA;AACA,cAAM,SAAS,MAAM,KAAK;AAC1B,YAAI,cAAc,OAAQ,QAAO,QAAQ,GAAI,SAAmD;AAEhG,cAAM,MAAM,IAAI,SAAS,QAAQ,MAAM;AACvC,cAAM,gBAAgB,QAAQ;AAC9B,cAAM,cAAc,MAAM;AAC1B,cAAM,UAAU,gBAAgB,GAAG;AACnC,cAAM,EAAE,iBAAiB,gBAAgB,UAAU,YAAY,QAAQ,IAAI;AAE3E,eAAO,SAAS,eAAe;AAC/B,eAAO,OAAO,IAAI,KAAK;AAEvB,YAAI,SAAU,QAAO,gBAAgB,QAAQ;YACxC,UAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,IAAK,gBAAe,KAAK,IAAI;AAE9E,YAAI,QAAS,UAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAK,YAAW,KAAK,QAAQ,CAAC,IAAI,aAAa;AAEhG,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAM,QAAQ,aAAa;AAM3B,cAAI,QAAQ,SAAU;AAItB,gBAAM,MAAM,QAAQ,UAAU,KAAK;AAGnC,gBAAM,UAAU,MAAM,IAAI,eAAe;AAEzC,gBAAM,OAAO,QAAQ,CAAC;AACtB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAM,MAAM,KAAK,CAAC;AAClB,kBAAM,SAAS,UAAU,IAAI,MAAM;AAInC,gBAAI,UAAU,YAAY,UAAU,WAAY;AAEhD,gBAAI,IAAI,WAAW,GAAG;AACpB,kBAAI,KAAK,CAAC,MAAM,CAAC;AACjB;YACF;AAEA,kBAAM,eAAe,gBAAgB,IAAI,aAAa;AACtD,kBAAM,aAAa,IAAI,WAAW;AAClC,kBAAM,eAAe,IAAI,aAAa;AACtC,gBAAI;cACF,IAAI,WAAW,IACX,CAAC,QAAQ,cAAc,YAAY,YAAY,IAC/C,CAAC,QAAQ,cAAc,YAAY,cAAc,cAAc,IAAI,WAAW,CAAC;YACrF;UACF;QACF;MACF;AAEA,eAAS,OAAU,KAAU,OAAY;AACvC,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,KAAI,KAAK,MAAM,CAAC,CAAC;MAC1D;AAEA,eAAS,QAAW,KAAY,OAAoB;AAClD,iBAAS,IAAI,IAAI,QAAQ,KAAK,OAAO,IAAK,KAAI,CAAC,IAAI,CAAC;AACpD,eAAO,IAAI,KAAK;MAClB;ARhHA,UAAM,gBAAgB;AACtB,UAAM,kBAAkB;AAEjB,UAAM,oBAAoB;AAC1B,UAAM,uBAAuB;AAI7B,UAAM,WAAN,MAAoC;QAkBzC,YAAY,KAAyB,QAAwB;AAC3D,gBAAM,WAAW,OAAO,QAAQ;AAChC,cAAI,CAAC,YAAa,IAAyC,aAAc,QAAO;AAEhF,gBAAM,SAAS,MAAM,GAAwC;AAE7D,gBAAM,EAAE,SAAS,MAAM,OAAO,YAAY,SAAS,eAAe,IAAI;AACtE,eAAK,UAAU;AACf,eAAK,OAAO;AACZ,eAAK,QAAQ,SAAS,CAAC;AACvB,eAAK,aAAa;AAClB,eAAK,UAAU;AACf,eAAK,iBAAiB;AACtB,eAAK,aAAa,OAAO,cAAe,OAAkB,uBAAuB;AAEjF,gBAAM,UAAU,SAAS,QAAQ,UAAU;AAC3C,eAAK,kBAAkB,QAAQ,IAAI,OAAO;AAE1C,gBAAM,EAAE,SAAS,IAAI;AACrB,cAAI,OAAO,aAAa,UAAU;AAChC,iBAAK,WAAW;AAChB,iBAAK,WAAW;UAClB,WAAW,MAAM,QAAQ,QAAQ,GAAG;AAClC,iBAAK,WAAW;AAChB,iBAAK,WAAW,UAAU,UAAU,QAAQ;UAC9C,WAAY,OAAyC,UAAU;AAC7D,kBAAM,IAAI,MAAM,4EAA4E;UAC9F,OAAO;AACL,kBAAM,IAAI,MAAM,uBAAuB,KAAK,UAAU,MAAM,CAAC,EAAE;UACjE;AAEA,eAAK,eAAe,cAAc;AAClC,eAAK,aAAa;AAClB,eAAK,iBAAiB;QACxB;MACF;AAMA,eAAS,KAAK,KAAyB;AACrC,eAAO;MACT;AAKO,eAAS,gBAAgB,KAA6C;AAzJ7E,YAAA,IAAA;AA0JE,gBAAQ,MAAA,KAAA,KAAK,GAAG,GAAE,aAAV,OAAA,KAAA,GAAU,YAAA,GAAa,uBAAA,QAAO,KAAK,GAAG,EAAE,QAAS;MAC3D;AAKO,eAAS,gBAAgB,KAAuD;AAhKvF,YAAA;AAiKE,gBAAQ,KAAA,KAAK,GAAG,GAAE,aAAV,GAAU,YAAA,GAAa,uBAAA,QAAO,KAAK,GAAG,EAAE,QAAS;MAC3D;AAMO,eAAS,aACd,KACA,MACA,QACmC;AACnC,cAAM,UAAU,gBAAgB,GAAG;AAInC,YAAI,QAAQ,QAAQ,OAAQ,QAAO;AAEnC,cAAM,WAAW,QAAQ,IAAI;AAC7B,cAAM,QAAQ;UACZ;UACA,KAAK,GAAG,EAAE;UACV;UACA;UACA;QACF;AAEA,eAAO,UAAU,KAAK,OAAO,SAAS,KAAK;MAC7C;AAOO,eAAS,oBACd,KACA,QAC0C;AAC1C,YAAI,EAAE,MAAM,QAAQ,KAAK,IAAI;AAC7B;AACA,YAAI,OAAO,EAAG,OAAM,IAAI,MAAM,aAAa;AAC3C,YAAI,SAAS,EAAG,OAAM,IAAI,MAAM,eAAe;AAE/C,cAAM,UAAU,gBAAgB,GAAG;AAInC,YAAI,QAAQ,QAAQ,OAAQ,QAAO,SAAS,MAAM,MAAM,MAAM,IAAI;AAElE,cAAM,WAAW,QAAQ,IAAI;AAC7B,cAAM,QAAQ;UACZ;UACA,KAAK,GAAG,EAAE;UACV;UACA;UACA,QAAQ;QACV;AAEA,YAAI,UAAU,GAAI,QAAO,SAAS,MAAM,MAAM,MAAM,IAAI;AAExD,cAAM,UAAU,SAAS,KAAK;AAC9B,YAAI,QAAQ,WAAW,EAAG,QAAO,SAAS,MAAM,MAAM,MAAM,IAAI;AAEhE,cAAM,EAAE,OAAO,gBAAgB,IAAI;AACnC,eAAO;UACL,gBAAgB,QAAQ,aAAa,CAAC;UACtC,QAAQ,WAAW,IAAI;UACvB,QAAQ,aAAa;UACrB,QAAQ,WAAW,IAAI,MAAM,QAAQ,WAAW,CAAC,IAAI;QACvD;MACF;AAKO,eAAS,qBACd,KACA,QAC4C;AAC5C,cAAM,EAAE,QAAQ,MAAM,QAAQ,KAAK,IAAI;AACvC,eAAO,kBAAkB,KAAK,QAAQ,MAAM,QAAQ,QAAQ,sBAAsB,KAAK;MACzF;AAKO,eAAS,yBAAyB,KAAe,QAA0C;AAChG,cAAM,EAAE,QAAQ,MAAM,QAAQ,KAAK,IAAI;AAEvC,eAAO,kBAAkB,KAAK,QAAQ,MAAM,QAAQ,QAAQ,mBAAmB,IAAI;MACrF;AAKO,eAAS,YAAY,KAAe,IAA0C;AACnF,cAAM,UAAU,gBAAgB,GAAG;AACnC,cAAM,EAAE,OAAO,gBAAgB,IAAI;AAEnC,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAM,OAAO,QAAQ,CAAC;AACtB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAM,MAAM,KAAK,CAAC;AAElB,kBAAM,gBAAgB,IAAI;AAC1B,kBAAM,kBAAkB,IAAI,CAAC;AAC7B,gBAAI,SAAS;AACb,gBAAI,eAAe;AACnB,gBAAI,iBAAiB;AACrB,gBAAI,OAAO;AACX,gBAAI,IAAI,WAAW,GAAG;AACpB,uBAAS,gBAAgB,IAAI,CAAC,CAAC;AAC/B,6BAAe,IAAI,CAAC,IAAI;AACxB,+BAAiB,IAAI,CAAC;YACxB;AACA,gBAAI,IAAI,WAAW,EAAG,QAAO,MAAM,IAAI,CAAC,CAAC;AAEzC,eAAG;cACD;cACA;cACA;cACA;cACA;cACA;YACF,CAAgB;UAClB;QACF;MACF;AAEA,eAAS,YAAY,KAAe,QAAwB;AAC1D,cAAM,EAAE,SAAS,gBAAgB,IAAI;AACrC,YAAI,QAAQ,QAAQ,QAAQ,MAAM;AAClC,YAAI,UAAU,GAAI,SAAQ,gBAAgB,QAAQ,MAAM;AACxD,eAAO;MACT;AAKO,eAAS,iBAAiB,KAAe,QAA+B;AAC7E,cAAM,EAAE,eAAe,IAAI;AAC3B,YAAI,kBAAkB,KAAM,QAAO;AACnC,cAAM,QAAQ,YAAY,KAAK,MAAM;AACrC,eAAO,UAAU,KAAK,OAAO,eAAe,KAAK;MACnD;AAKO,eAAS,UAAU,KAAe,QAAyB;AAChE,cAAM,EAAE,WAAW,IAAI;AACvB,YAAI,cAAc,KAAM,QAAO;AAC/B,cAAM,QAAQ,YAAY,KAAK,MAAM;AACrC,eAAO,UAAU,KAAK,QAAQ,WAAW,SAAS,KAAK;MACzD;AAMO,eAAS,oBAAoB,KAAuB,QAA2B;AACpF,cAAM,SAAS,IAAI,SAAS,MAAM,KAAK,CAAC,CAAC,GAAG,MAAM;AAClD,aAAK,MAAM,EAAE,WAAW,IAAI;AAC5B,eAAO;MACT;AAMO,eAAS,WACd,KACkF;AAClF,eAAO,MAAM,KAAK,gBAAgB,GAAG,CAAC;MACxC;AAMO,eAAS,WAAW,KAAiC;AAC1D,eAAO,MAAM,KAAK,gBAAgB,GAAG,CAAC;MACxC;AAEA,eAAS,MACP,KACA,UACwD;AACxD,eAAO;UACL,SAAS,IAAI;UACb,MAAM,IAAI;UACV,OAAO,IAAI;UACX,YAAY,IAAI;UAChB,SAAS,IAAI;UACb,gBAAgB,IAAI;UACpB;UACA,YAAY,IAAI,cAAe,IAAe;QAChD;MACF;AASA,eAAS,SACP,QACA,MACA,QACA,MAC0C;AAC1C,eAAO,EAAE,QAAQ,MAAM,QAAQ,KAAK;MACtC;AAIA,eAAS,SACP,MACA,QAC4C;AAC5C,eAAO,EAAE,MAAM,OAAO;MACxB;AAgBA,eAAS,qBACP,UACA,MACA,MACA,QACA,MACQ;AACR,YAAI,QAAQ,qBAAqB,UAAU,QAAQ,MAAM,IAAI;AAC7D,YAAI,OAAS;AACX,mBAAS,SAAS,oBAAoB,aAAa,YAAY,UAAU,QAAQ,KAAK;QACxF,WAAW,SAAS,kBAAmB;AAEvC,YAAI,UAAU,MAAM,UAAU,SAAS,OAAQ,QAAO;AACtD,eAAO;MACT;AAEA,eAAS,wBACP,UACA,MACA,MACA,QACA,MACoB;AACpB,YAAI,MAAM,qBAAqB,UAAU,MAAM,MAAM,QAAQ,oBAAoB;AAQjF,YAAI,CAAC,SAAW,SAAS,kBAAmB;AAE5C,YAAI,QAAQ,MAAM,QAAQ,SAAS,OAAQ,QAAO,CAAC;AAKnD,cAAM,gBAAgB,QAAU,SAAS,SAAS,GAAG,EAAE,MAAM;AAG7D,YAAI,CAAC,MAAS,OAAM,WAAW,UAAU,eAAe,GAAG;AAC3D,cAAM,MAAM,WAAW,UAAU,eAAe,GAAG;AAEnD,cAAM,SAAS,CAAC;AAChB,eAAO,OAAO,KAAK,OAAO;AACxB,gBAAM,UAAU,SAAS,GAAG;AAC5B,iBAAO,KAAK,SAAS,QAAQ,kBAAkB,IAAI,GAAG,QAAQ,oBAAoB,CAAC,CAAC;QACtF;AACA,eAAO;MACT;AAkBA,eAAS,kBACP,KACA,QACA,MACA,QACA,MACA,KACiE;AA5dnE,YAAA;AA6dE;AACA,YAAI,OAAO,EAAG,OAAM,IAAI,MAAM,aAAa;AAC3C,YAAI,SAAS,EAAG,OAAM,IAAI,MAAM,eAAe;AAE/C,cAAM,EAAE,SAAS,gBAAgB,IAAI;AACrC,YAAIA,eAAc,QAAQ,QAAQ,MAAM;AACxC,YAAIA,iBAAgB,GAAIA,gBAAc,gBAAgB,QAAQ,MAAM;AACpE,YAAIA,iBAAgB,GAAI,QAAO,MAAM,CAAC,IAAI,SAAS,MAAM,IAAI;AAE7D,cAAM,aAAa,KAAA,KAAK,GAAG,GAAE,eAAV,GAAU,aAAe;UAC1C,gBAAgB,GAAG;UAClB,KAAK,GAAG,EAAE,iBAAiB,QAAQ,IAAI,aAAa;QACvD;AAEA,cAAM,WAAW,UAAUA,YAAW,EAAE,IAAI;AAC5C,YAAI,YAAY,KAAM,QAAO,MAAM,CAAC,IAAI,SAAS,MAAM,IAAI;AAE3D,cAAM,OAAO,KAAK,GAAG,EAAE,eAAgBA,YAAW;AAElD,YAAI,IAAK,QAAO,wBAAwB,UAAU,MAAM,MAAM,QAAQ,IAAI;AAE1E,cAAM,QAAQ,qBAAqB,UAAU,MAAM,MAAM,QAAQ,IAAI;AACrE,YAAI,UAAU,GAAI,QAAO,SAAS,MAAM,IAAI;AAE5C,cAAM,UAAU,SAAS,KAAK;AAC9B,eAAO,SAAS,QAAQ,kBAAkB,IAAI,GAAG,QAAQ,oBAAoB,CAAC;MAChF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ASvfA,UAAA,0BAAAC,YAAA;QAAA,kCAAAC,UAAAC,UAAA;AAAAA,UAAAA,SAAO,UAAU;QAAA;MAAA,CAAA;ACAjB,UAAA,wBAAAF,YAAA;QAAA,gCAAAC,UAAAC,UAAA;AAAAA,UAAAA,SAAO,UAAU;QAAA;MAAA,CAAA;ACAjB,UAAA,sBAAA,CAAA;AAAA,eAAA,qBAAA;QAAA,YAAA,MAAA;QAAA,YAAA,MAAA;QAAA,YAAA,MAAA;QAAA,aAAA,MAAA;QAAA,SAAA,MAAA;QAAA,iBAAA,MAAA;QAAA,iBAAA,MAAA;QAAA,WAAA,MAAA;QAAA,kBAAA,MAAA;QAAA,cAAA,MAAA;QAAA,cAAA,MAAA;MAAA,CAAA;AAAA,MAAAA,QAAA,UAAA,aAAA,mBAAA;ACUO,UAAM,WAAN,MAAoC;QAIzC,cAAc;AACZ,eAAK,WAAW,EAAE,WAAW,KAAK;AAClC,eAAK,QAAQ,CAAC;QAChB;MACF;AAWA,eAAS,KAAoB,KAAgC;AAC3D,eAAO;MACT;AAKO,eAAS,IAAmB,QAAqB,KAA4B;AAClF,eAAO,KAAK,MAAM,EAAE,SAAS,GAAG;MAClC;AAMO,eAAS,IAAmB,QAAqB,KAAgB;AAEtE,cAAM,QAAQ,IAAI,QAAQ,GAAG;AAC7B,YAAI,UAAU,OAAW,QAAO;AAEhC,cAAM,EAAE,OAAO,UAAU,QAAQ,IAAI,KAAK,MAAM;AAEhD,cAAM,SAAS,MAAM,KAAK,GAAG;AAC7B,eAAQ,QAAQ,GAAG,IAAI,SAAS;MAClC;AAgBO,eAAS,OAAsB,QAAqB,KAAc;AACvE,cAAM,QAAQ,IAAI,QAAQ,GAAG;AAC7B,YAAI,UAAU,OAAW;AAEzB,cAAM,EAAE,OAAO,UAAU,QAAQ,IAAI,KAAK,MAAM;AAChD,iBAAS,IAAI,QAAQ,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC7C,gBAAM,IAAI,MAAM,CAAC;AACjB,gBAAM,IAAI,CAAC,IAAI;AACf,kBAAQ,CAAC;QACX;AACA,gBAAQ,GAAG,IAAI;AACf,cAAM,IAAI;MACZ;ADhFA,UAAA,yBAIO,QAAA,wBAAA,CAAA;AACP,UAAA,uBAA0C,QAAA,sBAAA,CAAA;AEKnC,UAAM,SAAS;AACf,UAAM,gBAAgB;AACtB,UAAM,cAAc;AACpB,UAAM,gBAAgB;AACtB,UAAM,cAAc;AFsB3B,UAAM,UAAU;AAKT,UAAM,aAAN,MAAiB;QAWtB,YAAY,EAAE,MAAM,WAAW,IAAa,CAAC,GAAG;AAC9C,eAAK,SAAS,IAAI,SAAS;AAC3B,eAAK,WAAW,IAAI,SAAS;AAC7B,eAAK,kBAAkB,CAAC;AACxB,eAAK,YAAY,CAAC;AAGlB,eAAK,OAAO;AACZ,eAAK,aAAa;AAClB,eAAK,cAAc,IAAI,SAAS;QAClC;MACF;AAgBA,eAASC,MAAK,KAAyB;AACrC,eAAO;MACT;AAoCO,eAAS,WACd,KACA,SACA,WACA,QACA,YACA,cACA,MACA,SACM;AACN,eAAO;UACL;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACF;MACF;AAoCO,eAAS,WACd,KACA,SAOM;AACN,eAAO,mBAAmB,OAAO,KAAK,OAAmD;MAC3F;AAOO,UAAM,kBAAqC,CAChD,KACA,SACA,WACA,QACA,YACA,cACA,MACA,YACG;AACH,eAAO;UACL;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACF;MACF;AAOO,UAAM,kBAAqC,CAAC,KAAK,YAAY;AAClE,eAAO,mBAAmB,MAAM,KAAK,OAAmD;MAC1F;AAKO,eAAS,iBAAiB,KAAiB,QAAgB,SAA8B;AAC9F,cAAM;UACJ,UAAU;UACV,iBAAiB;;QAEnB,IAAIA,MAAK,GAAG;AACZ,cAAM,QAAQ,IAAI,SAAS,MAAM;AACjC,uBAAe,KAAK,IAAI;MAE1B;AAEO,eAAS,UAAU,KAAiB,QAAgB,SAAS,MAAM;AACxE,cAAM;UACJ,UAAU;UACV,iBAAiB;UACjB,aAAa;;QAEf,IAAIA,MAAK,GAAG;AACZ,cAAM,QAAQ,IAAI,SAAS,MAAM;AACjC,YAAI,UAAU,eAAe,OAAQ,gBAAe,KAAK,IAAI;AAE7D,YAAI,OAAQ,KAAI,YAAY,KAAK;YAC5B,QAAO,YAAY,KAAK;MAC/B;AAMO,eAAS,aAAa,KAAmC;AAC9D,cAAM;UACJ,WAAW;UACX,UAAU;UACV,iBAAiB;UACjB,QAAQ;UACR,aAAa;;;QAGf,IAAIA,MAAK,GAAG;AACZ,8BAAsB,QAAQ;AAE9B,eAAO;UACL,SAAS;UACT,MAAM,IAAI,QAAQ;UAClB,OAAO,MAAM;UACb,YAAY,IAAI,cAAc;UAC9B,SAAS,QAAQ;UACjB;UACA;;;UAGA,YAAY,WAAW;QACzB;MACF;AAMO,eAAS,aAAa,KAAmC;AAC9D,cAAM,UAAU,aAAa,GAAG;AAChC,eAAO,OAAO,OAAO,CAAC,GAAG,SAAS;;;UAGhC,WAAA,GAAU,uBAAA,QAAO,QAAQ,QAAgC;QAC3D,CAAC;MACH;AAKO,eAAS,QAAQ,OAAmC;AACzD,cAAM,MAAM,IAAI,qBAAA,SAAS,KAAK;AAC9B,cAAM,MAAM,IAAI,WAAW,EAAE,MAAM,IAAI,MAAM,YAAY,IAAI,WAAW,CAAC;AAEzE,eAAOA,MAAK,GAAG,EAAE,QAAQ,IAAI,KAAK;AAClC,eAAOA,MAAK,GAAG,EAAE,UAAU,IAAI,OAAmB;AAClDA,cAAK,GAAG,EAAE,kBAAkB,IAAI,kBAAkB,IAAI,QAAQ,IAAI,MAAM,IAAI;AAC5EA,cAAK,GAAG,EAAE,aAAA,GAAY,qBAAA,iBAAgB,GAAG;AAEzC,YAAI,IAAI,WAAY,QAAOA,MAAK,GAAG,EAAE,aAAa,IAAI,UAAU;AAEhE,eAAO;MACT;AAMO,eAAS,YAAY,KAA4B;AACtD,cAAM,MAAiB,CAAC;AACxB,cAAM,EAAE,WAAW,UAAU,UAAU,SAAS,QAAQ,MAAM,IAAIA,MAAK,GAAG;AAE1E,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAM,OAAO,SAAS,CAAC;AACvB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAM,MAAM,KAAK,CAAC;AAElB,kBAAM,YAAY,EAAE,MAAM,IAAI,GAAG,QAAQ,IAAI,MAAM,EAAE;AACrD,gBAAI,SAA6B;AACjC,gBAAI,WAA4B;AAChC,gBAAI,OAA2B;AAE/B,gBAAI,IAAI,WAAW,GAAG;AACpB,uBAAS,QAAQ,MAAM,IAAI,aAAa,CAAC;AACzC,yBAAW,EAAE,MAAM,IAAI,WAAW,IAAI,GAAG,QAAQ,IAAI,aAAa,EAAE;AAEpE,kBAAI,IAAI,WAAW,EAAG,QAAO,MAAM,MAAM,IAAI,WAAW,CAAC;YAC3D;AAEA,gBAAI,KAAK,EAAE,WAAW,QAAQ,UAAU,KAAK,CAAY;UAC3D;QACF;AAEA,eAAO;MACT;AAGA,eAAS,mBACP,UACA,KACA,SACA,WACA,QACA,YACA,cACA,MACA,SACM;AACN,cAAM;UACJ,WAAW;UACX,UAAU;UACV,iBAAiB;UACjB,QAAQ;;QAEV,IAAIA,MAAK,GAAG;AACZ,cAAM,OAAO,SAAS,UAAU,OAAO;AACvC,cAAM,QAAQ,eAAe,MAAM,SAAS;AAE5C,YAAI,CAAC,QAAQ;AACX,cAAI,YAAY,eAAe,MAAM,KAAK,EAAG;AAC7C,iBAAO,OAAO,MAAM,OAAO,CAAC,SAAS,CAAC;QACxC;AAIA,eAAe,UAAU;AACzB,eAAe,YAAY;AAE3B,cAAM,eAAe,IAAI,SAAS,MAAM;AACxC,cAAM,aAAa,OAAO,IAAI,OAAO,IAAI,IAAI;AAC7C,YAAI,iBAAiB,eAAe,OAAQ,gBAAe,YAAY,IAAI,WAAA,OAAA,UAAW;AAGtF,YAAI,YAAY,WAAW,MAAM,OAAO,cAAc,YAAY,cAAc,UAAU,GAAG;AAC3F;QACF;AAEA,eAAO;UACL;UACA;UACA,OACI,CAAC,WAAW,cAAc,YAAY,cAAc,UAAU,IAC9D,CAAC,WAAW,cAAc,YAAY,YAAY;QACxD;MACF;AAEA,eAAS,OAAU,MAAkC;MAErD;AAEA,eAAS,SAAY,KAAY,OAAoB;AACnD,iBAAS,IAAI,IAAI,QAAQ,KAAK,OAAO,KAAK;AACxC,cAAI,CAAC,IAAI,CAAC;QACZ;AACA,eAAO,IAAI,KAAK;MAClB;AAEA,eAAS,eAAe,MAA0B,WAA2B;AAC3E,YAAI,QAAQ,KAAK;AACjB,iBAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,QAAQ,KAAK;AAC3C,gBAAM,UAAU,KAAK,CAAC;AACtB,cAAI,aAAa,QAAQ,MAAM,EAAG;QACpC;AACA,eAAO;MACT;AAEA,eAAS,OAAU,OAAY,OAAe,OAAU;AACtD,iBAAS,IAAI,MAAM,QAAQ,IAAI,OAAO,KAAK;AACzC,gBAAM,CAAC,IAAI,MAAM,IAAI,CAAC;QACxB;AACA,cAAM,KAAK,IAAI;MACjB;AAEA,eAAS,sBAAsB,UAAgC;AAC7D,cAAM,EAAE,OAAO,IAAI;AACnB,YAAI,MAAM;AACV,iBAAS,IAAI,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK;AAC1C,cAAI,SAAS,CAAC,EAAE,SAAS,EAAG;QAC9B;AACA,YAAI,MAAM,OAAQ,UAAS,SAAS;MACtC;AAEA,eAAS,OAAkC,QAAqB,OAAY;AAC1E,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,KAAI,QAAQ,MAAM,CAAC,CAAC;MAC7D;AAEA,eAAS,eAAe,MAA0B,OAAwB;AAGxE,YAAI,UAAU,EAAG,QAAO;AAExB,cAAM,OAAO,KAAK,QAAQ,CAAC;AAI3B,eAAO,KAAK,WAAW;MACzB;AAEA,eAAS,WACP,MACA,OACA,cACA,YACA,cACA,YACS;AAET,YAAI,UAAU,EAAG,QAAO;AAExB,cAAM,OAAO,KAAK,QAAQ,CAAC;AAG3B,YAAI,KAAK,WAAW,EAAG,QAAO;AAI9B,eACE,iBAAiB,KAAK,aAAa,KACnC,eAAe,KAAK,WAAW,KAC/B,iBAAiB,KAAK,aAAa,KACnC,gBAAgB,KAAK,WAAW,IAAI,KAAK,WAAW,IAAI;MAE5D;AAEA,eAAS,mBACP,UACA,KACA,SAOA;AACA,cAAM,EAAE,WAAW,QAAQ,UAAU,MAAM,QAAQ,IAAI;AACvD,YAAI,CAAC,QAAQ;AACX,iBAAO;YACL;YACA;YACA,UAAU,OAAO;YACjB,UAAU;YACV;YACA;YACA;YACA;YACA;UACF;QACF;AACA,eAAY,QAAQ;AACpB,eAAO;UACL;UACA;UACA,UAAU,OAAO;UACjB,UAAU;UACV;UACA,SAAS,OAAO;UAChB,SAAS;UACT;UACA;QACF;MACF;;;;;;;;;;;;AG9dA,YAAM,qBAAqC,cAAc,IAAI,IAAI,IAAI,IAAI,MAAM,KAAK;AACpF,YAAM,gBAA2B,CAAA;AAEjC,eAAS,cACP,QACA,MACA,QACA,MACA,SACA,QAAe;AAEf,eAAO,EAAE,QAAQ,MAAM,QAAQ,MAAM,SAAS,OAAM;MACtD;AAgBA,eAAS,OACP,KACA,SACA,QACA,SACA,QAAe;AAEf,eAAO;UACL;UACA;UACA;UACA;UACA;;MAEJ;AAMgB,eAAA,UAAU,KAAe,SAAkB;AACzD,eAAO,OAAO,KAAK,SAAS,IAAI,MAAM,KAAK;MAC7C;eAMgB,eACd,QACA,SACA,QAAe;AAEf,eAAO,OAAO,MAAM,eAAe,QAAQ,SAAS,MAAM;MAC5D;AAMM,eAAU,cAAc,MAAe;AAG3C,cAAM,MAAM,IAAIC,WAAAA,WAAW,EAAE,MAAM,KAAK,IAAI,KAAI,CAAE;AAClD,cAAM,EAAE,SAAS,aAAa,IAAG,IAAK;AACtC,cAAM,YAAY,IAAI;AACtB,cAAM,eAAeC,aAAAA,gBAAgB,GAAG;AAExC,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,gBAAM,WAAW,aAAa,CAAC;AAE/B,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,kBAAM,UAAU,SAAS,CAAC;AAC1B,kBAAM,SAAS,QAAQ,CAAC;AACxB,gBAAI,SAAwC;AAI5C,gBAAI,QAAQ,WAAW,GAAG;AACxB,oBAAMC,UAAS,YAAY,QAAQ,CAAC,CAAC;AACrC,uBAAS,oBACPA,SACA,QAAQ,CAAC,GACT,QAAQ,CAAC,GACT,QAAQ,WAAW,IAAI,UAAU,QAAQ,CAAC,CAAC,IAAI,EAAE;AAKnD,kBAAI,UAAU;AAAM;YACrB;AAED,kBAAM,EAAE,QAAQ,MAAM,MAAM,SAAS,QAAQ,OAAM,IAAK;AAExDC,uBAAAA,gBAAgB,KAAK,GAAG,QAAQ,QAAQ,MAAM,QAAQ,IAAI;AAC1D,gBAAI,UAAU,WAAW;AAAMC,yBAAAA,iBAAiB,KAAK,QAAQ,OAAO;AACpE,gBAAI;AAAQC,yBAAAA,UAAU,KAAK,QAAQ,IAAI;UACxC;QACF;AAED,eAAO;MACT;AAMM,eAAU,oBACd,QACA,MACA,QACA,MAAY;AAEZ,YAAI,CAAC,OAAO,KAAK;AACf,iBAAO,cAAc,OAAO,QAAQ,MAAM,QAAQ,MAAM,OAAO,SAAS,OAAO,MAAM;QACtF;AAED,cAAM,UAAUC,aAAAA,aAAa,OAAO,KAAK,MAAM,MAAM;AAGrD,YAAI,WAAW;AAAM,iBAAO;AAG5B,YAAI,QAAQ,WAAW;AAAG,iBAAO;AAEjC,eAAO,oBACL,OAAO,QAAQ,QAAQ,CAAC,CAAC,GACzB,QAAQ,CAAC,GACT,QAAQ,CAAC,GACT,QAAQ,WAAW,IAAI,OAAO,IAAI,MAAM,QAAQ,CAAC,CAAC,IAAI,IAAI;MAE9D;ACpKA,eAAS,QAAW,OAAc;AAChC,YAAI,MAAM,QAAQ,KAAK;AAAG,iBAAO;AACjC,eAAO,CAAC,KAAK;MACf;AAac,eAAU,mBACtB,OACA,QAAuB;AAEvB,cAAM,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,MAAM,IAAIC,aAAAA,SAAS,GAAG,EAAE,CAAC;AAC1D,cAAM,MAAM,KAAK,IAAG;AAEpB,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,KAAK,CAAC,EAAE,QAAQ,SAAS,GAAG;AAC9B,kBAAM,IAAI,MACR,sBAAsB,CAAC;sEACkD;UAE5E;QACF;AAED,YAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,CAAC;AACnC,iBAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,iBAAO,UAAU,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;QACjC;AACD,eAAO;MACT;AAEA,eAAS,MACP,KACA,QACA,UACA,eAAqB;AAErB,cAAM,EAAE,iBAAiB,gBAAgB,WAAU,IAAK;AAExD,cAAM,QAAQ,gBAAgB;AAC9B,cAAM,WAAW,gBAAgB,IAAI,CAAC,YAA2B,MAAsB;AAKrF,gBAAM,MAAqB;YACzB;YACA;YACA,QAAQ,cAAc;YACtB,SAAS;YACT,QAAQ;;AAKV,gBAAM,YAAY,OAAO,IAAI,QAAQ,GAAG;AAExC,gBAAM,EAAE,QAAQ,SAAS,OAAM,IAAK;AAGpC,cAAI;AAAW,mBAAO,MAAM,IAAIA,aAAAA,SAAS,WAAW,MAAM,GAAG,QAAQ,QAAQ,KAAK;AAMlF,gBAAM,gBACJ,YAAY,SAAY,UAAU,iBAAiB,eAAe,CAAC,IAAI;AACzE,gBAAM,UAAU,WAAW,SAAY,SAAS,aAAa,WAAW,SAAS,CAAC,IAAI;AACtF,iBAAO,eAAe,QAAQ,eAAe,OAAO;QACtD,CAAC;AAED,eAAO,UAAU,KAAK,QAAQ;MAChC;MC/Ec,MAAO,UAAS;QAU5B,YAAY,KAAiB,SAAgB;AAC3C,gBAAM,MAAM,QAAQ,kBAAkBC,WAAAA,aAAa,GAAG,IAAIC,WAAAA,aAAa,GAAG;AAC1E,eAAK,UAAU,IAAI;AACnB,eAAK,OAAO,IAAI;AAChB,eAAK,WAAW,IAAI;AACpB,eAAK,QAAQ,IAAI;AACjB,eAAK,aAAa,IAAI;AACtB,eAAK,aAAa,IAAI;AAEtB,eAAK,UAAU,IAAI;AACnB,cAAI,CAAC,QAAQ,gBAAgB;AAC3B,iBAAK,iBAAiB,IAAI;UAC3B;;QAGH,WAAQ;AACN,iBAAO,KAAK,UAAU,IAAI;;MAE7B;ACLuB,eAAA,UACtB,OACA,QACA,SAA2B;AAE3B,cAAM,OACJ,OAAO,YAAY,WAAW,UAAU,EAAE,gBAAgB,CAAC,CAAC,SAAS,iBAAiB,MAAK;AAC7F,cAAM,OAAO,mBAAmB,OAAO,MAAM;AAC7C,eAAO,IAAI,UAAU,cAAc,IAAI,GAAG,IAAI;MAChD;;;;;", "names": ["url", "module", "__commonJS", "exports", "module", "resolve<PERSON>ri", "sourceIndex", "__commonJS", "exports", "module", "cast", "GenMapping", "decodedMappings", "source", "maybeAddSegment", "setSourceContent", "setIgnore", "traceSegment", "TraceMap", "toDecodedMap", "toEncodedMap"]}