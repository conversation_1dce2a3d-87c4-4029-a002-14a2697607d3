import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// optional-peer-dep:__vite-optional-peer-dep:msw/browser:@vitest/mocker:false
var require_mocker_false = __commonJS({
  "optional-peer-dep:__vite-optional-peer-dep:msw/browser:@vitest/mocker:false"(exports, module) {
    module.exports = {};
    throw new Error(`Could not resolve "msw/browser" imported by "@vitest/mocker". Is it installed?`);
  }
});
export default require_mocker_false();
//# sourceMappingURL=mocker_false-546SUKEZ.js.map
