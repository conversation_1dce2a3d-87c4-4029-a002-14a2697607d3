{"version": 3, "sources": ["../../../../../@vitest/mocker/dist/chunk-pathe.M-eThtNZ.js", "../../../../../@vitest/mocker/dist/chunk-mocker.js", "../../../../../@vitest/mocker/dist/chunk-utils.js", "../../../../../@vitest/mocker/dist/chunk-interceptor-native.js", "../../../../../@vitest/mocker/dist/browser.js"], "sourcesContent": ["const _DRIVE_LETTER_START_RE = /^[A-Za-z]:\\//;\nfunction normalizeWindowsPath(input = \"\") {\n  if (!input) {\n    return input;\n  }\n  return input.replace(/\\\\/g, \"/\").replace(_DRIVE_LETTER_START_RE, (r) => r.toUpperCase());\n}\n\nconst _UNC_REGEX = /^[/\\\\]{2}/;\nconst _IS_ABSOLUTE_RE = /^[/\\\\](?![/\\\\])|^[/\\\\]{2}(?!\\.)|^[A-Za-z]:[/\\\\]/;\nconst _DRIVE_LETTER_RE = /^[A-Za-z]:$/;\nconst _EXTNAME_RE = /.(\\.[^./]+|\\.)$/;\nconst normalize = function(path) {\n  if (path.length === 0) {\n    return \".\";\n  }\n  path = normalizeWindowsPath(path);\n  const isUNCPath = path.match(_UNC_REGEX);\n  const isPathAbsolute = isAbsolute(path);\n  const trailingSeparator = path[path.length - 1] === \"/\";\n  path = normalizeString(path, !isPathAbsolute);\n  if (path.length === 0) {\n    if (isPathAbsolute) {\n      return \"/\";\n    }\n    return trailingSeparator ? \"./\" : \".\";\n  }\n  if (trailingSeparator) {\n    path += \"/\";\n  }\n  if (_DRIVE_LETTER_RE.test(path)) {\n    path += \"/\";\n  }\n  if (isUNCPath) {\n    if (!isPathAbsolute) {\n      return `//./${path}`;\n    }\n    return `//${path}`;\n  }\n  return isPathAbsolute && !isAbsolute(path) ? `/${path}` : path;\n};\nconst join = function(...segments) {\n  let path = \"\";\n  for (const seg of segments) {\n    if (!seg) {\n      continue;\n    }\n    if (path.length > 0) {\n      const pathTrailing = path[path.length - 1] === \"/\";\n      const segLeading = seg[0] === \"/\";\n      const both = pathTrailing && segLeading;\n      if (both) {\n        path += seg.slice(1);\n      } else {\n        path += pathTrailing || segLeading ? seg : `/${seg}`;\n      }\n    } else {\n      path += seg;\n    }\n  }\n  return normalize(path);\n};\nfunction cwd() {\n  if (typeof process !== \"undefined\" && typeof process.cwd === \"function\") {\n    return process.cwd().replace(/\\\\/g, \"/\");\n  }\n  return \"/\";\n}\nconst resolve = function(...arguments_) {\n  arguments_ = arguments_.map((argument) => normalizeWindowsPath(argument));\n  let resolvedPath = \"\";\n  let resolvedAbsolute = false;\n  for (let index = arguments_.length - 1; index >= -1 && !resolvedAbsolute; index--) {\n    const path = index >= 0 ? arguments_[index] : cwd();\n    if (!path || path.length === 0) {\n      continue;\n    }\n    resolvedPath = `${path}/${resolvedPath}`;\n    resolvedAbsolute = isAbsolute(path);\n  }\n  resolvedPath = normalizeString(resolvedPath, !resolvedAbsolute);\n  if (resolvedAbsolute && !isAbsolute(resolvedPath)) {\n    return `/${resolvedPath}`;\n  }\n  return resolvedPath.length > 0 ? resolvedPath : \".\";\n};\nfunction normalizeString(path, allowAboveRoot) {\n  let res = \"\";\n  let lastSegmentLength = 0;\n  let lastSlash = -1;\n  let dots = 0;\n  let char = null;\n  for (let index = 0; index <= path.length; ++index) {\n    if (index < path.length) {\n      char = path[index];\n    } else if (char === \"/\") {\n      break;\n    } else {\n      char = \"/\";\n    }\n    if (char === \"/\") {\n      if (lastSlash === index - 1 || dots === 1) ; else if (dots === 2) {\n        if (res.length < 2 || lastSegmentLength !== 2 || res[res.length - 1] !== \".\" || res[res.length - 2] !== \".\") {\n          if (res.length > 2) {\n            const lastSlashIndex = res.lastIndexOf(\"/\");\n            if (lastSlashIndex === -1) {\n              res = \"\";\n              lastSegmentLength = 0;\n            } else {\n              res = res.slice(0, lastSlashIndex);\n              lastSegmentLength = res.length - 1 - res.lastIndexOf(\"/\");\n            }\n            lastSlash = index;\n            dots = 0;\n            continue;\n          } else if (res.length > 0) {\n            res = \"\";\n            lastSegmentLength = 0;\n            lastSlash = index;\n            dots = 0;\n            continue;\n          }\n        }\n        if (allowAboveRoot) {\n          res += res.length > 0 ? \"/..\" : \"..\";\n          lastSegmentLength = 2;\n        }\n      } else {\n        if (res.length > 0) {\n          res += `/${path.slice(lastSlash + 1, index)}`;\n        } else {\n          res = path.slice(lastSlash + 1, index);\n        }\n        lastSegmentLength = index - lastSlash - 1;\n      }\n      lastSlash = index;\n      dots = 0;\n    } else if (char === \".\" && dots !== -1) {\n      ++dots;\n    } else {\n      dots = -1;\n    }\n  }\n  return res;\n}\nconst isAbsolute = function(p) {\n  return _IS_ABSOLUTE_RE.test(p);\n};\nconst extname = function(p) {\n  if (p === \"..\") return \"\";\n  const match = _EXTNAME_RE.exec(normalizeWindowsPath(p));\n  return match && match[1] || \"\";\n};\nconst dirname = function(p) {\n  const segments = normalizeWindowsPath(p).replace(/\\/$/, \"\").split(\"/\").slice(0, -1);\n  if (segments.length === 1 && _DRIVE_LETTER_RE.test(segments[0])) {\n    segments[0] += \"/\";\n  }\n  return segments.join(\"/\") || (isAbsolute(p) ? \"/\" : \".\");\n};\nconst basename = function(p, extension) {\n  const segments = normalizeWindowsPath(p).split(\"/\");\n  let lastSegment = \"\";\n  for (let i = segments.length - 1; i >= 0; i--) {\n    const val = segments[i];\n    if (val) {\n      lastSegment = val;\n      break;\n    }\n  }\n  return extension && lastSegment.endsWith(extension) ? lastSegment.slice(0, -extension.length) : lastSegment;\n};\n\nexport { basename as b, dirname as d, extname as e, isAbsolute as i, join as j, resolve as r };\n", "import { mockObject } from './index.js';\nimport { M as MockerRegistry, R as RedirectedModule, A as AutomockedModule } from './chunk-registry.js';\nimport { e as extname, j as join } from './chunk-pathe.M-eThtNZ.js';\n\n// src/index.ts\nvar f = {\n  reset: [0, 0],\n  bold: [1, 22, \"\\x1B[22m\\x1B[1m\"],\n  dim: [2, 22, \"\\x1B[22m\\x1B[2m\"],\n  italic: [3, 23],\n  underline: [4, 24],\n  inverse: [7, 27],\n  hidden: [8, 28],\n  strikethrough: [9, 29],\n  black: [30, 39],\n  red: [31, 39],\n  green: [32, 39],\n  yellow: [33, 39],\n  blue: [34, 39],\n  magenta: [35, 39],\n  cyan: [36, 39],\n  white: [37, 39],\n  gray: [90, 39],\n  bgBlack: [40, 49],\n  bgRed: [41, 49],\n  bgGreen: [42, 49],\n  bgYellow: [43, 49],\n  bgBlue: [44, 49],\n  bgMagenta: [45, 49],\n  bg<PERSON>yan: [46, 49],\n  bgWhite: [47, 49],\n  blackBright: [90, 39],\n  redBright: [91, 39],\n  greenBright: [92, 39],\n  yellowBright: [93, 39],\n  blueBright: [94, 39],\n  magentaBright: [95, 39],\n  cyanBright: [96, 39],\n  whiteBright: [97, 39],\n  bgBlackBright: [100, 49],\n  bgRedBright: [101, 49],\n  bgGreenBright: [102, 49],\n  bgYellowBright: [103, 49],\n  bgBlueBright: [104, 49],\n  bgMagentaBright: [105, 49],\n  bgCyanBright: [106, 49],\n  bgWhiteBright: [107, 49]\n}, h = Object.entries(f);\nfunction a(n) {\n  return String(n);\n}\na.open = \"\";\na.close = \"\";\nfunction C(n = false) {\n  let e = typeof process != \"undefined\" ? process : void 0, i = (e == null ? void 0 : e.env) || {}, g = (e == null ? void 0 : e.argv) || [];\n  return !(\"NO_COLOR\" in i || g.includes(\"--no-color\")) && (\"FORCE_COLOR\" in i || g.includes(\"--color\") || (e == null ? void 0 : e.platform) === \"win32\" || n && i.TERM !== \"dumb\" || \"CI\" in i) || typeof window != \"undefined\" && !!window.chrome;\n}\nfunction p(n = false) {\n  let e = C(n), i = (r, t, c, o) => {\n    let l = \"\", s = 0;\n    do\n      l += r.substring(s, o) + c, s = o + t.length, o = r.indexOf(t, s);\n    while (~o);\n    return l + r.substring(s);\n  }, g = (r, t, c = r) => {\n    let o = (l) => {\n      let s = String(l), b = s.indexOf(t, r.length);\n      return ~b ? r + i(s, t, c, b) + t : r + s + t;\n    };\n    return o.open = r, o.close = t, o;\n  }, u = {\n    isColorSupported: e\n  }, d = (r) => `\\x1B[${r}m`;\n  for (let [r, t] of h)\n    u[r] = e ? g(\n      d(t[0]),\n      d(t[1]),\n      t[2]\n    ) : a;\n  return u;\n}\n\np();\n\nfunction _mergeNamespaces(n, m) {\n\tm.forEach(function(e) {\n\t\te && typeof e !== \"string\" && !Array.isArray(e) && Object.keys(e).forEach(function(k) {\n\t\t\tif (k !== \"default\" && !(k in n)) {\n\t\t\t\tvar d = Object.getOwnPropertyDescriptor(e, k);\n\t\t\t\tObject.defineProperty(n, k, d.get ? d : {\n\t\t\t\t\tenumerable: true,\n\t\t\t\t\tget: function() {\n\t\t\t\t\t\treturn e[k];\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t});\n\treturn Object.freeze(n);\n}\nfunction getDefaultExportFromCjs(x) {\n\treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, \"default\") ? x[\"default\"] : x;\n}\nvar reactIs$1 = { exports: {} };\nvar reactIs_production = {};\n/**\n* @license React\n* react-is.production.js\n*\n* Copyright (c) Meta Platforms, Inc. and affiliates.\n*\n* This source code is licensed under the MIT license found in the\n* LICENSE file in the root directory of this source tree.\n*/\nvar hasRequiredReactIs_production;\nfunction requireReactIs_production() {\n\tif (hasRequiredReactIs_production) return reactIs_production;\n\thasRequiredReactIs_production = 1;\n\tvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"), REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"), REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"), REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"), REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n\tvar REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"), REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"), REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"), REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"), REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"), REACT_MEMO_TYPE = Symbol.for(\"react.memo\"), REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"), REACT_VIEW_TRANSITION_TYPE = Symbol.for(\"react.view_transition\"), REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\");\n\tfunction typeOf(object) {\n\t\tif (\"object\" === typeof object && null !== object) {\n\t\t\tvar $$typeof = object.$$typeof;\n\t\t\tswitch ($$typeof) {\n\t\t\t\tcase REACT_ELEMENT_TYPE: switch (object = object.type, object) {\n\t\t\t\t\tcase REACT_FRAGMENT_TYPE:\n\t\t\t\t\tcase REACT_PROFILER_TYPE:\n\t\t\t\t\tcase REACT_STRICT_MODE_TYPE:\n\t\t\t\t\tcase REACT_SUSPENSE_TYPE:\n\t\t\t\t\tcase REACT_SUSPENSE_LIST_TYPE:\n\t\t\t\t\tcase REACT_VIEW_TRANSITION_TYPE: return object;\n\t\t\t\t\tdefault: switch (object = object && object.$$typeof, object) {\n\t\t\t\t\t\tcase REACT_CONTEXT_TYPE:\n\t\t\t\t\t\tcase REACT_FORWARD_REF_TYPE:\n\t\t\t\t\t\tcase REACT_LAZY_TYPE:\n\t\t\t\t\t\tcase REACT_MEMO_TYPE: return object;\n\t\t\t\t\t\tcase REACT_CONSUMER_TYPE: return object;\n\t\t\t\t\t\tdefault: return $$typeof;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tcase REACT_PORTAL_TYPE: return $$typeof;\n\t\t\t}\n\t\t}\n\t}\n\treactIs_production.ContextConsumer = REACT_CONSUMER_TYPE;\n\treactIs_production.ContextProvider = REACT_CONTEXT_TYPE;\n\treactIs_production.Element = REACT_ELEMENT_TYPE;\n\treactIs_production.ForwardRef = REACT_FORWARD_REF_TYPE;\n\treactIs_production.Fragment = REACT_FRAGMENT_TYPE;\n\treactIs_production.Lazy = REACT_LAZY_TYPE;\n\treactIs_production.Memo = REACT_MEMO_TYPE;\n\treactIs_production.Portal = REACT_PORTAL_TYPE;\n\treactIs_production.Profiler = REACT_PROFILER_TYPE;\n\treactIs_production.StrictMode = REACT_STRICT_MODE_TYPE;\n\treactIs_production.Suspense = REACT_SUSPENSE_TYPE;\n\treactIs_production.SuspenseList = REACT_SUSPENSE_LIST_TYPE;\n\treactIs_production.isContextConsumer = function(object) {\n\t\treturn typeOf(object) === REACT_CONSUMER_TYPE;\n\t};\n\treactIs_production.isContextProvider = function(object) {\n\t\treturn typeOf(object) === REACT_CONTEXT_TYPE;\n\t};\n\treactIs_production.isElement = function(object) {\n\t\treturn \"object\" === typeof object && null !== object && object.$$typeof === REACT_ELEMENT_TYPE;\n\t};\n\treactIs_production.isForwardRef = function(object) {\n\t\treturn typeOf(object) === REACT_FORWARD_REF_TYPE;\n\t};\n\treactIs_production.isFragment = function(object) {\n\t\treturn typeOf(object) === REACT_FRAGMENT_TYPE;\n\t};\n\treactIs_production.isLazy = function(object) {\n\t\treturn typeOf(object) === REACT_LAZY_TYPE;\n\t};\n\treactIs_production.isMemo = function(object) {\n\t\treturn typeOf(object) === REACT_MEMO_TYPE;\n\t};\n\treactIs_production.isPortal = function(object) {\n\t\treturn typeOf(object) === REACT_PORTAL_TYPE;\n\t};\n\treactIs_production.isProfiler = function(object) {\n\t\treturn typeOf(object) === REACT_PROFILER_TYPE;\n\t};\n\treactIs_production.isStrictMode = function(object) {\n\t\treturn typeOf(object) === REACT_STRICT_MODE_TYPE;\n\t};\n\treactIs_production.isSuspense = function(object) {\n\t\treturn typeOf(object) === REACT_SUSPENSE_TYPE;\n\t};\n\treactIs_production.isSuspenseList = function(object) {\n\t\treturn typeOf(object) === REACT_SUSPENSE_LIST_TYPE;\n\t};\n\treactIs_production.isValidElementType = function(type) {\n\t\treturn \"string\" === typeof type || \"function\" === typeof type || type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || \"object\" === typeof type && null !== type && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_CONSUMER_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_CLIENT_REFERENCE || void 0 !== type.getModuleId) ? true : false;\n\t};\n\treactIs_production.typeOf = typeOf;\n\treturn reactIs_production;\n}\nvar reactIs_development$1 = {};\n/**\n* @license React\n* react-is.development.js\n*\n* Copyright (c) Meta Platforms, Inc. and affiliates.\n*\n* This source code is licensed under the MIT license found in the\n* LICENSE file in the root directory of this source tree.\n*/\nvar hasRequiredReactIs_development$1;\nfunction requireReactIs_development$1() {\n\tif (hasRequiredReactIs_development$1) return reactIs_development$1;\n\thasRequiredReactIs_development$1 = 1;\n\t\"production\" !== process.env.NODE_ENV && function() {\n\t\tfunction typeOf(object) {\n\t\t\tif (\"object\" === typeof object && null !== object) {\n\t\t\t\tvar $$typeof = object.$$typeof;\n\t\t\t\tswitch ($$typeof) {\n\t\t\t\t\tcase REACT_ELEMENT_TYPE: switch (object = object.type, object) {\n\t\t\t\t\t\tcase REACT_FRAGMENT_TYPE:\n\t\t\t\t\t\tcase REACT_PROFILER_TYPE:\n\t\t\t\t\t\tcase REACT_STRICT_MODE_TYPE:\n\t\t\t\t\t\tcase REACT_SUSPENSE_TYPE:\n\t\t\t\t\t\tcase REACT_SUSPENSE_LIST_TYPE:\n\t\t\t\t\t\tcase REACT_VIEW_TRANSITION_TYPE: return object;\n\t\t\t\t\t\tdefault: switch (object = object && object.$$typeof, object) {\n\t\t\t\t\t\t\tcase REACT_CONTEXT_TYPE:\n\t\t\t\t\t\t\tcase REACT_FORWARD_REF_TYPE:\n\t\t\t\t\t\t\tcase REACT_LAZY_TYPE:\n\t\t\t\t\t\t\tcase REACT_MEMO_TYPE: return object;\n\t\t\t\t\t\t\tcase REACT_CONSUMER_TYPE: return object;\n\t\t\t\t\t\t\tdefault: return $$typeof;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tcase REACT_PORTAL_TYPE: return $$typeof;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"), REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"), REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"), REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"), REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n\t\tvar REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"), REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"), REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"), REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"), REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"), REACT_MEMO_TYPE = Symbol.for(\"react.memo\"), REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"), REACT_VIEW_TRANSITION_TYPE = Symbol.for(\"react.view_transition\"), REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\");\n\t\treactIs_development$1.ContextConsumer = REACT_CONSUMER_TYPE;\n\t\treactIs_development$1.ContextProvider = REACT_CONTEXT_TYPE;\n\t\treactIs_development$1.Element = REACT_ELEMENT_TYPE;\n\t\treactIs_development$1.ForwardRef = REACT_FORWARD_REF_TYPE;\n\t\treactIs_development$1.Fragment = REACT_FRAGMENT_TYPE;\n\t\treactIs_development$1.Lazy = REACT_LAZY_TYPE;\n\t\treactIs_development$1.Memo = REACT_MEMO_TYPE;\n\t\treactIs_development$1.Portal = REACT_PORTAL_TYPE;\n\t\treactIs_development$1.Profiler = REACT_PROFILER_TYPE;\n\t\treactIs_development$1.StrictMode = REACT_STRICT_MODE_TYPE;\n\t\treactIs_development$1.Suspense = REACT_SUSPENSE_TYPE;\n\t\treactIs_development$1.SuspenseList = REACT_SUSPENSE_LIST_TYPE;\n\t\treactIs_development$1.isContextConsumer = function(object) {\n\t\t\treturn typeOf(object) === REACT_CONSUMER_TYPE;\n\t\t};\n\t\treactIs_development$1.isContextProvider = function(object) {\n\t\t\treturn typeOf(object) === REACT_CONTEXT_TYPE;\n\t\t};\n\t\treactIs_development$1.isElement = function(object) {\n\t\t\treturn \"object\" === typeof object && null !== object && object.$$typeof === REACT_ELEMENT_TYPE;\n\t\t};\n\t\treactIs_development$1.isForwardRef = function(object) {\n\t\t\treturn typeOf(object) === REACT_FORWARD_REF_TYPE;\n\t\t};\n\t\treactIs_development$1.isFragment = function(object) {\n\t\t\treturn typeOf(object) === REACT_FRAGMENT_TYPE;\n\t\t};\n\t\treactIs_development$1.isLazy = function(object) {\n\t\t\treturn typeOf(object) === REACT_LAZY_TYPE;\n\t\t};\n\t\treactIs_development$1.isMemo = function(object) {\n\t\t\treturn typeOf(object) === REACT_MEMO_TYPE;\n\t\t};\n\t\treactIs_development$1.isPortal = function(object) {\n\t\t\treturn typeOf(object) === REACT_PORTAL_TYPE;\n\t\t};\n\t\treactIs_development$1.isProfiler = function(object) {\n\t\t\treturn typeOf(object) === REACT_PROFILER_TYPE;\n\t\t};\n\t\treactIs_development$1.isStrictMode = function(object) {\n\t\t\treturn typeOf(object) === REACT_STRICT_MODE_TYPE;\n\t\t};\n\t\treactIs_development$1.isSuspense = function(object) {\n\t\t\treturn typeOf(object) === REACT_SUSPENSE_TYPE;\n\t\t};\n\t\treactIs_development$1.isSuspenseList = function(object) {\n\t\t\treturn typeOf(object) === REACT_SUSPENSE_LIST_TYPE;\n\t\t};\n\t\treactIs_development$1.isValidElementType = function(type) {\n\t\t\treturn \"string\" === typeof type || \"function\" === typeof type || type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || \"object\" === typeof type && null !== type && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_CONSUMER_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_CLIENT_REFERENCE || void 0 !== type.getModuleId) ? true : false;\n\t\t};\n\t\treactIs_development$1.typeOf = typeOf;\n\t}();\n\treturn reactIs_development$1;\n}\nvar hasRequiredReactIs$1;\nfunction requireReactIs$1() {\n\tif (hasRequiredReactIs$1) return reactIs$1.exports;\n\thasRequiredReactIs$1 = 1;\n\tif (process.env.NODE_ENV === \"production\") {\n\t\treactIs$1.exports = requireReactIs_production();\n\t} else {\n\t\treactIs$1.exports = requireReactIs_development$1();\n\t}\n\treturn reactIs$1.exports;\n}\nvar reactIsExports$1 = requireReactIs$1();\nvar index$1 = /* @__PURE__ */ getDefaultExportFromCjs(reactIsExports$1);\nvar ReactIs19 = /* @__PURE__ */ _mergeNamespaces({\n\t__proto__: null,\n\tdefault: index$1\n}, [reactIsExports$1]);\nvar reactIs = { exports: {} };\nvar reactIs_production_min = {};\n/**\n* @license React\n* react-is.production.min.js\n*\n* Copyright (c) Facebook, Inc. and its affiliates.\n*\n* This source code is licensed under the MIT license found in the\n* LICENSE file in the root directory of this source tree.\n*/\nvar hasRequiredReactIs_production_min;\nfunction requireReactIs_production_min() {\n\tif (hasRequiredReactIs_production_min) return reactIs_production_min;\n\thasRequiredReactIs_production_min = 1;\n\tvar b = Symbol.for(\"react.element\"), c = Symbol.for(\"react.portal\"), d = Symbol.for(\"react.fragment\"), e = Symbol.for(\"react.strict_mode\"), f = Symbol.for(\"react.profiler\"), g = Symbol.for(\"react.provider\"), h = Symbol.for(\"react.context\"), k = Symbol.for(\"react.server_context\"), l = Symbol.for(\"react.forward_ref\"), m = Symbol.for(\"react.suspense\"), n = Symbol.for(\"react.suspense_list\"), p = Symbol.for(\"react.memo\"), q = Symbol.for(\"react.lazy\"), t = Symbol.for(\"react.offscreen\"), u;\n\tu = Symbol.for(\"react.module.reference\");\n\tfunction v(a) {\n\t\tif (\"object\" === typeof a && null !== a) {\n\t\t\tvar r = a.$$typeof;\n\t\t\tswitch (r) {\n\t\t\t\tcase b: switch (a = a.type, a) {\n\t\t\t\t\tcase d:\n\t\t\t\t\tcase f:\n\t\t\t\t\tcase e:\n\t\t\t\t\tcase m:\n\t\t\t\t\tcase n: return a;\n\t\t\t\t\tdefault: switch (a = a && a.$$typeof, a) {\n\t\t\t\t\t\tcase k:\n\t\t\t\t\t\tcase h:\n\t\t\t\t\t\tcase l:\n\t\t\t\t\t\tcase q:\n\t\t\t\t\t\tcase p:\n\t\t\t\t\t\tcase g: return a;\n\t\t\t\t\t\tdefault: return r;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tcase c: return r;\n\t\t\t}\n\t\t}\n\t}\n\treactIs_production_min.ContextConsumer = h;\n\treactIs_production_min.ContextProvider = g;\n\treactIs_production_min.Element = b;\n\treactIs_production_min.ForwardRef = l;\n\treactIs_production_min.Fragment = d;\n\treactIs_production_min.Lazy = q;\n\treactIs_production_min.Memo = p;\n\treactIs_production_min.Portal = c;\n\treactIs_production_min.Profiler = f;\n\treactIs_production_min.StrictMode = e;\n\treactIs_production_min.Suspense = m;\n\treactIs_production_min.SuspenseList = n;\n\treactIs_production_min.isAsyncMode = function() {\n\t\treturn false;\n\t};\n\treactIs_production_min.isConcurrentMode = function() {\n\t\treturn false;\n\t};\n\treactIs_production_min.isContextConsumer = function(a) {\n\t\treturn v(a) === h;\n\t};\n\treactIs_production_min.isContextProvider = function(a) {\n\t\treturn v(a) === g;\n\t};\n\treactIs_production_min.isElement = function(a) {\n\t\treturn \"object\" === typeof a && null !== a && a.$$typeof === b;\n\t};\n\treactIs_production_min.isForwardRef = function(a) {\n\t\treturn v(a) === l;\n\t};\n\treactIs_production_min.isFragment = function(a) {\n\t\treturn v(a) === d;\n\t};\n\treactIs_production_min.isLazy = function(a) {\n\t\treturn v(a) === q;\n\t};\n\treactIs_production_min.isMemo = function(a) {\n\t\treturn v(a) === p;\n\t};\n\treactIs_production_min.isPortal = function(a) {\n\t\treturn v(a) === c;\n\t};\n\treactIs_production_min.isProfiler = function(a) {\n\t\treturn v(a) === f;\n\t};\n\treactIs_production_min.isStrictMode = function(a) {\n\t\treturn v(a) === e;\n\t};\n\treactIs_production_min.isSuspense = function(a) {\n\t\treturn v(a) === m;\n\t};\n\treactIs_production_min.isSuspenseList = function(a) {\n\t\treturn v(a) === n;\n\t};\n\treactIs_production_min.isValidElementType = function(a) {\n\t\treturn \"string\" === typeof a || \"function\" === typeof a || a === d || a === f || a === e || a === m || a === n || a === t || \"object\" === typeof a && null !== a && (a.$$typeof === q || a.$$typeof === p || a.$$typeof === g || a.$$typeof === h || a.$$typeof === l || a.$$typeof === u || void 0 !== a.getModuleId) ? true : false;\n\t};\n\treactIs_production_min.typeOf = v;\n\treturn reactIs_production_min;\n}\nvar reactIs_development = {};\n/**\n* @license React\n* react-is.development.js\n*\n* Copyright (c) Facebook, Inc. and its affiliates.\n*\n* This source code is licensed under the MIT license found in the\n* LICENSE file in the root directory of this source tree.\n*/\nvar hasRequiredReactIs_development;\nfunction requireReactIs_development() {\n\tif (hasRequiredReactIs_development) return reactIs_development;\n\thasRequiredReactIs_development = 1;\n\tif (process.env.NODE_ENV !== \"production\") {\n\t\t(function() {\n\t\t\t// ATTENTION\n\t\t\t// When adding new symbols to this file,\n\t\t\t// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n\t\t\t// The Symbol used to tag the ReactElement-like types.\n\t\t\tvar REACT_ELEMENT_TYPE = Symbol.for(\"react.element\");\n\t\t\tvar REACT_PORTAL_TYPE = Symbol.for(\"react.portal\");\n\t\t\tvar REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\n\t\t\tvar REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\");\n\t\t\tvar REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n\t\t\tvar REACT_PROVIDER_TYPE = Symbol.for(\"react.provider\");\n\t\t\tvar REACT_CONTEXT_TYPE = Symbol.for(\"react.context\");\n\t\t\tvar REACT_SERVER_CONTEXT_TYPE = Symbol.for(\"react.server_context\");\n\t\t\tvar REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\");\n\t\t\tvar REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\");\n\t\t\tvar REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\");\n\t\t\tvar REACT_MEMO_TYPE = Symbol.for(\"react.memo\");\n\t\t\tvar REACT_LAZY_TYPE = Symbol.for(\"react.lazy\");\n\t\t\tvar REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\");\n\t\t\t// -----------------------------------------------------------------------------\n\t\t\tvar enableScopeAPI = false;\n\t\t\tvar enableCacheElement = false;\n\t\t\tvar enableTransitionTracing = false;\n\t\t\tvar enableLegacyHidden = false;\n\t\t\t// stuff. Intended to enable React core members to more easily debug scheduling\n\t\t\t// issues in DEV builds.\n\t\t\tvar enableDebugTracing = false;\n\t\t\tvar REACT_MODULE_REFERENCE;\n\t\t\t{\n\t\t\t\tREACT_MODULE_REFERENCE = Symbol.for(\"react.module.reference\");\n\t\t\t}\n\t\t\tfunction isValidElementType(type) {\n\t\t\t\tif (typeof type === \"string\" || typeof type === \"function\") {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\tif (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden || type === REACT_OFFSCREEN_TYPE || enableScopeAPI || enableCacheElement || enableTransitionTracing) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\tif (typeof type === \"object\" && type !== null) {\n\t\t\t\t\tif (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfunction typeOf(object) {\n\t\t\t\tif (typeof object === \"object\" && object !== null) {\n\t\t\t\t\tvar $$typeof = object.$$typeof;\n\t\t\t\t\tswitch ($$typeof) {\n\t\t\t\t\t\tcase REACT_ELEMENT_TYPE:\n\t\t\t\t\t\t\tvar type = object.type;\n\t\t\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\t\t\tcase REACT_FRAGMENT_TYPE:\n\t\t\t\t\t\t\t\tcase REACT_PROFILER_TYPE:\n\t\t\t\t\t\t\t\tcase REACT_STRICT_MODE_TYPE:\n\t\t\t\t\t\t\t\tcase REACT_SUSPENSE_TYPE:\n\t\t\t\t\t\t\t\tcase REACT_SUSPENSE_LIST_TYPE: return type;\n\t\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\t\tvar $$typeofType = type && type.$$typeof;\n\t\t\t\t\t\t\t\t\tswitch ($$typeofType) {\n\t\t\t\t\t\t\t\t\t\tcase REACT_SERVER_CONTEXT_TYPE:\n\t\t\t\t\t\t\t\t\t\tcase REACT_CONTEXT_TYPE:\n\t\t\t\t\t\t\t\t\t\tcase REACT_FORWARD_REF_TYPE:\n\t\t\t\t\t\t\t\t\t\tcase REACT_LAZY_TYPE:\n\t\t\t\t\t\t\t\t\t\tcase REACT_MEMO_TYPE:\n\t\t\t\t\t\t\t\t\t\tcase REACT_PROVIDER_TYPE: return $$typeofType;\n\t\t\t\t\t\t\t\t\t\tdefault: return $$typeof;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\tcase REACT_PORTAL_TYPE: return $$typeof;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn undefined;\n\t\t\t}\n\t\t\tvar ContextConsumer = REACT_CONTEXT_TYPE;\n\t\t\tvar ContextProvider = REACT_PROVIDER_TYPE;\n\t\t\tvar Element = REACT_ELEMENT_TYPE;\n\t\t\tvar ForwardRef = REACT_FORWARD_REF_TYPE;\n\t\t\tvar Fragment = REACT_FRAGMENT_TYPE;\n\t\t\tvar Lazy = REACT_LAZY_TYPE;\n\t\t\tvar Memo = REACT_MEMO_TYPE;\n\t\t\tvar Portal = REACT_PORTAL_TYPE;\n\t\t\tvar Profiler = REACT_PROFILER_TYPE;\n\t\t\tvar StrictMode = REACT_STRICT_MODE_TYPE;\n\t\t\tvar Suspense = REACT_SUSPENSE_TYPE;\n\t\t\tvar SuspenseList = REACT_SUSPENSE_LIST_TYPE;\n\t\t\tvar hasWarnedAboutDeprecatedIsAsyncMode = false;\n\t\t\tvar hasWarnedAboutDeprecatedIsConcurrentMode = false;\n\t\t\tfunction isAsyncMode(object) {\n\t\t\t\t{\n\t\t\t\t\tif (!hasWarnedAboutDeprecatedIsAsyncMode) {\n\t\t\t\t\t\thasWarnedAboutDeprecatedIsAsyncMode = true;\n\t\t\t\t\t\tconsole[\"warn\"](\"The ReactIs.isAsyncMode() alias has been deprecated, \" + \"and will be removed in React 18+.\");\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfunction isConcurrentMode(object) {\n\t\t\t\t{\n\t\t\t\t\tif (!hasWarnedAboutDeprecatedIsConcurrentMode) {\n\t\t\t\t\t\thasWarnedAboutDeprecatedIsConcurrentMode = true;\n\t\t\t\t\t\tconsole[\"warn\"](\"The ReactIs.isConcurrentMode() alias has been deprecated, \" + \"and will be removed in React 18+.\");\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfunction isContextConsumer(object) {\n\t\t\t\treturn typeOf(object) === REACT_CONTEXT_TYPE;\n\t\t\t}\n\t\t\tfunction isContextProvider(object) {\n\t\t\t\treturn typeOf(object) === REACT_PROVIDER_TYPE;\n\t\t\t}\n\t\t\tfunction isElement(object) {\n\t\t\t\treturn typeof object === \"object\" && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n\t\t\t}\n\t\t\tfunction isForwardRef(object) {\n\t\t\t\treturn typeOf(object) === REACT_FORWARD_REF_TYPE;\n\t\t\t}\n\t\t\tfunction isFragment(object) {\n\t\t\t\treturn typeOf(object) === REACT_FRAGMENT_TYPE;\n\t\t\t}\n\t\t\tfunction isLazy(object) {\n\t\t\t\treturn typeOf(object) === REACT_LAZY_TYPE;\n\t\t\t}\n\t\t\tfunction isMemo(object) {\n\t\t\t\treturn typeOf(object) === REACT_MEMO_TYPE;\n\t\t\t}\n\t\t\tfunction isPortal(object) {\n\t\t\t\treturn typeOf(object) === REACT_PORTAL_TYPE;\n\t\t\t}\n\t\t\tfunction isProfiler(object) {\n\t\t\t\treturn typeOf(object) === REACT_PROFILER_TYPE;\n\t\t\t}\n\t\t\tfunction isStrictMode(object) {\n\t\t\t\treturn typeOf(object) === REACT_STRICT_MODE_TYPE;\n\t\t\t}\n\t\t\tfunction isSuspense(object) {\n\t\t\t\treturn typeOf(object) === REACT_SUSPENSE_TYPE;\n\t\t\t}\n\t\t\tfunction isSuspenseList(object) {\n\t\t\t\treturn typeOf(object) === REACT_SUSPENSE_LIST_TYPE;\n\t\t\t}\n\t\t\treactIs_development.ContextConsumer = ContextConsumer;\n\t\t\treactIs_development.ContextProvider = ContextProvider;\n\t\t\treactIs_development.Element = Element;\n\t\t\treactIs_development.ForwardRef = ForwardRef;\n\t\t\treactIs_development.Fragment = Fragment;\n\t\t\treactIs_development.Lazy = Lazy;\n\t\t\treactIs_development.Memo = Memo;\n\t\t\treactIs_development.Portal = Portal;\n\t\t\treactIs_development.Profiler = Profiler;\n\t\t\treactIs_development.StrictMode = StrictMode;\n\t\t\treactIs_development.Suspense = Suspense;\n\t\t\treactIs_development.SuspenseList = SuspenseList;\n\t\t\treactIs_development.isAsyncMode = isAsyncMode;\n\t\t\treactIs_development.isConcurrentMode = isConcurrentMode;\n\t\t\treactIs_development.isContextConsumer = isContextConsumer;\n\t\t\treactIs_development.isContextProvider = isContextProvider;\n\t\t\treactIs_development.isElement = isElement;\n\t\t\treactIs_development.isForwardRef = isForwardRef;\n\t\t\treactIs_development.isFragment = isFragment;\n\t\t\treactIs_development.isLazy = isLazy;\n\t\t\treactIs_development.isMemo = isMemo;\n\t\t\treactIs_development.isPortal = isPortal;\n\t\t\treactIs_development.isProfiler = isProfiler;\n\t\t\treactIs_development.isStrictMode = isStrictMode;\n\t\t\treactIs_development.isSuspense = isSuspense;\n\t\t\treactIs_development.isSuspenseList = isSuspenseList;\n\t\t\treactIs_development.isValidElementType = isValidElementType;\n\t\t\treactIs_development.typeOf = typeOf;\n\t\t})();\n\t}\n\treturn reactIs_development;\n}\nvar hasRequiredReactIs;\nfunction requireReactIs() {\n\tif (hasRequiredReactIs) return reactIs.exports;\n\thasRequiredReactIs = 1;\n\tif (process.env.NODE_ENV === \"production\") {\n\t\treactIs.exports = requireReactIs_production_min();\n\t} else {\n\t\treactIs.exports = requireReactIs_development();\n\t}\n\treturn reactIs.exports;\n}\nvar reactIsExports = requireReactIs();\nvar index = /* @__PURE__ */ getDefaultExportFromCjs(reactIsExports);\nvar ReactIs18 = /* @__PURE__ */ _mergeNamespaces({\n\t__proto__: null,\n\tdefault: index\n}, [reactIsExports]);\nconst reactIsMethods = [\n\t\"isAsyncMode\",\n\t\"isConcurrentMode\",\n\t\"isContextConsumer\",\n\t\"isContextProvider\",\n\t\"isElement\",\n\t\"isForwardRef\",\n\t\"isFragment\",\n\t\"isLazy\",\n\t\"isMemo\",\n\t\"isPortal\",\n\t\"isProfiler\",\n\t\"isStrictMode\",\n\t\"isSuspense\",\n\t\"isSuspenseList\",\n\t\"isValidElementType\"\n];\nObject.fromEntries(reactIsMethods.map((m) => [m, (v) => ReactIs18[m](v) || ReactIs19[m](v)]));\n\nlet getPromiseValue = () => 'Promise{…}';\ntry {\n    // @ts-ignore\n    const { getPromiseDetails, kPending, kRejected } = process.binding('util');\n    if (Array.isArray(getPromiseDetails(Promise.resolve()))) {\n        getPromiseValue = (value, options) => {\n            const [state, innerValue] = getPromiseDetails(value);\n            if (state === kPending) {\n                return 'Promise{<pending>}';\n            }\n            return `Promise${state === kRejected ? '!' : ''}{${options.inspect(innerValue, options)}}`;\n        };\n    }\n}\ncatch (notNode) {\n    /* ignore */\n}\n\n/**\n* Get original stacktrace without source map support the most performant way.\n* - Create only 1 stack frame.\n* - Rewrite prepareStackTrace to bypass \"support-stack-trace\" (usually takes ~250ms).\n*/\nfunction createSimpleStackTrace(options) {\n\tconst { message = \"$$stack trace error\", stackTraceLimit = 1 } = options || {};\n\tconst limit = Error.stackTraceLimit;\n\tconst prepareStackTrace = Error.prepareStackTrace;\n\tError.stackTraceLimit = stackTraceLimit;\n\tError.prepareStackTrace = (e) => e.stack;\n\tconst err = new Error(message);\n\tconst stackTrace = err.stack || \"\";\n\tError.prepareStackTrace = prepareStackTrace;\n\tError.stackTraceLimit = limit;\n\treturn stackTrace;\n}\n\nvar jsTokens_1;\nvar hasRequiredJsTokens;\nfunction requireJsTokens() {\n\tif (hasRequiredJsTokens) return jsTokens_1;\n\thasRequiredJsTokens = 1;\n\t// Copyright 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023 Simon Lydell\n\t// License: MIT.\n\tvar Identifier, JSXIdentifier, JSXPunctuator, JSXString, JSXText, KeywordsWithExpressionAfter, KeywordsWithNoLineTerminatorAfter, LineTerminatorSequence, MultiLineComment, Newline, NumericLiteral, Punctuator, RegularExpressionLiteral, SingleLineComment, StringLiteral, Template, TokensNotPrecedingObjectLiteral, TokensPrecedingExpression, WhiteSpace;\n\tRegularExpressionLiteral = /\\/(?![*\\/])(?:\\[(?:(?![\\]\\\\]).|\\\\.)*\\]|(?![\\/\\\\]).|\\\\.)*(\\/[$_\\u200C\\u200D\\p{ID_Continue}]*|\\\\)?/uy;\n\tPunctuator = /--|\\+\\+|=>|\\.{3}|\\??\\.(?!\\d)|(?:&&|\\|\\||\\?\\?|[+\\-%&|^]|\\*{1,2}|<{1,2}|>{1,3}|!=?|={1,2}|\\/(?![\\/*]))=?|[?~,:;[\\](){}]/y;\n\tIdentifier = /(\\x23?)(?=[$_\\p{ID_Start}\\\\])(?:[$_\\u200C\\u200D\\p{ID_Continue}]|\\\\u[\\da-fA-F]{4}|\\\\u\\{[\\da-fA-F]+\\})+/uy;\n\tStringLiteral = /(['\"])(?:(?!\\1)[^\\\\\\n\\r]|\\\\(?:\\r\\n|[^]))*(\\1)?/y;\n\tNumericLiteral = /(?:0[xX][\\da-fA-F](?:_?[\\da-fA-F])*|0[oO][0-7](?:_?[0-7])*|0[bB][01](?:_?[01])*)n?|0n|[1-9](?:_?\\d)*n|(?:(?:0(?!\\d)|0\\d*[89]\\d*|[1-9](?:_?\\d)*)(?:\\.(?:\\d(?:_?\\d)*)?)?|\\.\\d(?:_?\\d)*)(?:[eE][+-]?\\d(?:_?\\d)*)?|0[0-7]+/y;\n\tTemplate = /[`}](?:[^`\\\\$]|\\\\[^]|\\$(?!\\{))*(`|\\$\\{)?/y;\n\tWhiteSpace = /[\\t\\v\\f\\ufeff\\p{Zs}]+/uy;\n\tLineTerminatorSequence = /\\r?\\n|[\\r\\u2028\\u2029]/y;\n\tMultiLineComment = /\\/\\*(?:[^*]|\\*(?!\\/))*(\\*\\/)?/y;\n\tSingleLineComment = /\\/\\/.*/y;\n\tJSXPunctuator = /[<>.:={}]|\\/(?![\\/*])/y;\n\tJSXIdentifier = /[$_\\p{ID_Start}][$_\\u200C\\u200D\\p{ID_Continue}-]*/uy;\n\tJSXString = /(['\"])(?:(?!\\1)[^])*(\\1)?/y;\n\tJSXText = /[^<>{}]+/y;\n\tTokensPrecedingExpression = /^(?:[\\/+-]|\\.{3}|\\?(?:InterpolationIn(?:JSX|Template)|NoLineTerminatorHere|NonExpressionParenEnd|UnaryIncDec))?$|[{}([,;<>=*%&|^!~?:]$/;\n\tTokensNotPrecedingObjectLiteral = /^(?:=>|[;\\]){}]|else|\\?(?:NoLineTerminatorHere|NonExpressionParenEnd))?$/;\n\tKeywordsWithExpressionAfter = /^(?:await|case|default|delete|do|else|instanceof|new|return|throw|typeof|void|yield)$/;\n\tKeywordsWithNoLineTerminatorAfter = /^(?:return|throw|yield)$/;\n\tNewline = RegExp(LineTerminatorSequence.source);\n\tjsTokens_1 = function* (input, { jsx = false } = {}) {\n\t\tvar braces, firstCodePoint, isExpression, lastIndex, lastSignificantToken, length, match, mode, nextLastIndex, nextLastSignificantToken, parenNesting, postfixIncDec, punctuator, stack;\n\t\t({length} = input);\n\t\tlastIndex = 0;\n\t\tlastSignificantToken = \"\";\n\t\tstack = [{ tag: \"JS\" }];\n\t\tbraces = [];\n\t\tparenNesting = 0;\n\t\tpostfixIncDec = false;\n\t\twhile (lastIndex < length) {\n\t\t\tmode = stack[stack.length - 1];\n\t\t\tswitch (mode.tag) {\n\t\t\t\tcase \"JS\":\n\t\t\t\tcase \"JSNonExpressionParen\":\n\t\t\t\tcase \"InterpolationInTemplate\":\n\t\t\t\tcase \"InterpolationInJSX\":\n\t\t\t\t\tif (input[lastIndex] === \"/\" && (TokensPrecedingExpression.test(lastSignificantToken) || KeywordsWithExpressionAfter.test(lastSignificantToken))) {\n\t\t\t\t\t\tRegularExpressionLiteral.lastIndex = lastIndex;\n\t\t\t\t\t\tif (match = RegularExpressionLiteral.exec(input)) {\n\t\t\t\t\t\t\tlastIndex = RegularExpressionLiteral.lastIndex;\n\t\t\t\t\t\t\tlastSignificantToken = match[0];\n\t\t\t\t\t\t\tpostfixIncDec = true;\n\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\ttype: \"RegularExpressionLiteral\",\n\t\t\t\t\t\t\t\tvalue: match[0],\n\t\t\t\t\t\t\t\tclosed: match[1] !== void 0 && match[1] !== \"\\\\\"\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tPunctuator.lastIndex = lastIndex;\n\t\t\t\t\tif (match = Punctuator.exec(input)) {\n\t\t\t\t\t\tpunctuator = match[0];\n\t\t\t\t\t\tnextLastIndex = Punctuator.lastIndex;\n\t\t\t\t\t\tnextLastSignificantToken = punctuator;\n\t\t\t\t\t\tswitch (punctuator) {\n\t\t\t\t\t\t\tcase \"(\":\n\t\t\t\t\t\t\t\tif (lastSignificantToken === \"?NonExpressionParenKeyword\") {\n\t\t\t\t\t\t\t\t\tstack.push({\n\t\t\t\t\t\t\t\t\t\ttag: \"JSNonExpressionParen\",\n\t\t\t\t\t\t\t\t\t\tnesting: parenNesting\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tparenNesting++;\n\t\t\t\t\t\t\t\tpostfixIncDec = false;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase \")\":\n\t\t\t\t\t\t\t\tparenNesting--;\n\t\t\t\t\t\t\t\tpostfixIncDec = true;\n\t\t\t\t\t\t\t\tif (mode.tag === \"JSNonExpressionParen\" && parenNesting === mode.nesting) {\n\t\t\t\t\t\t\t\t\tstack.pop();\n\t\t\t\t\t\t\t\t\tnextLastSignificantToken = \"?NonExpressionParenEnd\";\n\t\t\t\t\t\t\t\t\tpostfixIncDec = false;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase \"{\":\n\t\t\t\t\t\t\t\tPunctuator.lastIndex = 0;\n\t\t\t\t\t\t\t\tisExpression = !TokensNotPrecedingObjectLiteral.test(lastSignificantToken) && (TokensPrecedingExpression.test(lastSignificantToken) || KeywordsWithExpressionAfter.test(lastSignificantToken));\n\t\t\t\t\t\t\t\tbraces.push(isExpression);\n\t\t\t\t\t\t\t\tpostfixIncDec = false;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase \"}\":\n\t\t\t\t\t\t\t\tswitch (mode.tag) {\n\t\t\t\t\t\t\t\t\tcase \"InterpolationInTemplate\":\n\t\t\t\t\t\t\t\t\t\tif (braces.length === mode.nesting) {\n\t\t\t\t\t\t\t\t\t\t\tTemplate.lastIndex = lastIndex;\n\t\t\t\t\t\t\t\t\t\t\tmatch = Template.exec(input);\n\t\t\t\t\t\t\t\t\t\t\tlastIndex = Template.lastIndex;\n\t\t\t\t\t\t\t\t\t\t\tlastSignificantToken = match[0];\n\t\t\t\t\t\t\t\t\t\t\tif (match[1] === \"${\") {\n\t\t\t\t\t\t\t\t\t\t\t\tlastSignificantToken = \"?InterpolationInTemplate\";\n\t\t\t\t\t\t\t\t\t\t\t\tpostfixIncDec = false;\n\t\t\t\t\t\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"TemplateMiddle\",\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue: match[0]\n\t\t\t\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\t\t\t\tstack.pop();\n\t\t\t\t\t\t\t\t\t\t\t\tpostfixIncDec = true;\n\t\t\t\t\t\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype: \"TemplateTail\",\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue: match[0],\n\t\t\t\t\t\t\t\t\t\t\t\t\tclosed: match[1] === \"`\"\n\t\t\t\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t\t\tcase \"InterpolationInJSX\": if (braces.length === mode.nesting) {\n\t\t\t\t\t\t\t\t\t\tstack.pop();\n\t\t\t\t\t\t\t\t\t\tlastIndex += 1;\n\t\t\t\t\t\t\t\t\t\tlastSignificantToken = \"}\";\n\t\t\t\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\t\t\t\ttype: \"JSXPunctuator\",\n\t\t\t\t\t\t\t\t\t\t\tvalue: \"}\"\n\t\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tpostfixIncDec = braces.pop();\n\t\t\t\t\t\t\t\tnextLastSignificantToken = postfixIncDec ? \"?ExpressionBraceEnd\" : \"}\";\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase \"]\":\n\t\t\t\t\t\t\t\tpostfixIncDec = true;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase \"++\":\n\t\t\t\t\t\t\tcase \"--\":\n\t\t\t\t\t\t\t\tnextLastSignificantToken = postfixIncDec ? \"?PostfixIncDec\" : \"?UnaryIncDec\";\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase \"<\":\n\t\t\t\t\t\t\t\tif (jsx && (TokensPrecedingExpression.test(lastSignificantToken) || KeywordsWithExpressionAfter.test(lastSignificantToken))) {\n\t\t\t\t\t\t\t\t\tstack.push({ tag: \"JSXTag\" });\n\t\t\t\t\t\t\t\t\tlastIndex += 1;\n\t\t\t\t\t\t\t\t\tlastSignificantToken = \"<\";\n\t\t\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\t\t\ttype: \"JSXPunctuator\",\n\t\t\t\t\t\t\t\t\t\tvalue: punctuator\n\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tpostfixIncDec = false;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tdefault: postfixIncDec = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlastIndex = nextLastIndex;\n\t\t\t\t\t\tlastSignificantToken = nextLastSignificantToken;\n\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\ttype: \"Punctuator\",\n\t\t\t\t\t\t\tvalue: punctuator\n\t\t\t\t\t\t};\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tIdentifier.lastIndex = lastIndex;\n\t\t\t\t\tif (match = Identifier.exec(input)) {\n\t\t\t\t\t\tlastIndex = Identifier.lastIndex;\n\t\t\t\t\t\tnextLastSignificantToken = match[0];\n\t\t\t\t\t\tswitch (match[0]) {\n\t\t\t\t\t\t\tcase \"for\":\n\t\t\t\t\t\t\tcase \"if\":\n\t\t\t\t\t\t\tcase \"while\":\n\t\t\t\t\t\t\tcase \"with\": if (lastSignificantToken !== \".\" && lastSignificantToken !== \"?.\") {\n\t\t\t\t\t\t\t\tnextLastSignificantToken = \"?NonExpressionParenKeyword\";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlastSignificantToken = nextLastSignificantToken;\n\t\t\t\t\t\tpostfixIncDec = !KeywordsWithExpressionAfter.test(match[0]);\n\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\ttype: match[1] === \"#\" ? \"PrivateIdentifier\" : \"IdentifierName\",\n\t\t\t\t\t\t\tvalue: match[0]\n\t\t\t\t\t\t};\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tStringLiteral.lastIndex = lastIndex;\n\t\t\t\t\tif (match = StringLiteral.exec(input)) {\n\t\t\t\t\t\tlastIndex = StringLiteral.lastIndex;\n\t\t\t\t\t\tlastSignificantToken = match[0];\n\t\t\t\t\t\tpostfixIncDec = true;\n\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\ttype: \"StringLiteral\",\n\t\t\t\t\t\t\tvalue: match[0],\n\t\t\t\t\t\t\tclosed: match[2] !== void 0\n\t\t\t\t\t\t};\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tNumericLiteral.lastIndex = lastIndex;\n\t\t\t\t\tif (match = NumericLiteral.exec(input)) {\n\t\t\t\t\t\tlastIndex = NumericLiteral.lastIndex;\n\t\t\t\t\t\tlastSignificantToken = match[0];\n\t\t\t\t\t\tpostfixIncDec = true;\n\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\ttype: \"NumericLiteral\",\n\t\t\t\t\t\t\tvalue: match[0]\n\t\t\t\t\t\t};\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tTemplate.lastIndex = lastIndex;\n\t\t\t\t\tif (match = Template.exec(input)) {\n\t\t\t\t\t\tlastIndex = Template.lastIndex;\n\t\t\t\t\t\tlastSignificantToken = match[0];\n\t\t\t\t\t\tif (match[1] === \"${\") {\n\t\t\t\t\t\t\tlastSignificantToken = \"?InterpolationInTemplate\";\n\t\t\t\t\t\t\tstack.push({\n\t\t\t\t\t\t\t\ttag: \"InterpolationInTemplate\",\n\t\t\t\t\t\t\t\tnesting: braces.length\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tpostfixIncDec = false;\n\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\ttype: \"TemplateHead\",\n\t\t\t\t\t\t\t\tvalue: match[0]\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tpostfixIncDec = true;\n\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\ttype: \"NoSubstitutionTemplate\",\n\t\t\t\t\t\t\t\tvalue: match[0],\n\t\t\t\t\t\t\t\tclosed: match[1] === \"`\"\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"JSXTag\":\n\t\t\t\tcase \"JSXTagEnd\":\n\t\t\t\t\tJSXPunctuator.lastIndex = lastIndex;\n\t\t\t\t\tif (match = JSXPunctuator.exec(input)) {\n\t\t\t\t\t\tlastIndex = JSXPunctuator.lastIndex;\n\t\t\t\t\t\tnextLastSignificantToken = match[0];\n\t\t\t\t\t\tswitch (match[0]) {\n\t\t\t\t\t\t\tcase \"<\":\n\t\t\t\t\t\t\t\tstack.push({ tag: \"JSXTag\" });\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase \">\":\n\t\t\t\t\t\t\t\tstack.pop();\n\t\t\t\t\t\t\t\tif (lastSignificantToken === \"/\" || mode.tag === \"JSXTagEnd\") {\n\t\t\t\t\t\t\t\t\tnextLastSignificantToken = \"?JSX\";\n\t\t\t\t\t\t\t\t\tpostfixIncDec = true;\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tstack.push({ tag: \"JSXChildren\" });\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase \"{\":\n\t\t\t\t\t\t\t\tstack.push({\n\t\t\t\t\t\t\t\t\ttag: \"InterpolationInJSX\",\n\t\t\t\t\t\t\t\t\tnesting: braces.length\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tnextLastSignificantToken = \"?InterpolationInJSX\";\n\t\t\t\t\t\t\t\tpostfixIncDec = false;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase \"/\": if (lastSignificantToken === \"<\") {\n\t\t\t\t\t\t\t\tstack.pop();\n\t\t\t\t\t\t\t\tif (stack[stack.length - 1].tag === \"JSXChildren\") {\n\t\t\t\t\t\t\t\t\tstack.pop();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tstack.push({ tag: \"JSXTagEnd\" });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlastSignificantToken = nextLastSignificantToken;\n\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\ttype: \"JSXPunctuator\",\n\t\t\t\t\t\t\tvalue: match[0]\n\t\t\t\t\t\t};\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tJSXIdentifier.lastIndex = lastIndex;\n\t\t\t\t\tif (match = JSXIdentifier.exec(input)) {\n\t\t\t\t\t\tlastIndex = JSXIdentifier.lastIndex;\n\t\t\t\t\t\tlastSignificantToken = match[0];\n\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\ttype: \"JSXIdentifier\",\n\t\t\t\t\t\t\tvalue: match[0]\n\t\t\t\t\t\t};\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tJSXString.lastIndex = lastIndex;\n\t\t\t\t\tif (match = JSXString.exec(input)) {\n\t\t\t\t\t\tlastIndex = JSXString.lastIndex;\n\t\t\t\t\t\tlastSignificantToken = match[0];\n\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\ttype: \"JSXString\",\n\t\t\t\t\t\t\tvalue: match[0],\n\t\t\t\t\t\t\tclosed: match[2] !== void 0\n\t\t\t\t\t\t};\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"JSXChildren\":\n\t\t\t\t\tJSXText.lastIndex = lastIndex;\n\t\t\t\t\tif (match = JSXText.exec(input)) {\n\t\t\t\t\t\tlastIndex = JSXText.lastIndex;\n\t\t\t\t\t\tlastSignificantToken = match[0];\n\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\ttype: \"JSXText\",\n\t\t\t\t\t\t\tvalue: match[0]\n\t\t\t\t\t\t};\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tswitch (input[lastIndex]) {\n\t\t\t\t\t\tcase \"<\":\n\t\t\t\t\t\t\tstack.push({ tag: \"JSXTag\" });\n\t\t\t\t\t\t\tlastIndex++;\n\t\t\t\t\t\t\tlastSignificantToken = \"<\";\n\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\ttype: \"JSXPunctuator\",\n\t\t\t\t\t\t\t\tvalue: \"<\"\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\tcase \"{\":\n\t\t\t\t\t\t\tstack.push({\n\t\t\t\t\t\t\t\ttag: \"InterpolationInJSX\",\n\t\t\t\t\t\t\t\tnesting: braces.length\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tlastIndex++;\n\t\t\t\t\t\t\tlastSignificantToken = \"?InterpolationInJSX\";\n\t\t\t\t\t\t\tpostfixIncDec = false;\n\t\t\t\t\t\t\tyield {\n\t\t\t\t\t\t\t\ttype: \"JSXPunctuator\",\n\t\t\t\t\t\t\t\tvalue: \"{\"\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t}\n\t\t\tWhiteSpace.lastIndex = lastIndex;\n\t\t\tif (match = WhiteSpace.exec(input)) {\n\t\t\t\tlastIndex = WhiteSpace.lastIndex;\n\t\t\t\tyield {\n\t\t\t\t\ttype: \"WhiteSpace\",\n\t\t\t\t\tvalue: match[0]\n\t\t\t\t};\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tLineTerminatorSequence.lastIndex = lastIndex;\n\t\t\tif (match = LineTerminatorSequence.exec(input)) {\n\t\t\t\tlastIndex = LineTerminatorSequence.lastIndex;\n\t\t\t\tpostfixIncDec = false;\n\t\t\t\tif (KeywordsWithNoLineTerminatorAfter.test(lastSignificantToken)) {\n\t\t\t\t\tlastSignificantToken = \"?NoLineTerminatorHere\";\n\t\t\t\t}\n\t\t\t\tyield {\n\t\t\t\t\ttype: \"LineTerminatorSequence\",\n\t\t\t\t\tvalue: match[0]\n\t\t\t\t};\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tMultiLineComment.lastIndex = lastIndex;\n\t\t\tif (match = MultiLineComment.exec(input)) {\n\t\t\t\tlastIndex = MultiLineComment.lastIndex;\n\t\t\t\tif (Newline.test(match[0])) {\n\t\t\t\t\tpostfixIncDec = false;\n\t\t\t\t\tif (KeywordsWithNoLineTerminatorAfter.test(lastSignificantToken)) {\n\t\t\t\t\t\tlastSignificantToken = \"?NoLineTerminatorHere\";\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tyield {\n\t\t\t\t\ttype: \"MultiLineComment\",\n\t\t\t\t\tvalue: match[0],\n\t\t\t\t\tclosed: match[1] !== void 0\n\t\t\t\t};\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tSingleLineComment.lastIndex = lastIndex;\n\t\t\tif (match = SingleLineComment.exec(input)) {\n\t\t\t\tlastIndex = SingleLineComment.lastIndex;\n\t\t\t\tpostfixIncDec = false;\n\t\t\t\tyield {\n\t\t\t\t\ttype: \"SingleLineComment\",\n\t\t\t\t\tvalue: match[0]\n\t\t\t\t};\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tfirstCodePoint = String.fromCodePoint(input.codePointAt(lastIndex));\n\t\t\tlastIndex += firstCodePoint.length;\n\t\t\tlastSignificantToken = firstCodePoint;\n\t\t\tpostfixIncDec = false;\n\t\t\tyield {\n\t\t\t\ttype: mode.tag.startsWith(\"JSX\") ? \"JSXInvalid\" : \"Invalid\",\n\t\t\t\tvalue: firstCodePoint\n\t\t\t};\n\t\t}\n\t\treturn void 0;\n\t};\n\treturn jsTokens_1;\n}\nrequireJsTokens();\n// src/index.ts\nvar reservedWords = {\n\tkeyword: [\n\t\t\"break\",\n\t\t\"case\",\n\t\t\"catch\",\n\t\t\"continue\",\n\t\t\"debugger\",\n\t\t\"default\",\n\t\t\"do\",\n\t\t\"else\",\n\t\t\"finally\",\n\t\t\"for\",\n\t\t\"function\",\n\t\t\"if\",\n\t\t\"return\",\n\t\t\"switch\",\n\t\t\"throw\",\n\t\t\"try\",\n\t\t\"var\",\n\t\t\"const\",\n\t\t\"while\",\n\t\t\"with\",\n\t\t\"new\",\n\t\t\"this\",\n\t\t\"super\",\n\t\t\"class\",\n\t\t\"extends\",\n\t\t\"export\",\n\t\t\"import\",\n\t\t\"null\",\n\t\t\"true\",\n\t\t\"false\",\n\t\t\"in\",\n\t\t\"instanceof\",\n\t\t\"typeof\",\n\t\t\"void\",\n\t\t\"delete\"\n\t],\n\tstrict: [\n\t\t\"implements\",\n\t\t\"interface\",\n\t\t\"let\",\n\t\t\"package\",\n\t\t\"private\",\n\t\t\"protected\",\n\t\t\"public\",\n\t\t\"static\",\n\t\t\"yield\"\n\t]\n}; new Set(reservedWords.keyword); new Set(reservedWords.strict);\n\nconst chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\nconst intToChar = new Uint8Array(64);\nconst charToInt = new Uint8Array(128);\nfor (let i = 0; i < chars.length; i++) {\n\tconst c = chars.charCodeAt(i);\n\tintToChar[i] = c;\n\tcharToInt[c] = i;\n}\nvar UrlType;\n(function(UrlType) {\n\tUrlType[UrlType[\"Empty\"] = 1] = \"Empty\";\n\tUrlType[UrlType[\"Hash\"] = 2] = \"Hash\";\n\tUrlType[UrlType[\"Query\"] = 3] = \"Query\";\n\tUrlType[UrlType[\"RelativePath\"] = 4] = \"RelativePath\";\n\tUrlType[UrlType[\"AbsolutePath\"] = 5] = \"AbsolutePath\";\n\tUrlType[UrlType[\"SchemeRelative\"] = 6] = \"SchemeRelative\";\n\tUrlType[UrlType[\"Absolute\"] = 7] = \"Absolute\";\n})(UrlType || (UrlType = {}));\nconst _DRIVE_LETTER_START_RE = /^[A-Za-z]:\\//;\nfunction normalizeWindowsPath(input = \"\") {\n\tif (!input) {\n\t\treturn input;\n\t}\n\treturn input.replace(/\\\\/g, \"/\").replace(_DRIVE_LETTER_START_RE, (r) => r.toUpperCase());\n}\nconst _IS_ABSOLUTE_RE = /^[/\\\\](?![/\\\\])|^[/\\\\]{2}(?!\\.)|^[A-Za-z]:[/\\\\]/;\nfunction cwd() {\n\tif (typeof process !== \"undefined\" && typeof process.cwd === \"function\") {\n\t\treturn process.cwd().replace(/\\\\/g, \"/\");\n\t}\n\treturn \"/\";\n}\nconst resolve = function(...arguments_) {\n\targuments_ = arguments_.map((argument) => normalizeWindowsPath(argument));\n\tlet resolvedPath = \"\";\n\tlet resolvedAbsolute = false;\n\tfor (let index = arguments_.length - 1; index >= -1 && !resolvedAbsolute; index--) {\n\t\tconst path = index >= 0 ? arguments_[index] : cwd();\n\t\tif (!path || path.length === 0) {\n\t\t\tcontinue;\n\t\t}\n\t\tresolvedPath = `${path}/${resolvedPath}`;\n\t\tresolvedAbsolute = isAbsolute(path);\n\t}\n\tresolvedPath = normalizeString(resolvedPath, !resolvedAbsolute);\n\tif (resolvedAbsolute && !isAbsolute(resolvedPath)) {\n\t\treturn `/${resolvedPath}`;\n\t}\n\treturn resolvedPath.length > 0 ? resolvedPath : \".\";\n};\nfunction normalizeString(path, allowAboveRoot) {\n\tlet res = \"\";\n\tlet lastSegmentLength = 0;\n\tlet lastSlash = -1;\n\tlet dots = 0;\n\tlet char = null;\n\tfor (let index = 0; index <= path.length; ++index) {\n\t\tif (index < path.length) {\n\t\t\tchar = path[index];\n\t\t} else if (char === \"/\") {\n\t\t\tbreak;\n\t\t} else {\n\t\t\tchar = \"/\";\n\t\t}\n\t\tif (char === \"/\") {\n\t\t\tif (lastSlash === index - 1 || dots === 1);\n\t\t\telse if (dots === 2) {\n\t\t\t\tif (res.length < 2 || lastSegmentLength !== 2 || res[res.length - 1] !== \".\" || res[res.length - 2] !== \".\") {\n\t\t\t\t\tif (res.length > 2) {\n\t\t\t\t\t\tconst lastSlashIndex = res.lastIndexOf(\"/\");\n\t\t\t\t\t\tif (lastSlashIndex === -1) {\n\t\t\t\t\t\t\tres = \"\";\n\t\t\t\t\t\t\tlastSegmentLength = 0;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tres = res.slice(0, lastSlashIndex);\n\t\t\t\t\t\t\tlastSegmentLength = res.length - 1 - res.lastIndexOf(\"/\");\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlastSlash = index;\n\t\t\t\t\t\tdots = 0;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (res.length > 0) {\n\t\t\t\t\t\tres = \"\";\n\t\t\t\t\t\tlastSegmentLength = 0;\n\t\t\t\t\t\tlastSlash = index;\n\t\t\t\t\t\tdots = 0;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (allowAboveRoot) {\n\t\t\t\t\tres += res.length > 0 ? \"/..\" : \"..\";\n\t\t\t\t\tlastSegmentLength = 2;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (res.length > 0) {\n\t\t\t\t\tres += `/${path.slice(lastSlash + 1, index)}`;\n\t\t\t\t} else {\n\t\t\t\t\tres = path.slice(lastSlash + 1, index);\n\t\t\t\t}\n\t\t\t\tlastSegmentLength = index - lastSlash - 1;\n\t\t\t}\n\t\t\tlastSlash = index;\n\t\t\tdots = 0;\n\t\t} else if (char === \".\" && dots !== -1) {\n\t\t\t++dots;\n\t\t} else {\n\t\t\tdots = -1;\n\t\t}\n\t}\n\treturn res;\n}\nconst isAbsolute = function(p) {\n\treturn _IS_ABSOLUTE_RE.test(p);\n};\nconst CHROME_IE_STACK_REGEXP = /^\\s*at .*(?:\\S:\\d+|\\(native\\))/m;\nconst SAFARI_NATIVE_CODE_REGEXP = /^(?:eval@)?(?:\\[native code\\])?$/;\nfunction extractLocation(urlLike) {\n\t// Fail-fast but return locations like \"(native)\"\n\tif (!urlLike.includes(\":\")) {\n\t\treturn [urlLike];\n\t}\n\tconst regExp = /(.+?)(?::(\\d+))?(?::(\\d+))?$/;\n\tconst parts = regExp.exec(urlLike.replace(/^\\(|\\)$/g, \"\"));\n\tif (!parts) {\n\t\treturn [urlLike];\n\t}\n\tlet url = parts[1];\n\tif (url.startsWith(\"async \")) {\n\t\turl = url.slice(6);\n\t}\n\tif (url.startsWith(\"http:\") || url.startsWith(\"https:\")) {\n\t\tconst urlObj = new URL(url);\n\t\turlObj.searchParams.delete(\"import\");\n\t\turlObj.searchParams.delete(\"browserv\");\n\t\turl = urlObj.pathname + urlObj.hash + urlObj.search;\n\t}\n\tif (url.startsWith(\"/@fs/\")) {\n\t\tconst isWindows = /^\\/@fs\\/[a-zA-Z]:\\//.test(url);\n\t\turl = url.slice(isWindows ? 5 : 4);\n\t}\n\treturn [\n\t\turl,\n\t\tparts[2] || undefined,\n\t\tparts[3] || undefined\n\t];\n}\nfunction parseSingleFFOrSafariStack(raw) {\n\tlet line = raw.trim();\n\tif (SAFARI_NATIVE_CODE_REGEXP.test(line)) {\n\t\treturn null;\n\t}\n\tif (line.includes(\" > eval\")) {\n\t\tline = line.replace(/ line (\\d+)(?: > eval line \\d+)* > eval:\\d+:\\d+/g, \":$1\");\n\t}\n\tif (!line.includes(\"@\") && !line.includes(\":\")) {\n\t\treturn null;\n\t}\n\t// eslint-disable-next-line regexp/no-super-linear-backtracking, regexp/optimal-quantifier-concatenation\n\tconst functionNameRegex = /((.*\".+\"[^@]*)?[^@]*)(@)/;\n\tconst matches = line.match(functionNameRegex);\n\tconst functionName = matches && matches[1] ? matches[1] : undefined;\n\tconst [url, lineNumber, columnNumber] = extractLocation(line.replace(functionNameRegex, \"\"));\n\tif (!url || !lineNumber || !columnNumber) {\n\t\treturn null;\n\t}\n\treturn {\n\t\tfile: url,\n\t\tmethod: functionName || \"\",\n\t\tline: Number.parseInt(lineNumber),\n\t\tcolumn: Number.parseInt(columnNumber)\n\t};\n}\nfunction parseSingleStack(raw) {\n\tconst line = raw.trim();\n\tif (!CHROME_IE_STACK_REGEXP.test(line)) {\n\t\treturn parseSingleFFOrSafariStack(line);\n\t}\n\treturn parseSingleV8Stack(line);\n}\n// Based on https://github.com/stacktracejs/error-stack-parser\n// Credit to stacktracejs\nfunction parseSingleV8Stack(raw) {\n\tlet line = raw.trim();\n\tif (!CHROME_IE_STACK_REGEXP.test(line)) {\n\t\treturn null;\n\t}\n\tif (line.includes(\"(eval \")) {\n\t\tline = line.replace(/eval code/g, \"eval\").replace(/(\\(eval at [^()]*)|(,.*$)/g, \"\");\n\t}\n\tlet sanitizedLine = line.replace(/^\\s+/, \"\").replace(/\\(eval code/g, \"(\").replace(/^.*?\\s+/, \"\");\n\t// capture and preserve the parenthesized location \"(/foo/my bar.js:12:87)\" in\n\t// case it has spaces in it, as the string is split on \\s+ later on\n\tconst location = sanitizedLine.match(/ (\\(.+\\)$)/);\n\t// remove the parenthesized location from the line, if it was matched\n\tsanitizedLine = location ? sanitizedLine.replace(location[0], \"\") : sanitizedLine;\n\t// if a location was matched, pass it to extractLocation() otherwise pass all sanitizedLine\n\t// because this line doesn't have function name\n\tconst [url, lineNumber, columnNumber] = extractLocation(location ? location[1] : sanitizedLine);\n\tlet method = location && sanitizedLine || \"\";\n\tlet file = url && [\"eval\", \"<anonymous>\"].includes(url) ? undefined : url;\n\tif (!file || !lineNumber || !columnNumber) {\n\t\treturn null;\n\t}\n\tif (method.startsWith(\"async \")) {\n\t\tmethod = method.slice(6);\n\t}\n\tif (file.startsWith(\"file://\")) {\n\t\tfile = file.slice(7);\n\t}\n\t// normalize Windows path (\\ -> /)\n\tfile = file.startsWith(\"node:\") || file.startsWith(\"internal:\") ? file : resolve(file);\n\tif (method) {\n\t\tmethod = method.replace(/__vite_ssr_import_\\d+__\\./g, \"\");\n\t}\n\treturn {\n\t\tmethod,\n\t\tfile,\n\t\tline: Number.parseInt(lineNumber),\n\t\tcolumn: Number.parseInt(columnNumber)\n\t};\n}\n\nfunction createCompilerHints(options) {\n\tconst globalThisAccessor = (options === null || options === void 0 ? void 0 : options.globalThisKey) || \"__vitest_mocker__\";\n\tfunction _mocker() {\n\t\t// @ts-expect-error injected by the plugin\n\t\treturn typeof globalThis[globalThisAccessor] !== \"undefined\" ? globalThis[globalThisAccessor] : new Proxy({}, { get(_, name) {\n\t\t\tthrow new Error(\"Vitest mocker was not initialized in this environment. \" + `vi.${String(name)}() is forbidden.`);\n\t\t} });\n\t}\n\treturn {\n\t\thoisted(factory) {\n\t\t\tif (typeof factory !== \"function\") {\n\t\t\t\tthrow new TypeError(`vi.hoisted() expects a function, but received a ${typeof factory}`);\n\t\t\t}\n\t\t\treturn factory();\n\t\t},\n\t\tmock(path, factory) {\n\t\t\tif (typeof path !== \"string\") {\n\t\t\t\tthrow new TypeError(`vi.mock() expects a string path, but received a ${typeof path}`);\n\t\t\t}\n\t\t\tconst importer = getImporter(\"mock\");\n\t\t\t_mocker().queueMock(path, importer, typeof factory === \"function\" ? () => factory(() => _mocker().importActual(path, importer)) : factory);\n\t\t},\n\t\tunmock(path) {\n\t\t\tif (typeof path !== \"string\") {\n\t\t\t\tthrow new TypeError(`vi.unmock() expects a string path, but received a ${typeof path}`);\n\t\t\t}\n\t\t\t_mocker().queueUnmock(path, getImporter(\"unmock\"));\n\t\t},\n\t\tdoMock(path, factory) {\n\t\t\tif (typeof path !== \"string\") {\n\t\t\t\tthrow new TypeError(`vi.doMock() expects a string path, but received a ${typeof path}`);\n\t\t\t}\n\t\t\tconst importer = getImporter(\"doMock\");\n\t\t\t_mocker().queueMock(path, importer, typeof factory === \"function\" ? () => factory(() => _mocker().importActual(path, importer)) : factory);\n\t\t},\n\t\tdoUnmock(path) {\n\t\t\tif (typeof path !== \"string\") {\n\t\t\t\tthrow new TypeError(`vi.doUnmock() expects a string path, but received a ${typeof path}`);\n\t\t\t}\n\t\t\t_mocker().queueUnmock(path, getImporter(\"doUnmock\"));\n\t\t},\n\t\tasync importActual(path) {\n\t\t\treturn _mocker().importActual(path, getImporter(\"importActual\"));\n\t\t},\n\t\tasync importMock(path) {\n\t\t\treturn _mocker().importMock(path, getImporter(\"importMock\"));\n\t\t}\n\t};\n}\nfunction getImporter(name) {\n\tconst stackTrace = /* @__PURE__ */ createSimpleStackTrace({ stackTraceLimit: 5 });\n\tconst stackArray = stackTrace.split(\"\\n\");\n\t// if there is no message in a stack trace, use the item - 1\n\tconst importerStackIndex = stackArray.findIndex((stack) => {\n\t\treturn stack.includes(` at Object.${name}`) || stack.includes(`${name}@`);\n\t});\n\tconst stack = /* @__PURE__ */ parseSingleStack(stackArray[importerStackIndex + 1]);\n\treturn (stack === null || stack === void 0 ? void 0 : stack.file) || \"\";\n}\n\nconst hot = import.meta.hot || {\n\ton: warn,\n\toff: warn,\n\tsend: warn\n};\nfunction warn() {\n\tconsole.warn(\"Vitest mocker cannot work if Vite didn't establish WS connection.\");\n}\nfunction rpc(event, data) {\n\thot.send(event, data);\n\treturn new Promise((resolve, reject) => {\n\t\tconst timeout = setTimeout(() => {\n\t\t\treject(new Error(`Failed to resolve ${event} in time`));\n\t\t}, 5e3);\n\t\thot.on(`${event}:result`, function r(data) {\n\t\t\tresolve(data);\n\t\t\tclearTimeout(timeout);\n\t\t\thot.off(`${event}:result`, r);\n\t\t});\n\t});\n}\n\nconst { now } = Date;\nclass ModuleMocker {\n\tregistry = new MockerRegistry();\n\tqueue = new Set();\n\tmockedIds = new Set();\n\tconstructor(interceptor, rpc, spyOn, config) {\n\t\tthis.interceptor = interceptor;\n\t\tthis.rpc = rpc;\n\t\tthis.spyOn = spyOn;\n\t\tthis.config = config;\n\t}\n\tasync prepare() {\n\t\tif (!this.queue.size) {\n\t\t\treturn;\n\t\t}\n\t\tawait Promise.all([...this.queue.values()]);\n\t}\n\tasync resolveFactoryModule(id) {\n\t\tconst mock = this.registry.get(id);\n\t\tif (!mock || mock.type !== \"manual\") {\n\t\t\tthrow new Error(`Mock ${id} wasn't registered. This is probably a Vitest error. Please, open a new issue with reproduction.`);\n\t\t}\n\t\tconst result = await mock.resolve();\n\t\treturn result;\n\t}\n\tgetFactoryModule(id) {\n\t\tconst mock = this.registry.get(id);\n\t\tif (!mock || mock.type !== \"manual\") {\n\t\t\tthrow new Error(`Mock ${id} wasn't registered. This is probably a Vitest error. Please, open a new issue with reproduction.`);\n\t\t}\n\t\tif (!mock.cache) {\n\t\t\tthrow new Error(`Mock ${id} wasn't resolved. This is probably a Vitest error. Please, open a new issue with reproduction.`);\n\t\t}\n\t\treturn mock.cache;\n\t}\n\tasync invalidate() {\n\t\tconst ids = Array.from(this.mockedIds);\n\t\tif (!ids.length) {\n\t\t\treturn;\n\t\t}\n\t\tawait this.rpc.invalidate(ids);\n\t\tawait this.interceptor.invalidate();\n\t\tthis.registry.clear();\n\t}\n\tasync importActual(id, importer) {\n\t\tconst resolved = await this.rpc.resolveId(id, importer);\n\t\tif (resolved == null) {\n\t\t\tthrow new Error(`[vitest] Cannot resolve \"${id}\" imported from \"${importer}\"`);\n\t\t}\n\t\tconst ext = extname(resolved.id);\n\t\tconst url = new URL(resolved.url, location.href);\n\t\tconst query = `_vitest_original&ext${ext}`;\n\t\tconst actualUrl = `${url.pathname}${url.search ? `${url.search}&${query}` : `?${query}`}${url.hash}`;\n\t\treturn this.wrapDynamicImport(() => import(\n\t\t\t/* @vite-ignore */\n\t\t\tactualUrl\n)).then((mod) => {\n\t\t\tif (!resolved.optimized || typeof mod.default === \"undefined\") {\n\t\t\t\treturn mod;\n\t\t\t}\n\t\t\t// vite injects this helper for optimized modules, so we try to follow the same behavior\n\t\t\tconst m = mod.default;\n\t\t\treturn (m === null || m === void 0 ? void 0 : m.__esModule) ? m : {\n\t\t\t\t...typeof m === \"object\" && !Array.isArray(m) || typeof m === \"function\" ? m : {},\n\t\t\t\tdefault: m\n\t\t\t};\n\t\t});\n\t}\n\tasync importMock(rawId, importer) {\n\t\tawait this.prepare();\n\t\tconst { resolvedId, resolvedUrl, redirectUrl } = await this.rpc.resolveMock(rawId, importer, { mock: \"auto\" });\n\t\tconst mockUrl = this.resolveMockPath(cleanVersion(resolvedUrl));\n\t\tlet mock = this.registry.get(mockUrl);\n\t\tif (!mock) {\n\t\t\tif (redirectUrl) {\n\t\t\t\tconst resolvedRedirect = new URL(this.resolveMockPath(cleanVersion(redirectUrl)), location.href).toString();\n\t\t\t\tmock = new RedirectedModule(rawId, resolvedId, mockUrl, resolvedRedirect);\n\t\t\t} else {\n\t\t\t\tmock = new AutomockedModule(rawId, resolvedId, mockUrl);\n\t\t\t}\n\t\t}\n\t\tif (mock.type === \"manual\") {\n\t\t\treturn await mock.resolve();\n\t\t}\n\t\tif (mock.type === \"automock\" || mock.type === \"autospy\") {\n\t\t\tconst url = new URL(`/@id/${resolvedId}`, location.href);\n\t\t\tconst query = url.search ? `${url.search}&t=${now()}` : `?t=${now()}`;\n\t\t\tconst moduleObject = await import(\n\t\t\t\t/* @vite-ignore */\n\t\t\t\t`${url.pathname}${query}&mock=${mock.type}${url.hash}`\n);\n\t\t\treturn this.mockObject(moduleObject, mock.type);\n\t\t}\n\t\treturn import(\n\t\t\t/* @vite-ignore */\n\t\t\tmock.redirect\n);\n\t}\n\tmockObject(object, moduleType = \"automock\") {\n\t\treturn mockObject({\n\t\t\tglobalConstructors: {\n\t\t\t\tObject,\n\t\t\t\tFunction,\n\t\t\t\tArray,\n\t\t\t\tMap,\n\t\t\t\tRegExp\n\t\t\t},\n\t\t\tspyOn: this.spyOn,\n\t\t\ttype: moduleType\n\t\t}, object);\n\t}\n\tqueueMock(rawId, importer, factoryOrOptions) {\n\t\tconst promise = this.rpc.resolveMock(rawId, importer, { mock: typeof factoryOrOptions === \"function\" ? \"factory\" : (factoryOrOptions === null || factoryOrOptions === void 0 ? void 0 : factoryOrOptions.spy) ? \"spy\" : \"auto\" }).then(async ({ redirectUrl, resolvedId, resolvedUrl, needsInterop, mockType }) => {\n\t\t\tconst mockUrl = this.resolveMockPath(cleanVersion(resolvedUrl));\n\t\t\tthis.mockedIds.add(resolvedId);\n\t\t\tconst factory = typeof factoryOrOptions === \"function\" ? async () => {\n\t\t\t\tconst data = await factoryOrOptions();\n\t\t\t\t// vite wraps all external modules that have \"needsInterop\" in a function that\n\t\t\t\t// merges all exports from default into the module object\n\t\t\t\treturn needsInterop ? { default: data } : data;\n\t\t\t} : undefined;\n\t\t\tconst mockRedirect = typeof redirectUrl === \"string\" ? new URL(this.resolveMockPath(cleanVersion(redirectUrl)), location.href).toString() : null;\n\t\t\tlet module;\n\t\t\tif (mockType === \"manual\") {\n\t\t\t\tmodule = this.registry.register(\"manual\", rawId, resolvedId, mockUrl, factory);\n\t\t\t} else if (mockType === \"autospy\") {\n\t\t\t\tmodule = this.registry.register(\"autospy\", rawId, resolvedId, mockUrl);\n\t\t\t} else if (mockType === \"redirect\") {\n\t\t\t\tmodule = this.registry.register(\"redirect\", rawId, resolvedId, mockUrl, mockRedirect);\n\t\t\t} else {\n\t\t\t\tmodule = this.registry.register(\"automock\", rawId, resolvedId, mockUrl);\n\t\t\t}\n\t\t\tawait this.interceptor.register(module);\n\t\t}).finally(() => {\n\t\t\tthis.queue.delete(promise);\n\t\t});\n\t\tthis.queue.add(promise);\n\t}\n\tqueueUnmock(id, importer) {\n\t\tconst promise = this.rpc.resolveId(id, importer).then(async (resolved) => {\n\t\t\tif (!resolved) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst mockUrl = this.resolveMockPath(cleanVersion(resolved.url));\n\t\t\tthis.mockedIds.add(resolved.id);\n\t\t\tthis.registry.delete(mockUrl);\n\t\t\tawait this.interceptor.delete(mockUrl);\n\t\t}).finally(() => {\n\t\t\tthis.queue.delete(promise);\n\t\t});\n\t\tthis.queue.add(promise);\n\t}\n\t// We need to await mock registration before importing the actual module\n\t// In case there is a mocked module in the import chain\n\twrapDynamicImport(moduleFactory) {\n\t\tif (typeof moduleFactory === \"function\") {\n\t\t\tconst promise = new Promise((resolve, reject) => {\n\t\t\t\tthis.prepare().finally(() => {\n\t\t\t\t\tmoduleFactory().then(resolve, reject);\n\t\t\t\t});\n\t\t\t});\n\t\t\treturn promise;\n\t\t}\n\t\treturn moduleFactory;\n\t}\n\tresolveMockPath(path) {\n\t\tconst config = this.config;\n\t\tconst fsRoot = join(\"/@fs/\", config.root);\n\t\t// URL can be /file/path.js, but path is resolved to /file/path\n\t\tif (path.startsWith(config.root)) {\n\t\t\treturn path.slice(config.root.length);\n\t\t}\n\t\tif (path.startsWith(fsRoot)) {\n\t\t\treturn path.slice(fsRoot.length);\n\t\t}\n\t\treturn path;\n\t}\n}\nconst versionRegexp = /(\\?|&)v=\\w{8}/;\nfunction cleanVersion(url) {\n\treturn url.replace(versionRegexp, \"\");\n}\n\nexport { ModuleMocker as M, createCompilerHints as c, hot as h, rpc as r };\n", "const postfixRE = /[?#].*$/;\nfunction cleanUrl(url) {\n\treturn url.replace(postfixRE, \"\");\n}\nfunction createManualModuleSource(moduleUrl, exports, globalAccessor = \"\\\"__vitest_mocker__\\\"\") {\n\tconst source = `const module = globalThis[${globalAccessor}].getFactoryModule(\"${moduleUrl}\");`;\n\tconst keys = exports.map((name) => {\n\t\tif (name === \"default\") {\n\t\t\treturn `export default module[\"default\"];`;\n\t\t}\n\t\treturn `export const ${name} = module[\"${name}\"];`;\n\t}).join(\"\\n\");\n\treturn `${source}\\n${keys}`;\n}\n\nexport { cleanUrl as a, createManualModuleSource as c };\n", "import { r as rpc } from './chunk-mocker.js';\n\nclass ModuleMockerServerInterceptor {\n\tasync register(module) {\n\t\tawait rpc(\"vitest:interceptor:register\", module.toJSON());\n\t}\n\tasync delete(id) {\n\t\tawait rpc(\"vitest:interceptor:delete\", id);\n\t}\n\tasync invalidate() {\n\t\tawait rpc(\"vitest:interceptor:invalidate\");\n\t}\n}\n\nexport { ModuleMockerServerInterceptor as M };\n", "export { M as ModuleMocker, c as createCompilerHints } from './chunk-mocker.js';\nimport { M as MockerRegistry } from './chunk-registry.js';\nimport { c as createManualModuleSource, a as cleanUrl } from './chunk-utils.js';\nexport { M as ModuleMockerServerInterceptor } from './chunk-interceptor-native.js';\nimport './index.js';\nimport './chunk-pathe.M-eThtNZ.js';\n\nclass ModuleMockerMSWInterceptor {\n\tmocks = new MockerRegistry();\n\tstartPromise;\n\tworker;\n\tconstructor(options = {}) {\n\t\tthis.options = options;\n\t\tif (!options.globalThisAccessor) {\n\t\t\toptions.globalThisAccessor = \"\\\"__vitest_mocker__\\\"\";\n\t\t}\n\t}\n\tasync register(module) {\n\t\tawait this.init();\n\t\tthis.mocks.add(module);\n\t}\n\tasync delete(url) {\n\t\tawait this.init();\n\t\tthis.mocks.delete(url);\n\t}\n\tasync invalidate() {\n\t\tthis.mocks.clear();\n\t}\n\tasync resolveManualMock(mock) {\n\t\tconst exports = Object.keys(await mock.resolve());\n\t\tconst text = createManualModuleSource(mock.url, exports, this.options.globalThisAccessor);\n\t\treturn new Response(text, { headers: { \"Content-Type\": \"application/javascript\" } });\n\t}\n\tasync init() {\n\t\tif (this.worker) {\n\t\t\treturn this.worker;\n\t\t}\n\t\tif (this.startPromise) {\n\t\t\treturn this.startPromise;\n\t\t}\n\t\tconst worker = this.options.mswWorker;\n\t\tthis.startPromise = Promise.all([worker ? { setupWorker(handler) {\n\t\t\tworker.use(handler);\n\t\t\treturn worker;\n\t\t} } : import('msw/browser'), import('msw/core/http')]).then(([{ setupWorker }, { http }]) => {\n\t\t\tconst worker = setupWorker(http.get(/.+/, async ({ request }) => {\n\t\t\t\tconst path = cleanQuery(request.url.slice(location.origin.length));\n\t\t\t\tif (!this.mocks.has(path)) {\n\t\t\t\t\treturn passthrough();\n\t\t\t\t}\n\t\t\t\tconst mock = this.mocks.get(path);\n\t\t\t\tswitch (mock.type) {\n\t\t\t\t\tcase \"manual\": return this.resolveManualMock(mock);\n\t\t\t\t\tcase \"automock\":\n\t\t\t\t\tcase \"autospy\": return Response.redirect(injectQuery(path, `mock=${mock.type}`));\n\t\t\t\t\tcase \"redirect\": return Response.redirect(mock.redirect);\n\t\t\t\t\tdefault: throw new Error(`Unknown mock type: ${mock.type}`);\n\t\t\t\t}\n\t\t\t}));\n\t\t\treturn worker.start(this.options.mswOptions).then(() => worker);\n\t\t}).finally(() => {\n\t\t\tthis.worker = worker;\n\t\t\tthis.startPromise = undefined;\n\t\t});\n\t\treturn await this.startPromise;\n\t}\n}\nconst trailingSeparatorRE = /[?&]$/;\nconst timestampRE = /\\bt=\\d{13}&?\\b/;\nconst versionRE = /\\bv=\\w{8}&?\\b/;\nfunction cleanQuery(url) {\n\treturn url.replace(timestampRE, \"\").replace(versionRE, \"\").replace(trailingSeparatorRE, \"\");\n}\nfunction passthrough() {\n\treturn new Response(null, {\n\t\tstatus: 302,\n\t\tstatusText: \"Passthrough\",\n\t\theaders: { \"x-msw-intention\": \"passthrough\" }\n\t});\n}\nconst replacePercentageRE = /%/g;\nfunction injectQuery(url, queryToInject) {\n\t// encode percents for consistent behavior with pathToFileURL\n\t// see #2614 for details\n\tconst resolvedUrl = new URL(url.replace(replacePercentageRE, \"%25\"), location.href);\n\tconst { search, hash } = resolvedUrl;\n\tconst pathname = cleanUrl(url);\n\treturn `${pathname}?${queryToInject}${search ? `&${search.slice(1)}` : \"\"}${hash ?? \"\"}`;\n}\n\nexport { ModuleMockerMSWInterceptor };\n"], "mappings": ";;;;;;;;;AAAA,IAAM,yBAAyB;AAC/B,SAAS,qBAAqB,QAAQ,IAAI;AACxC,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,SAAO,MAAM,QAAQ,OAAO,GAAG,EAAE,QAAQ,wBAAwB,CAAC,MAAM,EAAE,YAAY,CAAC;AACzF;AAEA,IAAM,aAAa;AACnB,IAAM,kBAAkB;AACxB,IAAM,mBAAmB;AACzB,IAAM,cAAc;AACpB,IAAM,YAAY,SAAS,MAAM;AAC/B,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AACA,SAAO,qBAAqB,IAAI;AAChC,QAAM,YAAY,KAAK,MAAM,UAAU;AACvC,QAAM,iBAAiB,WAAW,IAAI;AACtC,QAAM,oBAAoB,KAAK,KAAK,SAAS,CAAC,MAAM;AACpD,SAAO,gBAAgB,MAAM,CAAC,cAAc;AAC5C,MAAI,KAAK,WAAW,GAAG;AACrB,QAAI,gBAAgB;AAClB,aAAO;AAAA,IACT;AACA,WAAO,oBAAoB,OAAO;AAAA,EACpC;AACA,MAAI,mBAAmB;AACrB,YAAQ;AAAA,EACV;AACA,MAAI,iBAAiB,KAAK,IAAI,GAAG;AAC/B,YAAQ;AAAA,EACV;AACA,MAAI,WAAW;AACb,QAAI,CAAC,gBAAgB;AACnB,aAAO,OAAO,IAAI;AAAA,IACpB;AACA,WAAO,KAAK,IAAI;AAAA,EAClB;AACA,SAAO,kBAAkB,CAAC,WAAW,IAAI,IAAI,IAAI,IAAI,KAAK;AAC5D;AACA,IAAM,OAAO,YAAY,UAAU;AACjC,MAAI,OAAO;AACX,aAAW,OAAO,UAAU;AAC1B,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,QAAI,KAAK,SAAS,GAAG;AACnB,YAAM,eAAe,KAAK,KAAK,SAAS,CAAC,MAAM;AAC/C,YAAM,aAAa,IAAI,CAAC,MAAM;AAC9B,YAAM,OAAO,gBAAgB;AAC7B,UAAI,MAAM;AACR,gBAAQ,IAAI,MAAM,CAAC;AAAA,MACrB,OAAO;AACL,gBAAQ,gBAAgB,aAAa,MAAM,IAAI,GAAG;AAAA,MACpD;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF;AACA,SAAO,UAAU,IAAI;AACvB;AAyBA,SAAS,gBAAgB,MAAM,gBAAgB;AAC7C,MAAI,MAAM;AACV,MAAI,oBAAoB;AACxB,MAAI,YAAY;AAChB,MAAI,OAAO;AACX,MAAI,OAAO;AACX,WAASA,SAAQ,GAAGA,UAAS,KAAK,QAAQ,EAAEA,QAAO;AACjD,QAAIA,SAAQ,KAAK,QAAQ;AACvB,aAAO,KAAKA,MAAK;AAAA,IACnB,WAAW,SAAS,KAAK;AACvB;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AACA,QAAI,SAAS,KAAK;AAChB,UAAI,cAAcA,SAAQ,KAAK,SAAS,EAAG;AAAA,eAAW,SAAS,GAAG;AAChE,YAAI,IAAI,SAAS,KAAK,sBAAsB,KAAK,IAAI,IAAI,SAAS,CAAC,MAAM,OAAO,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK;AAC3G,cAAI,IAAI,SAAS,GAAG;AAClB,kBAAM,iBAAiB,IAAI,YAAY,GAAG;AAC1C,gBAAI,mBAAmB,IAAI;AACzB,oBAAM;AACN,kCAAoB;AAAA,YACtB,OAAO;AACL,oBAAM,IAAI,MAAM,GAAG,cAAc;AACjC,kCAAoB,IAAI,SAAS,IAAI,IAAI,YAAY,GAAG;AAAA,YAC1D;AACA,wBAAYA;AACZ,mBAAO;AACP;AAAA,UACF,WAAW,IAAI,SAAS,GAAG;AACzB,kBAAM;AACN,gCAAoB;AACpB,wBAAYA;AACZ,mBAAO;AACP;AAAA,UACF;AAAA,QACF;AACA,YAAI,gBAAgB;AAClB,iBAAO,IAAI,SAAS,IAAI,QAAQ;AAChC,8BAAoB;AAAA,QACtB;AAAA,MACF,OAAO;AACL,YAAI,IAAI,SAAS,GAAG;AAClB,iBAAO,IAAI,KAAK,MAAM,YAAY,GAAGA,MAAK,CAAC;AAAA,QAC7C,OAAO;AACL,gBAAM,KAAK,MAAM,YAAY,GAAGA,MAAK;AAAA,QACvC;AACA,4BAAoBA,SAAQ,YAAY;AAAA,MAC1C;AACA,kBAAYA;AACZ,aAAO;AAAA,IACT,WAAW,SAAS,OAAO,SAAS,IAAI;AACtC,QAAE;AAAA,IACJ,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,aAAa,SAASC,IAAG;AAC7B,SAAO,gBAAgB,KAAKA,EAAC;AAC/B;AACA,IAAM,UAAU,SAASA,IAAG;AAC1B,MAAIA,OAAM,KAAM,QAAO;AACvB,QAAM,QAAQ,YAAY,KAAK,qBAAqBA,EAAC,CAAC;AACtD,SAAO,SAAS,MAAM,CAAC,KAAK;AAC9B;;;ACnJA,IAAI,IAAI;AAAA,EACN,OAAO,CAAC,GAAG,CAAC;AAAA,EACZ,MAAM,CAAC,GAAG,IAAI,iBAAiB;AAAA,EAC/B,KAAK,CAAC,GAAG,IAAI,iBAAiB;AAAA,EAC9B,QAAQ,CAAC,GAAG,EAAE;AAAA,EACd,WAAW,CAAC,GAAG,EAAE;AAAA,EACjB,SAAS,CAAC,GAAG,EAAE;AAAA,EACf,QAAQ,CAAC,GAAG,EAAE;AAAA,EACd,eAAe,CAAC,GAAG,EAAE;AAAA,EACrB,OAAO,CAAC,IAAI,EAAE;AAAA,EACd,KAAK,CAAC,IAAI,EAAE;AAAA,EACZ,OAAO,CAAC,IAAI,EAAE;AAAA,EACd,QAAQ,CAAC,IAAI,EAAE;AAAA,EACf,MAAM,CAAC,IAAI,EAAE;AAAA,EACb,SAAS,CAAC,IAAI,EAAE;AAAA,EAChB,MAAM,CAAC,IAAI,EAAE;AAAA,EACb,OAAO,CAAC,IAAI,EAAE;AAAA,EACd,MAAM,CAAC,IAAI,EAAE;AAAA,EACb,SAAS,CAAC,IAAI,EAAE;AAAA,EAChB,OAAO,CAAC,IAAI,EAAE;AAAA,EACd,SAAS,CAAC,IAAI,EAAE;AAAA,EAChB,UAAU,CAAC,IAAI,EAAE;AAAA,EACjB,QAAQ,CAAC,IAAI,EAAE;AAAA,EACf,WAAW,CAAC,IAAI,EAAE;AAAA,EAClB,QAAQ,CAAC,IAAI,EAAE;AAAA,EACf,SAAS,CAAC,IAAI,EAAE;AAAA,EAChB,aAAa,CAAC,IAAI,EAAE;AAAA,EACpB,WAAW,CAAC,IAAI,EAAE;AAAA,EAClB,aAAa,CAAC,IAAI,EAAE;AAAA,EACpB,cAAc,CAAC,IAAI,EAAE;AAAA,EACrB,YAAY,CAAC,IAAI,EAAE;AAAA,EACnB,eAAe,CAAC,IAAI,EAAE;AAAA,EACtB,YAAY,CAAC,IAAI,EAAE;AAAA,EACnB,aAAa,CAAC,IAAI,EAAE;AAAA,EACpB,eAAe,CAAC,KAAK,EAAE;AAAA,EACvB,aAAa,CAAC,KAAK,EAAE;AAAA,EACrB,eAAe,CAAC,KAAK,EAAE;AAAA,EACvB,gBAAgB,CAAC,KAAK,EAAE;AAAA,EACxB,cAAc,CAAC,KAAK,EAAE;AAAA,EACtB,iBAAiB,CAAC,KAAK,EAAE;AAAA,EACzB,cAAc,CAAC,KAAK,EAAE;AAAA,EACtB,eAAe,CAAC,KAAK,EAAE;AACzB;AA1CA,IA0CG,IAAI,OAAO,QAAQ,CAAC;AACvB,SAAS,EAAE,GAAG;AACZ,SAAO,OAAO,CAAC;AACjB;AACA,EAAE,OAAO;AACT,EAAE,QAAQ;AACV,SAAS,EAAE,IAAI,OAAO;AACpB,MAAI,IAAI,OAAO,WAAW,cAAc,UAAU,QAAQ,KAAK,KAAK,OAAO,SAAS,EAAE,QAAQ,CAAC,GAAG,KAAK,KAAK,OAAO,SAAS,EAAE,SAAS,CAAC;AACxI,SAAO,EAAE,cAAc,KAAK,EAAE,SAAS,YAAY,OAAO,iBAAiB,KAAK,EAAE,SAAS,SAAS,MAAM,KAAK,OAAO,SAAS,EAAE,cAAc,WAAW,KAAK,EAAE,SAAS,UAAU,QAAQ,MAAM,OAAO,UAAU,eAAe,CAAC,CAAC,OAAO;AAC7O;AACA,SAAS,EAAE,IAAI,OAAO;AACpB,MAAI,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM;AAChC,QAAI,IAAI,IAAI,IAAI;AAChB;AACE,WAAK,EAAE,UAAU,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,QAAQ,IAAI,EAAE,QAAQ,GAAG,CAAC;AAAA,WAC3D,CAAC;AACR,WAAO,IAAI,EAAE,UAAU,CAAC;AAAA,EAC1B,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,MAAM;AACtB,QAAI,IAAI,CAAC,MAAM;AACb,UAAI,IAAI,OAAO,CAAC,GAAG,IAAI,EAAE,QAAQ,GAAG,EAAE,MAAM;AAC5C,aAAO,CAAC,IAAI,IAAI,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI;AAAA,IAC9C;AACA,WAAO,EAAE,OAAO,GAAG,EAAE,QAAQ,GAAG;AAAA,EAClC,GAAG,IAAI;AAAA,IACL,kBAAkB;AAAA,EACpB,GAAG,IAAI,CAAC,MAAM,QAAQ,CAAC;AACvB,WAAS,CAAC,GAAG,CAAC,KAAK;AACjB,MAAE,CAAC,IAAI,IAAI;AAAA,MACT,EAAE,EAAE,CAAC,CAAC;AAAA,MACN,EAAE,EAAE,CAAC,CAAC;AAAA,MACN,EAAE,CAAC;AAAA,IACL,IAAI;AACN,SAAO;AACT;AAEA,EAAE;AAEF,SAAS,iBAAiB,GAAG,GAAG;AAC/B,IAAE,QAAQ,SAAS,GAAG;AACrB,SAAK,OAAO,MAAM,YAAY,CAAC,MAAM,QAAQ,CAAC,KAAK,OAAO,KAAK,CAAC,EAAE,QAAQ,SAAS,GAAG;AACrF,UAAI,MAAM,aAAa,EAAE,KAAK,IAAI;AACjC,YAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,eAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,UACvC,YAAY;AAAA,UACZ,KAAK,WAAW;AACf,mBAAO,EAAE,CAAC;AAAA,UACX;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD,CAAC;AAAA,EACF,CAAC;AACD,SAAO,OAAO,OAAO,CAAC;AACvB;AACA,SAAS,wBAAwB,GAAG;AACnC,SAAO,KAAK,EAAE,cAAc,OAAO,UAAU,eAAe,KAAK,GAAG,SAAS,IAAI,EAAE,SAAS,IAAI;AACjG;AACA,IAAI,YAAY,EAAE,SAAS,CAAC,EAAE;AA+F9B,IAAI,wBAAwB,CAAC;AAU7B,IAAI;AACJ,SAAS,+BAA+B;AACvC,MAAI,iCAAkC,QAAO;AAC7C,qCAAmC;AACnC,GAAyC,WAAW;AACnD,aAAS,OAAO,QAAQ;AACvB,UAAI,aAAa,OAAO,UAAU,SAAS,QAAQ;AAClD,YAAI,WAAW,OAAO;AACtB,gBAAQ,UAAU;AAAA,UACjB,KAAK;AAAoB,oBAAQ,SAAS,OAAO,MAAM,QAAQ;AAAA,cAC9D,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAA4B,uBAAO;AAAA,cACxC;AAAS,wBAAQ,SAAS,UAAU,OAAO,UAAU,QAAQ;AAAA,kBAC5D,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAiB,2BAAO;AAAA,kBAC7B,KAAK;AAAqB,2BAAO;AAAA,kBACjC;AAAS,2BAAO;AAAA,gBACjB;AAAA,YACD;AAAA,UACA,KAAK;AAAmB,mBAAO;AAAA,QAChC;AAAA,MACD;AAAA,IACD;AACA,QAAI,qBAAqB,OAAO,IAAI,4BAA4B,GAAG,oBAAoB,OAAO,IAAI,cAAc,GAAG,sBAAsB,OAAO,IAAI,gBAAgB,GAAG,yBAAyB,OAAO,IAAI,mBAAmB,GAAG,sBAAsB,OAAO,IAAI,gBAAgB;AAClR,QAAI,sBAAsB,OAAO,IAAI,gBAAgB,GAAG,qBAAqB,OAAO,IAAI,eAAe,GAAG,yBAAyB,OAAO,IAAI,mBAAmB,GAAG,sBAAsB,OAAO,IAAI,gBAAgB,GAAG,2BAA2B,OAAO,IAAI,qBAAqB,GAAG,kBAAkB,OAAO,IAAI,YAAY,GAAG,kBAAkB,OAAO,IAAI,YAAY,GAAG,6BAA6B,OAAO,IAAI,uBAAuB,GAAG,yBAAyB,OAAO,IAAI,wBAAwB;AAC5e,0BAAsB,kBAAkB;AACxC,0BAAsB,kBAAkB;AACxC,0BAAsB,UAAU;AAChC,0BAAsB,aAAa;AACnC,0BAAsB,WAAW;AACjC,0BAAsB,OAAO;AAC7B,0BAAsB,OAAO;AAC7B,0BAAsB,SAAS;AAC/B,0BAAsB,WAAW;AACjC,0BAAsB,aAAa;AACnC,0BAAsB,WAAW;AACjC,0BAAsB,eAAe;AACrC,0BAAsB,oBAAoB,SAAS,QAAQ;AAC1D,aAAO,OAAO,MAAM,MAAM;AAAA,IAC3B;AACA,0BAAsB,oBAAoB,SAAS,QAAQ;AAC1D,aAAO,OAAO,MAAM,MAAM;AAAA,IAC3B;AACA,0BAAsB,YAAY,SAAS,QAAQ;AAClD,aAAO,aAAa,OAAO,UAAU,SAAS,UAAU,OAAO,aAAa;AAAA,IAC7E;AACA,0BAAsB,eAAe,SAAS,QAAQ;AACrD,aAAO,OAAO,MAAM,MAAM;AAAA,IAC3B;AACA,0BAAsB,aAAa,SAAS,QAAQ;AACnD,aAAO,OAAO,MAAM,MAAM;AAAA,IAC3B;AACA,0BAAsB,SAAS,SAAS,QAAQ;AAC/C,aAAO,OAAO,MAAM,MAAM;AAAA,IAC3B;AACA,0BAAsB,SAAS,SAAS,QAAQ;AAC/C,aAAO,OAAO,MAAM,MAAM;AAAA,IAC3B;AACA,0BAAsB,WAAW,SAAS,QAAQ;AACjD,aAAO,OAAO,MAAM,MAAM;AAAA,IAC3B;AACA,0BAAsB,aAAa,SAAS,QAAQ;AACnD,aAAO,OAAO,MAAM,MAAM;AAAA,IAC3B;AACA,0BAAsB,eAAe,SAAS,QAAQ;AACrD,aAAO,OAAO,MAAM,MAAM;AAAA,IAC3B;AACA,0BAAsB,aAAa,SAAS,QAAQ;AACnD,aAAO,OAAO,MAAM,MAAM;AAAA,IAC3B;AACA,0BAAsB,iBAAiB,SAAS,QAAQ;AACvD,aAAO,OAAO,MAAM,MAAM;AAAA,IAC3B;AACA,0BAAsB,qBAAqB,SAAS,MAAM;AACzD,aAAO,aAAa,OAAO,QAAQ,eAAe,OAAO,QAAQ,SAAS,uBAAuB,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,aAAa,OAAO,QAAQ,SAAS,SAAS,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,sBAAsB,KAAK,aAAa,uBAAuB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,WAAW,KAAK,eAAe,OAAO;AAAA,IACjjB;AACA,0BAAsB,SAAS;AAAA,EAChC,GAAE;AACF,SAAO;AACR;AACA,IAAI;AACJ,SAAS,mBAAmB;AAC3B,MAAI,qBAAsB,QAAO,UAAU;AAC3C,yBAAuB;AACvB,MAAI,OAAuC;AAC1C,cAAU,UAAU,0BAA0B;AAAA,EAC/C,OAAO;AACN,cAAU,UAAU,6BAA6B;AAAA,EAClD;AACA,SAAO,UAAU;AAClB;AACA,IAAI,mBAAmB,iBAAiB;AACxC,IAAI,UAA0B,wBAAwB,gBAAgB;AACtE,IAAI,YAA4B,iBAAiB;AAAA,EAChD,WAAW;AAAA,EACX,SAAS;AACV,GAAG,CAAC,gBAAgB,CAAC;AACrB,IAAI,UAAU,EAAE,SAAS,CAAC,EAAE;AAqG5B,IAAI,sBAAsB,CAAC;AAU3B,IAAI;AACJ,SAAS,6BAA6B;AACrC,MAAI,+BAAgC,QAAO;AAC3C,mCAAiC;AACjC,MAAI,MAAuC;AAC1C,KAAC,WAAW;AAKX,UAAI,qBAAqB,OAAO,IAAI,eAAe;AACnD,UAAI,oBAAoB,OAAO,IAAI,cAAc;AACjD,UAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,UAAI,yBAAyB,OAAO,IAAI,mBAAmB;AAC3D,UAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,UAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,UAAI,qBAAqB,OAAO,IAAI,eAAe;AACnD,UAAI,4BAA4B,OAAO,IAAI,sBAAsB;AACjE,UAAI,yBAAyB,OAAO,IAAI,mBAAmB;AAC3D,UAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,UAAI,2BAA2B,OAAO,IAAI,qBAAqB;AAC/D,UAAI,kBAAkB,OAAO,IAAI,YAAY;AAC7C,UAAI,kBAAkB,OAAO,IAAI,YAAY;AAC7C,UAAI,uBAAuB,OAAO,IAAI,iBAAiB;AAEvD,UAAI,iBAAiB;AACrB,UAAI,qBAAqB;AACzB,UAAI,0BAA0B;AAC9B,UAAI,qBAAqB;AAGzB,UAAI,qBAAqB;AACzB,UAAI;AACJ;AACC,iCAAyB,OAAO,IAAI,wBAAwB;AAAA,MAC7D;AACA,eAAS,mBAAmB,MAAM;AACjC,YAAI,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAC3D,iBAAO;AAAA,QACR;AACA,YAAI,SAAS,uBAAuB,SAAS,uBAAuB,sBAAsB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,sBAAsB,SAAS,wBAAwB,kBAAkB,sBAAsB,yBAAyB;AACzT,iBAAO;AAAA,QACR;AACA,YAAI,OAAO,SAAS,YAAY,SAAS,MAAM;AAC9C,cAAI,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,gBAAgB,QAAW;AACtR,mBAAO;AAAA,UACR;AAAA,QACD;AACA,eAAO;AAAA,MACR;AACA,eAAS,OAAO,QAAQ;AACvB,YAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AAClD,cAAI,WAAW,OAAO;AACtB,kBAAQ,UAAU;AAAA,YACjB,KAAK;AACJ,kBAAI,OAAO,OAAO;AAClB,sBAAQ,MAAM;AAAA,gBACb,KAAK;AAAA,gBACL,KAAK;AAAA,gBACL,KAAK;AAAA,gBACL,KAAK;AAAA,gBACL,KAAK;AAA0B,yBAAO;AAAA,gBACtC;AACC,sBAAI,eAAe,QAAQ,KAAK;AAChC,0BAAQ,cAAc;AAAA,oBACrB,KAAK;AAAA,oBACL,KAAK;AAAA,oBACL,KAAK;AAAA,oBACL,KAAK;AAAA,oBACL,KAAK;AAAA,oBACL,KAAK;AAAqB,6BAAO;AAAA,oBACjC;AAAS,6BAAO;AAAA,kBACjB;AAAA,cACF;AAAA,YACD,KAAK;AAAmB,qBAAO;AAAA,UAChC;AAAA,QACD;AACA,eAAO;AAAA,MACR;AACA,UAAI,kBAAkB;AACtB,UAAI,kBAAkB;AACtB,UAAI,UAAU;AACd,UAAI,aAAa;AACjB,UAAI,WAAW;AACf,UAAI,OAAO;AACX,UAAI,OAAO;AACX,UAAI,SAAS;AACb,UAAI,WAAW;AACf,UAAI,aAAa;AACjB,UAAI,WAAW;AACf,UAAI,eAAe;AACnB,UAAI,sCAAsC;AAC1C,UAAI,2CAA2C;AAC/C,eAAS,YAAY,QAAQ;AAC5B;AACC,cAAI,CAAC,qCAAqC;AACzC,kDAAsC;AACtC,oBAAQ,MAAM,EAAE,wFAA6F;AAAA,UAC9G;AAAA,QACD;AACA,eAAO;AAAA,MACR;AACA,eAAS,iBAAiB,QAAQ;AACjC;AACC,cAAI,CAAC,0CAA0C;AAC9C,uDAA2C;AAC3C,oBAAQ,MAAM,EAAE,6FAAkG;AAAA,UACnH;AAAA,QACD;AACA,eAAO;AAAA,MACR;AACA,eAAS,kBAAkB,QAAQ;AAClC,eAAO,OAAO,MAAM,MAAM;AAAA,MAC3B;AACA,eAAS,kBAAkB,QAAQ;AAClC,eAAO,OAAO,MAAM,MAAM;AAAA,MAC3B;AACA,eAAS,UAAU,QAAQ;AAC1B,eAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,MAC7E;AACA,eAAS,aAAa,QAAQ;AAC7B,eAAO,OAAO,MAAM,MAAM;AAAA,MAC3B;AACA,eAAS,WAAW,QAAQ;AAC3B,eAAO,OAAO,MAAM,MAAM;AAAA,MAC3B;AACA,eAAS,OAAO,QAAQ;AACvB,eAAO,OAAO,MAAM,MAAM;AAAA,MAC3B;AACA,eAAS,OAAO,QAAQ;AACvB,eAAO,OAAO,MAAM,MAAM;AAAA,MAC3B;AACA,eAAS,SAAS,QAAQ;AACzB,eAAO,OAAO,MAAM,MAAM;AAAA,MAC3B;AACA,eAAS,WAAW,QAAQ;AAC3B,eAAO,OAAO,MAAM,MAAM;AAAA,MAC3B;AACA,eAAS,aAAa,QAAQ;AAC7B,eAAO,OAAO,MAAM,MAAM;AAAA,MAC3B;AACA,eAAS,WAAW,QAAQ;AAC3B,eAAO,OAAO,MAAM,MAAM;AAAA,MAC3B;AACA,eAAS,eAAe,QAAQ;AAC/B,eAAO,OAAO,MAAM,MAAM;AAAA,MAC3B;AACA,0BAAoB,kBAAkB;AACtC,0BAAoB,kBAAkB;AACtC,0BAAoB,UAAU;AAC9B,0BAAoB,aAAa;AACjC,0BAAoB,WAAW;AAC/B,0BAAoB,OAAO;AAC3B,0BAAoB,OAAO;AAC3B,0BAAoB,SAAS;AAC7B,0BAAoB,WAAW;AAC/B,0BAAoB,aAAa;AACjC,0BAAoB,WAAW;AAC/B,0BAAoB,eAAe;AACnC,0BAAoB,cAAc;AAClC,0BAAoB,mBAAmB;AACvC,0BAAoB,oBAAoB;AACxC,0BAAoB,oBAAoB;AACxC,0BAAoB,YAAY;AAChC,0BAAoB,eAAe;AACnC,0BAAoB,aAAa;AACjC,0BAAoB,SAAS;AAC7B,0BAAoB,SAAS;AAC7B,0BAAoB,WAAW;AAC/B,0BAAoB,aAAa;AACjC,0BAAoB,eAAe;AACnC,0BAAoB,aAAa;AACjC,0BAAoB,iBAAiB;AACrC,0BAAoB,qBAAqB;AACzC,0BAAoB,SAAS;AAAA,IAC9B,GAAG;AAAA,EACJ;AACA,SAAO;AACR;AACA,IAAI;AACJ,SAAS,iBAAiB;AACzB,MAAI,mBAAoB,QAAO,QAAQ;AACvC,uBAAqB;AACrB,MAAI,OAAuC;AAC1C,YAAQ,UAAU,8BAA8B;AAAA,EACjD,OAAO;AACN,YAAQ,UAAU,2BAA2B;AAAA,EAC9C;AACA,SAAO,QAAQ;AAChB;AACA,IAAI,iBAAiB,eAAe;AACpC,IAAI,QAAwB,wBAAwB,cAAc;AAClE,IAAI,YAA4B,iBAAiB;AAAA,EAChD,WAAW;AAAA,EACX,SAAS;AACV,GAAG,CAAC,cAAc,CAAC;AACnB,IAAM,iBAAiB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AACA,OAAO,YAAY,eAAe,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,UAAU,CAAC,EAAE,CAAC,KAAK,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAE5F,IAAI,kBAAkB,MAAM;AAC5B,IAAI;AAEA,QAAM,EAAE,mBAAmB,UAAU,UAAU,IAAI,QAAQ,QAAQ,MAAM;AACzE,MAAI,MAAM,QAAQ,kBAAkB,QAAQ,QAAQ,CAAC,CAAC,GAAG;AACrD,sBAAkB,CAAC,OAAO,YAAY;AAClC,YAAM,CAAC,OAAO,UAAU,IAAI,kBAAkB,KAAK;AACnD,UAAI,UAAU,UAAU;AACpB,eAAO;AAAA,MACX;AACA,aAAO,UAAU,UAAU,YAAY,MAAM,EAAE,IAAI,QAAQ,QAAQ,YAAY,OAAO,CAAC;AAAA,IAC3F;AAAA,EACJ;AACJ,SACO,SAAS;AAEhB;AAOA,SAAS,uBAAuB,SAAS;AACxC,QAAM,EAAE,UAAU,uBAAuB,kBAAkB,EAAE,IAAI,WAAW,CAAC;AAC7E,QAAM,QAAQ,MAAM;AACpB,QAAM,oBAAoB,MAAM;AAChC,QAAM,kBAAkB;AACxB,QAAM,oBAAoB,CAAC,MAAM,EAAE;AACnC,QAAM,MAAM,IAAI,MAAM,OAAO;AAC7B,QAAM,aAAa,IAAI,SAAS;AAChC,QAAM,oBAAoB;AAC1B,QAAM,kBAAkB;AACxB,SAAO;AACR;AAEA,IAAI;AACJ,IAAI;AACJ,SAAS,kBAAkB;AAC1B,MAAI,oBAAqB,QAAO;AAChC,wBAAsB;AAGtB,MAAI,YAAY,eAAe,eAAe,WAAW,SAAS,6BAA6B,mCAAmC,wBAAwB,kBAAkB,SAAS,gBAAgB,YAAY,0BAA0B,mBAAmB,eAAe,UAAU,iCAAiC,2BAA2B;AACnV,6BAA2B;AAC3B,eAAa;AACb,eAAa;AACb,kBAAgB;AAChB,mBAAiB;AACjB,aAAW;AACX,eAAa;AACb,2BAAyB;AACzB,qBAAmB;AACnB,sBAAoB;AACpB,kBAAgB;AAChB,kBAAgB;AAChB,cAAY;AACZ,YAAU;AACV,8BAA4B;AAC5B,oCAAkC;AAClC,gCAA8B;AAC9B,sCAAoC;AACpC,YAAU,OAAO,uBAAuB,MAAM;AAC9C,eAAa,WAAW,OAAO,EAAE,MAAM,MAAM,IAAI,CAAC,GAAG;AACpD,QAAI,QAAQ,gBAAgB,cAAc,WAAW,sBAAsB,QAAQ,OAAO,MAAM,eAAe,0BAA0B,cAAc,eAAe,YAAY;AAClL,KAAC,EAAC,OAAM,IAAI;AACZ,gBAAY;AACZ,2BAAuB;AACvB,YAAQ,CAAC,EAAE,KAAK,KAAK,CAAC;AACtB,aAAS,CAAC;AACV,mBAAe;AACf,oBAAgB;AAChB,WAAO,YAAY,QAAQ;AAC1B,aAAO,MAAM,MAAM,SAAS,CAAC;AAC7B,cAAQ,KAAK,KAAK;AAAA,QACjB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACJ,cAAI,MAAM,SAAS,MAAM,QAAQ,0BAA0B,KAAK,oBAAoB,KAAK,4BAA4B,KAAK,oBAAoB,IAAI;AACjJ,qCAAyB,YAAY;AACrC,gBAAI,QAAQ,yBAAyB,KAAK,KAAK,GAAG;AACjD,0BAAY,yBAAyB;AACrC,qCAAuB,MAAM,CAAC;AAC9B,8BAAgB;AAChB,oBAAM;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO,MAAM,CAAC;AAAA,gBACd,QAAQ,MAAM,CAAC,MAAM,UAAU,MAAM,CAAC,MAAM;AAAA,cAC7C;AACA;AAAA,YACD;AAAA,UACD;AACA,qBAAW,YAAY;AACvB,cAAI,QAAQ,WAAW,KAAK,KAAK,GAAG;AACnC,yBAAa,MAAM,CAAC;AACpB,4BAAgB,WAAW;AAC3B,uCAA2B;AAC3B,oBAAQ,YAAY;AAAA,cACnB,KAAK;AACJ,oBAAI,yBAAyB,8BAA8B;AAC1D,wBAAM,KAAK;AAAA,oBACV,KAAK;AAAA,oBACL,SAAS;AAAA,kBACV,CAAC;AAAA,gBACF;AACA;AACA,gCAAgB;AAChB;AAAA,cACD,KAAK;AACJ;AACA,gCAAgB;AAChB,oBAAI,KAAK,QAAQ,0BAA0B,iBAAiB,KAAK,SAAS;AACzE,wBAAM,IAAI;AACV,6CAA2B;AAC3B,kCAAgB;AAAA,gBACjB;AACA;AAAA,cACD,KAAK;AACJ,2BAAW,YAAY;AACvB,+BAAe,CAAC,gCAAgC,KAAK,oBAAoB,MAAM,0BAA0B,KAAK,oBAAoB,KAAK,4BAA4B,KAAK,oBAAoB;AAC5L,uBAAO,KAAK,YAAY;AACxB,gCAAgB;AAChB;AAAA,cACD,KAAK;AACJ,wBAAQ,KAAK,KAAK;AAAA,kBACjB,KAAK;AACJ,wBAAI,OAAO,WAAW,KAAK,SAAS;AACnC,+BAAS,YAAY;AACrB,8BAAQ,SAAS,KAAK,KAAK;AAC3B,kCAAY,SAAS;AACrB,6CAAuB,MAAM,CAAC;AAC9B,0BAAI,MAAM,CAAC,MAAM,MAAM;AACtB,+CAAuB;AACvB,wCAAgB;AAChB,8BAAM;AAAA,0BACL,MAAM;AAAA,0BACN,OAAO,MAAM,CAAC;AAAA,wBACf;AAAA,sBACD,OAAO;AACN,8BAAM,IAAI;AACV,wCAAgB;AAChB,8BAAM;AAAA,0BACL,MAAM;AAAA,0BACN,OAAO,MAAM,CAAC;AAAA,0BACd,QAAQ,MAAM,CAAC,MAAM;AAAA,wBACtB;AAAA,sBACD;AACA;AAAA,oBACD;AACA;AAAA,kBACD,KAAK;AAAsB,wBAAI,OAAO,WAAW,KAAK,SAAS;AAC9D,4BAAM,IAAI;AACV,mCAAa;AACb,6CAAuB;AACvB,4BAAM;AAAA,wBACL,MAAM;AAAA,wBACN,OAAO;AAAA,sBACR;AACA;AAAA,oBACD;AAAA,gBACD;AACA,gCAAgB,OAAO,IAAI;AAC3B,2CAA2B,gBAAgB,wBAAwB;AACnE;AAAA,cACD,KAAK;AACJ,gCAAgB;AAChB;AAAA,cACD,KAAK;AAAA,cACL,KAAK;AACJ,2CAA2B,gBAAgB,mBAAmB;AAC9D;AAAA,cACD,KAAK;AACJ,oBAAI,QAAQ,0BAA0B,KAAK,oBAAoB,KAAK,4BAA4B,KAAK,oBAAoB,IAAI;AAC5H,wBAAM,KAAK,EAAE,KAAK,SAAS,CAAC;AAC5B,+BAAa;AACb,yCAAuB;AACvB,wBAAM;AAAA,oBACL,MAAM;AAAA,oBACN,OAAO;AAAA,kBACR;AACA;AAAA,gBACD;AACA,gCAAgB;AAChB;AAAA,cACD;AAAS,gCAAgB;AAAA,YAC1B;AACA,wBAAY;AACZ,mCAAuB;AACvB,kBAAM;AAAA,cACL,MAAM;AAAA,cACN,OAAO;AAAA,YACR;AACA;AAAA,UACD;AACA,qBAAW,YAAY;AACvB,cAAI,QAAQ,WAAW,KAAK,KAAK,GAAG;AACnC,wBAAY,WAAW;AACvB,uCAA2B,MAAM,CAAC;AAClC,oBAAQ,MAAM,CAAC,GAAG;AAAA,cACjB,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAQ,oBAAI,yBAAyB,OAAO,yBAAyB,MAAM;AAC/E,6CAA2B;AAAA,gBAC5B;AAAA,YACD;AACA,mCAAuB;AACvB,4BAAgB,CAAC,4BAA4B,KAAK,MAAM,CAAC,CAAC;AAC1D,kBAAM;AAAA,cACL,MAAM,MAAM,CAAC,MAAM,MAAM,sBAAsB;AAAA,cAC/C,OAAO,MAAM,CAAC;AAAA,YACf;AACA;AAAA,UACD;AACA,wBAAc,YAAY;AAC1B,cAAI,QAAQ,cAAc,KAAK,KAAK,GAAG;AACtC,wBAAY,cAAc;AAC1B,mCAAuB,MAAM,CAAC;AAC9B,4BAAgB;AAChB,kBAAM;AAAA,cACL,MAAM;AAAA,cACN,OAAO,MAAM,CAAC;AAAA,cACd,QAAQ,MAAM,CAAC,MAAM;AAAA,YACtB;AACA;AAAA,UACD;AACA,yBAAe,YAAY;AAC3B,cAAI,QAAQ,eAAe,KAAK,KAAK,GAAG;AACvC,wBAAY,eAAe;AAC3B,mCAAuB,MAAM,CAAC;AAC9B,4BAAgB;AAChB,kBAAM;AAAA,cACL,MAAM;AAAA,cACN,OAAO,MAAM,CAAC;AAAA,YACf;AACA;AAAA,UACD;AACA,mBAAS,YAAY;AACrB,cAAI,QAAQ,SAAS,KAAK,KAAK,GAAG;AACjC,wBAAY,SAAS;AACrB,mCAAuB,MAAM,CAAC;AAC9B,gBAAI,MAAM,CAAC,MAAM,MAAM;AACtB,qCAAuB;AACvB,oBAAM,KAAK;AAAA,gBACV,KAAK;AAAA,gBACL,SAAS,OAAO;AAAA,cACjB,CAAC;AACD,8BAAgB;AAChB,oBAAM;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO,MAAM,CAAC;AAAA,cACf;AAAA,YACD,OAAO;AACN,8BAAgB;AAChB,oBAAM;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO,MAAM,CAAC;AAAA,gBACd,QAAQ,MAAM,CAAC,MAAM;AAAA,cACtB;AAAA,YACD;AACA;AAAA,UACD;AACA;AAAA,QACD,KAAK;AAAA,QACL,KAAK;AACJ,wBAAc,YAAY;AAC1B,cAAI,QAAQ,cAAc,KAAK,KAAK,GAAG;AACtC,wBAAY,cAAc;AAC1B,uCAA2B,MAAM,CAAC;AAClC,oBAAQ,MAAM,CAAC,GAAG;AAAA,cACjB,KAAK;AACJ,sBAAM,KAAK,EAAE,KAAK,SAAS,CAAC;AAC5B;AAAA,cACD,KAAK;AACJ,sBAAM,IAAI;AACV,oBAAI,yBAAyB,OAAO,KAAK,QAAQ,aAAa;AAC7D,6CAA2B;AAC3B,kCAAgB;AAAA,gBACjB,OAAO;AACN,wBAAM,KAAK,EAAE,KAAK,cAAc,CAAC;AAAA,gBAClC;AACA;AAAA,cACD,KAAK;AACJ,sBAAM,KAAK;AAAA,kBACV,KAAK;AAAA,kBACL,SAAS,OAAO;AAAA,gBACjB,CAAC;AACD,2CAA2B;AAC3B,gCAAgB;AAChB;AAAA,cACD,KAAK;AAAK,oBAAI,yBAAyB,KAAK;AAC3C,wBAAM,IAAI;AACV,sBAAI,MAAM,MAAM,SAAS,CAAC,EAAE,QAAQ,eAAe;AAClD,0BAAM,IAAI;AAAA,kBACX;AACA,wBAAM,KAAK,EAAE,KAAK,YAAY,CAAC;AAAA,gBAChC;AAAA,YACD;AACA,mCAAuB;AACvB,kBAAM;AAAA,cACL,MAAM;AAAA,cACN,OAAO,MAAM,CAAC;AAAA,YACf;AACA;AAAA,UACD;AACA,wBAAc,YAAY;AAC1B,cAAI,QAAQ,cAAc,KAAK,KAAK,GAAG;AACtC,wBAAY,cAAc;AAC1B,mCAAuB,MAAM,CAAC;AAC9B,kBAAM;AAAA,cACL,MAAM;AAAA,cACN,OAAO,MAAM,CAAC;AAAA,YACf;AACA;AAAA,UACD;AACA,oBAAU,YAAY;AACtB,cAAI,QAAQ,UAAU,KAAK,KAAK,GAAG;AAClC,wBAAY,UAAU;AACtB,mCAAuB,MAAM,CAAC;AAC9B,kBAAM;AAAA,cACL,MAAM;AAAA,cACN,OAAO,MAAM,CAAC;AAAA,cACd,QAAQ,MAAM,CAAC,MAAM;AAAA,YACtB;AACA;AAAA,UACD;AACA;AAAA,QACD,KAAK;AACJ,kBAAQ,YAAY;AACpB,cAAI,QAAQ,QAAQ,KAAK,KAAK,GAAG;AAChC,wBAAY,QAAQ;AACpB,mCAAuB,MAAM,CAAC;AAC9B,kBAAM;AAAA,cACL,MAAM;AAAA,cACN,OAAO,MAAM,CAAC;AAAA,YACf;AACA;AAAA,UACD;AACA,kBAAQ,MAAM,SAAS,GAAG;AAAA,YACzB,KAAK;AACJ,oBAAM,KAAK,EAAE,KAAK,SAAS,CAAC;AAC5B;AACA,qCAAuB;AACvB,oBAAM;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO;AAAA,cACR;AACA;AAAA,YACD,KAAK;AACJ,oBAAM,KAAK;AAAA,gBACV,KAAK;AAAA,gBACL,SAAS,OAAO;AAAA,cACjB,CAAC;AACD;AACA,qCAAuB;AACvB,8BAAgB;AAChB,oBAAM;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO;AAAA,cACR;AACA;AAAA,UACF;AAAA,MACF;AACA,iBAAW,YAAY;AACvB,UAAI,QAAQ,WAAW,KAAK,KAAK,GAAG;AACnC,oBAAY,WAAW;AACvB,cAAM;AAAA,UACL,MAAM;AAAA,UACN,OAAO,MAAM,CAAC;AAAA,QACf;AACA;AAAA,MACD;AACA,6BAAuB,YAAY;AACnC,UAAI,QAAQ,uBAAuB,KAAK,KAAK,GAAG;AAC/C,oBAAY,uBAAuB;AACnC,wBAAgB;AAChB,YAAI,kCAAkC,KAAK,oBAAoB,GAAG;AACjE,iCAAuB;AAAA,QACxB;AACA,cAAM;AAAA,UACL,MAAM;AAAA,UACN,OAAO,MAAM,CAAC;AAAA,QACf;AACA;AAAA,MACD;AACA,uBAAiB,YAAY;AAC7B,UAAI,QAAQ,iBAAiB,KAAK,KAAK,GAAG;AACzC,oBAAY,iBAAiB;AAC7B,YAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,GAAG;AAC3B,0BAAgB;AAChB,cAAI,kCAAkC,KAAK,oBAAoB,GAAG;AACjE,mCAAuB;AAAA,UACxB;AAAA,QACD;AACA,cAAM;AAAA,UACL,MAAM;AAAA,UACN,OAAO,MAAM,CAAC;AAAA,UACd,QAAQ,MAAM,CAAC,MAAM;AAAA,QACtB;AACA;AAAA,MACD;AACA,wBAAkB,YAAY;AAC9B,UAAI,QAAQ,kBAAkB,KAAK,KAAK,GAAG;AAC1C,oBAAY,kBAAkB;AAC9B,wBAAgB;AAChB,cAAM;AAAA,UACL,MAAM;AAAA,UACN,OAAO,MAAM,CAAC;AAAA,QACf;AACA;AAAA,MACD;AACA,uBAAiB,OAAO,cAAc,MAAM,YAAY,SAAS,CAAC;AAClE,mBAAa,eAAe;AAC5B,6BAAuB;AACvB,sBAAgB;AAChB,YAAM;AAAA,QACL,MAAM,KAAK,IAAI,WAAW,KAAK,IAAI,eAAe;AAAA,QAClD,OAAO;AAAA,MACR;AAAA,IACD;AACA,WAAO;AAAA,EACR;AACA,SAAO;AACR;AACA,gBAAgB;AAEhB,IAAI,gBAAgB;AAAA,EACnB,SAAS;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACA,QAAQ;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAAG,IAAI,IAAI,cAAc,OAAO;AAAG,IAAI,IAAI,cAAc,MAAM;AAE/D,IAAM,QAAQ;AACd,IAAM,YAAY,IAAI,WAAW,EAAE;AACnC,IAAM,YAAY,IAAI,WAAW,GAAG;AACpC,SAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,QAAM,IAAI,MAAM,WAAW,CAAC;AAC5B,YAAU,CAAC,IAAI;AACf,YAAU,CAAC,IAAI;AAChB;AACA,IAAI;AAAA,CACH,SAASC,UAAS;AAClB,EAAAA,SAAQA,SAAQ,OAAO,IAAI,CAAC,IAAI;AAChC,EAAAA,SAAQA,SAAQ,MAAM,IAAI,CAAC,IAAI;AAC/B,EAAAA,SAAQA,SAAQ,OAAO,IAAI,CAAC,IAAI;AAChC,EAAAA,SAAQA,SAAQ,cAAc,IAAI,CAAC,IAAI;AACvC,EAAAA,SAAQA,SAAQ,cAAc,IAAI,CAAC,IAAI;AACvC,EAAAA,SAAQA,SAAQ,gBAAgB,IAAI,CAAC,IAAI;AACzC,EAAAA,SAAQA,SAAQ,UAAU,IAAI,CAAC,IAAI;AACpC,GAAG,YAAY,UAAU,CAAC,EAAE;AAC5B,IAAMC,0BAAyB;AAC/B,SAASC,sBAAqB,QAAQ,IAAI;AACzC,MAAI,CAAC,OAAO;AACX,WAAO;AAAA,EACR;AACA,SAAO,MAAM,QAAQ,OAAO,GAAG,EAAE,QAAQD,yBAAwB,CAAC,MAAM,EAAE,YAAY,CAAC;AACxF;AACA,IAAME,mBAAkB;AACxB,SAAS,MAAM;AACd,MAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,QAAQ,YAAY;AACxE,WAAO,QAAQ,IAAI,EAAE,QAAQ,OAAO,GAAG;AAAA,EACxC;AACA,SAAO;AACR;AACA,IAAM,UAAU,YAAY,YAAY;AACvC,eAAa,WAAW,IAAI,CAAC,aAAaD,sBAAqB,QAAQ,CAAC;AACxE,MAAI,eAAe;AACnB,MAAI,mBAAmB;AACvB,WAASE,SAAQ,WAAW,SAAS,GAAGA,UAAS,MAAM,CAAC,kBAAkBA,UAAS;AAClF,UAAM,OAAOA,UAAS,IAAI,WAAWA,MAAK,IAAI,IAAI;AAClD,QAAI,CAAC,QAAQ,KAAK,WAAW,GAAG;AAC/B;AAAA,IACD;AACA,mBAAe,GAAG,IAAI,IAAI,YAAY;AACtC,uBAAmBC,YAAW,IAAI;AAAA,EACnC;AACA,iBAAeC,iBAAgB,cAAc,CAAC,gBAAgB;AAC9D,MAAI,oBAAoB,CAACD,YAAW,YAAY,GAAG;AAClD,WAAO,IAAI,YAAY;AAAA,EACxB;AACA,SAAO,aAAa,SAAS,IAAI,eAAe;AACjD;AACA,SAASC,iBAAgB,MAAM,gBAAgB;AAC9C,MAAI,MAAM;AACV,MAAI,oBAAoB;AACxB,MAAI,YAAY;AAChB,MAAI,OAAO;AACX,MAAI,OAAO;AACX,WAASF,SAAQ,GAAGA,UAAS,KAAK,QAAQ,EAAEA,QAAO;AAClD,QAAIA,SAAQ,KAAK,QAAQ;AACxB,aAAO,KAAKA,MAAK;AAAA,IAClB,WAAW,SAAS,KAAK;AACxB;AAAA,IACD,OAAO;AACN,aAAO;AAAA,IACR;AACA,QAAI,SAAS,KAAK;AACjB,UAAI,cAAcA,SAAQ,KAAK,SAAS,EAAE;AAAA,eACjC,SAAS,GAAG;AACpB,YAAI,IAAI,SAAS,KAAK,sBAAsB,KAAK,IAAI,IAAI,SAAS,CAAC,MAAM,OAAO,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK;AAC5G,cAAI,IAAI,SAAS,GAAG;AACnB,kBAAM,iBAAiB,IAAI,YAAY,GAAG;AAC1C,gBAAI,mBAAmB,IAAI;AAC1B,oBAAM;AACN,kCAAoB;AAAA,YACrB,OAAO;AACN,oBAAM,IAAI,MAAM,GAAG,cAAc;AACjC,kCAAoB,IAAI,SAAS,IAAI,IAAI,YAAY,GAAG;AAAA,YACzD;AACA,wBAAYA;AACZ,mBAAO;AACP;AAAA,UACD,WAAW,IAAI,SAAS,GAAG;AAC1B,kBAAM;AACN,gCAAoB;AACpB,wBAAYA;AACZ,mBAAO;AACP;AAAA,UACD;AAAA,QACD;AACA,YAAI,gBAAgB;AACnB,iBAAO,IAAI,SAAS,IAAI,QAAQ;AAChC,8BAAoB;AAAA,QACrB;AAAA,MACD,OAAO;AACN,YAAI,IAAI,SAAS,GAAG;AACnB,iBAAO,IAAI,KAAK,MAAM,YAAY,GAAGA,MAAK,CAAC;AAAA,QAC5C,OAAO;AACN,gBAAM,KAAK,MAAM,YAAY,GAAGA,MAAK;AAAA,QACtC;AACA,4BAAoBA,SAAQ,YAAY;AAAA,MACzC;AACA,kBAAYA;AACZ,aAAO;AAAA,IACR,WAAW,SAAS,OAAO,SAAS,IAAI;AACvC,QAAE;AAAA,IACH,OAAO;AACN,aAAO;AAAA,IACR;AAAA,EACD;AACA,SAAO;AACR;AACA,IAAMC,cAAa,SAASE,IAAG;AAC9B,SAAOJ,iBAAgB,KAAKI,EAAC;AAC9B;AACA,IAAM,yBAAyB;AAC/B,IAAM,4BAA4B;AAClC,SAAS,gBAAgB,SAAS;AAEjC,MAAI,CAAC,QAAQ,SAAS,GAAG,GAAG;AAC3B,WAAO,CAAC,OAAO;AAAA,EAChB;AACA,QAAM,SAAS;AACf,QAAM,QAAQ,OAAO,KAAK,QAAQ,QAAQ,YAAY,EAAE,CAAC;AACzD,MAAI,CAAC,OAAO;AACX,WAAO,CAAC,OAAO;AAAA,EAChB;AACA,MAAI,MAAM,MAAM,CAAC;AACjB,MAAI,IAAI,WAAW,QAAQ,GAAG;AAC7B,UAAM,IAAI,MAAM,CAAC;AAAA,EAClB;AACA,MAAI,IAAI,WAAW,OAAO,KAAK,IAAI,WAAW,QAAQ,GAAG;AACxD,UAAM,SAAS,IAAI,IAAI,GAAG;AAC1B,WAAO,aAAa,OAAO,QAAQ;AACnC,WAAO,aAAa,OAAO,UAAU;AACrC,UAAM,OAAO,WAAW,OAAO,OAAO,OAAO;AAAA,EAC9C;AACA,MAAI,IAAI,WAAW,OAAO,GAAG;AAC5B,UAAM,YAAY,sBAAsB,KAAK,GAAG;AAChD,UAAM,IAAI,MAAM,YAAY,IAAI,CAAC;AAAA,EAClC;AACA,SAAO;AAAA,IACN;AAAA,IACA,MAAM,CAAC,KAAK;AAAA,IACZ,MAAM,CAAC,KAAK;AAAA,EACb;AACD;AACA,SAAS,2BAA2B,KAAK;AACxC,MAAI,OAAO,IAAI,KAAK;AACpB,MAAI,0BAA0B,KAAK,IAAI,GAAG;AACzC,WAAO;AAAA,EACR;AACA,MAAI,KAAK,SAAS,SAAS,GAAG;AAC7B,WAAO,KAAK,QAAQ,oDAAoD,KAAK;AAAA,EAC9E;AACA,MAAI,CAAC,KAAK,SAAS,GAAG,KAAK,CAAC,KAAK,SAAS,GAAG,GAAG;AAC/C,WAAO;AAAA,EACR;AAEA,QAAM,oBAAoB;AAC1B,QAAM,UAAU,KAAK,MAAM,iBAAiB;AAC5C,QAAM,eAAe,WAAW,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI;AAC1D,QAAM,CAAC,KAAK,YAAY,YAAY,IAAI,gBAAgB,KAAK,QAAQ,mBAAmB,EAAE,CAAC;AAC3F,MAAI,CAAC,OAAO,CAAC,cAAc,CAAC,cAAc;AACzC,WAAO;AAAA,EACR;AACA,SAAO;AAAA,IACN,MAAM;AAAA,IACN,QAAQ,gBAAgB;AAAA,IACxB,MAAM,OAAO,SAAS,UAAU;AAAA,IAChC,QAAQ,OAAO,SAAS,YAAY;AAAA,EACrC;AACD;AACA,SAAS,iBAAiB,KAAK;AAC9B,QAAM,OAAO,IAAI,KAAK;AACtB,MAAI,CAAC,uBAAuB,KAAK,IAAI,GAAG;AACvC,WAAO,2BAA2B,IAAI;AAAA,EACvC;AACA,SAAO,mBAAmB,IAAI;AAC/B;AAGA,SAAS,mBAAmB,KAAK;AAChC,MAAI,OAAO,IAAI,KAAK;AACpB,MAAI,CAAC,uBAAuB,KAAK,IAAI,GAAG;AACvC,WAAO;AAAA,EACR;AACA,MAAI,KAAK,SAAS,QAAQ,GAAG;AAC5B,WAAO,KAAK,QAAQ,cAAc,MAAM,EAAE,QAAQ,8BAA8B,EAAE;AAAA,EACnF;AACA,MAAI,gBAAgB,KAAK,QAAQ,QAAQ,EAAE,EAAE,QAAQ,gBAAgB,GAAG,EAAE,QAAQ,WAAW,EAAE;AAG/F,QAAMC,YAAW,cAAc,MAAM,YAAY;AAEjD,kBAAgBA,YAAW,cAAc,QAAQA,UAAS,CAAC,GAAG,EAAE,IAAI;AAGpE,QAAM,CAAC,KAAK,YAAY,YAAY,IAAI,gBAAgBA,YAAWA,UAAS,CAAC,IAAI,aAAa;AAC9F,MAAI,SAASA,aAAY,iBAAiB;AAC1C,MAAI,OAAO,OAAO,CAAC,QAAQ,aAAa,EAAE,SAAS,GAAG,IAAI,SAAY;AACtE,MAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,cAAc;AAC1C,WAAO;AAAA,EACR;AACA,MAAI,OAAO,WAAW,QAAQ,GAAG;AAChC,aAAS,OAAO,MAAM,CAAC;AAAA,EACxB;AACA,MAAI,KAAK,WAAW,SAAS,GAAG;AAC/B,WAAO,KAAK,MAAM,CAAC;AAAA,EACpB;AAEA,SAAO,KAAK,WAAW,OAAO,KAAK,KAAK,WAAW,WAAW,IAAI,OAAO,QAAQ,IAAI;AACrF,MAAI,QAAQ;AACX,aAAS,OAAO,QAAQ,8BAA8B,EAAE;AAAA,EACzD;AACA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA,MAAM,OAAO,SAAS,UAAU;AAAA,IAChC,QAAQ,OAAO,SAAS,YAAY;AAAA,EACrC;AACD;AAEA,SAAS,oBAAoB,SAAS;AACrC,QAAM,sBAAsB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,kBAAkB;AACxG,WAAS,UAAU;AAElB,WAAO,OAAO,WAAW,kBAAkB,MAAM,cAAc,WAAW,kBAAkB,IAAI,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,GAAG,MAAM;AAC5H,YAAM,IAAI,MAAM,6DAAkE,OAAO,IAAI,CAAC,kBAAkB;AAAA,IACjH,EAAE,CAAC;AAAA,EACJ;AACA,SAAO;AAAA,IACN,QAAQ,SAAS;AAChB,UAAI,OAAO,YAAY,YAAY;AAClC,cAAM,IAAI,UAAU,mDAAmD,OAAO,OAAO,EAAE;AAAA,MACxF;AACA,aAAO,QAAQ;AAAA,IAChB;AAAA,IACA,KAAK,MAAM,SAAS;AACnB,UAAI,OAAO,SAAS,UAAU;AAC7B,cAAM,IAAI,UAAU,mDAAmD,OAAO,IAAI,EAAE;AAAA,MACrF;AACA,YAAM,WAAW,YAAY,MAAM;AACnC,cAAQ,EAAE,UAAU,MAAM,UAAU,OAAO,YAAY,aAAa,MAAM,QAAQ,MAAM,QAAQ,EAAE,aAAa,MAAM,QAAQ,CAAC,IAAI,OAAO;AAAA,IAC1I;AAAA,IACA,OAAO,MAAM;AACZ,UAAI,OAAO,SAAS,UAAU;AAC7B,cAAM,IAAI,UAAU,qDAAqD,OAAO,IAAI,EAAE;AAAA,MACvF;AACA,cAAQ,EAAE,YAAY,MAAM,YAAY,QAAQ,CAAC;AAAA,IAClD;AAAA,IACA,OAAO,MAAM,SAAS;AACrB,UAAI,OAAO,SAAS,UAAU;AAC7B,cAAM,IAAI,UAAU,qDAAqD,OAAO,IAAI,EAAE;AAAA,MACvF;AACA,YAAM,WAAW,YAAY,QAAQ;AACrC,cAAQ,EAAE,UAAU,MAAM,UAAU,OAAO,YAAY,aAAa,MAAM,QAAQ,MAAM,QAAQ,EAAE,aAAa,MAAM,QAAQ,CAAC,IAAI,OAAO;AAAA,IAC1I;AAAA,IACA,SAAS,MAAM;AACd,UAAI,OAAO,SAAS,UAAU;AAC7B,cAAM,IAAI,UAAU,uDAAuD,OAAO,IAAI,EAAE;AAAA,MACzF;AACA,cAAQ,EAAE,YAAY,MAAM,YAAY,UAAU,CAAC;AAAA,IACpD;AAAA,IACA,MAAM,aAAa,MAAM;AACxB,aAAO,QAAQ,EAAE,aAAa,MAAM,YAAY,cAAc,CAAC;AAAA,IAChE;AAAA,IACA,MAAM,WAAW,MAAM;AACtB,aAAO,QAAQ,EAAE,WAAW,MAAM,YAAY,YAAY,CAAC;AAAA,IAC5D;AAAA,EACD;AACD;AACA,SAAS,YAAY,MAAM;AAC1B,QAAM,aAA6B,uBAAuB,EAAE,iBAAiB,EAAE,CAAC;AAChF,QAAM,aAAa,WAAW,MAAM,IAAI;AAExC,QAAM,qBAAqB,WAAW,UAAU,CAACC,WAAU;AAC1D,WAAOA,OAAM,SAAS,cAAc,IAAI,EAAE,KAAKA,OAAM,SAAS,GAAG,IAAI,GAAG;AAAA,EACzE,CAAC;AACD,QAAM,QAAwB,iBAAiB,WAAW,qBAAqB,CAAC,CAAC;AACjF,UAAQ,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,SAAS;AACtE;AAEA,IAAM,MAAM,YAAY,OAAO;AAAA,EAC9B,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,MAAM;AACP;AACA,SAAS,OAAO;AACf,UAAQ,KAAK,mEAAmE;AACjF;AACA,SAAS,IAAI,OAAO,MAAM;AACzB,MAAI,KAAK,OAAO,IAAI;AACpB,SAAO,IAAI,QAAQ,CAACC,UAAS,WAAW;AACvC,UAAM,UAAU,WAAW,MAAM;AAChC,aAAO,IAAI,MAAM,qBAAqB,KAAK,UAAU,CAAC;AAAA,IACvD,GAAG,GAAG;AACN,QAAI,GAAG,GAAG,KAAK,WAAW,SAAS,EAAEC,OAAM;AAC1C,MAAAD,SAAQC,KAAI;AACZ,mBAAa,OAAO;AACpB,UAAI,IAAI,GAAG,KAAK,WAAW,CAAC;AAAA,IAC7B,CAAC;AAAA,EACF,CAAC;AACF;AAEA,IAAM,EAAE,IAAI,IAAI;AAChB,IAAM,eAAN,MAAmB;AAAA,EAClB,WAAW,IAAI,eAAe;AAAA,EAC9B,QAAQ,oBAAI,IAAI;AAAA,EAChB,YAAY,oBAAI,IAAI;AAAA,EACpB,YAAY,aAAaC,MAAK,OAAO,QAAQ;AAC5C,SAAK,cAAc;AACnB,SAAK,MAAMA;AACX,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EACf;AAAA,EACA,MAAM,UAAU;AACf,QAAI,CAAC,KAAK,MAAM,MAAM;AACrB;AAAA,IACD;AACA,UAAM,QAAQ,IAAI,CAAC,GAAG,KAAK,MAAM,OAAO,CAAC,CAAC;AAAA,EAC3C;AAAA,EACA,MAAM,qBAAqB,IAAI;AAC9B,UAAM,OAAO,KAAK,SAAS,IAAI,EAAE;AACjC,QAAI,CAAC,QAAQ,KAAK,SAAS,UAAU;AACpC,YAAM,IAAI,MAAM,QAAQ,EAAE,kGAAkG;AAAA,IAC7H;AACA,UAAM,SAAS,MAAM,KAAK,QAAQ;AAClC,WAAO;AAAA,EACR;AAAA,EACA,iBAAiB,IAAI;AACpB,UAAM,OAAO,KAAK,SAAS,IAAI,EAAE;AACjC,QAAI,CAAC,QAAQ,KAAK,SAAS,UAAU;AACpC,YAAM,IAAI,MAAM,QAAQ,EAAE,kGAAkG;AAAA,IAC7H;AACA,QAAI,CAAC,KAAK,OAAO;AAChB,YAAM,IAAI,MAAM,QAAQ,EAAE,gGAAgG;AAAA,IAC3H;AACA,WAAO,KAAK;AAAA,EACb;AAAA,EACA,MAAM,aAAa;AAClB,UAAM,MAAM,MAAM,KAAK,KAAK,SAAS;AACrC,QAAI,CAAC,IAAI,QAAQ;AAChB;AAAA,IACD;AACA,UAAM,KAAK,IAAI,WAAW,GAAG;AAC7B,UAAM,KAAK,YAAY,WAAW;AAClC,SAAK,SAAS,MAAM;AAAA,EACrB;AAAA,EACA,MAAM,aAAa,IAAI,UAAU;AAChC,UAAM,WAAW,MAAM,KAAK,IAAI,UAAU,IAAI,QAAQ;AACtD,QAAI,YAAY,MAAM;AACrB,YAAM,IAAI,MAAM,4BAA4B,EAAE,oBAAoB,QAAQ,GAAG;AAAA,IAC9E;AACA,UAAM,MAAM,QAAQ,SAAS,EAAE;AAC/B,UAAM,MAAM,IAAI,IAAI,SAAS,KAAK,SAAS,IAAI;AAC/C,UAAM,QAAQ,uBAAuB,GAAG;AACxC,UAAM,YAAY,GAAG,IAAI,QAAQ,GAAG,IAAI,SAAS,GAAG,IAAI,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,EAAE,GAAG,IAAI,IAAI;AAClG,WAAO,KAAK,kBAAkB,MAAM;AAAA;AAAA,MAEnC;AAAA,KACF,EAAE,KAAK,CAAC,QAAQ;AACd,UAAI,CAAC,SAAS,aAAa,OAAO,IAAI,YAAY,aAAa;AAC9D,eAAO;AAAA,MACR;AAEA,YAAM,IAAI,IAAI;AACd,cAAQ,MAAM,QAAQ,MAAM,SAAS,SAAS,EAAE,cAAc,IAAI;AAAA,QACjE,GAAG,OAAO,MAAM,YAAY,CAAC,MAAM,QAAQ,CAAC,KAAK,OAAO,MAAM,aAAa,IAAI,CAAC;AAAA,QAChF,SAAS;AAAA,MACV;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EACA,MAAM,WAAW,OAAO,UAAU;AACjC,UAAM,KAAK,QAAQ;AACnB,UAAM,EAAE,YAAY,aAAa,YAAY,IAAI,MAAM,KAAK,IAAI,YAAY,OAAO,UAAU,EAAE,MAAM,OAAO,CAAC;AAC7G,UAAM,UAAU,KAAK,gBAAgB,aAAa,WAAW,CAAC;AAC9D,QAAI,OAAO,KAAK,SAAS,IAAI,OAAO;AACpC,QAAI,CAAC,MAAM;AACV,UAAI,aAAa;AAChB,cAAM,mBAAmB,IAAI,IAAI,KAAK,gBAAgB,aAAa,WAAW,CAAC,GAAG,SAAS,IAAI,EAAE,SAAS;AAC1G,eAAO,IAAI,iBAAiB,OAAO,YAAY,SAAS,gBAAgB;AAAA,MACzE,OAAO;AACN,eAAO,IAAI,iBAAiB,OAAO,YAAY,OAAO;AAAA,MACvD;AAAA,IACD;AACA,QAAI,KAAK,SAAS,UAAU;AAC3B,aAAO,MAAM,KAAK,QAAQ;AAAA,IAC3B;AACA,QAAI,KAAK,SAAS,cAAc,KAAK,SAAS,WAAW;AACxD,YAAM,MAAM,IAAI,IAAI,QAAQ,UAAU,IAAI,SAAS,IAAI;AACvD,YAAM,QAAQ,IAAI,SAAS,GAAG,IAAI,MAAM,MAAM,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC;AACnE,YAAM,eAAe,MAAM;AAAA;AAAA,QAE1B,GAAG,IAAI,QAAQ,GAAG,KAAK,SAAS,KAAK,IAAI,GAAG,IAAI,IAAI;AAAA;AAErD,aAAO,KAAK,WAAW,cAAc,KAAK,IAAI;AAAA,IAC/C;AACA,WAAO;AAAA;AAAA,MAEN,KAAK;AAAA;AAAA,EAEP;AAAA,EACA,WAAW,QAAQ,aAAa,YAAY;AAC3C,WAAO,WAAW;AAAA,MACjB,oBAAoB;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACA,OAAO,KAAK;AAAA,MACZ,MAAM;AAAA,IACP,GAAG,MAAM;AAAA,EACV;AAAA,EACA,UAAU,OAAO,UAAU,kBAAkB;AAC5C,UAAM,UAAU,KAAK,IAAI,YAAY,OAAO,UAAU,EAAE,MAAM,OAAO,qBAAqB,aAAa,aAAa,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,OAAO,QAAQ,OAAO,CAAC,EAAE,KAAK,OAAO,EAAE,aAAa,YAAY,aAAa,cAAc,SAAS,MAAM;AAClT,YAAM,UAAU,KAAK,gBAAgB,aAAa,WAAW,CAAC;AAC9D,WAAK,UAAU,IAAI,UAAU;AAC7B,YAAM,UAAU,OAAO,qBAAqB,aAAa,YAAY;AACpE,cAAM,OAAO,MAAM,iBAAiB;AAGpC,eAAO,eAAe,EAAE,SAAS,KAAK,IAAI;AAAA,MAC3C,IAAI;AACJ,YAAM,eAAe,OAAO,gBAAgB,WAAW,IAAI,IAAI,KAAK,gBAAgB,aAAa,WAAW,CAAC,GAAG,SAAS,IAAI,EAAE,SAAS,IAAI;AAC5I,UAAI;AACJ,UAAI,aAAa,UAAU;AAC1B,iBAAS,KAAK,SAAS,SAAS,UAAU,OAAO,YAAY,SAAS,OAAO;AAAA,MAC9E,WAAW,aAAa,WAAW;AAClC,iBAAS,KAAK,SAAS,SAAS,WAAW,OAAO,YAAY,OAAO;AAAA,MACtE,WAAW,aAAa,YAAY;AACnC,iBAAS,KAAK,SAAS,SAAS,YAAY,OAAO,YAAY,SAAS,YAAY;AAAA,MACrF,OAAO;AACN,iBAAS,KAAK,SAAS,SAAS,YAAY,OAAO,YAAY,OAAO;AAAA,MACvE;AACA,YAAM,KAAK,YAAY,SAAS,MAAM;AAAA,IACvC,CAAC,EAAE,QAAQ,MAAM;AAChB,WAAK,MAAM,OAAO,OAAO;AAAA,IAC1B,CAAC;AACD,SAAK,MAAM,IAAI,OAAO;AAAA,EACvB;AAAA,EACA,YAAY,IAAI,UAAU;AACzB,UAAM,UAAU,KAAK,IAAI,UAAU,IAAI,QAAQ,EAAE,KAAK,OAAO,aAAa;AACzE,UAAI,CAAC,UAAU;AACd;AAAA,MACD;AACA,YAAM,UAAU,KAAK,gBAAgB,aAAa,SAAS,GAAG,CAAC;AAC/D,WAAK,UAAU,IAAI,SAAS,EAAE;AAC9B,WAAK,SAAS,OAAO,OAAO;AAC5B,YAAM,KAAK,YAAY,OAAO,OAAO;AAAA,IACtC,CAAC,EAAE,QAAQ,MAAM;AAChB,WAAK,MAAM,OAAO,OAAO;AAAA,IAC1B,CAAC;AACD,SAAK,MAAM,IAAI,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA,EAGA,kBAAkB,eAAe;AAChC,QAAI,OAAO,kBAAkB,YAAY;AACxC,YAAM,UAAU,IAAI,QAAQ,CAACF,UAAS,WAAW;AAChD,aAAK,QAAQ,EAAE,QAAQ,MAAM;AAC5B,wBAAc,EAAE,KAAKA,UAAS,MAAM;AAAA,QACrC,CAAC;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACR;AACA,WAAO;AAAA,EACR;AAAA,EACA,gBAAgB,MAAM;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,SAAS,KAAK,SAAS,OAAO,IAAI;AAExC,QAAI,KAAK,WAAW,OAAO,IAAI,GAAG;AACjC,aAAO,KAAK,MAAM,OAAO,KAAK,MAAM;AAAA,IACrC;AACA,QAAI,KAAK,WAAW,MAAM,GAAG;AAC5B,aAAO,KAAK,MAAM,OAAO,MAAM;AAAA,IAChC;AACA,WAAO;AAAA,EACR;AACD;AACA,IAAM,gBAAgB;AACtB,SAAS,aAAa,KAAK;AAC1B,SAAO,IAAI,QAAQ,eAAe,EAAE;AACrC;;;AC/jDA,IAAM,YAAY;AAClB,SAAS,SAAS,KAAK;AACtB,SAAO,IAAI,QAAQ,WAAW,EAAE;AACjC;AACA,SAAS,yBAAyB,WAAW,SAAS,iBAAiB,uBAAyB;AAC/F,QAAM,SAAS,6BAA6B,cAAc,uBAAuB,SAAS;AAC1F,QAAM,OAAO,QAAQ,IAAI,CAAC,SAAS;AAClC,QAAI,SAAS,WAAW;AACvB,aAAO;AAAA,IACR;AACA,WAAO,gBAAgB,IAAI,cAAc,IAAI;AAAA,EAC9C,CAAC,EAAE,KAAK,IAAI;AACZ,SAAO,GAAG,MAAM;AAAA,EAAK,IAAI;AAC1B;;;ACXA,IAAM,gCAAN,MAAoC;AAAA,EACnC,MAAM,SAAS,QAAQ;AACtB,UAAM,IAAI,+BAA+B,OAAO,OAAO,CAAC;AAAA,EACzD;AAAA,EACA,MAAM,OAAO,IAAI;AAChB,UAAM,IAAI,6BAA6B,EAAE;AAAA,EAC1C;AAAA,EACA,MAAM,aAAa;AAClB,UAAM,IAAI,+BAA+B;AAAA,EAC1C;AACD;;;ACLA,IAAM,6BAAN,MAAiC;AAAA,EAChC,QAAQ,IAAI,eAAe;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,YAAY,UAAU,CAAC,GAAG;AACzB,SAAK,UAAU;AACf,QAAI,CAAC,QAAQ,oBAAoB;AAChC,cAAQ,qBAAqB;AAAA,IAC9B;AAAA,EACD;AAAA,EACA,MAAM,SAAS,QAAQ;AACtB,UAAM,KAAK,KAAK;AAChB,SAAK,MAAM,IAAI,MAAM;AAAA,EACtB;AAAA,EACA,MAAM,OAAO,KAAK;AACjB,UAAM,KAAK,KAAK;AAChB,SAAK,MAAM,OAAO,GAAG;AAAA,EACtB;AAAA,EACA,MAAM,aAAa;AAClB,SAAK,MAAM,MAAM;AAAA,EAClB;AAAA,EACA,MAAM,kBAAkB,MAAM;AAC7B,UAAM,UAAU,OAAO,KAAK,MAAM,KAAK,QAAQ,CAAC;AAChD,UAAM,OAAO,yBAAyB,KAAK,KAAK,SAAS,KAAK,QAAQ,kBAAkB;AACxF,WAAO,IAAI,SAAS,MAAM,EAAE,SAAS,EAAE,gBAAgB,yBAAyB,EAAE,CAAC;AAAA,EACpF;AAAA,EACA,MAAM,OAAO;AACZ,QAAI,KAAK,QAAQ;AAChB,aAAO,KAAK;AAAA,IACb;AACA,QAAI,KAAK,cAAc;AACtB,aAAO,KAAK;AAAA,IACb;AACA,UAAM,SAAS,KAAK,QAAQ;AAC5B,SAAK,eAAe,QAAQ,IAAI,CAAC,SAAS,EAAE,YAAY,SAAS;AAChE,aAAO,IAAI,OAAO;AAClB,aAAO;AAAA,IACR,EAAE,IAAI,OAAO,4BAAa,GAAG,OAAO,4BAAe,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,YAAY,GAAG,EAAE,KAAK,CAAC,MAAM;AAC5F,YAAMG,UAAS,YAAY,KAAK,IAAI,MAAM,OAAO,EAAE,QAAQ,MAAM;AAChE,cAAM,OAAO,WAAW,QAAQ,IAAI,MAAM,SAAS,OAAO,MAAM,CAAC;AACjE,YAAI,CAAC,KAAK,MAAM,IAAI,IAAI,GAAG;AAC1B,iBAAO,YAAY;AAAA,QACpB;AACA,cAAM,OAAO,KAAK,MAAM,IAAI,IAAI;AAChC,gBAAQ,KAAK,MAAM;AAAA,UAClB,KAAK;AAAU,mBAAO,KAAK,kBAAkB,IAAI;AAAA,UACjD,KAAK;AAAA,UACL,KAAK;AAAW,mBAAO,SAAS,SAAS,YAAY,MAAM,QAAQ,KAAK,IAAI,EAAE,CAAC;AAAA,UAC/E,KAAK;AAAY,mBAAO,SAAS,SAAS,KAAK,QAAQ;AAAA,UACvD;AAAS,kBAAM,IAAI,MAAM,sBAAsB,KAAK,IAAI,EAAE;AAAA,QAC3D;AAAA,MACD,CAAC,CAAC;AACF,aAAOA,QAAO,MAAM,KAAK,QAAQ,UAAU,EAAE,KAAK,MAAMA,OAAM;AAAA,IAC/D,CAAC,EAAE,QAAQ,MAAM;AAChB,WAAK,SAAS;AACd,WAAK,eAAe;AAAA,IACrB,CAAC;AACD,WAAO,MAAM,KAAK;AAAA,EACnB;AACD;AACA,IAAM,sBAAsB;AAC5B,IAAM,cAAc;AACpB,IAAM,YAAY;AAClB,SAAS,WAAW,KAAK;AACxB,SAAO,IAAI,QAAQ,aAAa,EAAE,EAAE,QAAQ,WAAW,EAAE,EAAE,QAAQ,qBAAqB,EAAE;AAC3F;AACA,SAAS,cAAc;AACtB,SAAO,IAAI,SAAS,MAAM;AAAA,IACzB,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,SAAS,EAAE,mBAAmB,cAAc;AAAA,EAC7C,CAAC;AACF;AACA,IAAM,sBAAsB;AAC5B,SAAS,YAAY,KAAK,eAAe;AAGxC,QAAM,cAAc,IAAI,IAAI,IAAI,QAAQ,qBAAqB,KAAK,GAAG,SAAS,IAAI;AAClF,QAAM,EAAE,QAAQ,KAAK,IAAI;AACzB,QAAM,WAAW,SAAS,GAAG;AAC7B,SAAO,GAAG,QAAQ,IAAI,aAAa,GAAG,SAAS,IAAI,OAAO,MAAM,CAAC,CAAC,KAAK,EAAE,GAAG,QAAQ,EAAE;AACvF;", "names": ["index", "p", "UrlType", "_DRIVE_LETTER_START_RE", "normalizeWindowsPath", "_IS_ABSOLUTE_RE", "index", "isAbsolute", "normalizeString", "p", "location", "stack", "resolve", "data", "rpc", "worker"]}