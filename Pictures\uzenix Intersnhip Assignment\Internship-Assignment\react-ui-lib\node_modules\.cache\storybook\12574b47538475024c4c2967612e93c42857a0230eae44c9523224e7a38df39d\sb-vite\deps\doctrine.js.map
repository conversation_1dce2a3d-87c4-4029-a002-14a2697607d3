{"version": 3, "sources": ["../../../../../esutils/lib/ast.js", "../../../../../esutils/lib/code.js", "../../../../../esutils/lib/keyword.js", "../../../../../esutils/lib/utils.js", "../../../../../doctrine/package.json", "browser-external:assert", "../../../../../doctrine/lib/utility.js", "../../../../../doctrine/lib/typed.js", "../../../../../doctrine/lib/doctrine.js"], "sourcesContent": ["/*\n  Copyright (C) 2013 <PERSON><PERSON> <<EMAIL>>\n\n  Redistribution and use in source and binary forms, with or without\n  modification, are permitted provided that the following conditions are met:\n\n    * Redistributions of source code must retain the above copyright\n      notice, this list of conditions and the following disclaimer.\n    * Redistributions in binary form must reproduce the above copyright\n      notice, this list of conditions and the following disclaimer in the\n      documentation and/or other materials provided with the distribution.\n\n  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS 'AS IS'\n  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n  ARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES;\n  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n  THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n\n(function () {\n    'use strict';\n\n    function isExpression(node) {\n        if (node == null) { return false; }\n        switch (node.type) {\n            case 'ArrayExpression':\n            case 'AssignmentExpression':\n            case 'BinaryExpression':\n            case 'CallExpression':\n            case 'ConditionalExpression':\n            case 'FunctionExpression':\n            case 'Identifier':\n            case 'Literal':\n            case 'LogicalExpression':\n            case 'MemberExpression':\n            case 'NewExpression':\n            case 'ObjectExpression':\n            case 'SequenceExpression':\n            case 'ThisExpression':\n            case 'UnaryExpression':\n            case 'UpdateExpression':\n                return true;\n        }\n        return false;\n    }\n\n    function isIterationStatement(node) {\n        if (node == null) { return false; }\n        switch (node.type) {\n            case 'DoWhileStatement':\n            case 'ForInStatement':\n            case 'ForStatement':\n            case 'WhileStatement':\n                return true;\n        }\n        return false;\n    }\n\n    function isStatement(node) {\n        if (node == null) { return false; }\n        switch (node.type) {\n            case 'BlockStatement':\n            case 'BreakStatement':\n            case 'ContinueStatement':\n            case 'DebuggerStatement':\n            case 'DoWhileStatement':\n            case 'EmptyStatement':\n            case 'ExpressionStatement':\n            case 'ForInStatement':\n            case 'ForStatement':\n            case 'IfStatement':\n            case 'LabeledStatement':\n            case 'ReturnStatement':\n            case 'SwitchStatement':\n            case 'ThrowStatement':\n            case 'TryStatement':\n            case 'VariableDeclaration':\n            case 'WhileStatement':\n            case 'WithStatement':\n                return true;\n        }\n        return false;\n    }\n\n    function isSourceElement(node) {\n      return isStatement(node) || node != null && node.type === 'FunctionDeclaration';\n    }\n\n    function trailingStatement(node) {\n        switch (node.type) {\n        case 'IfStatement':\n            if (node.alternate != null) {\n                return node.alternate;\n            }\n            return node.consequent;\n\n        case 'LabeledStatement':\n        case 'ForStatement':\n        case 'ForInStatement':\n        case 'WhileStatement':\n        case 'WithStatement':\n            return node.body;\n        }\n        return null;\n    }\n\n    function isProblematicIfStatement(node) {\n        var current;\n\n        if (node.type !== 'IfStatement') {\n            return false;\n        }\n        if (node.alternate == null) {\n            return false;\n        }\n        current = node.consequent;\n        do {\n            if (current.type === 'IfStatement') {\n                if (current.alternate == null)  {\n                    return true;\n                }\n            }\n            current = trailingStatement(current);\n        } while (current);\n\n        return false;\n    }\n\n    module.exports = {\n        isExpression: isExpression,\n        isStatement: isStatement,\n        isIterationStatement: isIterationStatement,\n        isSourceElement: isSourceElement,\n        isProblematicIfStatement: isProblematicIfStatement,\n\n        trailingStatement: trailingStatement\n    };\n}());\n/* vim: set sw=4 ts=4 et tw=80 : */\n", "/*\n  Copyright (C) 2013-2014 <PERSON><PERSON> <<EMAIL>>\n  Copyright (C) 2014 <PERSON> <<EMAIL>>\n\n  Redistribution and use in source and binary forms, with or without\n  modification, are permitted provided that the following conditions are met:\n\n    * Redistributions of source code must retain the above copyright\n      notice, this list of conditions and the following disclaimer.\n    * Redistributions in binary form must reproduce the above copyright\n      notice, this list of conditions and the following disclaimer in the\n      documentation and/or other materials provided with the distribution.\n\n  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n  ARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n  THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n\n(function () {\n    'use strict';\n\n    var ES6Regex, ES5Regex, NON_ASCII_WHITESPACES, IDENTIFIER_START, IDENTIFIER_PART, ch;\n\n    // See `tools/generate-identifier-regex.js`.\n    ES5Regex = {\n        // ECMAScript 5.1/Unicode v9.0.0 NonAsciiIdentifierStart:\n        NonAsciiIdentifierStart: /[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u08A0-\\u08B4\\u08B6-\\u08BD\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u1884\\u1887-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FD5\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]/,\n        // ECMAScript 5.1/Unicode v9.0.0 NonAsciiIdentifierPart:\n        NonAsciiIdentifierPart: /[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0300-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u0483-\\u0487\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0610-\\u061A\\u0620-\\u0669\\u066E-\\u06D3\\u06D5-\\u06DC\\u06DF-\\u06E8\\u06EA-\\u06FC\\u06FF\\u0710-\\u074A\\u074D-\\u07B1\\u07C0-\\u07F5\\u07FA\\u0800-\\u082D\\u0840-\\u085B\\u08A0-\\u08B4\\u08B6-\\u08BD\\u08D4-\\u08E1\\u08E3-\\u0963\\u0966-\\u096F\\u0971-\\u0983\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BC-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CE\\u09D7\\u09DC\\u09DD\\u09DF-\\u09E3\\u09E6-\\u09F1\\u0A01-\\u0A03\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A59-\\u0A5C\\u0A5E\\u0A66-\\u0A75\\u0A81-\\u0A83\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABC-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AD0\\u0AE0-\\u0AE3\\u0AE6-\\u0AEF\\u0AF9\\u0B01-\\u0B03\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3C-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B5C\\u0B5D\\u0B5F-\\u0B63\\u0B66-\\u0B6F\\u0B71\\u0B82\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD0\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C03\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C58-\\u0C5A\\u0C60-\\u0C63\\u0C66-\\u0C6F\\u0C80-\\u0C83\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBC-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CDE\\u0CE0-\\u0CE3\\u0CE6-\\u0CEF\\u0CF1\\u0CF2\\u0D01-\\u0D03\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4E\\u0D54-\\u0D57\\u0D5F-\\u0D63\\u0D66-\\u0D6F\\u0D7A-\\u0D7F\\u0D82\\u0D83\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E01-\\u0E3A\\u0E40-\\u0E4E\\u0E50-\\u0E59\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB9\\u0EBB-\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EC8-\\u0ECD\\u0ED0-\\u0ED9\\u0EDC-\\u0EDF\\u0F00\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E-\\u0F47\\u0F49-\\u0F6C\\u0F71-\\u0F84\\u0F86-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u1000-\\u1049\\u1050-\\u109D\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u135D-\\u135F\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1714\\u1720-\\u1734\\u1740-\\u1753\\u1760-\\u176C\\u176E-\\u1770\\u1772\\u1773\\u1780-\\u17D3\\u17D7\\u17DC\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u1810-\\u1819\\u1820-\\u1877\\u1880-\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u19D0-\\u19D9\\u1A00-\\u1A1B\\u1A20-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AA7\\u1AB0-\\u1ABD\\u1B00-\\u1B4B\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1BF3\\u1C00-\\u1C37\\u1C40-\\u1C49\\u1C4D-\\u1C7D\\u1C80-\\u1C88\\u1CD0-\\u1CD2\\u1CD4-\\u1CF6\\u1CF8\\u1CF9\\u1D00-\\u1DF5\\u1DFB-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u200C\\u200D\\u203F\\u2040\\u2054\\u2071\\u207F\\u2090-\\u209C\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D7F-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2DE0-\\u2DFF\\u2E2F\\u3005-\\u3007\\u3021-\\u302F\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u3099\\u309A\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FD5\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66F\\uA674-\\uA67D\\uA67F-\\uA6F1\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA827\\uA840-\\uA873\\uA880-\\uA8C5\\uA8D0-\\uA8D9\\uA8E0-\\uA8F7\\uA8FB\\uA8FD\\uA900-\\uA92D\\uA930-\\uA953\\uA960-\\uA97C\\uA980-\\uA9C0\\uA9CF-\\uA9D9\\uA9E0-\\uA9FE\\uAA00-\\uAA36\\uAA40-\\uAA4D\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A-\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEF\\uAAF2-\\uAAF6\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF3F\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]/\n    };\n\n    ES6Regex = {\n        // ECMAScript 6/Unicode v9.0.0 NonAsciiIdentifierStart:\n        NonAsciiIdentifierStart: /[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u08A0-\\u08B4\\u08B6-\\u08BD\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C88\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2118-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309B-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FD5\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF30-\\uDF4A\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE33\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2]|\\uD804[\\uDC03-\\uDC37\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDF00-\\uDF19]|\\uD806[\\uDCA0-\\uDCDF\\uDCFF\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDF00-\\uDF44\\uDF50\\uDF93-\\uDF9F\\uDFE0]|\\uD821[\\uDC00-\\uDFEC]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00\\uDC01]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1]|\\uD87E[\\uDC00-\\uDE1D]/,\n        // ECMAScript 6/Unicode v9.0.0 NonAsciiIdentifierPart:\n        NonAsciiIdentifierPart: /[\\xAA\\xB5\\xB7\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0300-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u0483-\\u0487\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0610-\\u061A\\u0620-\\u0669\\u066E-\\u06D3\\u06D5-\\u06DC\\u06DF-\\u06E8\\u06EA-\\u06FC\\u06FF\\u0710-\\u074A\\u074D-\\u07B1\\u07C0-\\u07F5\\u07FA\\u0800-\\u082D\\u0840-\\u085B\\u08A0-\\u08B4\\u08B6-\\u08BD\\u08D4-\\u08E1\\u08E3-\\u0963\\u0966-\\u096F\\u0971-\\u0983\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BC-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CE\\u09D7\\u09DC\\u09DD\\u09DF-\\u09E3\\u09E6-\\u09F1\\u0A01-\\u0A03\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A59-\\u0A5C\\u0A5E\\u0A66-\\u0A75\\u0A81-\\u0A83\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABC-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AD0\\u0AE0-\\u0AE3\\u0AE6-\\u0AEF\\u0AF9\\u0B01-\\u0B03\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3C-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B5C\\u0B5D\\u0B5F-\\u0B63\\u0B66-\\u0B6F\\u0B71\\u0B82\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD0\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C03\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C58-\\u0C5A\\u0C60-\\u0C63\\u0C66-\\u0C6F\\u0C80-\\u0C83\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBC-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CDE\\u0CE0-\\u0CE3\\u0CE6-\\u0CEF\\u0CF1\\u0CF2\\u0D01-\\u0D03\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4E\\u0D54-\\u0D57\\u0D5F-\\u0D63\\u0D66-\\u0D6F\\u0D7A-\\u0D7F\\u0D82\\u0D83\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E01-\\u0E3A\\u0E40-\\u0E4E\\u0E50-\\u0E59\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB9\\u0EBB-\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EC8-\\u0ECD\\u0ED0-\\u0ED9\\u0EDC-\\u0EDF\\u0F00\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E-\\u0F47\\u0F49-\\u0F6C\\u0F71-\\u0F84\\u0F86-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u1000-\\u1049\\u1050-\\u109D\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u135D-\\u135F\\u1369-\\u1371\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1714\\u1720-\\u1734\\u1740-\\u1753\\u1760-\\u176C\\u176E-\\u1770\\u1772\\u1773\\u1780-\\u17D3\\u17D7\\u17DC\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u1810-\\u1819\\u1820-\\u1877\\u1880-\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u19D0-\\u19DA\\u1A00-\\u1A1B\\u1A20-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AA7\\u1AB0-\\u1ABD\\u1B00-\\u1B4B\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1BF3\\u1C00-\\u1C37\\u1C40-\\u1C49\\u1C4D-\\u1C7D\\u1C80-\\u1C88\\u1CD0-\\u1CD2\\u1CD4-\\u1CF6\\u1CF8\\u1CF9\\u1D00-\\u1DF5\\u1DFB-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u200C\\u200D\\u203F\\u2040\\u2054\\u2071\\u207F\\u2090-\\u209C\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2118-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D7F-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2DE0-\\u2DFF\\u3005-\\u3007\\u3021-\\u302F\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u3099-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FD5\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66F\\uA674-\\uA67D\\uA67F-\\uA6F1\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7AE\\uA7B0-\\uA7B7\\uA7F7-\\uA827\\uA840-\\uA873\\uA880-\\uA8C5\\uA8D0-\\uA8D9\\uA8E0-\\uA8F7\\uA8FB\\uA8FD\\uA900-\\uA92D\\uA930-\\uA953\\uA960-\\uA97C\\uA980-\\uA9C0\\uA9CF-\\uA9D9\\uA9E0-\\uA9FE\\uAA00-\\uAA36\\uAA40-\\uAA4D\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A-\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEF\\uAAF2-\\uAAF6\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB65\\uAB70-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF3F\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDDFD\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDEE0\\uDF00-\\uDF1F\\uDF30-\\uDF4A\\uDF50-\\uDF7A\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCA0-\\uDCA9\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00-\\uDE03\\uDE05\\uDE06\\uDE0C-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE33\\uDE38-\\uDE3A\\uDE3F\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE6\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2]|\\uD804[\\uDC00-\\uDC46\\uDC66-\\uDC6F\\uDC7F-\\uDCBA\\uDCD0-\\uDCE8\\uDCF0-\\uDCF9\\uDD00-\\uDD34\\uDD36-\\uDD3F\\uDD50-\\uDD73\\uDD76\\uDD80-\\uDDC4\\uDDCA-\\uDDCC\\uDDD0-\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE37\\uDE3E\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEEA\\uDEF0-\\uDEF9\\uDF00-\\uDF03\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3C-\\uDF44\\uDF47\\uDF48\\uDF4B-\\uDF4D\\uDF50\\uDF57\\uDF5D-\\uDF63\\uDF66-\\uDF6C\\uDF70-\\uDF74]|\\uD805[\\uDC00-\\uDC4A\\uDC50-\\uDC59\\uDC80-\\uDCC5\\uDCC7\\uDCD0-\\uDCD9\\uDD80-\\uDDB5\\uDDB8-\\uDDC0\\uDDD8-\\uDDDD\\uDE00-\\uDE40\\uDE44\\uDE50-\\uDE59\\uDE80-\\uDEB7\\uDEC0-\\uDEC9\\uDF00-\\uDF19\\uDF1D-\\uDF2B\\uDF30-\\uDF39]|\\uD806[\\uDCA0-\\uDCE9\\uDCFF\\uDEC0-\\uDEF8]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC36\\uDC38-\\uDC40\\uDC50-\\uDC59\\uDC72-\\uDC8F\\uDC92-\\uDCA7\\uDCA9-\\uDCB6]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|[\\uD80C\\uD81C-\\uD820\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2E]|\\uD811[\\uDC00-\\uDE46]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE60-\\uDE69\\uDED0-\\uDEED\\uDEF0-\\uDEF4\\uDF00-\\uDF36\\uDF40-\\uDF43\\uDF50-\\uDF59\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDF00-\\uDF44\\uDF50-\\uDF7E\\uDF8F-\\uDF9F\\uDFE0]|\\uD821[\\uDC00-\\uDFEC]|\\uD822[\\uDC00-\\uDEF2]|\\uD82C[\\uDC00\\uDC01]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99\\uDC9D\\uDC9E]|\\uD834[\\uDD65-\\uDD69\\uDD6D-\\uDD72\\uDD7B-\\uDD82\\uDD85-\\uDD8B\\uDDAA-\\uDDAD\\uDE42-\\uDE44]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB\\uDFCE-\\uDFFF]|\\uD836[\\uDE00-\\uDE36\\uDE3B-\\uDE6C\\uDE75\\uDE84\\uDE9B-\\uDE9F\\uDEA1-\\uDEAF]|\\uD838[\\uDC00-\\uDC06\\uDC08-\\uDC18\\uDC1B-\\uDC21\\uDC23\\uDC24\\uDC26-\\uDC2A]|\\uD83A[\\uDC00-\\uDCC4\\uDCD0-\\uDCD6\\uDD00-\\uDD4A\\uDD50-\\uDD59]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDED6\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF34\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1]|\\uD87E[\\uDC00-\\uDE1D]|\\uDB40[\\uDD00-\\uDDEF]/\n    };\n\n    function isDecimalDigit(ch) {\n        return 0x30 <= ch && ch <= 0x39;  // 0..9\n    }\n\n    function isHexDigit(ch) {\n        return 0x30 <= ch && ch <= 0x39 ||  // 0..9\n            0x61 <= ch && ch <= 0x66 ||     // a..f\n            0x41 <= ch && ch <= 0x46;       // A..F\n    }\n\n    function isOctalDigit(ch) {\n        return ch >= 0x30 && ch <= 0x37;  // 0..7\n    }\n\n    // 7.2 White Space\n\n    NON_ASCII_WHITESPACES = [\n        0x1680,\n        0x2000, 0x2001, 0x2002, 0x2003, 0x2004, 0x2005, 0x2006, 0x2007, 0x2008, 0x2009, 0x200A,\n        0x202F, 0x205F,\n        0x3000,\n        0xFEFF\n    ];\n\n    function isWhiteSpace(ch) {\n        return ch === 0x20 || ch === 0x09 || ch === 0x0B || ch === 0x0C || ch === 0xA0 ||\n            ch >= 0x1680 && NON_ASCII_WHITESPACES.indexOf(ch) >= 0;\n    }\n\n    // 7.3 Line Terminators\n\n    function isLineTerminator(ch) {\n        return ch === 0x0A || ch === 0x0D || ch === 0x2028 || ch === 0x2029;\n    }\n\n    // 7.6 Identifier Names and Identifiers\n\n    function fromCodePoint(cp) {\n        if (cp <= 0xFFFF) { return String.fromCharCode(cp); }\n        var cu1 = String.fromCharCode(Math.floor((cp - 0x10000) / 0x400) + 0xD800);\n        var cu2 = String.fromCharCode(((cp - 0x10000) % 0x400) + 0xDC00);\n        return cu1 + cu2;\n    }\n\n    IDENTIFIER_START = new Array(0x80);\n    for(ch = 0; ch < 0x80; ++ch) {\n        IDENTIFIER_START[ch] =\n            ch >= 0x61 && ch <= 0x7A ||  // a..z\n            ch >= 0x41 && ch <= 0x5A ||  // A..Z\n            ch === 0x24 || ch === 0x5F;  // $ (dollar) and _ (underscore)\n    }\n\n    IDENTIFIER_PART = new Array(0x80);\n    for(ch = 0; ch < 0x80; ++ch) {\n        IDENTIFIER_PART[ch] =\n            ch >= 0x61 && ch <= 0x7A ||  // a..z\n            ch >= 0x41 && ch <= 0x5A ||  // A..Z\n            ch >= 0x30 && ch <= 0x39 ||  // 0..9\n            ch === 0x24 || ch === 0x5F;  // $ (dollar) and _ (underscore)\n    }\n\n    function isIdentifierStartES5(ch) {\n        return ch < 0x80 ? IDENTIFIER_START[ch] : ES5Regex.NonAsciiIdentifierStart.test(fromCodePoint(ch));\n    }\n\n    function isIdentifierPartES5(ch) {\n        return ch < 0x80 ? IDENTIFIER_PART[ch] : ES5Regex.NonAsciiIdentifierPart.test(fromCodePoint(ch));\n    }\n\n    function isIdentifierStartES6(ch) {\n        return ch < 0x80 ? IDENTIFIER_START[ch] : ES6Regex.NonAsciiIdentifierStart.test(fromCodePoint(ch));\n    }\n\n    function isIdentifierPartES6(ch) {\n        return ch < 0x80 ? IDENTIFIER_PART[ch] : ES6Regex.NonAsciiIdentifierPart.test(fromCodePoint(ch));\n    }\n\n    module.exports = {\n        isDecimalDigit: isDecimalDigit,\n        isHexDigit: isHexDigit,\n        isOctalDigit: isOctalDigit,\n        isWhiteSpace: isWhiteSpace,\n        isLineTerminator: isLineTerminator,\n        isIdentifierStartES5: isIdentifierStartES5,\n        isIdentifierPartES5: isIdentifierPartES5,\n        isIdentifierStartES6: isIdentifierStartES6,\n        isIdentifierPartES6: isIdentifierPartES6\n    };\n}());\n/* vim: set sw=4 ts=4 et tw=80 : */\n", "/*\n  Copyright (C) 2013 <PERSON><PERSON> <<EMAIL>>\n\n  Redistribution and use in source and binary forms, with or without\n  modification, are permitted provided that the following conditions are met:\n\n    * Redistributions of source code must retain the above copyright\n      notice, this list of conditions and the following disclaimer.\n    * Redistributions in binary form must reproduce the above copyright\n      notice, this list of conditions and the following disclaimer in the\n      documentation and/or other materials provided with the distribution.\n\n  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n  ARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES;\n  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n  THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n\n(function () {\n    'use strict';\n\n    var code = require('./code');\n\n    function isStrictModeReservedWordES6(id) {\n        switch (id) {\n        case 'implements':\n        case 'interface':\n        case 'package':\n        case 'private':\n        case 'protected':\n        case 'public':\n        case 'static':\n        case 'let':\n            return true;\n        default:\n            return false;\n        }\n    }\n\n    function isKeywordES5(id, strict) {\n        // yield should not be treated as keyword under non-strict mode.\n        if (!strict && id === 'yield') {\n            return false;\n        }\n        return isKeywordES6(id, strict);\n    }\n\n    function isKeywordES6(id, strict) {\n        if (strict && isStrictModeReservedWordES6(id)) {\n            return true;\n        }\n\n        switch (id.length) {\n        case 2:\n            return (id === 'if') || (id === 'in') || (id === 'do');\n        case 3:\n            return (id === 'var') || (id === 'for') || (id === 'new') || (id === 'try');\n        case 4:\n            return (id === 'this') || (id === 'else') || (id === 'case') ||\n                (id === 'void') || (id === 'with') || (id === 'enum');\n        case 5:\n            return (id === 'while') || (id === 'break') || (id === 'catch') ||\n                (id === 'throw') || (id === 'const') || (id === 'yield') ||\n                (id === 'class') || (id === 'super');\n        case 6:\n            return (id === 'return') || (id === 'typeof') || (id === 'delete') ||\n                (id === 'switch') || (id === 'export') || (id === 'import');\n        case 7:\n            return (id === 'default') || (id === 'finally') || (id === 'extends');\n        case 8:\n            return (id === 'function') || (id === 'continue') || (id === 'debugger');\n        case 10:\n            return (id === 'instanceof');\n        default:\n            return false;\n        }\n    }\n\n    function isReservedWordES5(id, strict) {\n        return id === 'null' || id === 'true' || id === 'false' || isKeywordES5(id, strict);\n    }\n\n    function isReservedWordES6(id, strict) {\n        return id === 'null' || id === 'true' || id === 'false' || isKeywordES6(id, strict);\n    }\n\n    function isRestrictedWord(id) {\n        return id === 'eval' || id === 'arguments';\n    }\n\n    function isIdentifierNameES5(id) {\n        var i, iz, ch;\n\n        if (id.length === 0) { return false; }\n\n        ch = id.charCodeAt(0);\n        if (!code.isIdentifierStartES5(ch)) {\n            return false;\n        }\n\n        for (i = 1, iz = id.length; i < iz; ++i) {\n            ch = id.charCodeAt(i);\n            if (!code.isIdentifierPartES5(ch)) {\n                return false;\n            }\n        }\n        return true;\n    }\n\n    function decodeUtf16(lead, trail) {\n        return (lead - 0xD800) * 0x400 + (trail - 0xDC00) + 0x10000;\n    }\n\n    function isIdentifierNameES6(id) {\n        var i, iz, ch, lowCh, check;\n\n        if (id.length === 0) { return false; }\n\n        check = code.isIdentifierStartES6;\n        for (i = 0, iz = id.length; i < iz; ++i) {\n            ch = id.charCodeAt(i);\n            if (0xD800 <= ch && ch <= 0xDBFF) {\n                ++i;\n                if (i >= iz) { return false; }\n                lowCh = id.charCodeAt(i);\n                if (!(0xDC00 <= lowCh && lowCh <= 0xDFFF)) {\n                    return false;\n                }\n                ch = decodeUtf16(ch, lowCh);\n            }\n            if (!check(ch)) {\n                return false;\n            }\n            check = code.isIdentifierPartES6;\n        }\n        return true;\n    }\n\n    function isIdentifierES5(id, strict) {\n        return isIdentifierNameES5(id) && !isReservedWordES5(id, strict);\n    }\n\n    function isIdentifierES6(id, strict) {\n        return isIdentifierNameES6(id) && !isReservedWordES6(id, strict);\n    }\n\n    module.exports = {\n        isKeywordES5: isKeywordES5,\n        isKeywordES6: isKeywordES6,\n        isReservedWordES5: isReservedWordES5,\n        isReservedWordES6: isReservedWordES6,\n        isRestrictedWord: isRestrictedWord,\n        isIdentifierNameES5: isIdentifierNameES5,\n        isIdentifierNameES6: isIdentifierNameES6,\n        isIdentifierES5: isIdentifierES5,\n        isIdentifierES6: isIdentifierES6\n    };\n}());\n/* vim: set sw=4 ts=4 et tw=80 : */\n", "/*\n  Copyright (C) 2013 <PERSON><PERSON> <<EMAIL>>\n\n  Redistribution and use in source and binary forms, with or without\n  modification, are permitted provided that the following conditions are met:\n\n    * Redistributions of source code must retain the above copyright\n      notice, this list of conditions and the following disclaimer.\n    * Redistributions in binary form must reproduce the above copyright\n      notice, this list of conditions and the following disclaimer in the\n      documentation and/or other materials provided with the distribution.\n\n  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n  ARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES;\n  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n  THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n\n\n(function () {\n    'use strict';\n\n    exports.ast = require('./ast');\n    exports.code = require('./code');\n    exports.keyword = require('./keyword');\n}());\n/* vim: set sw=4 ts=4 et tw=80 : */\n", "{\n  \"name\": \"doctrine\",\n  \"description\": \"JSDoc parser\",\n  \"homepage\": \"https://github.com/eslint/doctrine\",\n  \"main\": \"lib/doctrine.js\",\n  \"version\": \"3.0.0\",\n  \"engines\": {\n    \"node\": \">=6.0.0\"\n  },\n  \"directories\": {\n    \"lib\": \"./lib\"\n  },\n  \"files\": [\n    \"lib\"\n  ],\n  \"maintainers\": [\n    {\n      \"name\": \"<PERSON>\",\n      \"email\": \"<EMAIL>\",\n      \"web\": \"https://www.nczonline.net\"\n    },\n    {\n      \"name\": \"<PERSON><PERSON>\",\n      \"email\": \"<EMAIL>\",\n      \"web\": \"https://github.com/Constellation\"\n    }\n  ],\n  \"repository\": \"eslint/doctrine\",\n  \"devDependencies\": {\n    \"coveralls\": \"^3.0.1\",\n    \"dateformat\": \"^1.0.11\",\n    \"eslint\": \"^1.10.3\",\n    \"eslint-release\": \"^1.0.0\",\n    \"linefix\": \"^0.1.1\",\n    \"mocha\": \"^3.4.2\",\n    \"npm-license\": \"^0.3.1\",\n    \"nyc\": \"^10.3.2\",\n    \"semver\": \"^5.0.3\",\n    \"shelljs\": \"^0.5.3\",\n    \"shelljs-nodecli\": \"^0.1.1\",\n    \"should\": \"^5.0.1\"\n  },\n  \"license\": \"Apache-2.0\",\n  \"scripts\": {\n    \"pretest\": \"npm run lint\",\n    \"test\": \"nyc mocha\",\n    \"coveralls\": \"nyc report --reporter=text-lcov | coveralls\",\n    \"lint\": \"eslint lib/\",\n    \"generate-release\": \"eslint-generate-release\",\n    \"generate-alpharelease\": \"eslint-generate-prerelease alpha\",\n    \"generate-betarelease\": \"eslint-generate-prerelease beta\",\n    \"generate-rcrelease\": \"eslint-generate-prerelease rc\",\n    \"publish-release\": \"eslint-publish-release\"\n  },\n  \"dependencies\": {\n    \"esutils\": \"^2.0.2\"\n  }\n}\n", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"assert\" has been externalized for browser compatibility. Cannot access \"assert.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "/*\n * @fileoverview Utilities for Doctrine\n * <AUTHOR> <<EMAIL>>\n */\n\n\n(function () {\n    'use strict';\n\n    var VERSION;\n\n    VERSION = require('../package.json').version;\n    exports.VERSION = VERSION;\n\n    function DoctrineError(message) {\n        this.name = 'DoctrineError';\n        this.message = message;\n    }\n    DoctrineError.prototype = (function () {\n        var Middle = function () { };\n        Middle.prototype = Error.prototype;\n        return new Middle();\n    }());\n    DoctrineError.prototype.constructor = DoctrineError;\n    exports.DoctrineError = DoctrineError;\n\n    function throwError(message) {\n        throw new DoctrineError(message);\n    }\n    exports.throwError = throwError;\n\n    exports.assert = require('assert');\n}());\n\n/* vim: set sw=4 ts=4 et tw=80 : */\n", "/*\n * @fileoverview Type expression parser.\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n */\n\n// \"typed\", the Type Expression Parser for doctrine.\n\n(function () {\n    'use strict';\n\n    var Syntax,\n        Token,\n        source,\n        length,\n        index,\n        previous,\n        token,\n        value,\n        esutils,\n        utility,\n        rangeOffset,\n        addRange;\n\n    esutils = require('esutils');\n    utility = require('./utility');\n\n    Syntax = {\n        NullableLiteral: 'NullableLiteral',\n        AllLiteral: 'AllLiteral',\n        NullLiteral: 'NullLiteral',\n        UndefinedLiteral: 'UndefinedLiteral',\n        VoidLiteral: 'VoidLiteral',\n        UnionType: 'UnionType',\n        ArrayType: 'ArrayType',\n        RecordType: 'RecordType',\n        FieldType: 'FieldType',\n        FunctionType: 'FunctionType',\n        ParameterType: 'ParameterType',\n        RestType: 'RestType',\n        NonNullableType: 'NonNullableType',\n        OptionalType: 'OptionalType',\n        NullableType: 'NullableType',\n        NameExpression: 'NameExpression',\n        TypeApplication: 'TypeApplication',\n        StringLiteralType: 'StringLiteralType',\n        NumericLiteralType: 'NumericLiteralType',\n        BooleanLiteralType: 'BooleanLiteralType'\n    };\n\n    Token = {\n        ILLEGAL: 0,    // ILLEGAL\n        DOT_LT: 1,     // .<\n        REST: 2,       // ...\n        LT: 3,         // <\n        GT: 4,         // >\n        LPAREN: 5,     // (\n        RPAREN: 6,     // )\n        LBRACE: 7,     // {\n        RBRACE: 8,     // }\n        LBRACK: 9,    // [\n        RBRACK: 10,    // ]\n        COMMA: 11,     // ,\n        COLON: 12,     // :\n        STAR: 13,      // *\n        PIPE: 14,      // |\n        QUESTION: 15,  // ?\n        BANG: 16,      // !\n        EQUAL: 17,     // =\n        NAME: 18,      // name token\n        STRING: 19,    // string\n        NUMBER: 20,    // number\n        EOF: 21\n    };\n\n    function isTypeName(ch) {\n        return '><(){}[],:*|?!='.indexOf(String.fromCharCode(ch)) === -1 && !esutils.code.isWhiteSpace(ch) && !esutils.code.isLineTerminator(ch);\n    }\n\n    function Context(previous, index, token, value) {\n        this._previous = previous;\n        this._index = index;\n        this._token = token;\n        this._value = value;\n    }\n\n    Context.prototype.restore = function () {\n        previous = this._previous;\n        index = this._index;\n        token = this._token;\n        value = this._value;\n    };\n\n    Context.save = function () {\n        return new Context(previous, index, token, value);\n    };\n\n    function maybeAddRange(node, range) {\n        if (addRange) {\n            node.range = [range[0] + rangeOffset, range[1] + rangeOffset];\n        }\n        return node;\n    }\n\n    function advance() {\n        var ch = source.charAt(index);\n        index += 1;\n        return ch;\n    }\n\n    function scanHexEscape(prefix) {\n        var i, len, ch, code = 0;\n\n        len = (prefix === 'u') ? 4 : 2;\n        for (i = 0; i < len; ++i) {\n            if (index < length && esutils.code.isHexDigit(source.charCodeAt(index))) {\n                ch = advance();\n                code = code * 16 + '0123456789abcdef'.indexOf(ch.toLowerCase());\n            } else {\n                return '';\n            }\n        }\n        return String.fromCharCode(code);\n    }\n\n    function scanString() {\n        var str = '', quote, ch, code, unescaped, restore; //TODO review removal octal = false\n        quote = source.charAt(index);\n        ++index;\n\n        while (index < length) {\n            ch = advance();\n\n            if (ch === quote) {\n                quote = '';\n                break;\n            } else if (ch === '\\\\') {\n                ch = advance();\n                if (!esutils.code.isLineTerminator(ch.charCodeAt(0))) {\n                    switch (ch) {\n                    case 'n':\n                        str += '\\n';\n                        break;\n                    case 'r':\n                        str += '\\r';\n                        break;\n                    case 't':\n                        str += '\\t';\n                        break;\n                    case 'u':\n                    case 'x':\n                        restore = index;\n                        unescaped = scanHexEscape(ch);\n                        if (unescaped) {\n                            str += unescaped;\n                        } else {\n                            index = restore;\n                            str += ch;\n                        }\n                        break;\n                    case 'b':\n                        str += '\\b';\n                        break;\n                    case 'f':\n                        str += '\\f';\n                        break;\n                    case 'v':\n                        str += '\\v';\n                        break;\n\n                    default:\n                        if (esutils.code.isOctalDigit(ch.charCodeAt(0))) {\n                            code = '01234567'.indexOf(ch);\n\n                            // \\0 is not octal escape sequence\n                            // Deprecating unused code. TODO review removal\n                            //if (code !== 0) {\n                            //    octal = true;\n                            //}\n\n                            if (index < length && esutils.code.isOctalDigit(source.charCodeAt(index))) {\n                                //TODO Review Removal octal = true;\n                                code = code * 8 + '01234567'.indexOf(advance());\n\n                                // 3 digits are only allowed when string starts\n                                // with 0, 1, 2, 3\n                                if ('0123'.indexOf(ch) >= 0 &&\n                                        index < length &&\n                                        esutils.code.isOctalDigit(source.charCodeAt(index))) {\n                                    code = code * 8 + '01234567'.indexOf(advance());\n                                }\n                            }\n                            str += String.fromCharCode(code);\n                        } else {\n                            str += ch;\n                        }\n                        break;\n                    }\n                } else {\n                    if (ch ===  '\\r' && source.charCodeAt(index) === 0x0A  /* '\\n' */) {\n                        ++index;\n                    }\n                }\n            } else if (esutils.code.isLineTerminator(ch.charCodeAt(0))) {\n                break;\n            } else {\n                str += ch;\n            }\n        }\n\n        if (quote !== '') {\n            utility.throwError('unexpected quote');\n        }\n\n        value = str;\n        return Token.STRING;\n    }\n\n    function scanNumber() {\n        var number, ch;\n\n        number = '';\n        ch = source.charCodeAt(index);\n\n        if (ch !== 0x2E  /* '.' */) {\n            number = advance();\n            ch = source.charCodeAt(index);\n\n            if (number === '0') {\n                if (ch === 0x78  /* 'x' */ || ch === 0x58  /* 'X' */) {\n                    number += advance();\n                    while (index < length) {\n                        ch = source.charCodeAt(index);\n                        if (!esutils.code.isHexDigit(ch)) {\n                            break;\n                        }\n                        number += advance();\n                    }\n\n                    if (number.length <= 2) {\n                        // only 0x\n                        utility.throwError('unexpected token');\n                    }\n\n                    if (index < length) {\n                        ch = source.charCodeAt(index);\n                        if (esutils.code.isIdentifierStartES5(ch)) {\n                            utility.throwError('unexpected token');\n                        }\n                    }\n                    value = parseInt(number, 16);\n                    return Token.NUMBER;\n                }\n\n                if (esutils.code.isOctalDigit(ch)) {\n                    number += advance();\n                    while (index < length) {\n                        ch = source.charCodeAt(index);\n                        if (!esutils.code.isOctalDigit(ch)) {\n                            break;\n                        }\n                        number += advance();\n                    }\n\n                    if (index < length) {\n                        ch = source.charCodeAt(index);\n                        if (esutils.code.isIdentifierStartES5(ch) || esutils.code.isDecimalDigit(ch)) {\n                            utility.throwError('unexpected token');\n                        }\n                    }\n                    value = parseInt(number, 8);\n                    return Token.NUMBER;\n                }\n\n                if (esutils.code.isDecimalDigit(ch)) {\n                    utility.throwError('unexpected token');\n                }\n            }\n\n            while (index < length) {\n                ch = source.charCodeAt(index);\n                if (!esutils.code.isDecimalDigit(ch)) {\n                    break;\n                }\n                number += advance();\n            }\n        }\n\n        if (ch === 0x2E  /* '.' */) {\n            number += advance();\n            while (index < length) {\n                ch = source.charCodeAt(index);\n                if (!esutils.code.isDecimalDigit(ch)) {\n                    break;\n                }\n                number += advance();\n            }\n        }\n\n        if (ch === 0x65  /* 'e' */ || ch === 0x45  /* 'E' */) {\n            number += advance();\n\n            ch = source.charCodeAt(index);\n            if (ch === 0x2B  /* '+' */ || ch === 0x2D  /* '-' */) {\n                number += advance();\n            }\n\n            ch = source.charCodeAt(index);\n            if (esutils.code.isDecimalDigit(ch)) {\n                number += advance();\n                while (index < length) {\n                    ch = source.charCodeAt(index);\n                    if (!esutils.code.isDecimalDigit(ch)) {\n                        break;\n                    }\n                    number += advance();\n                }\n            } else {\n                utility.throwError('unexpected token');\n            }\n        }\n\n        if (index < length) {\n            ch = source.charCodeAt(index);\n            if (esutils.code.isIdentifierStartES5(ch)) {\n                utility.throwError('unexpected token');\n            }\n        }\n\n        value = parseFloat(number);\n        return Token.NUMBER;\n    }\n\n\n    function scanTypeName() {\n        var ch, ch2;\n\n        value = advance();\n        while (index < length && isTypeName(source.charCodeAt(index))) {\n            ch = source.charCodeAt(index);\n            if (ch === 0x2E  /* '.' */) {\n                if ((index + 1) >= length) {\n                    return Token.ILLEGAL;\n                }\n                ch2 = source.charCodeAt(index + 1);\n                if (ch2 === 0x3C  /* '<' */) {\n                    break;\n                }\n            }\n            value += advance();\n        }\n        return Token.NAME;\n    }\n\n    function next() {\n        var ch;\n\n        previous = index;\n\n        while (index < length && esutils.code.isWhiteSpace(source.charCodeAt(index))) {\n            advance();\n        }\n        if (index >= length) {\n            token = Token.EOF;\n            return token;\n        }\n\n        ch = source.charCodeAt(index);\n        switch (ch) {\n        case 0x27:  /* ''' */\n        case 0x22:  /* '\"' */\n            token = scanString();\n            return token;\n\n        case 0x3A:  /* ':' */\n            advance();\n            token = Token.COLON;\n            return token;\n\n        case 0x2C:  /* ',' */\n            advance();\n            token = Token.COMMA;\n            return token;\n\n        case 0x28:  /* '(' */\n            advance();\n            token = Token.LPAREN;\n            return token;\n\n        case 0x29:  /* ')' */\n            advance();\n            token = Token.RPAREN;\n            return token;\n\n        case 0x5B:  /* '[' */\n            advance();\n            token = Token.LBRACK;\n            return token;\n\n        case 0x5D:  /* ']' */\n            advance();\n            token = Token.RBRACK;\n            return token;\n\n        case 0x7B:  /* '{' */\n            advance();\n            token = Token.LBRACE;\n            return token;\n\n        case 0x7D:  /* '}' */\n            advance();\n            token = Token.RBRACE;\n            return token;\n\n        case 0x2E:  /* '.' */\n            if (index + 1 < length) {\n                ch = source.charCodeAt(index + 1);\n                if (ch === 0x3C  /* '<' */) {\n                    advance();  // '.'\n                    advance();  // '<'\n                    token = Token.DOT_LT;\n                    return token;\n                }\n\n                if (ch === 0x2E  /* '.' */ && index + 2 < length && source.charCodeAt(index + 2) === 0x2E  /* '.' */) {\n                    advance();  // '.'\n                    advance();  // '.'\n                    advance();  // '.'\n                    token = Token.REST;\n                    return token;\n                }\n\n                if (esutils.code.isDecimalDigit(ch)) {\n                    token = scanNumber();\n                    return token;\n                }\n            }\n            token = Token.ILLEGAL;\n            return token;\n\n        case 0x3C:  /* '<' */\n            advance();\n            token = Token.LT;\n            return token;\n\n        case 0x3E:  /* '>' */\n            advance();\n            token = Token.GT;\n            return token;\n\n        case 0x2A:  /* '*' */\n            advance();\n            token = Token.STAR;\n            return token;\n\n        case 0x7C:  /* '|' */\n            advance();\n            token = Token.PIPE;\n            return token;\n\n        case 0x3F:  /* '?' */\n            advance();\n            token = Token.QUESTION;\n            return token;\n\n        case 0x21:  /* '!' */\n            advance();\n            token = Token.BANG;\n            return token;\n\n        case 0x3D:  /* '=' */\n            advance();\n            token = Token.EQUAL;\n            return token;\n\n        case 0x2D: /* '-' */\n            token = scanNumber();\n            return token;\n\n        default:\n            if (esutils.code.isDecimalDigit(ch)) {\n                token = scanNumber();\n                return token;\n            }\n\n            // type string permits following case,\n            //\n            // namespace.module.MyClass\n            //\n            // this reduced 1 token TK_NAME\n            utility.assert(isTypeName(ch));\n            token = scanTypeName();\n            return token;\n        }\n    }\n\n    function consume(target, text) {\n        utility.assert(token === target, text || 'consumed token not matched');\n        next();\n    }\n\n    function expect(target, message) {\n        if (token !== target) {\n            utility.throwError(message || 'unexpected token');\n        }\n        next();\n    }\n\n    // UnionType := '(' TypeUnionList ')'\n    //\n    // TypeUnionList :=\n    //     <<empty>>\n    //   | NonemptyTypeUnionList\n    //\n    // NonemptyTypeUnionList :=\n    //     TypeExpression\n    //   | TypeExpression '|' NonemptyTypeUnionList\n    function parseUnionType() {\n        var elements, startIndex = index - 1;\n        consume(Token.LPAREN, 'UnionType should start with (');\n        elements = [];\n        if (token !== Token.RPAREN) {\n            while (true) {\n                elements.push(parseTypeExpression());\n                if (token === Token.RPAREN) {\n                    break;\n                }\n                expect(Token.PIPE);\n            }\n        }\n        consume(Token.RPAREN, 'UnionType should end with )');\n        return maybeAddRange({\n            type: Syntax.UnionType,\n            elements: elements\n        }, [startIndex, previous]);\n    }\n\n    // ArrayType := '[' ElementTypeList ']'\n    //\n    // ElementTypeList :=\n    //     <<empty>>\n    //  | TypeExpression\n    //  | '...' TypeExpression\n    //  | TypeExpression ',' ElementTypeList\n    function parseArrayType() {\n        var elements, startIndex = index - 1, restStartIndex;\n        consume(Token.LBRACK, 'ArrayType should start with [');\n        elements = [];\n        while (token !== Token.RBRACK) {\n            if (token === Token.REST) {\n                restStartIndex = index - 3;\n                consume(Token.REST);\n                elements.push(maybeAddRange({\n                    type: Syntax.RestType,\n                    expression: parseTypeExpression()\n                }, [restStartIndex, previous]));\n                break;\n            } else {\n                elements.push(parseTypeExpression());\n            }\n            if (token !== Token.RBRACK) {\n                expect(Token.COMMA);\n            }\n        }\n        expect(Token.RBRACK);\n        return maybeAddRange({\n            type: Syntax.ArrayType,\n            elements: elements\n        }, [startIndex, previous]);\n    }\n\n    function parseFieldName() {\n        var v = value;\n        if (token === Token.NAME || token === Token.STRING) {\n            next();\n            return v;\n        }\n\n        if (token === Token.NUMBER) {\n            consume(Token.NUMBER);\n            return String(v);\n        }\n\n        utility.throwError('unexpected token');\n    }\n\n    // FieldType :=\n    //     FieldName\n    //   | FieldName ':' TypeExpression\n    //\n    // FieldName :=\n    //     NameExpression\n    //   | StringLiteral\n    //   | NumberLiteral\n    //   | ReservedIdentifier\n    function parseFieldType() {\n        var key, rangeStart = previous;\n\n        key = parseFieldName();\n        if (token === Token.COLON) {\n            consume(Token.COLON);\n            return maybeAddRange({\n                type: Syntax.FieldType,\n                key: key,\n                value: parseTypeExpression()\n            }, [rangeStart, previous]);\n        }\n        return maybeAddRange({\n            type: Syntax.FieldType,\n            key: key,\n            value: null\n        }, [rangeStart, previous]);\n    }\n\n    // RecordType := '{' FieldTypeList '}'\n    //\n    // FieldTypeList :=\n    //     <<empty>>\n    //   | FieldType\n    //   | FieldType ',' FieldTypeList\n    function parseRecordType() {\n        var fields, rangeStart = index - 1, rangeEnd;\n\n        consume(Token.LBRACE, 'RecordType should start with {');\n        fields = [];\n        if (token === Token.COMMA) {\n            consume(Token.COMMA);\n        } else {\n            while (token !== Token.RBRACE) {\n                fields.push(parseFieldType());\n                if (token !== Token.RBRACE) {\n                    expect(Token.COMMA);\n                }\n            }\n        }\n        rangeEnd = index;\n        expect(Token.RBRACE);\n        return maybeAddRange({\n            type: Syntax.RecordType,\n            fields: fields\n        }, [rangeStart, rangeEnd]);\n    }\n\n    // NameExpression :=\n    //    Identifier\n    //  | TagIdentifier ':' Identifier\n    //\n    // Tag identifier is one of \"module\", \"external\" or \"event\"\n    // Identifier is the same as Token.NAME, including any dots, something like\n    // namespace.module.MyClass\n    function parseNameExpression() {\n        var name = value, rangeStart = index - name.length;\n        expect(Token.NAME);\n\n        if (token === Token.COLON && (\n                name === 'module' ||\n                name === 'external' ||\n                name === 'event')) {\n            consume(Token.COLON);\n            name += ':' + value;\n            expect(Token.NAME);\n        }\n\n        return maybeAddRange({\n            type: Syntax.NameExpression,\n            name: name\n        }, [rangeStart, previous]);\n    }\n\n    // TypeExpressionList :=\n    //     TopLevelTypeExpression\n    //   | TopLevelTypeExpression ',' TypeExpressionList\n    function parseTypeExpressionList() {\n        var elements = [];\n\n        elements.push(parseTop());\n        while (token === Token.COMMA) {\n            consume(Token.COMMA);\n            elements.push(parseTop());\n        }\n        return elements;\n    }\n\n    // TypeName :=\n    //     NameExpression\n    //   | NameExpression TypeApplication\n    //\n    // TypeApplication :=\n    //     '.<' TypeExpressionList '>'\n    //   | '<' TypeExpressionList '>'   // this is extension of doctrine\n    function parseTypeName() {\n        var expr, applications, startIndex = index - value.length;\n\n        expr = parseNameExpression();\n        if (token === Token.DOT_LT || token === Token.LT) {\n            next();\n            applications = parseTypeExpressionList();\n            expect(Token.GT);\n            return maybeAddRange({\n                type: Syntax.TypeApplication,\n                expression: expr,\n                applications: applications\n            }, [startIndex, previous]);\n        }\n        return expr;\n    }\n\n    // ResultType :=\n    //     <<empty>>\n    //   | ':' void\n    //   | ':' TypeExpression\n    //\n    // BNF is above\n    // but, we remove <<empty>> pattern, so token is always TypeToken::COLON\n    function parseResultType() {\n        consume(Token.COLON, 'ResultType should start with :');\n        if (token === Token.NAME && value === 'void') {\n            consume(Token.NAME);\n            return {\n                type: Syntax.VoidLiteral\n            };\n        }\n        return parseTypeExpression();\n    }\n\n    // ParametersType :=\n    //     RestParameterType\n    //   | NonRestParametersType\n    //   | NonRestParametersType ',' RestParameterType\n    //\n    // RestParameterType :=\n    //     '...'\n    //     '...' Identifier\n    //\n    // NonRestParametersType :=\n    //     ParameterType ',' NonRestParametersType\n    //   | ParameterType\n    //   | OptionalParametersType\n    //\n    // OptionalParametersType :=\n    //     OptionalParameterType\n    //   | OptionalParameterType, OptionalParametersType\n    //\n    // OptionalParameterType := ParameterType=\n    //\n    // ParameterType := TypeExpression | Identifier ':' TypeExpression\n    //\n    // Identifier is \"new\" or \"this\"\n    function parseParametersType() {\n        var params = [], optionalSequence = false, expr, rest = false, startIndex, restStartIndex = index - 3, nameStartIndex;\n\n        while (token !== Token.RPAREN) {\n            if (token === Token.REST) {\n                // RestParameterType\n                consume(Token.REST);\n                rest = true;\n            }\n\n            startIndex = previous;\n\n            expr = parseTypeExpression();\n            if (expr.type === Syntax.NameExpression && token === Token.COLON) {\n                nameStartIndex = previous - expr.name.length;\n                // Identifier ':' TypeExpression\n                consume(Token.COLON);\n                expr = maybeAddRange({\n                    type: Syntax.ParameterType,\n                    name: expr.name,\n                    expression: parseTypeExpression()\n                }, [nameStartIndex, previous]);\n            }\n            if (token === Token.EQUAL) {\n                consume(Token.EQUAL);\n                expr = maybeAddRange({\n                    type: Syntax.OptionalType,\n                    expression: expr\n                }, [startIndex, previous]);\n                optionalSequence = true;\n            } else {\n                if (optionalSequence) {\n                    utility.throwError('unexpected token');\n                }\n            }\n            if (rest) {\n                expr = maybeAddRange({\n                    type: Syntax.RestType,\n                    expression: expr\n                }, [restStartIndex, previous]);\n            }\n            params.push(expr);\n            if (token !== Token.RPAREN) {\n                expect(Token.COMMA);\n            }\n        }\n        return params;\n    }\n\n    // FunctionType := 'function' FunctionSignatureType\n    //\n    // FunctionSignatureType :=\n    //   | TypeParameters '(' ')' ResultType\n    //   | TypeParameters '(' ParametersType ')' ResultType\n    //   | TypeParameters '(' 'this' ':' TypeName ')' ResultType\n    //   | TypeParameters '(' 'this' ':' TypeName ',' ParametersType ')' ResultType\n    function parseFunctionType() {\n        var isNew, thisBinding, params, result, fnType, startIndex = index - value.length;\n        utility.assert(token === Token.NAME && value === 'function', 'FunctionType should start with \\'function\\'');\n        consume(Token.NAME);\n\n        // Google Closure Compiler is not implementing TypeParameters.\n        // So we do not. if we don't get '(', we see it as error.\n        expect(Token.LPAREN);\n\n        isNew = false;\n        params = [];\n        thisBinding = null;\n        if (token !== Token.RPAREN) {\n            // ParametersType or 'this'\n            if (token === Token.NAME &&\n                    (value === 'this' || value === 'new')) {\n                // 'this' or 'new'\n                // 'new' is Closure Compiler extension\n                isNew = value === 'new';\n                consume(Token.NAME);\n                expect(Token.COLON);\n                thisBinding = parseTypeName();\n                if (token === Token.COMMA) {\n                    consume(Token.COMMA);\n                    params = parseParametersType();\n                }\n            } else {\n                params = parseParametersType();\n            }\n        }\n\n        expect(Token.RPAREN);\n\n        result = null;\n        if (token === Token.COLON) {\n            result = parseResultType();\n        }\n\n        fnType = maybeAddRange({\n            type: Syntax.FunctionType,\n            params: params,\n            result: result\n        }, [startIndex, previous]);\n        if (thisBinding) {\n            // avoid adding null 'new' and 'this' properties\n            fnType['this'] = thisBinding;\n            if (isNew) {\n                fnType['new'] = true;\n            }\n        }\n        return fnType;\n    }\n\n    // BasicTypeExpression :=\n    //     '*'\n    //   | 'null'\n    //   | 'undefined'\n    //   | TypeName\n    //   | FunctionType\n    //   | UnionType\n    //   | RecordType\n    //   | ArrayType\n    function parseBasicTypeExpression() {\n        var context, startIndex;\n        switch (token) {\n        case Token.STAR:\n            consume(Token.STAR);\n            return maybeAddRange({\n                type: Syntax.AllLiteral\n            }, [previous - 1, previous]);\n\n        case Token.LPAREN:\n            return parseUnionType();\n\n        case Token.LBRACK:\n            return parseArrayType();\n\n        case Token.LBRACE:\n            return parseRecordType();\n\n        case Token.NAME:\n            startIndex = index - value.length;\n\n            if (value === 'null') {\n                consume(Token.NAME);\n                return maybeAddRange({\n                    type: Syntax.NullLiteral\n                }, [startIndex, previous]);\n            }\n\n            if (value === 'undefined') {\n                consume(Token.NAME);\n                return maybeAddRange({\n                    type: Syntax.UndefinedLiteral\n                }, [startIndex, previous]);\n            }\n\n            if (value === 'true' || value === 'false') {\n                consume(Token.NAME);\n                return maybeAddRange({\n                    type: Syntax.BooleanLiteralType,\n                    value: value === 'true'\n                }, [startIndex, previous]);\n            }\n\n            context = Context.save();\n            if (value === 'function') {\n                try {\n                    return parseFunctionType();\n                } catch (e) {\n                    context.restore();\n                }\n            }\n\n            return parseTypeName();\n\n        case Token.STRING:\n            next();\n            return maybeAddRange({\n                type: Syntax.StringLiteralType,\n                value: value\n            }, [previous - value.length - 2, previous]);\n\n        case Token.NUMBER:\n            next();\n            return maybeAddRange({\n                type: Syntax.NumericLiteralType,\n                value: value\n            }, [previous - String(value).length, previous]);\n\n        default:\n            utility.throwError('unexpected token');\n        }\n    }\n\n    // TypeExpression :=\n    //     BasicTypeExpression\n    //   | '?' BasicTypeExpression\n    //   | '!' BasicTypeExpression\n    //   | BasicTypeExpression '?'\n    //   | BasicTypeExpression '!'\n    //   | '?'\n    //   | BasicTypeExpression '[]'\n    function parseTypeExpression() {\n        var expr, rangeStart;\n\n        if (token === Token.QUESTION) {\n            rangeStart = index - 1;\n            consume(Token.QUESTION);\n            if (token === Token.COMMA || token === Token.EQUAL || token === Token.RBRACE ||\n                    token === Token.RPAREN || token === Token.PIPE || token === Token.EOF ||\n                    token === Token.RBRACK || token === Token.GT) {\n                return maybeAddRange({\n                    type: Syntax.NullableLiteral\n                }, [rangeStart, previous]);\n            }\n            return maybeAddRange({\n                type: Syntax.NullableType,\n                expression: parseBasicTypeExpression(),\n                prefix: true\n            }, [rangeStart, previous]);\n        } else if (token === Token.BANG) {\n            rangeStart = index - 1;\n            consume(Token.BANG);\n            return maybeAddRange({\n                type: Syntax.NonNullableType,\n                expression: parseBasicTypeExpression(),\n                prefix: true\n            }, [rangeStart, previous]);\n        } else {\n            rangeStart = previous;\n        }\n\n        expr = parseBasicTypeExpression();\n        if (token === Token.BANG) {\n            consume(Token.BANG);\n            return maybeAddRange({\n                type: Syntax.NonNullableType,\n                expression: expr,\n                prefix: false\n            }, [rangeStart, previous]);\n        }\n\n        if (token === Token.QUESTION) {\n            consume(Token.QUESTION);\n            return maybeAddRange({\n                type: Syntax.NullableType,\n                expression: expr,\n                prefix: false\n            }, [rangeStart, previous]);\n        }\n\n        if (token === Token.LBRACK) {\n            consume(Token.LBRACK);\n            expect(Token.RBRACK, 'expected an array-style type declaration (' + value + '[])');\n            return maybeAddRange({\n                type: Syntax.TypeApplication,\n                expression: maybeAddRange({\n                    type: Syntax.NameExpression,\n                    name: 'Array'\n                }, [rangeStart, previous]),\n                applications: [expr]\n            }, [rangeStart, previous]);\n        }\n\n        return expr;\n    }\n\n    // TopLevelTypeExpression :=\n    //      TypeExpression\n    //    | TypeUnionList\n    //\n    // This rule is Google Closure Compiler extension, not ES4\n    // like,\n    //   { number | string }\n    // If strict to ES4, we should write it as\n    //   { (number|string) }\n    function parseTop() {\n        var expr, elements;\n\n        expr = parseTypeExpression();\n        if (token !== Token.PIPE) {\n            return expr;\n        }\n\n        elements = [expr];\n        consume(Token.PIPE);\n        while (true) {\n            elements.push(parseTypeExpression());\n            if (token !== Token.PIPE) {\n                break;\n            }\n            consume(Token.PIPE);\n        }\n\n        return maybeAddRange({\n            type: Syntax.UnionType,\n            elements: elements\n        }, [0, index]);\n    }\n\n    function parseTopParamType() {\n        var expr;\n\n        if (token === Token.REST) {\n            consume(Token.REST);\n            return maybeAddRange({\n                type: Syntax.RestType,\n                expression: parseTop()\n            }, [0, index]);\n        }\n\n        expr = parseTop();\n        if (token === Token.EQUAL) {\n            consume(Token.EQUAL);\n            return maybeAddRange({\n                type: Syntax.OptionalType,\n                expression: expr\n            }, [0, index]);\n        }\n\n        return expr;\n    }\n\n    function parseType(src, opt) {\n        var expr;\n\n        source = src;\n        length = source.length;\n        index = 0;\n        previous = 0;\n        addRange = opt && opt.range;\n        rangeOffset = opt && opt.startIndex || 0;\n\n        next();\n        expr = parseTop();\n\n        if (opt && opt.midstream) {\n            return {\n                expression: expr,\n                index: previous\n            };\n        }\n\n        if (token !== Token.EOF) {\n            utility.throwError('not reach to EOF');\n        }\n\n        return expr;\n    }\n\n    function parseParamType(src, opt) {\n        var expr;\n\n        source = src;\n        length = source.length;\n        index = 0;\n        previous = 0;\n        addRange = opt && opt.range;\n        rangeOffset = opt && opt.startIndex || 0;\n\n        next();\n        expr = parseTopParamType();\n\n        if (opt && opt.midstream) {\n            return {\n                expression: expr,\n                index: previous\n            };\n        }\n\n        if (token !== Token.EOF) {\n            utility.throwError('not reach to EOF');\n        }\n\n        return expr;\n    }\n\n    function stringifyImpl(node, compact, topLevel) {\n        var result, i, iz;\n\n        switch (node.type) {\n        case Syntax.NullableLiteral:\n            result = '?';\n            break;\n\n        case Syntax.AllLiteral:\n            result = '*';\n            break;\n\n        case Syntax.NullLiteral:\n            result = 'null';\n            break;\n\n        case Syntax.UndefinedLiteral:\n            result = 'undefined';\n            break;\n\n        case Syntax.VoidLiteral:\n            result = 'void';\n            break;\n\n        case Syntax.UnionType:\n            if (!topLevel) {\n                result = '(';\n            } else {\n                result = '';\n            }\n\n            for (i = 0, iz = node.elements.length; i < iz; ++i) {\n                result += stringifyImpl(node.elements[i], compact);\n                if ((i + 1) !== iz) {\n                    result += compact ? '|' : ' | ';\n                }\n            }\n\n            if (!topLevel) {\n                result += ')';\n            }\n            break;\n\n        case Syntax.ArrayType:\n            result = '[';\n            for (i = 0, iz = node.elements.length; i < iz; ++i) {\n                result += stringifyImpl(node.elements[i], compact);\n                if ((i + 1) !== iz) {\n                    result += compact ? ',' : ', ';\n                }\n            }\n            result += ']';\n            break;\n\n        case Syntax.RecordType:\n            result = '{';\n            for (i = 0, iz = node.fields.length; i < iz; ++i) {\n                result += stringifyImpl(node.fields[i], compact);\n                if ((i + 1) !== iz) {\n                    result += compact ? ',' : ', ';\n                }\n            }\n            result += '}';\n            break;\n\n        case Syntax.FieldType:\n            if (node.value) {\n                result = node.key + (compact ? ':' : ': ') + stringifyImpl(node.value, compact);\n            } else {\n                result = node.key;\n            }\n            break;\n\n        case Syntax.FunctionType:\n            result = compact ? 'function(' : 'function (';\n\n            if (node['this']) {\n                if (node['new']) {\n                    result += (compact ? 'new:' : 'new: ');\n                } else {\n                    result += (compact ? 'this:' : 'this: ');\n                }\n\n                result += stringifyImpl(node['this'], compact);\n\n                if (node.params.length !== 0) {\n                    result += compact ? ',' : ', ';\n                }\n            }\n\n            for (i = 0, iz = node.params.length; i < iz; ++i) {\n                result += stringifyImpl(node.params[i], compact);\n                if ((i + 1) !== iz) {\n                    result += compact ? ',' : ', ';\n                }\n            }\n\n            result += ')';\n\n            if (node.result) {\n                result += (compact ? ':' : ': ') + stringifyImpl(node.result, compact);\n            }\n            break;\n\n        case Syntax.ParameterType:\n            result = node.name + (compact ? ':' : ': ') + stringifyImpl(node.expression, compact);\n            break;\n\n        case Syntax.RestType:\n            result = '...';\n            if (node.expression) {\n                result += stringifyImpl(node.expression, compact);\n            }\n            break;\n\n        case Syntax.NonNullableType:\n            if (node.prefix) {\n                result = '!' + stringifyImpl(node.expression, compact);\n            } else {\n                result = stringifyImpl(node.expression, compact) + '!';\n            }\n            break;\n\n        case Syntax.OptionalType:\n            result = stringifyImpl(node.expression, compact) + '=';\n            break;\n\n        case Syntax.NullableType:\n            if (node.prefix) {\n                result = '?' + stringifyImpl(node.expression, compact);\n            } else {\n                result = stringifyImpl(node.expression, compact) + '?';\n            }\n            break;\n\n        case Syntax.NameExpression:\n            result = node.name;\n            break;\n\n        case Syntax.TypeApplication:\n            result = stringifyImpl(node.expression, compact) + '.<';\n            for (i = 0, iz = node.applications.length; i < iz; ++i) {\n                result += stringifyImpl(node.applications[i], compact);\n                if ((i + 1) !== iz) {\n                    result += compact ? ',' : ', ';\n                }\n            }\n            result += '>';\n            break;\n\n        case Syntax.StringLiteralType:\n            result = '\"' + node.value + '\"';\n            break;\n\n        case Syntax.NumericLiteralType:\n            result = String(node.value);\n            break;\n\n        case Syntax.BooleanLiteralType:\n            result = String(node.value);\n            break;\n\n        default:\n            utility.throwError('Unknown type ' + node.type);\n        }\n\n        return result;\n    }\n\n    function stringify(node, options) {\n        if (options == null) {\n            options = {};\n        }\n        return stringifyImpl(node, options.compact, options.topLevel);\n    }\n\n    exports.parseType = parseType;\n    exports.parseParamType = parseParamType;\n    exports.stringify = stringify;\n    exports.Syntax = Syntax;\n}());\n/* vim: set sw=4 ts=4 et tw=80 : */\n", "/*\n * @fileoverview Main Doctrine object\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n */\n\n(function () {\n    'use strict';\n\n    var typed,\n        utility,\n        jsdoc,\n        esutils,\n        hasOwnProperty;\n\n    esutils = require('esutils');\n    typed = require('./typed');\n    utility = require('./utility');\n\n    function sliceSource(source, index, last) {\n        return source.slice(index, last);\n    }\n\n    hasOwnProperty = (function () {\n        var func = Object.prototype.hasOwnProperty;\n        return function hasOwnProperty(obj, name) {\n            return func.call(obj, name);\n        };\n    }());\n    function shallowCopy(obj) {\n        var ret = {}, key;\n        for (key in obj) {\n            if (obj.hasOwnProperty(key)) {\n                ret[key] = obj[key];\n            }\n        }\n        return ret;\n    }\n\n    function isASCIIAlphanumeric(ch) {\n        return (ch >= 0x61  /* 'a' */ && ch <= 0x7A  /* 'z' */) ||\n            (ch >= 0x41  /* 'A' */ && ch <= 0x5A  /* 'Z' */) ||\n            (ch >= 0x30  /* '0' */ && ch <= 0x39  /* '9' */);\n    }\n\n    function isParamTitle(title) {\n        return title === 'param' || title === 'argument' || title === 'arg';\n    }\n\n    function isReturnTitle(title) {\n        return title === 'return' || title === 'returns';\n    }\n\n    function isProperty(title) {\n        return title === 'property' || title === 'prop';\n    }\n\n    function isNameParameterRequired(title) {\n        return isParamTitle(title) || isProperty(title) ||\n            title === 'alias' || title === 'this' || title === 'mixes' || title === 'requires';\n    }\n\n    function isAllowedName(title) {\n        return isNameParameterRequired(title) || title === 'const' || title === 'constant';\n    }\n\n    function isAllowedNested(title) {\n        return isProperty(title) || isParamTitle(title);\n    }\n\n    function isAllowedOptional(title) {\n        return isProperty(title) || isParamTitle(title);\n    }\n\n    function isTypeParameterRequired(title) {\n        return isParamTitle(title) || isReturnTitle(title) ||\n            title === 'define' || title === 'enum' ||\n            title === 'implements' || title === 'this' ||\n            title === 'type' || title === 'typedef' || isProperty(title);\n    }\n\n    // Consider deprecation instead using 'isTypeParameterRequired' and 'Rules' declaration to pick when a type is optional/required\n    // This would require changes to 'parseType'\n    function isAllowedType(title) {\n        return isTypeParameterRequired(title) || title === 'throws' || title === 'const' || title === 'constant' ||\n            title === 'namespace' || title === 'member' || title === 'var' || title === 'module' ||\n            title === 'constructor' || title === 'class' || title === 'extends' || title === 'augments' ||\n            title === 'public' || title === 'private' || title === 'protected';\n    }\n\n    // A regex character class that contains all whitespace except linebreak characters (\\r, \\n, \\u2028, \\u2029)\n    var WHITESPACE = '[ \\\\f\\\\t\\\\v\\\\u00a0\\\\u1680\\\\u180e\\\\u2000-\\\\u200a\\\\u202f\\\\u205f\\\\u3000\\\\ufeff]';\n\n    var STAR_MATCHER = '(' + WHITESPACE + '*(?:\\\\*' + WHITESPACE + '?)?)(.+|[\\r\\n\\u2028\\u2029])';\n\n    function unwrapComment(doc) {\n        // JSDoc comment is following form\n        //   /**\n        //    * .......\n        //    */\n\n        return doc.\n            // remove /**\n            replace(/^\\/\\*\\*?/, '').\n            // remove */\n            replace(/\\*\\/$/, '').\n            // remove ' * ' at the beginning of a line\n            replace(new RegExp(STAR_MATCHER, 'g'), '$2').\n            // remove trailing whitespace\n            replace(/\\s*$/, '');\n    }\n\n    /**\n     * Converts an index in an \"unwrapped\" JSDoc comment to the corresponding index in the original \"wrapped\" version\n     * @param {string} originalSource The original wrapped comment\n     * @param {number} unwrappedIndex The index of a character in the unwrapped string\n     * @returns {number} The index of the corresponding character in the original wrapped string\n     */\n    function convertUnwrappedCommentIndex(originalSource, unwrappedIndex) {\n        var replacedSource = originalSource.replace(/^\\/\\*\\*?/, '');\n        var numSkippedChars = 0;\n        var matcher = new RegExp(STAR_MATCHER, 'g');\n        var match;\n\n        while ((match = matcher.exec(replacedSource))) {\n            numSkippedChars += match[1].length;\n\n            if (match.index + match[0].length > unwrappedIndex + numSkippedChars) {\n                return unwrappedIndex + numSkippedChars + originalSource.length - replacedSource.length;\n            }\n        }\n\n        return originalSource.replace(/\\*\\/$/, '').replace(/\\s*$/, '').length;\n    }\n\n    // JSDoc Tag Parser\n\n    (function (exports) {\n        var Rules,\n            index,\n            lineNumber,\n            length,\n            source,\n            originalSource,\n            recoverable,\n            sloppy,\n            strict;\n\n        function advance() {\n            var ch = source.charCodeAt(index);\n            index += 1;\n            if (esutils.code.isLineTerminator(ch) && !(ch === 0x0D  /* '\\r' */ && source.charCodeAt(index) === 0x0A  /* '\\n' */)) {\n                lineNumber += 1;\n            }\n            return String.fromCharCode(ch);\n        }\n\n        function scanTitle() {\n            var title = '';\n            // waste '@'\n            advance();\n\n            while (index < length && isASCIIAlphanumeric(source.charCodeAt(index))) {\n                title += advance();\n            }\n\n            return title;\n        }\n\n        function seekContent() {\n            var ch, waiting, last = index;\n\n            waiting = false;\n            while (last < length) {\n                ch = source.charCodeAt(last);\n                if (esutils.code.isLineTerminator(ch) && !(ch === 0x0D  /* '\\r' */ && source.charCodeAt(last + 1) === 0x0A  /* '\\n' */)) {\n                    waiting = true;\n                } else if (waiting) {\n                    if (ch === 0x40  /* '@' */) {\n                        break;\n                    }\n                    if (!esutils.code.isWhiteSpace(ch)) {\n                        waiting = false;\n                    }\n                }\n                last += 1;\n            }\n            return last;\n        }\n\n        // type expression may have nest brace, such as,\n        // { { ok: string } }\n        //\n        // therefore, scanning type expression with balancing braces.\n        function parseType(title, last, addRange) {\n            var ch, brace, type, startIndex, direct = false;\n\n\n            // search '{'\n            while (index < last) {\n                ch = source.charCodeAt(index);\n                if (esutils.code.isWhiteSpace(ch)) {\n                    advance();\n                } else if (ch === 0x7B  /* '{' */) {\n                    advance();\n                    break;\n                } else {\n                    // this is direct pattern\n                    direct = true;\n                    break;\n                }\n            }\n\n\n            if (direct) {\n                return null;\n            }\n\n            // type expression { is found\n            brace = 1;\n            type = '';\n            while (index < last) {\n                ch = source.charCodeAt(index);\n                if (esutils.code.isLineTerminator(ch)) {\n                    advance();\n                } else {\n                    if (ch === 0x7D  /* '}' */) {\n                        brace -= 1;\n                        if (brace === 0) {\n                            advance();\n                            break;\n                        }\n                    } else if (ch === 0x7B  /* '{' */) {\n                        brace += 1;\n                    }\n                    if (type === '') {\n                        startIndex = index;\n                    }\n                    type += advance();\n                }\n            }\n\n            if (brace !== 0) {\n                // braces is not balanced\n                return utility.throwError('Braces are not balanced');\n            }\n\n            if (isAllowedOptional(title)) {\n                return typed.parseParamType(type, {startIndex: convertIndex(startIndex), range: addRange});\n            }\n\n            return typed.parseType(type, {startIndex: convertIndex(startIndex), range: addRange});\n        }\n\n        function scanIdentifier(last) {\n            var identifier;\n            if (!esutils.code.isIdentifierStartES5(source.charCodeAt(index)) && !source[index].match(/[0-9]/)) {\n                return null;\n            }\n            identifier = advance();\n            while (index < last && esutils.code.isIdentifierPartES5(source.charCodeAt(index))) {\n                identifier += advance();\n            }\n            return identifier;\n        }\n\n        function skipWhiteSpace(last) {\n            while (index < last && (esutils.code.isWhiteSpace(source.charCodeAt(index)) || esutils.code.isLineTerminator(source.charCodeAt(index)))) {\n                advance();\n            }\n        }\n\n        function parseName(last, allowBrackets, allowNestedParams) {\n            var name = '',\n                useBrackets,\n                insideString;\n\n\n            skipWhiteSpace(last);\n\n            if (index >= last) {\n                return null;\n            }\n\n            if (source.charCodeAt(index) === 0x5B  /* '[' */) {\n                if (allowBrackets) {\n                    useBrackets = true;\n                    name = advance();\n                } else {\n                    return null;\n                }\n            }\n\n            name += scanIdentifier(last);\n\n            if (allowNestedParams) {\n                if (source.charCodeAt(index) === 0x3A /* ':' */ && (\n                        name === 'module' ||\n                        name === 'external' ||\n                        name === 'event')) {\n                    name += advance();\n                    name += scanIdentifier(last);\n\n                }\n                if(source.charCodeAt(index) === 0x5B  /* '[' */ && source.charCodeAt(index + 1) === 0x5D  /* ']' */){\n                    name += advance();\n                    name += advance();\n                }\n                while (source.charCodeAt(index) === 0x2E  /* '.' */ ||\n                        source.charCodeAt(index) === 0x2F  /* '/' */ ||\n                        source.charCodeAt(index) === 0x23  /* '#' */ ||\n                        source.charCodeAt(index) === 0x2D  /* '-' */ ||\n                        source.charCodeAt(index) === 0x7E  /* '~' */) {\n                    name += advance();\n                    name += scanIdentifier(last);\n                }\n            }\n\n            if (useBrackets) {\n                skipWhiteSpace(last);\n                // do we have a default value for this?\n                if (source.charCodeAt(index) === 0x3D  /* '=' */) {\n                    // consume the '='' symbol\n                    name += advance();\n                    skipWhiteSpace(last);\n\n                    var ch;\n                    var bracketDepth = 1;\n\n                    // scan in the default value\n                    while (index < last) {\n                        ch = source.charCodeAt(index);\n\n                        if (esutils.code.isWhiteSpace(ch)) {\n                            if (!insideString) {\n                                skipWhiteSpace(last);\n                                ch = source.charCodeAt(index);\n                            }\n                        }\n\n                        if (ch === 0x27 /* ''' */) {\n                            if (!insideString) {\n                                insideString = '\\'';\n                            } else {\n                                if (insideString === '\\'') {\n                                    insideString = '';\n                                }\n                            }\n                        }\n\n                        if (ch === 0x22 /* '\"' */) {\n                            if (!insideString) {\n                                insideString = '\"';\n                            } else {\n                                if (insideString === '\"') {\n                                    insideString = '';\n                                }\n                            }\n                        }\n\n                        if (ch === 0x5B /* '[' */) {\n                            bracketDepth++;\n                        } else if (ch === 0x5D  /* ']' */ &&\n                            --bracketDepth === 0) {\n                            break;\n                        }\n\n                        name += advance();\n                    }\n                }\n\n                skipWhiteSpace(last);\n\n                if (index >= last || source.charCodeAt(index) !== 0x5D  /* ']' */) {\n                    // we never found a closing ']'\n                    return null;\n                }\n\n                // collect the last ']'\n                name += advance();\n            }\n\n            return name;\n        }\n\n        function skipToTag() {\n            while (index < length && source.charCodeAt(index) !== 0x40  /* '@' */) {\n                advance();\n            }\n            if (index >= length) {\n                return false;\n            }\n            utility.assert(source.charCodeAt(index) === 0x40  /* '@' */);\n            return true;\n        }\n\n        function convertIndex(rangeIndex) {\n            if (source === originalSource) {\n                return rangeIndex;\n            }\n            return convertUnwrappedCommentIndex(originalSource, rangeIndex);\n        }\n\n        function TagParser(options, title) {\n            this._options = options;\n            this._title = title.toLowerCase();\n            this._tag = {\n                title: title,\n                description: null\n            };\n            if (this._options.lineNumbers) {\n                this._tag.lineNumber = lineNumber;\n            }\n            this._first = index - title.length - 1;\n            this._last = 0;\n            // space to save special information for title parsers.\n            this._extra = { };\n        }\n\n        // addError(err, ...)\n        TagParser.prototype.addError = function addError(errorText) {\n            var args = Array.prototype.slice.call(arguments, 1),\n                msg = errorText.replace(\n                    /%(\\d)/g,\n                    function (whole, index) {\n                        utility.assert(index < args.length, 'Message reference must be in range');\n                        return args[index];\n                    }\n                );\n\n            if (!this._tag.errors) {\n                this._tag.errors = [];\n            }\n            if (strict) {\n                utility.throwError(msg);\n            }\n            this._tag.errors.push(msg);\n            return recoverable;\n        };\n\n        TagParser.prototype.parseType = function () {\n            // type required titles\n            if (isTypeParameterRequired(this._title)) {\n                try {\n                    this._tag.type = parseType(this._title, this._last, this._options.range);\n                    if (!this._tag.type) {\n                        if (!isParamTitle(this._title) && !isReturnTitle(this._title)) {\n                            if (!this.addError('Missing or invalid tag type')) {\n                                return false;\n                            }\n                        }\n                    }\n                } catch (error) {\n                    this._tag.type = null;\n                    if (!this.addError(error.message)) {\n                        return false;\n                    }\n                }\n            } else if (isAllowedType(this._title)) {\n                // optional types\n                try {\n                    this._tag.type = parseType(this._title, this._last, this._options.range);\n                } catch (e) {\n                    //For optional types, lets drop the thrown error when we hit the end of the file\n                }\n            }\n            return true;\n        };\n\n        TagParser.prototype._parseNamePath = function (optional) {\n            var name;\n            name = parseName(this._last, sloppy && isAllowedOptional(this._title), true);\n            if (!name) {\n                if (!optional) {\n                    if (!this.addError('Missing or invalid tag name')) {\n                        return false;\n                    }\n                }\n            }\n            this._tag.name = name;\n            return true;\n        };\n\n        TagParser.prototype.parseNamePath = function () {\n            return this._parseNamePath(false);\n        };\n\n        TagParser.prototype.parseNamePathOptional = function () {\n            return this._parseNamePath(true);\n        };\n\n\n        TagParser.prototype.parseName = function () {\n            var assign, name;\n\n            // param, property requires name\n            if (isAllowedName(this._title)) {\n                this._tag.name = parseName(this._last, sloppy && isAllowedOptional(this._title), isAllowedNested(this._title));\n                if (!this._tag.name) {\n                    if (!isNameParameterRequired(this._title)) {\n                        return true;\n                    }\n\n                    // it's possible the name has already been parsed but interpreted as a type\n                    // it's also possible this is a sloppy declaration, in which case it will be\n                    // fixed at the end\n                    if (isParamTitle(this._title) && this._tag.type && this._tag.type.name) {\n                        this._extra.name = this._tag.type;\n                        this._tag.name = this._tag.type.name;\n                        this._tag.type = null;\n                    } else {\n                        if (!this.addError('Missing or invalid tag name')) {\n                            return false;\n                        }\n                    }\n                } else {\n                    name = this._tag.name;\n                    if (name.charAt(0) === '[' && name.charAt(name.length - 1) === ']') {\n                        // extract the default value if there is one\n                        // example: @param {string} [somebody=John Doe] description\n                        assign = name.substring(1, name.length - 1).split('=');\n                        if (assign.length > 1) {\n                            this._tag['default'] = assign.slice(1).join('=');\n                        }\n                        this._tag.name = assign[0];\n\n                        // convert to an optional type\n                        if (this._tag.type && this._tag.type.type !== 'OptionalType') {\n                            this._tag.type = {\n                                type: 'OptionalType',\n                                expression: this._tag.type\n                            };\n                        }\n                    }\n                }\n            }\n\n\n            return true;\n        };\n\n        TagParser.prototype.parseDescription = function parseDescription() {\n            var description = sliceSource(source, index, this._last).trim();\n            if (description) {\n                if ((/^-\\s+/).test(description)) {\n                    description = description.substring(2);\n                }\n                this._tag.description = description;\n            }\n            return true;\n        };\n\n        TagParser.prototype.parseCaption = function parseDescription() {\n            var description = sliceSource(source, index, this._last).trim();\n            var captionStartTag = '<caption>';\n            var captionEndTag = '</caption>';\n            var captionStart = description.indexOf(captionStartTag);\n            var captionEnd = description.indexOf(captionEndTag);\n            if (captionStart >= 0 && captionEnd >= 0) {\n                this._tag.caption = description.substring(\n                    captionStart + captionStartTag.length, captionEnd).trim();\n                this._tag.description = description.substring(captionEnd + captionEndTag.length).trim();\n            } else {\n                this._tag.description = description;\n            }\n            return true;\n        };\n\n        TagParser.prototype.parseKind = function parseKind() {\n            var kind, kinds;\n            kinds = {\n                'class': true,\n                'constant': true,\n                'event': true,\n                'external': true,\n                'file': true,\n                'function': true,\n                'member': true,\n                'mixin': true,\n                'module': true,\n                'namespace': true,\n                'typedef': true\n            };\n            kind = sliceSource(source, index, this._last).trim();\n            this._tag.kind = kind;\n            if (!hasOwnProperty(kinds, kind)) {\n                if (!this.addError('Invalid kind name \\'%0\\'', kind)) {\n                    return false;\n                }\n            }\n            return true;\n        };\n\n        TagParser.prototype.parseAccess = function parseAccess() {\n            var access;\n            access = sliceSource(source, index, this._last).trim();\n            this._tag.access = access;\n            if (access !== 'private' && access !== 'protected' && access !== 'public') {\n                if (!this.addError('Invalid access name \\'%0\\'', access)) {\n                    return false;\n                }\n            }\n            return true;\n        };\n\n        TagParser.prototype.parseThis = function parseThis() {\n            // this name may be a name expression (e.g. {foo.bar}),\n            // an union (e.g. {foo.bar|foo.baz}) or a name path (e.g. foo.bar)\n            var value = sliceSource(source, index, this._last).trim();\n            if (value && value.charAt(0) === '{') {\n                var gotType = this.parseType();\n                if (gotType && this._tag.type.type === 'NameExpression' || this._tag.type.type === 'UnionType') {\n                    this._tag.name = this._tag.type.name;\n                    return true;\n                } else {\n                    return this.addError('Invalid name for this');\n                }\n            } else {\n                return this.parseNamePath();\n            }\n        };\n\n        TagParser.prototype.parseVariation = function parseVariation() {\n            var variation, text;\n            text = sliceSource(source, index, this._last).trim();\n            variation = parseFloat(text, 10);\n            this._tag.variation = variation;\n            if (isNaN(variation)) {\n                if (!this.addError('Invalid variation \\'%0\\'', text)) {\n                    return false;\n                }\n            }\n            return true;\n        };\n\n        TagParser.prototype.ensureEnd = function () {\n            var shouldBeEmpty = sliceSource(source, index, this._last).trim();\n            if (shouldBeEmpty) {\n                if (!this.addError('Unknown content \\'%0\\'', shouldBeEmpty)) {\n                    return false;\n                }\n            }\n            return true;\n        };\n\n        TagParser.prototype.epilogue = function epilogue() {\n            var description;\n\n            description = this._tag.description;\n            // un-fix potentially sloppy declaration\n            if (isAllowedOptional(this._title) && !this._tag.type && description && description.charAt(0) === '[') {\n                this._tag.type = this._extra.name;\n                if (!this._tag.name) {\n                    this._tag.name = undefined;\n                }\n\n                if (!sloppy) {\n                    if (!this.addError('Missing or invalid tag name')) {\n                        return false;\n                    }\n                }\n            }\n\n            return true;\n        };\n\n        Rules = {\n            // http://usejsdoc.org/tags-access.html\n            'access': ['parseAccess'],\n            // http://usejsdoc.org/tags-alias.html\n            'alias': ['parseNamePath', 'ensureEnd'],\n            // http://usejsdoc.org/tags-augments.html\n            'augments': ['parseType', 'parseNamePathOptional', 'ensureEnd'],\n            // http://usejsdoc.org/tags-constructor.html\n            'constructor': ['parseType', 'parseNamePathOptional', 'ensureEnd'],\n            // Synonym: http://usejsdoc.org/tags-constructor.html\n            'class': ['parseType', 'parseNamePathOptional', 'ensureEnd'],\n            // Synonym: http://usejsdoc.org/tags-extends.html\n            'extends': ['parseType', 'parseNamePathOptional', 'ensureEnd'],\n            // http://usejsdoc.org/tags-example.html\n            'example': ['parseCaption'],\n            // http://usejsdoc.org/tags-deprecated.html\n            'deprecated': ['parseDescription'],\n            // http://usejsdoc.org/tags-global.html\n            'global': ['ensureEnd'],\n            // http://usejsdoc.org/tags-inner.html\n            'inner': ['ensureEnd'],\n            // http://usejsdoc.org/tags-instance.html\n            'instance': ['ensureEnd'],\n            // http://usejsdoc.org/tags-kind.html\n            'kind': ['parseKind'],\n            // http://usejsdoc.org/tags-mixes.html\n            'mixes': ['parseNamePath', 'ensureEnd'],\n            // http://usejsdoc.org/tags-mixin.html\n            'mixin': ['parseNamePathOptional', 'ensureEnd'],\n            // http://usejsdoc.org/tags-member.html\n            'member': ['parseType', 'parseNamePathOptional', 'ensureEnd'],\n            // http://usejsdoc.org/tags-method.html\n            'method': ['parseNamePathOptional', 'ensureEnd'],\n            // http://usejsdoc.org/tags-module.html\n            'module': ['parseType', 'parseNamePathOptional', 'ensureEnd'],\n            // Synonym: http://usejsdoc.org/tags-method.html\n            'func': ['parseNamePathOptional', 'ensureEnd'],\n            // Synonym: http://usejsdoc.org/tags-method.html\n            'function': ['parseNamePathOptional', 'ensureEnd'],\n            // Synonym: http://usejsdoc.org/tags-member.html\n            'var': ['parseType', 'parseNamePathOptional', 'ensureEnd'],\n            // http://usejsdoc.org/tags-name.html\n            'name': ['parseNamePath', 'ensureEnd'],\n            // http://usejsdoc.org/tags-namespace.html\n            'namespace': ['parseType', 'parseNamePathOptional', 'ensureEnd'],\n            // http://usejsdoc.org/tags-private.html\n            'private': ['parseType', 'parseDescription'],\n            // http://usejsdoc.org/tags-protected.html\n            'protected': ['parseType', 'parseDescription'],\n            // http://usejsdoc.org/tags-public.html\n            'public': ['parseType', 'parseDescription'],\n            // http://usejsdoc.org/tags-readonly.html\n            'readonly': ['ensureEnd'],\n            // http://usejsdoc.org/tags-requires.html\n            'requires': ['parseNamePath', 'ensureEnd'],\n            // http://usejsdoc.org/tags-since.html\n            'since': ['parseDescription'],\n            // http://usejsdoc.org/tags-static.html\n            'static': ['ensureEnd'],\n            // http://usejsdoc.org/tags-summary.html\n            'summary': ['parseDescription'],\n            // http://usejsdoc.org/tags-this.html\n            'this': ['parseThis', 'ensureEnd'],\n            // http://usejsdoc.org/tags-todo.html\n            'todo': ['parseDescription'],\n            // http://usejsdoc.org/tags-typedef.html\n            'typedef': ['parseType', 'parseNamePathOptional'],\n            // http://usejsdoc.org/tags-variation.html\n            'variation': ['parseVariation'],\n            // http://usejsdoc.org/tags-version.html\n            'version': ['parseDescription']\n        };\n\n        TagParser.prototype.parse = function parse() {\n            var i, iz, sequences, method;\n\n\n            // empty title\n            if (!this._title) {\n                if (!this.addError('Missing or invalid title')) {\n                    return null;\n                }\n            }\n\n            // Seek to content last index.\n            this._last = seekContent(this._title);\n\n            if (this._options.range) {\n                this._tag.range = [this._first, source.slice(0, this._last).replace(/\\s*$/, '').length].map(convertIndex);\n            }\n\n            if (hasOwnProperty(Rules, this._title)) {\n                sequences = Rules[this._title];\n            } else {\n                // default sequences\n                sequences = ['parseType', 'parseName', 'parseDescription', 'epilogue'];\n            }\n\n            for (i = 0, iz = sequences.length; i < iz; ++i) {\n                method = sequences[i];\n                if (!this[method]()) {\n                    return null;\n                }\n            }\n\n            return this._tag;\n        };\n\n        function parseTag(options) {\n            var title, parser, tag;\n\n            // skip to tag\n            if (!skipToTag()) {\n                return null;\n            }\n\n            // scan title\n            title = scanTitle();\n\n            // construct tag parser\n            parser = new TagParser(options, title);\n            tag = parser.parse();\n\n            // Seek global index to end of this tag.\n            while (index < parser._last) {\n                advance();\n            }\n\n            return tag;\n        }\n\n        //\n        // Parse JSDoc\n        //\n\n        function scanJSDocDescription(preserveWhitespace) {\n            var description = '', ch, atAllowed;\n\n            atAllowed = true;\n            while (index < length) {\n                ch = source.charCodeAt(index);\n\n                if (atAllowed && ch === 0x40  /* '@' */) {\n                    break;\n                }\n\n                if (esutils.code.isLineTerminator(ch)) {\n                    atAllowed = true;\n                } else if (atAllowed && !esutils.code.isWhiteSpace(ch)) {\n                    atAllowed = false;\n                }\n\n                description += advance();\n            }\n\n            return preserveWhitespace ? description : description.trim();\n        }\n\n        function parse(comment, options) {\n            var tags = [], tag, description, interestingTags, i, iz;\n\n            if (options === undefined) {\n                options = {};\n            }\n\n            if (typeof options.unwrap === 'boolean' && options.unwrap) {\n                source = unwrapComment(comment);\n            } else {\n                source = comment;\n            }\n\n            originalSource = comment;\n\n            // array of relevant tags\n            if (options.tags) {\n                if (Array.isArray(options.tags)) {\n                    interestingTags = { };\n                    for (i = 0, iz = options.tags.length; i < iz; i++) {\n                        if (typeof options.tags[i] === 'string') {\n                            interestingTags[options.tags[i]] = true;\n                        } else {\n                            utility.throwError('Invalid \"tags\" parameter: ' + options.tags);\n                        }\n                    }\n                } else {\n                    utility.throwError('Invalid \"tags\" parameter: ' + options.tags);\n                }\n            }\n\n            length = source.length;\n            index = 0;\n            lineNumber = 0;\n            recoverable = options.recoverable;\n            sloppy = options.sloppy;\n            strict = options.strict;\n\n            description = scanJSDocDescription(options.preserveWhitespace);\n\n            while (true) {\n                tag = parseTag(options);\n                if (!tag) {\n                    break;\n                }\n                if (!interestingTags || interestingTags.hasOwnProperty(tag.title)) {\n                    tags.push(tag);\n                }\n            }\n\n            return {\n                description: description,\n                tags: tags\n            };\n        }\n        exports.parse = parse;\n    }(jsdoc = {}));\n\n    exports.version = utility.VERSION;\n    exports.parse = jsdoc.parse;\n    exports.parseType = typed.parseType;\n    exports.parseParamType = typed.parseParamType;\n    exports.unwrapComment = unwrapComment;\n    exports.Syntax = shallowCopy(typed.Syntax);\n    exports.Error = utility.DoctrineError;\n    exports.type = {\n        Syntax: exports.Syntax,\n        parseType: typed.parseType,\n        parseParamType: typed.parseParamType,\n        stringify: typed.stringify\n    };\n}());\n/* vim: set sw=4 ts=4 et tw=80 : */\n"], "mappings": ";;;;;AAAA;AAAA;AAwBA,KAAC,WAAY;AACT;AAEA,eAAS,aAAa,MAAM;AACxB,YAAI,QAAQ,MAAM;AAAE,iBAAO;AAAA,QAAO;AAClC,gBAAQ,KAAK,MAAM;AAAA,UACf,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,mBAAO;AAAA,QACf;AACA,eAAO;AAAA,MACX;AAEA,eAAS,qBAAqB,MAAM;AAChC,YAAI,QAAQ,MAAM;AAAE,iBAAO;AAAA,QAAO;AAClC,gBAAQ,KAAK,MAAM;AAAA,UACf,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,mBAAO;AAAA,QACf;AACA,eAAO;AAAA,MACX;AAEA,eAAS,YAAY,MAAM;AACvB,YAAI,QAAQ,MAAM;AAAE,iBAAO;AAAA,QAAO;AAClC,gBAAQ,KAAK,MAAM;AAAA,UACf,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,mBAAO;AAAA,QACf;AACA,eAAO;AAAA,MACX;AAEA,eAAS,gBAAgB,MAAM;AAC7B,eAAO,YAAY,IAAI,KAAK,QAAQ,QAAQ,KAAK,SAAS;AAAA,MAC5D;AAEA,eAAS,kBAAkB,MAAM;AAC7B,gBAAQ,KAAK,MAAM;AAAA,UACnB,KAAK;AACD,gBAAI,KAAK,aAAa,MAAM;AACxB,qBAAO,KAAK;AAAA,YAChB;AACA,mBAAO,KAAK;AAAA,UAEhB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,mBAAO,KAAK;AAAA,QAChB;AACA,eAAO;AAAA,MACX;AAEA,eAAS,yBAAyB,MAAM;AACpC,YAAI;AAEJ,YAAI,KAAK,SAAS,eAAe;AAC7B,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,aAAa,MAAM;AACxB,iBAAO;AAAA,QACX;AACA,kBAAU,KAAK;AACf,WAAG;AACC,cAAI,QAAQ,SAAS,eAAe;AAChC,gBAAI,QAAQ,aAAa,MAAO;AAC5B,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,oBAAU,kBAAkB,OAAO;AAAA,QACvC,SAAS;AAET,eAAO;AAAA,MACX;AAEA,aAAO,UAAU;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QAEA;AAAA,MACJ;AAAA,IACJ,GAAE;AAAA;AAAA;;;AC9IF;AAAA;AAyBA,KAAC,WAAY;AACT;AAEA,UAAI,UAAU,UAAU,uBAAuB,kBAAkB,iBAAiB;AAGlF,iBAAW;AAAA;AAAA,QAEP,yBAAyB;AAAA;AAAA,QAEzB,wBAAwB;AAAA,MAC5B;AAEA,iBAAW;AAAA;AAAA,QAEP,yBAAyB;AAAA;AAAA,QAEzB,wBAAwB;AAAA,MAC5B;AAEA,eAAS,eAAeA,KAAI;AACxB,eAAO,MAAQA,OAAMA,OAAM;AAAA,MAC/B;AAEA,eAAS,WAAWA,KAAI;AACpB,eAAO,MAAQA,OAAMA,OAAM;AAAA,QACvB,MAAQA,OAAMA,OAAM;AAAA,QACpB,MAAQA,OAAMA,OAAM;AAAA,MAC5B;AAEA,eAAS,aAAaA,KAAI;AACtB,eAAOA,OAAM,MAAQA,OAAM;AAAA,MAC/B;AAIA,8BAAwB;AAAA,QACpB;AAAA,QACA;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAChF;AAAA,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,MACJ;AAEA,eAAS,aAAaA,KAAI;AACtB,eAAOA,QAAO,MAAQA,QAAO,KAAQA,QAAO,MAAQA,QAAO,MAAQA,QAAO,OACtEA,OAAM,QAAU,sBAAsB,QAAQA,GAAE,KAAK;AAAA,MAC7D;AAIA,eAAS,iBAAiBA,KAAI;AAC1B,eAAOA,QAAO,MAAQA,QAAO,MAAQA,QAAO,QAAUA,QAAO;AAAA,MACjE;AAIA,eAAS,cAAc,IAAI;AACvB,YAAI,MAAM,OAAQ;AAAE,iBAAO,OAAO,aAAa,EAAE;AAAA,QAAG;AACpD,YAAI,MAAM,OAAO,aAAa,KAAK,OAAO,KAAK,SAAW,IAAK,IAAI,KAAM;AACzE,YAAI,MAAM,OAAO,cAAe,KAAK,SAAW,OAAS,KAAM;AAC/D,eAAO,MAAM;AAAA,MACjB;AAEA,yBAAmB,IAAI,MAAM,GAAI;AACjC,WAAI,KAAK,GAAG,KAAK,KAAM,EAAE,IAAI;AACzB,yBAAiB,EAAE,IACf,MAAM,MAAQ,MAAM;AAAA,QACpB,MAAM,MAAQ,MAAM;AAAA,QACpB,OAAO,MAAQ,OAAO;AAAA,MAC9B;AAEA,wBAAkB,IAAI,MAAM,GAAI;AAChC,WAAI,KAAK,GAAG,KAAK,KAAM,EAAE,IAAI;AACzB,wBAAgB,EAAE,IACd,MAAM,MAAQ,MAAM;AAAA,QACpB,MAAM,MAAQ,MAAM;AAAA,QACpB,MAAM,MAAQ,MAAM;AAAA,QACpB,OAAO,MAAQ,OAAO;AAAA,MAC9B;AAEA,eAAS,qBAAqBA,KAAI;AAC9B,eAAOA,MAAK,MAAO,iBAAiBA,GAAE,IAAI,SAAS,wBAAwB,KAAK,cAAcA,GAAE,CAAC;AAAA,MACrG;AAEA,eAAS,oBAAoBA,KAAI;AAC7B,eAAOA,MAAK,MAAO,gBAAgBA,GAAE,IAAI,SAAS,uBAAuB,KAAK,cAAcA,GAAE,CAAC;AAAA,MACnG;AAEA,eAAS,qBAAqBA,KAAI;AAC9B,eAAOA,MAAK,MAAO,iBAAiBA,GAAE,IAAI,SAAS,wBAAwB,KAAK,cAAcA,GAAE,CAAC;AAAA,MACrG;AAEA,eAAS,oBAAoBA,KAAI;AAC7B,eAAOA,MAAK,MAAO,gBAAgBA,GAAE,IAAI,SAAS,uBAAuB,KAAK,cAAcA,GAAE,CAAC;AAAA,MACnG;AAEA,aAAO,UAAU;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ,GAAE;AAAA;AAAA;;;ACrIF;AAAA;AAwBA,KAAC,WAAY;AACT;AAEA,UAAI,OAAO;AAEX,eAAS,4BAA4B,IAAI;AACrC,gBAAQ,IAAI;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,mBAAO;AAAA,UACX;AACI,mBAAO;AAAA,QACX;AAAA,MACJ;AAEA,eAAS,aAAa,IAAI,QAAQ;AAE9B,YAAI,CAAC,UAAU,OAAO,SAAS;AAC3B,iBAAO;AAAA,QACX;AACA,eAAO,aAAa,IAAI,MAAM;AAAA,MAClC;AAEA,eAAS,aAAa,IAAI,QAAQ;AAC9B,YAAI,UAAU,4BAA4B,EAAE,GAAG;AAC3C,iBAAO;AAAA,QACX;AAEA,gBAAQ,GAAG,QAAQ;AAAA,UACnB,KAAK;AACD,mBAAQ,OAAO,QAAU,OAAO,QAAU,OAAO;AAAA,UACrD,KAAK;AACD,mBAAQ,OAAO,SAAW,OAAO,SAAW,OAAO,SAAW,OAAO;AAAA,UACzE,KAAK;AACD,mBAAQ,OAAO,UAAY,OAAO,UAAY,OAAO,UAChD,OAAO,UAAY,OAAO,UAAY,OAAO;AAAA,UACtD,KAAK;AACD,mBAAQ,OAAO,WAAa,OAAO,WAAa,OAAO,WAClD,OAAO,WAAa,OAAO,WAAa,OAAO,WAC/C,OAAO,WAAa,OAAO;AAAA,UACpC,KAAK;AACD,mBAAQ,OAAO,YAAc,OAAO,YAAc,OAAO,YACpD,OAAO,YAAc,OAAO,YAAc,OAAO;AAAA,UAC1D,KAAK;AACD,mBAAQ,OAAO,aAAe,OAAO,aAAe,OAAO;AAAA,UAC/D,KAAK;AACD,mBAAQ,OAAO,cAAgB,OAAO,cAAgB,OAAO;AAAA,UACjE,KAAK;AACD,mBAAQ,OAAO;AAAA,UACnB;AACI,mBAAO;AAAA,QACX;AAAA,MACJ;AAEA,eAAS,kBAAkB,IAAI,QAAQ;AACnC,eAAO,OAAO,UAAU,OAAO,UAAU,OAAO,WAAW,aAAa,IAAI,MAAM;AAAA,MACtF;AAEA,eAAS,kBAAkB,IAAI,QAAQ;AACnC,eAAO,OAAO,UAAU,OAAO,UAAU,OAAO,WAAW,aAAa,IAAI,MAAM;AAAA,MACtF;AAEA,eAAS,iBAAiB,IAAI;AAC1B,eAAO,OAAO,UAAU,OAAO;AAAA,MACnC;AAEA,eAAS,oBAAoB,IAAI;AAC7B,YAAI,GAAG,IAAI;AAEX,YAAI,GAAG,WAAW,GAAG;AAAE,iBAAO;AAAA,QAAO;AAErC,aAAK,GAAG,WAAW,CAAC;AACpB,YAAI,CAAC,KAAK,qBAAqB,EAAE,GAAG;AAChC,iBAAO;AAAA,QACX;AAEA,aAAK,IAAI,GAAG,KAAK,GAAG,QAAQ,IAAI,IAAI,EAAE,GAAG;AACrC,eAAK,GAAG,WAAW,CAAC;AACpB,cAAI,CAAC,KAAK,oBAAoB,EAAE,GAAG;AAC/B,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAEA,eAAS,YAAY,MAAM,OAAO;AAC9B,gBAAQ,OAAO,SAAU,QAAS,QAAQ,SAAU;AAAA,MACxD;AAEA,eAAS,oBAAoB,IAAI;AAC7B,YAAI,GAAG,IAAI,IAAI,OAAO;AAEtB,YAAI,GAAG,WAAW,GAAG;AAAE,iBAAO;AAAA,QAAO;AAErC,gBAAQ,KAAK;AACb,aAAK,IAAI,GAAG,KAAK,GAAG,QAAQ,IAAI,IAAI,EAAE,GAAG;AACrC,eAAK,GAAG,WAAW,CAAC;AACpB,cAAI,SAAU,MAAM,MAAM,OAAQ;AAC9B,cAAE;AACF,gBAAI,KAAK,IAAI;AAAE,qBAAO;AAAA,YAAO;AAC7B,oBAAQ,GAAG,WAAW,CAAC;AACvB,gBAAI,EAAE,SAAU,SAAS,SAAS,QAAS;AACvC,qBAAO;AAAA,YACX;AACA,iBAAK,YAAY,IAAI,KAAK;AAAA,UAC9B;AACA,cAAI,CAAC,MAAM,EAAE,GAAG;AACZ,mBAAO;AAAA,UACX;AACA,kBAAQ,KAAK;AAAA,QACjB;AACA,eAAO;AAAA,MACX;AAEA,eAAS,gBAAgB,IAAI,QAAQ;AACjC,eAAO,oBAAoB,EAAE,KAAK,CAAC,kBAAkB,IAAI,MAAM;AAAA,MACnE;AAEA,eAAS,gBAAgB,IAAI,QAAQ;AACjC,eAAO,oBAAoB,EAAE,KAAK,CAAC,kBAAkB,IAAI,MAAM;AAAA,MACnE;AAEA,aAAO,UAAU;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ,GAAE;AAAA;AAAA;;;ACnKF;AAAA;AAyBA,KAAC,WAAY;AACT;AAEA,cAAQ,MAAM;AACd,cAAQ,OAAO;AACf,cAAQ,UAAU;AAAA,IACtB,GAAE;AAAA;AAAA;;;AC/BF;AAAA;AAAA;AAAA,MACE,MAAQ;AAAA,MACR,aAAe;AAAA,MACf,UAAY;AAAA,MACZ,MAAQ;AAAA,MACR,SAAW;AAAA,MACX,SAAW;AAAA,QACT,MAAQ;AAAA,MACV;AAAA,MACA,aAAe;AAAA,QACb,KAAO;AAAA,MACT;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,aAAe;AAAA,QACb;AAAA,UACE,MAAQ;AAAA,UACR,OAAS;AAAA,UACT,KAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,MAAQ;AAAA,UACR,OAAS;AAAA,UACT,KAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,YAAc;AAAA,MACd,iBAAmB;AAAA,QACjB,WAAa;AAAA,QACb,YAAc;AAAA,QACd,QAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,SAAW;AAAA,QACX,OAAS;AAAA,QACT,eAAe;AAAA,QACf,KAAO;AAAA,QACP,QAAU;AAAA,QACV,SAAW;AAAA,QACX,mBAAmB;AAAA,QACnB,QAAU;AAAA,MACZ;AAAA,MACA,SAAW;AAAA,MACX,SAAW;AAAA,QACT,SAAW;AAAA,QACX,MAAQ;AAAA,QACR,WAAa;AAAA,QACb,MAAQ;AAAA,QACR,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,wBAAwB;AAAA,QACxB,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,cAAgB;AAAA,QACd,SAAW;AAAA,MACb;AAAA,IACF;AAAA;AAAA;;;ACzDA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,0FAA0F,GAAG,mIAAmI;AAAA,QAC/O;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAMA,KAAC,WAAY;AACT;AAEA,UAAI;AAEJ,gBAAU,kBAA2B;AACrC,cAAQ,UAAU;AAElB,eAAS,cAAc,SAAS;AAC5B,aAAK,OAAO;AACZ,aAAK,UAAU;AAAA,MACnB;AACA,oBAAc,aAAa,WAAY;AACnC,YAAI,SAAS,WAAY;AAAA,QAAE;AAC3B,eAAO,YAAY,MAAM;AACzB,eAAO,IAAI,OAAO;AAAA,MACtB,GAAE;AACF,oBAAc,UAAU,cAAc;AACtC,cAAQ,gBAAgB;AAExB,eAAS,WAAW,SAAS;AACzB,cAAM,IAAI,cAAc,OAAO;AAAA,MACnC;AACA,cAAQ,aAAa;AAErB,cAAQ,SAAS;AAAA,IACrB,GAAE;AAAA;AAAA;;;AChCF;AAAA;AASA,KAAC,WAAY;AACT;AAEA,UAAI,QACA,OACA,QACA,QACA,OACA,UACA,OACA,OACA,SACA,SACA,aACA;AAEJ,gBAAU;AACV,gBAAU;AAEV,eAAS;AAAA,QACL,iBAAiB;AAAA,QACjB,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,cAAc;AAAA,QACd,eAAe;AAAA,QACf,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,MACxB;AAEA,cAAQ;AAAA,QACJ,SAAS;AAAA;AAAA,QACT,QAAQ;AAAA;AAAA,QACR,MAAM;AAAA;AAAA,QACN,IAAI;AAAA;AAAA,QACJ,IAAI;AAAA;AAAA,QACJ,QAAQ;AAAA;AAAA,QACR,QAAQ;AAAA;AAAA,QACR,QAAQ;AAAA;AAAA,QACR,QAAQ;AAAA;AAAA,QACR,QAAQ;AAAA;AAAA,QACR,QAAQ;AAAA;AAAA,QACR,OAAO;AAAA;AAAA,QACP,OAAO;AAAA;AAAA,QACP,MAAM;AAAA;AAAA,QACN,MAAM;AAAA;AAAA,QACN,UAAU;AAAA;AAAA,QACV,MAAM;AAAA;AAAA,QACN,OAAO;AAAA;AAAA,QACP,MAAM;AAAA;AAAA,QACN,QAAQ;AAAA;AAAA,QACR,QAAQ;AAAA;AAAA,QACR,KAAK;AAAA,MACT;AAEA,eAAS,WAAW,IAAI;AACpB,eAAO,kBAAkB,QAAQ,OAAO,aAAa,EAAE,CAAC,MAAM,MAAM,CAAC,QAAQ,KAAK,aAAa,EAAE,KAAK,CAAC,QAAQ,KAAK,iBAAiB,EAAE;AAAA,MAC3I;AAEA,eAAS,QAAQC,WAAUC,QAAOC,QAAOC,QAAO;AAC5C,aAAK,YAAYH;AACjB,aAAK,SAASC;AACd,aAAK,SAASC;AACd,aAAK,SAASC;AAAA,MAClB;AAEA,cAAQ,UAAU,UAAU,WAAY;AACpC,mBAAW,KAAK;AAChB,gBAAQ,KAAK;AACb,gBAAQ,KAAK;AACb,gBAAQ,KAAK;AAAA,MACjB;AAEA,cAAQ,OAAO,WAAY;AACvB,eAAO,IAAI,QAAQ,UAAU,OAAO,OAAO,KAAK;AAAA,MACpD;AAEA,eAAS,cAAc,MAAM,OAAO;AAChC,YAAI,UAAU;AACV,eAAK,QAAQ,CAAC,MAAM,CAAC,IAAI,aAAa,MAAM,CAAC,IAAI,WAAW;AAAA,QAChE;AACA,eAAO;AAAA,MACX;AAEA,eAAS,UAAU;AACf,YAAI,KAAK,OAAO,OAAO,KAAK;AAC5B,iBAAS;AACT,eAAO;AAAA,MACX;AAEA,eAAS,cAAc,QAAQ;AAC3B,YAAI,GAAG,KAAK,IAAI,OAAO;AAEvB,cAAO,WAAW,MAAO,IAAI;AAC7B,aAAK,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AACtB,cAAI,QAAQ,UAAU,QAAQ,KAAK,WAAW,OAAO,WAAW,KAAK,CAAC,GAAG;AACrE,iBAAK,QAAQ;AACb,mBAAO,OAAO,KAAK,mBAAmB,QAAQ,GAAG,YAAY,CAAC;AAAA,UAClE,OAAO;AACH,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO,OAAO,aAAa,IAAI;AAAA,MACnC;AAEA,eAAS,aAAa;AAClB,YAAI,MAAM,IAAI,OAAO,IAAI,MAAM,WAAW;AAC1C,gBAAQ,OAAO,OAAO,KAAK;AAC3B,UAAE;AAEF,eAAO,QAAQ,QAAQ;AACnB,eAAK,QAAQ;AAEb,cAAI,OAAO,OAAO;AACd,oBAAQ;AACR;AAAA,UACJ,WAAW,OAAO,MAAM;AACpB,iBAAK,QAAQ;AACb,gBAAI,CAAC,QAAQ,KAAK,iBAAiB,GAAG,WAAW,CAAC,CAAC,GAAG;AAClD,sBAAQ,IAAI;AAAA,gBACZ,KAAK;AACD,yBAAO;AACP;AAAA,gBACJ,KAAK;AACD,yBAAO;AACP;AAAA,gBACJ,KAAK;AACD,yBAAO;AACP;AAAA,gBACJ,KAAK;AAAA,gBACL,KAAK;AACD,4BAAU;AACV,8BAAY,cAAc,EAAE;AAC5B,sBAAI,WAAW;AACX,2BAAO;AAAA,kBACX,OAAO;AACH,4BAAQ;AACR,2BAAO;AAAA,kBACX;AACA;AAAA,gBACJ,KAAK;AACD,yBAAO;AACP;AAAA,gBACJ,KAAK;AACD,yBAAO;AACP;AAAA,gBACJ,KAAK;AACD,yBAAO;AACP;AAAA,gBAEJ;AACI,sBAAI,QAAQ,KAAK,aAAa,GAAG,WAAW,CAAC,CAAC,GAAG;AAC7C,2BAAO,WAAW,QAAQ,EAAE;AAQ5B,wBAAI,QAAQ,UAAU,QAAQ,KAAK,aAAa,OAAO,WAAW,KAAK,CAAC,GAAG;AAEvE,6BAAO,OAAO,IAAI,WAAW,QAAQ,QAAQ,CAAC;AAI9C,0BAAI,OAAO,QAAQ,EAAE,KAAK,KAClB,QAAQ,UACR,QAAQ,KAAK,aAAa,OAAO,WAAW,KAAK,CAAC,GAAG;AACzD,+BAAO,OAAO,IAAI,WAAW,QAAQ,QAAQ,CAAC;AAAA,sBAClD;AAAA,oBACJ;AACA,2BAAO,OAAO,aAAa,IAAI;AAAA,kBACnC,OAAO;AACH,2BAAO;AAAA,kBACX;AACA;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,kBAAI,OAAQ,QAAQ,OAAO,WAAW,KAAK,MAAM,IAAkB;AAC/D,kBAAE;AAAA,cACN;AAAA,YACJ;AAAA,UACJ,WAAW,QAAQ,KAAK,iBAAiB,GAAG,WAAW,CAAC,CAAC,GAAG;AACxD;AAAA,UACJ,OAAO;AACH,mBAAO;AAAA,UACX;AAAA,QACJ;AAEA,YAAI,UAAU,IAAI;AACd,kBAAQ,WAAW,kBAAkB;AAAA,QACzC;AAEA,gBAAQ;AACR,eAAO,MAAM;AAAA,MACjB;AAEA,eAAS,aAAa;AAClB,YAAI,QAAQ;AAEZ,iBAAS;AACT,aAAK,OAAO,WAAW,KAAK;AAE5B,YAAI,OAAO,IAAiB;AACxB,mBAAS,QAAQ;AACjB,eAAK,OAAO,WAAW,KAAK;AAE5B,cAAI,WAAW,KAAK;AAChB,gBAAI,OAAO,OAAmB,OAAO,IAAiB;AAClD,wBAAU,QAAQ;AAClB,qBAAO,QAAQ,QAAQ;AACnB,qBAAK,OAAO,WAAW,KAAK;AAC5B,oBAAI,CAAC,QAAQ,KAAK,WAAW,EAAE,GAAG;AAC9B;AAAA,gBACJ;AACA,0BAAU,QAAQ;AAAA,cACtB;AAEA,kBAAI,OAAO,UAAU,GAAG;AAEpB,wBAAQ,WAAW,kBAAkB;AAAA,cACzC;AAEA,kBAAI,QAAQ,QAAQ;AAChB,qBAAK,OAAO,WAAW,KAAK;AAC5B,oBAAI,QAAQ,KAAK,qBAAqB,EAAE,GAAG;AACvC,0BAAQ,WAAW,kBAAkB;AAAA,gBACzC;AAAA,cACJ;AACA,sBAAQ,SAAS,QAAQ,EAAE;AAC3B,qBAAO,MAAM;AAAA,YACjB;AAEA,gBAAI,QAAQ,KAAK,aAAa,EAAE,GAAG;AAC/B,wBAAU,QAAQ;AAClB,qBAAO,QAAQ,QAAQ;AACnB,qBAAK,OAAO,WAAW,KAAK;AAC5B,oBAAI,CAAC,QAAQ,KAAK,aAAa,EAAE,GAAG;AAChC;AAAA,gBACJ;AACA,0BAAU,QAAQ;AAAA,cACtB;AAEA,kBAAI,QAAQ,QAAQ;AAChB,qBAAK,OAAO,WAAW,KAAK;AAC5B,oBAAI,QAAQ,KAAK,qBAAqB,EAAE,KAAK,QAAQ,KAAK,eAAe,EAAE,GAAG;AAC1E,0BAAQ,WAAW,kBAAkB;AAAA,gBACzC;AAAA,cACJ;AACA,sBAAQ,SAAS,QAAQ,CAAC;AAC1B,qBAAO,MAAM;AAAA,YACjB;AAEA,gBAAI,QAAQ,KAAK,eAAe,EAAE,GAAG;AACjC,sBAAQ,WAAW,kBAAkB;AAAA,YACzC;AAAA,UACJ;AAEA,iBAAO,QAAQ,QAAQ;AACnB,iBAAK,OAAO,WAAW,KAAK;AAC5B,gBAAI,CAAC,QAAQ,KAAK,eAAe,EAAE,GAAG;AAClC;AAAA,YACJ;AACA,sBAAU,QAAQ;AAAA,UACtB;AAAA,QACJ;AAEA,YAAI,OAAO,IAAiB;AACxB,oBAAU,QAAQ;AAClB,iBAAO,QAAQ,QAAQ;AACnB,iBAAK,OAAO,WAAW,KAAK;AAC5B,gBAAI,CAAC,QAAQ,KAAK,eAAe,EAAE,GAAG;AAClC;AAAA,YACJ;AACA,sBAAU,QAAQ;AAAA,UACtB;AAAA,QACJ;AAEA,YAAI,OAAO,OAAmB,OAAO,IAAiB;AAClD,oBAAU,QAAQ;AAElB,eAAK,OAAO,WAAW,KAAK;AAC5B,cAAI,OAAO,MAAmB,OAAO,IAAiB;AAClD,sBAAU,QAAQ;AAAA,UACtB;AAEA,eAAK,OAAO,WAAW,KAAK;AAC5B,cAAI,QAAQ,KAAK,eAAe,EAAE,GAAG;AACjC,sBAAU,QAAQ;AAClB,mBAAO,QAAQ,QAAQ;AACnB,mBAAK,OAAO,WAAW,KAAK;AAC5B,kBAAI,CAAC,QAAQ,KAAK,eAAe,EAAE,GAAG;AAClC;AAAA,cACJ;AACA,wBAAU,QAAQ;AAAA,YACtB;AAAA,UACJ,OAAO;AACH,oBAAQ,WAAW,kBAAkB;AAAA,UACzC;AAAA,QACJ;AAEA,YAAI,QAAQ,QAAQ;AAChB,eAAK,OAAO,WAAW,KAAK;AAC5B,cAAI,QAAQ,KAAK,qBAAqB,EAAE,GAAG;AACvC,oBAAQ,WAAW,kBAAkB;AAAA,UACzC;AAAA,QACJ;AAEA,gBAAQ,WAAW,MAAM;AACzB,eAAO,MAAM;AAAA,MACjB;AAGA,eAAS,eAAe;AACpB,YAAI,IAAI;AAER,gBAAQ,QAAQ;AAChB,eAAO,QAAQ,UAAU,WAAW,OAAO,WAAW,KAAK,CAAC,GAAG;AAC3D,eAAK,OAAO,WAAW,KAAK;AAC5B,cAAI,OAAO,IAAiB;AACxB,gBAAK,QAAQ,KAAM,QAAQ;AACvB,qBAAO,MAAM;AAAA,YACjB;AACA,kBAAM,OAAO,WAAW,QAAQ,CAAC;AACjC,gBAAI,QAAQ,IAAiB;AACzB;AAAA,YACJ;AAAA,UACJ;AACA,mBAAS,QAAQ;AAAA,QACrB;AACA,eAAO,MAAM;AAAA,MACjB;AAEA,eAAS,OAAO;AACZ,YAAI;AAEJ,mBAAW;AAEX,eAAO,QAAQ,UAAU,QAAQ,KAAK,aAAa,OAAO,WAAW,KAAK,CAAC,GAAG;AAC1E,kBAAQ;AAAA,QACZ;AACA,YAAI,SAAS,QAAQ;AACjB,kBAAQ,MAAM;AACd,iBAAO;AAAA,QACX;AAEA,aAAK,OAAO,WAAW,KAAK;AAC5B,gBAAQ,IAAI;AAAA,UACZ,KAAK;AAAA;AAAA,UACL,KAAK;AACD,oBAAQ,WAAW;AACnB,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,gBAAI,QAAQ,IAAI,QAAQ;AACpB,mBAAK,OAAO,WAAW,QAAQ,CAAC;AAChC,kBAAI,OAAO,IAAiB;AACxB,wBAAQ;AACR,wBAAQ;AACR,wBAAQ,MAAM;AACd,uBAAO;AAAA,cACX;AAEA,kBAAI,OAAO,MAAmB,QAAQ,IAAI,UAAU,OAAO,WAAW,QAAQ,CAAC,MAAM,IAAiB;AAClG,wBAAQ;AACR,wBAAQ;AACR,wBAAQ;AACR,wBAAQ,MAAM;AACd,uBAAO;AAAA,cACX;AAEA,kBAAI,QAAQ,KAAK,eAAe,EAAE,GAAG;AACjC,wBAAQ,WAAW;AACnB,uBAAO;AAAA,cACX;AAAA,YACJ;AACA,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ;AACR,oBAAQ,MAAM;AACd,mBAAO;AAAA,UAEX,KAAK;AACD,oBAAQ,WAAW;AACnB,mBAAO;AAAA,UAEX;AACI,gBAAI,QAAQ,KAAK,eAAe,EAAE,GAAG;AACjC,sBAAQ,WAAW;AACnB,qBAAO;AAAA,YACX;AAOA,oBAAQ,OAAO,WAAW,EAAE,CAAC;AAC7B,oBAAQ,aAAa;AACrB,mBAAO;AAAA,QACX;AAAA,MACJ;AAEA,eAAS,QAAQ,QAAQ,MAAM;AAC3B,gBAAQ,OAAO,UAAU,QAAQ,QAAQ,4BAA4B;AACrE,aAAK;AAAA,MACT;AAEA,eAAS,OAAO,QAAQ,SAAS;AAC7B,YAAI,UAAU,QAAQ;AAClB,kBAAQ,WAAW,WAAW,kBAAkB;AAAA,QACpD;AACA,aAAK;AAAA,MACT;AAWA,eAAS,iBAAiB;AACtB,YAAI,UAAU,aAAa,QAAQ;AACnC,gBAAQ,MAAM,QAAQ,+BAA+B;AACrD,mBAAW,CAAC;AACZ,YAAI,UAAU,MAAM,QAAQ;AACxB,iBAAO,MAAM;AACT,qBAAS,KAAK,oBAAoB,CAAC;AACnC,gBAAI,UAAU,MAAM,QAAQ;AACxB;AAAA,YACJ;AACA,mBAAO,MAAM,IAAI;AAAA,UACrB;AAAA,QACJ;AACA,gBAAQ,MAAM,QAAQ,6BAA6B;AACnD,eAAO,cAAc;AAAA,UACjB,MAAM,OAAO;AAAA,UACb;AAAA,QACJ,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,MAC7B;AASA,eAAS,iBAAiB;AACtB,YAAI,UAAU,aAAa,QAAQ,GAAG;AACtC,gBAAQ,MAAM,QAAQ,+BAA+B;AACrD,mBAAW,CAAC;AACZ,eAAO,UAAU,MAAM,QAAQ;AAC3B,cAAI,UAAU,MAAM,MAAM;AACtB,6BAAiB,QAAQ;AACzB,oBAAQ,MAAM,IAAI;AAClB,qBAAS,KAAK,cAAc;AAAA,cACxB,MAAM,OAAO;AAAA,cACb,YAAY,oBAAoB;AAAA,YACpC,GAAG,CAAC,gBAAgB,QAAQ,CAAC,CAAC;AAC9B;AAAA,UACJ,OAAO;AACH,qBAAS,KAAK,oBAAoB,CAAC;AAAA,UACvC;AACA,cAAI,UAAU,MAAM,QAAQ;AACxB,mBAAO,MAAM,KAAK;AAAA,UACtB;AAAA,QACJ;AACA,eAAO,MAAM,MAAM;AACnB,eAAO,cAAc;AAAA,UACjB,MAAM,OAAO;AAAA,UACb;AAAA,QACJ,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,MAC7B;AAEA,eAAS,iBAAiB;AACtB,YAAI,IAAI;AACR,YAAI,UAAU,MAAM,QAAQ,UAAU,MAAM,QAAQ;AAChD,eAAK;AACL,iBAAO;AAAA,QACX;AAEA,YAAI,UAAU,MAAM,QAAQ;AACxB,kBAAQ,MAAM,MAAM;AACpB,iBAAO,OAAO,CAAC;AAAA,QACnB;AAEA,gBAAQ,WAAW,kBAAkB;AAAA,MACzC;AAWA,eAAS,iBAAiB;AACtB,YAAI,KAAK,aAAa;AAEtB,cAAM,eAAe;AACrB,YAAI,UAAU,MAAM,OAAO;AACvB,kBAAQ,MAAM,KAAK;AACnB,iBAAO,cAAc;AAAA,YACjB,MAAM,OAAO;AAAA,YACb;AAAA,YACA,OAAO,oBAAoB;AAAA,UAC/B,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,QAC7B;AACA,eAAO,cAAc;AAAA,UACjB,MAAM,OAAO;AAAA,UACb;AAAA,UACA,OAAO;AAAA,QACX,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,MAC7B;AAQA,eAAS,kBAAkB;AACvB,YAAI,QAAQ,aAAa,QAAQ,GAAG;AAEpC,gBAAQ,MAAM,QAAQ,gCAAgC;AACtD,iBAAS,CAAC;AACV,YAAI,UAAU,MAAM,OAAO;AACvB,kBAAQ,MAAM,KAAK;AAAA,QACvB,OAAO;AACH,iBAAO,UAAU,MAAM,QAAQ;AAC3B,mBAAO,KAAK,eAAe,CAAC;AAC5B,gBAAI,UAAU,MAAM,QAAQ;AACxB,qBAAO,MAAM,KAAK;AAAA,YACtB;AAAA,UACJ;AAAA,QACJ;AACA,mBAAW;AACX,eAAO,MAAM,MAAM;AACnB,eAAO,cAAc;AAAA,UACjB,MAAM,OAAO;AAAA,UACb;AAAA,QACJ,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,MAC7B;AASA,eAAS,sBAAsB;AAC3B,YAAI,OAAO,OAAO,aAAa,QAAQ,KAAK;AAC5C,eAAO,MAAM,IAAI;AAEjB,YAAI,UAAU,MAAM,UACZ,SAAS,YACT,SAAS,cACT,SAAS,UAAU;AACvB,kBAAQ,MAAM,KAAK;AACnB,kBAAQ,MAAM;AACd,iBAAO,MAAM,IAAI;AAAA,QACrB;AAEA,eAAO,cAAc;AAAA,UACjB,MAAM,OAAO;AAAA,UACb;AAAA,QACJ,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,MAC7B;AAKA,eAAS,0BAA0B;AAC/B,YAAI,WAAW,CAAC;AAEhB,iBAAS,KAAK,SAAS,CAAC;AACxB,eAAO,UAAU,MAAM,OAAO;AAC1B,kBAAQ,MAAM,KAAK;AACnB,mBAAS,KAAK,SAAS,CAAC;AAAA,QAC5B;AACA,eAAO;AAAA,MACX;AASA,eAAS,gBAAgB;AACrB,YAAI,MAAM,cAAc,aAAa,QAAQ,MAAM;AAEnD,eAAO,oBAAoB;AAC3B,YAAI,UAAU,MAAM,UAAU,UAAU,MAAM,IAAI;AAC9C,eAAK;AACL,yBAAe,wBAAwB;AACvC,iBAAO,MAAM,EAAE;AACf,iBAAO,cAAc;AAAA,YACjB,MAAM,OAAO;AAAA,YACb,YAAY;AAAA,YACZ;AAAA,UACJ,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,QAC7B;AACA,eAAO;AAAA,MACX;AASA,eAAS,kBAAkB;AACvB,gBAAQ,MAAM,OAAO,gCAAgC;AACrD,YAAI,UAAU,MAAM,QAAQ,UAAU,QAAQ;AAC1C,kBAAQ,MAAM,IAAI;AAClB,iBAAO;AAAA,YACH,MAAM,OAAO;AAAA,UACjB;AAAA,QACJ;AACA,eAAO,oBAAoB;AAAA,MAC/B;AAyBA,eAAS,sBAAsB;AAC3B,YAAI,SAAS,CAAC,GAAG,mBAAmB,OAAO,MAAM,OAAO,OAAO,YAAY,iBAAiB,QAAQ,GAAG;AAEvG,eAAO,UAAU,MAAM,QAAQ;AAC3B,cAAI,UAAU,MAAM,MAAM;AAEtB,oBAAQ,MAAM,IAAI;AAClB,mBAAO;AAAA,UACX;AAEA,uBAAa;AAEb,iBAAO,oBAAoB;AAC3B,cAAI,KAAK,SAAS,OAAO,kBAAkB,UAAU,MAAM,OAAO;AAC9D,6BAAiB,WAAW,KAAK,KAAK;AAEtC,oBAAQ,MAAM,KAAK;AACnB,mBAAO,cAAc;AAAA,cACjB,MAAM,OAAO;AAAA,cACb,MAAM,KAAK;AAAA,cACX,YAAY,oBAAoB;AAAA,YACpC,GAAG,CAAC,gBAAgB,QAAQ,CAAC;AAAA,UACjC;AACA,cAAI,UAAU,MAAM,OAAO;AACvB,oBAAQ,MAAM,KAAK;AACnB,mBAAO,cAAc;AAAA,cACjB,MAAM,OAAO;AAAA,cACb,YAAY;AAAA,YAChB,GAAG,CAAC,YAAY,QAAQ,CAAC;AACzB,+BAAmB;AAAA,UACvB,OAAO;AACH,gBAAI,kBAAkB;AAClB,sBAAQ,WAAW,kBAAkB;AAAA,YACzC;AAAA,UACJ;AACA,cAAI,MAAM;AACN,mBAAO,cAAc;AAAA,cACjB,MAAM,OAAO;AAAA,cACb,YAAY;AAAA,YAChB,GAAG,CAAC,gBAAgB,QAAQ,CAAC;AAAA,UACjC;AACA,iBAAO,KAAK,IAAI;AAChB,cAAI,UAAU,MAAM,QAAQ;AACxB,mBAAO,MAAM,KAAK;AAAA,UACtB;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AASA,eAAS,oBAAoB;AACzB,YAAI,OAAO,aAAa,QAAQ,QAAQ,QAAQ,aAAa,QAAQ,MAAM;AAC3E,gBAAQ,OAAO,UAAU,MAAM,QAAQ,UAAU,YAAY,2CAA6C;AAC1G,gBAAQ,MAAM,IAAI;AAIlB,eAAO,MAAM,MAAM;AAEnB,gBAAQ;AACR,iBAAS,CAAC;AACV,sBAAc;AACd,YAAI,UAAU,MAAM,QAAQ;AAExB,cAAI,UAAU,MAAM,SACX,UAAU,UAAU,UAAU,QAAQ;AAG3C,oBAAQ,UAAU;AAClB,oBAAQ,MAAM,IAAI;AAClB,mBAAO,MAAM,KAAK;AAClB,0BAAc,cAAc;AAC5B,gBAAI,UAAU,MAAM,OAAO;AACvB,sBAAQ,MAAM,KAAK;AACnB,uBAAS,oBAAoB;AAAA,YACjC;AAAA,UACJ,OAAO;AACH,qBAAS,oBAAoB;AAAA,UACjC;AAAA,QACJ;AAEA,eAAO,MAAM,MAAM;AAEnB,iBAAS;AACT,YAAI,UAAU,MAAM,OAAO;AACvB,mBAAS,gBAAgB;AAAA,QAC7B;AAEA,iBAAS,cAAc;AAAA,UACnB,MAAM,OAAO;AAAA,UACb;AAAA,UACA;AAAA,QACJ,GAAG,CAAC,YAAY,QAAQ,CAAC;AACzB,YAAI,aAAa;AAEb,iBAAO,MAAM,IAAI;AACjB,cAAI,OAAO;AACP,mBAAO,KAAK,IAAI;AAAA,UACpB;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAWA,eAAS,2BAA2B;AAChC,YAAI,SAAS;AACb,gBAAQ,OAAO;AAAA,UACf,KAAK,MAAM;AACP,oBAAQ,MAAM,IAAI;AAClB,mBAAO,cAAc;AAAA,cACjB,MAAM,OAAO;AAAA,YACjB,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC;AAAA,UAE/B,KAAK,MAAM;AACP,mBAAO,eAAe;AAAA,UAE1B,KAAK,MAAM;AACP,mBAAO,eAAe;AAAA,UAE1B,KAAK,MAAM;AACP,mBAAO,gBAAgB;AAAA,UAE3B,KAAK,MAAM;AACP,yBAAa,QAAQ,MAAM;AAE3B,gBAAI,UAAU,QAAQ;AAClB,sBAAQ,MAAM,IAAI;AAClB,qBAAO,cAAc;AAAA,gBACjB,MAAM,OAAO;AAAA,cACjB,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,YAC7B;AAEA,gBAAI,UAAU,aAAa;AACvB,sBAAQ,MAAM,IAAI;AAClB,qBAAO,cAAc;AAAA,gBACjB,MAAM,OAAO;AAAA,cACjB,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,YAC7B;AAEA,gBAAI,UAAU,UAAU,UAAU,SAAS;AACvC,sBAAQ,MAAM,IAAI;AAClB,qBAAO,cAAc;AAAA,gBACjB,MAAM,OAAO;AAAA,gBACb,OAAO,UAAU;AAAA,cACrB,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,YAC7B;AAEA,sBAAU,QAAQ,KAAK;AACvB,gBAAI,UAAU,YAAY;AACtB,kBAAI;AACA,uBAAO,kBAAkB;AAAA,cAC7B,SAAS,GAAG;AACR,wBAAQ,QAAQ;AAAA,cACpB;AAAA,YACJ;AAEA,mBAAO,cAAc;AAAA,UAEzB,KAAK,MAAM;AACP,iBAAK;AACL,mBAAO,cAAc;AAAA,cACjB,MAAM,OAAO;AAAA,cACb;AAAA,YACJ,GAAG,CAAC,WAAW,MAAM,SAAS,GAAG,QAAQ,CAAC;AAAA,UAE9C,KAAK,MAAM;AACP,iBAAK;AACL,mBAAO,cAAc;AAAA,cACjB,MAAM,OAAO;AAAA,cACb;AAAA,YACJ,GAAG,CAAC,WAAW,OAAO,KAAK,EAAE,QAAQ,QAAQ,CAAC;AAAA,UAElD;AACI,oBAAQ,WAAW,kBAAkB;AAAA,QACzC;AAAA,MACJ;AAUA,eAAS,sBAAsB;AAC3B,YAAI,MAAM;AAEV,YAAI,UAAU,MAAM,UAAU;AAC1B,uBAAa,QAAQ;AACrB,kBAAQ,MAAM,QAAQ;AACtB,cAAI,UAAU,MAAM,SAAS,UAAU,MAAM,SAAS,UAAU,MAAM,UAC9D,UAAU,MAAM,UAAU,UAAU,MAAM,QAAQ,UAAU,MAAM,OAClE,UAAU,MAAM,UAAU,UAAU,MAAM,IAAI;AAClD,mBAAO,cAAc;AAAA,cACjB,MAAM,OAAO;AAAA,YACjB,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,UAC7B;AACA,iBAAO,cAAc;AAAA,YACjB,MAAM,OAAO;AAAA,YACb,YAAY,yBAAyB;AAAA,YACrC,QAAQ;AAAA,UACZ,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,QAC7B,WAAW,UAAU,MAAM,MAAM;AAC7B,uBAAa,QAAQ;AACrB,kBAAQ,MAAM,IAAI;AAClB,iBAAO,cAAc;AAAA,YACjB,MAAM,OAAO;AAAA,YACb,YAAY,yBAAyB;AAAA,YACrC,QAAQ;AAAA,UACZ,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,QAC7B,OAAO;AACH,uBAAa;AAAA,QACjB;AAEA,eAAO,yBAAyB;AAChC,YAAI,UAAU,MAAM,MAAM;AACtB,kBAAQ,MAAM,IAAI;AAClB,iBAAO,cAAc;AAAA,YACjB,MAAM,OAAO;AAAA,YACb,YAAY;AAAA,YACZ,QAAQ;AAAA,UACZ,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,QAC7B;AAEA,YAAI,UAAU,MAAM,UAAU;AAC1B,kBAAQ,MAAM,QAAQ;AACtB,iBAAO,cAAc;AAAA,YACjB,MAAM,OAAO;AAAA,YACb,YAAY;AAAA,YACZ,QAAQ;AAAA,UACZ,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,QAC7B;AAEA,YAAI,UAAU,MAAM,QAAQ;AACxB,kBAAQ,MAAM,MAAM;AACpB,iBAAO,MAAM,QAAQ,+CAA+C,QAAQ,KAAK;AACjF,iBAAO,cAAc;AAAA,YACjB,MAAM,OAAO;AAAA,YACb,YAAY,cAAc;AAAA,cACtB,MAAM,OAAO;AAAA,cACb,MAAM;AAAA,YACV,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,YACzB,cAAc,CAAC,IAAI;AAAA,UACvB,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,QAC7B;AAEA,eAAO;AAAA,MACX;AAWA,eAAS,WAAW;AAChB,YAAI,MAAM;AAEV,eAAO,oBAAoB;AAC3B,YAAI,UAAU,MAAM,MAAM;AACtB,iBAAO;AAAA,QACX;AAEA,mBAAW,CAAC,IAAI;AAChB,gBAAQ,MAAM,IAAI;AAClB,eAAO,MAAM;AACT,mBAAS,KAAK,oBAAoB,CAAC;AACnC,cAAI,UAAU,MAAM,MAAM;AACtB;AAAA,UACJ;AACA,kBAAQ,MAAM,IAAI;AAAA,QACtB;AAEA,eAAO,cAAc;AAAA,UACjB,MAAM,OAAO;AAAA,UACb;AAAA,QACJ,GAAG,CAAC,GAAG,KAAK,CAAC;AAAA,MACjB;AAEA,eAAS,oBAAoB;AACzB,YAAI;AAEJ,YAAI,UAAU,MAAM,MAAM;AACtB,kBAAQ,MAAM,IAAI;AAClB,iBAAO,cAAc;AAAA,YACjB,MAAM,OAAO;AAAA,YACb,YAAY,SAAS;AAAA,UACzB,GAAG,CAAC,GAAG,KAAK,CAAC;AAAA,QACjB;AAEA,eAAO,SAAS;AAChB,YAAI,UAAU,MAAM,OAAO;AACvB,kBAAQ,MAAM,KAAK;AACnB,iBAAO,cAAc;AAAA,YACjB,MAAM,OAAO;AAAA,YACb,YAAY;AAAA,UAChB,GAAG,CAAC,GAAG,KAAK,CAAC;AAAA,QACjB;AAEA,eAAO;AAAA,MACX;AAEA,eAAS,UAAU,KAAK,KAAK;AACzB,YAAI;AAEJ,iBAAS;AACT,iBAAS,OAAO;AAChB,gBAAQ;AACR,mBAAW;AACX,mBAAW,OAAO,IAAI;AACtB,sBAAc,OAAO,IAAI,cAAc;AAEvC,aAAK;AACL,eAAO,SAAS;AAEhB,YAAI,OAAO,IAAI,WAAW;AACtB,iBAAO;AAAA,YACH,YAAY;AAAA,YACZ,OAAO;AAAA,UACX;AAAA,QACJ;AAEA,YAAI,UAAU,MAAM,KAAK;AACrB,kBAAQ,WAAW,kBAAkB;AAAA,QACzC;AAEA,eAAO;AAAA,MACX;AAEA,eAAS,eAAe,KAAK,KAAK;AAC9B,YAAI;AAEJ,iBAAS;AACT,iBAAS,OAAO;AAChB,gBAAQ;AACR,mBAAW;AACX,mBAAW,OAAO,IAAI;AACtB,sBAAc,OAAO,IAAI,cAAc;AAEvC,aAAK;AACL,eAAO,kBAAkB;AAEzB,YAAI,OAAO,IAAI,WAAW;AACtB,iBAAO;AAAA,YACH,YAAY;AAAA,YACZ,OAAO;AAAA,UACX;AAAA,QACJ;AAEA,YAAI,UAAU,MAAM,KAAK;AACrB,kBAAQ,WAAW,kBAAkB;AAAA,QACzC;AAEA,eAAO;AAAA,MACX;AAEA,eAAS,cAAc,MAAM,SAAS,UAAU;AAC5C,YAAI,QAAQ,GAAG;AAEf,gBAAQ,KAAK,MAAM;AAAA,UACnB,KAAK,OAAO;AACR,qBAAS;AACT;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS;AACT;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS;AACT;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS;AACT;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS;AACT;AAAA,UAEJ,KAAK,OAAO;AACR,gBAAI,CAAC,UAAU;AACX,uBAAS;AAAA,YACb,OAAO;AACH,uBAAS;AAAA,YACb;AAEA,iBAAK,IAAI,GAAG,KAAK,KAAK,SAAS,QAAQ,IAAI,IAAI,EAAE,GAAG;AAChD,wBAAU,cAAc,KAAK,SAAS,CAAC,GAAG,OAAO;AACjD,kBAAK,IAAI,MAAO,IAAI;AAChB,0BAAU,UAAU,MAAM;AAAA,cAC9B;AAAA,YACJ;AAEA,gBAAI,CAAC,UAAU;AACX,wBAAU;AAAA,YACd;AACA;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS;AACT,iBAAK,IAAI,GAAG,KAAK,KAAK,SAAS,QAAQ,IAAI,IAAI,EAAE,GAAG;AAChD,wBAAU,cAAc,KAAK,SAAS,CAAC,GAAG,OAAO;AACjD,kBAAK,IAAI,MAAO,IAAI;AAChB,0BAAU,UAAU,MAAM;AAAA,cAC9B;AAAA,YACJ;AACA,sBAAU;AACV;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS;AACT,iBAAK,IAAI,GAAG,KAAK,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,wBAAU,cAAc,KAAK,OAAO,CAAC,GAAG,OAAO;AAC/C,kBAAK,IAAI,MAAO,IAAI;AAChB,0BAAU,UAAU,MAAM;AAAA,cAC9B;AAAA,YACJ;AACA,sBAAU;AACV;AAAA,UAEJ,KAAK,OAAO;AACR,gBAAI,KAAK,OAAO;AACZ,uBAAS,KAAK,OAAO,UAAU,MAAM,QAAQ,cAAc,KAAK,OAAO,OAAO;AAAA,YAClF,OAAO;AACH,uBAAS,KAAK;AAAA,YAClB;AACA;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS,UAAU,cAAc;AAEjC,gBAAI,KAAK,MAAM,GAAG;AACd,kBAAI,KAAK,KAAK,GAAG;AACb,0BAAW,UAAU,SAAS;AAAA,cAClC,OAAO;AACH,0BAAW,UAAU,UAAU;AAAA,cACnC;AAEA,wBAAU,cAAc,KAAK,MAAM,GAAG,OAAO;AAE7C,kBAAI,KAAK,OAAO,WAAW,GAAG;AAC1B,0BAAU,UAAU,MAAM;AAAA,cAC9B;AAAA,YACJ;AAEA,iBAAK,IAAI,GAAG,KAAK,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC9C,wBAAU,cAAc,KAAK,OAAO,CAAC,GAAG,OAAO;AAC/C,kBAAK,IAAI,MAAO,IAAI;AAChB,0BAAU,UAAU,MAAM;AAAA,cAC9B;AAAA,YACJ;AAEA,sBAAU;AAEV,gBAAI,KAAK,QAAQ;AACb,yBAAW,UAAU,MAAM,QAAQ,cAAc,KAAK,QAAQ,OAAO;AAAA,YACzE;AACA;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS,KAAK,QAAQ,UAAU,MAAM,QAAQ,cAAc,KAAK,YAAY,OAAO;AACpF;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS;AACT,gBAAI,KAAK,YAAY;AACjB,wBAAU,cAAc,KAAK,YAAY,OAAO;AAAA,YACpD;AACA;AAAA,UAEJ,KAAK,OAAO;AACR,gBAAI,KAAK,QAAQ;AACb,uBAAS,MAAM,cAAc,KAAK,YAAY,OAAO;AAAA,YACzD,OAAO;AACH,uBAAS,cAAc,KAAK,YAAY,OAAO,IAAI;AAAA,YACvD;AACA;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS,cAAc,KAAK,YAAY,OAAO,IAAI;AACnD;AAAA,UAEJ,KAAK,OAAO;AACR,gBAAI,KAAK,QAAQ;AACb,uBAAS,MAAM,cAAc,KAAK,YAAY,OAAO;AAAA,YACzD,OAAO;AACH,uBAAS,cAAc,KAAK,YAAY,OAAO,IAAI;AAAA,YACvD;AACA;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS,KAAK;AACd;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS,cAAc,KAAK,YAAY,OAAO,IAAI;AACnD,iBAAK,IAAI,GAAG,KAAK,KAAK,aAAa,QAAQ,IAAI,IAAI,EAAE,GAAG;AACpD,wBAAU,cAAc,KAAK,aAAa,CAAC,GAAG,OAAO;AACrD,kBAAK,IAAI,MAAO,IAAI;AAChB,0BAAU,UAAU,MAAM;AAAA,cAC9B;AAAA,YACJ;AACA,sBAAU;AACV;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS,MAAM,KAAK,QAAQ;AAC5B;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS,OAAO,KAAK,KAAK;AAC1B;AAAA,UAEJ,KAAK,OAAO;AACR,qBAAS,OAAO,KAAK,KAAK;AAC1B;AAAA,UAEJ;AACI,oBAAQ,WAAW,kBAAkB,KAAK,IAAI;AAAA,QAClD;AAEA,eAAO;AAAA,MACX;AAEA,eAAS,UAAU,MAAM,SAAS;AAC9B,YAAI,WAAW,MAAM;AACjB,oBAAU,CAAC;AAAA,QACf;AACA,eAAO,cAAc,MAAM,QAAQ,SAAS,QAAQ,QAAQ;AAAA,MAChE;AAEA,cAAQ,YAAY;AACpB,cAAQ,iBAAiB;AACzB,cAAQ,YAAY;AACpB,cAAQ,SAAS;AAAA,IACrB,GAAE;AAAA;AAAA;;;ACvxCF;AAAA;AAOA,KAAC,WAAY;AACT;AAEA,UAAI,OACA,SACA,OACA,SACA;AAEJ,gBAAU;AACV,cAAQ;AACR,gBAAU;AAEV,eAAS,YAAY,QAAQ,OAAO,MAAM;AACtC,eAAO,OAAO,MAAM,OAAO,IAAI;AAAA,MACnC;AAEA,uBAAkB,4BAAY;AAC1B,YAAI,OAAO,OAAO,UAAU;AAC5B,eAAO,SAASC,gBAAe,KAAK,MAAM;AACtC,iBAAO,KAAK,KAAK,KAAK,IAAI;AAAA,QAC9B;AAAA,MACJ,GAAE;AACF,eAAS,YAAY,KAAK;AACtB,YAAI,MAAM,CAAC,GAAG;AACd,aAAK,OAAO,KAAK;AACb,cAAI,IAAI,eAAe,GAAG,GAAG;AACzB,gBAAI,GAAG,IAAI,IAAI,GAAG;AAAA,UACtB;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAEA,eAAS,oBAAoB,IAAI;AAC7B,eAAQ,MAAM,MAAmB,MAAM,OAClC,MAAM,MAAmB,MAAM,MAC/B,MAAM,MAAmB,MAAM;AAAA,MACxC;AAEA,eAAS,aAAa,OAAO;AACzB,eAAO,UAAU,WAAW,UAAU,cAAc,UAAU;AAAA,MAClE;AAEA,eAAS,cAAc,OAAO;AAC1B,eAAO,UAAU,YAAY,UAAU;AAAA,MAC3C;AAEA,eAAS,WAAW,OAAO;AACvB,eAAO,UAAU,cAAc,UAAU;AAAA,MAC7C;AAEA,eAAS,wBAAwB,OAAO;AACpC,eAAO,aAAa,KAAK,KAAK,WAAW,KAAK,KAC1C,UAAU,WAAW,UAAU,UAAU,UAAU,WAAW,UAAU;AAAA,MAChF;AAEA,eAAS,cAAc,OAAO;AAC1B,eAAO,wBAAwB,KAAK,KAAK,UAAU,WAAW,UAAU;AAAA,MAC5E;AAEA,eAAS,gBAAgB,OAAO;AAC5B,eAAO,WAAW,KAAK,KAAK,aAAa,KAAK;AAAA,MAClD;AAEA,eAAS,kBAAkB,OAAO;AAC9B,eAAO,WAAW,KAAK,KAAK,aAAa,KAAK;AAAA,MAClD;AAEA,eAAS,wBAAwB,OAAO;AACpC,eAAO,aAAa,KAAK,KAAK,cAAc,KAAK,KAC7C,UAAU,YAAY,UAAU,UAChC,UAAU,gBAAgB,UAAU,UACpC,UAAU,UAAU,UAAU,aAAa,WAAW,KAAK;AAAA,MACnE;AAIA,eAAS,cAAc,OAAO;AAC1B,eAAO,wBAAwB,KAAK,KAAK,UAAU,YAAY,UAAU,WAAW,UAAU,cAC1F,UAAU,eAAe,UAAU,YAAY,UAAU,SAAS,UAAU,YAC5E,UAAU,iBAAiB,UAAU,WAAW,UAAU,aAAa,UAAU,cACjF,UAAU,YAAY,UAAU,aAAa,UAAU;AAAA,MAC/D;AAGA,UAAI,aAAa;AAEjB,UAAI,eAAe,MAAM,aAAa,YAAY,aAAa;AAE/D,eAAS,cAAc,KAAK;AAMxB,eAAO,IAEH,QAAQ,YAAY,EAAE,EAEtB,QAAQ,SAAS,EAAE,EAEnB,QAAQ,IAAI,OAAO,cAAc,GAAG,GAAG,IAAI,EAE3C,QAAQ,QAAQ,EAAE;AAAA,MAC1B;AAQA,eAAS,6BAA6B,gBAAgB,gBAAgB;AAClE,YAAI,iBAAiB,eAAe,QAAQ,YAAY,EAAE;AAC1D,YAAI,kBAAkB;AACtB,YAAI,UAAU,IAAI,OAAO,cAAc,GAAG;AAC1C,YAAI;AAEJ,eAAQ,QAAQ,QAAQ,KAAK,cAAc,GAAI;AAC3C,6BAAmB,MAAM,CAAC,EAAE;AAE5B,cAAI,MAAM,QAAQ,MAAM,CAAC,EAAE,SAAS,iBAAiB,iBAAiB;AAClE,mBAAO,iBAAiB,kBAAkB,eAAe,SAAS,eAAe;AAAA,UACrF;AAAA,QACJ;AAEA,eAAO,eAAe,QAAQ,SAAS,EAAE,EAAE,QAAQ,QAAQ,EAAE,EAAE;AAAA,MACnE;AAIA,OAAC,SAAUC,UAAS;AAChB,YAAI,OACA,OACA,YACA,QACA,QACA,gBACA,aACA,QACA;AAEJ,iBAAS,UAAU;AACf,cAAI,KAAK,OAAO,WAAW,KAAK;AAChC,mBAAS;AACT,cAAI,QAAQ,KAAK,iBAAiB,EAAE,KAAK,EAAE,OAAO,MAAoB,OAAO,WAAW,KAAK,MAAM,KAAmB;AAClH,0BAAc;AAAA,UAClB;AACA,iBAAO,OAAO,aAAa,EAAE;AAAA,QACjC;AAEA,iBAAS,YAAY;AACjB,cAAI,QAAQ;AAEZ,kBAAQ;AAER,iBAAO,QAAQ,UAAU,oBAAoB,OAAO,WAAW,KAAK,CAAC,GAAG;AACpE,qBAAS,QAAQ;AAAA,UACrB;AAEA,iBAAO;AAAA,QACX;AAEA,iBAAS,cAAc;AACnB,cAAI,IAAI,SAAS,OAAO;AAExB,oBAAU;AACV,iBAAO,OAAO,QAAQ;AAClB,iBAAK,OAAO,WAAW,IAAI;AAC3B,gBAAI,QAAQ,KAAK,iBAAiB,EAAE,KAAK,EAAE,OAAO,MAAoB,OAAO,WAAW,OAAO,CAAC,MAAM,KAAmB;AACrH,wBAAU;AAAA,YACd,WAAW,SAAS;AAChB,kBAAI,OAAO,IAAiB;AACxB;AAAA,cACJ;AACA,kBAAI,CAAC,QAAQ,KAAK,aAAa,EAAE,GAAG;AAChC,0BAAU;AAAA,cACd;AAAA,YACJ;AACA,oBAAQ;AAAA,UACZ;AACA,iBAAO;AAAA,QACX;AAMA,iBAAS,UAAU,OAAO,MAAM,UAAU;AACtC,cAAI,IAAI,OAAO,MAAM,YAAY,SAAS;AAI1C,iBAAO,QAAQ,MAAM;AACjB,iBAAK,OAAO,WAAW,KAAK;AAC5B,gBAAI,QAAQ,KAAK,aAAa,EAAE,GAAG;AAC/B,sBAAQ;AAAA,YACZ,WAAW,OAAO,KAAiB;AAC/B,sBAAQ;AACR;AAAA,YACJ,OAAO;AAEH,uBAAS;AACT;AAAA,YACJ;AAAA,UACJ;AAGA,cAAI,QAAQ;AACR,mBAAO;AAAA,UACX;AAGA,kBAAQ;AACR,iBAAO;AACP,iBAAO,QAAQ,MAAM;AACjB,iBAAK,OAAO,WAAW,KAAK;AAC5B,gBAAI,QAAQ,KAAK,iBAAiB,EAAE,GAAG;AACnC,sBAAQ;AAAA,YACZ,OAAO;AACH,kBAAI,OAAO,KAAiB;AACxB,yBAAS;AACT,oBAAI,UAAU,GAAG;AACb,0BAAQ;AACR;AAAA,gBACJ;AAAA,cACJ,WAAW,OAAO,KAAiB;AAC/B,yBAAS;AAAA,cACb;AACA,kBAAI,SAAS,IAAI;AACb,6BAAa;AAAA,cACjB;AACA,sBAAQ,QAAQ;AAAA,YACpB;AAAA,UACJ;AAEA,cAAI,UAAU,GAAG;AAEb,mBAAO,QAAQ,WAAW,yBAAyB;AAAA,UACvD;AAEA,cAAI,kBAAkB,KAAK,GAAG;AAC1B,mBAAO,MAAM,eAAe,MAAM,EAAC,YAAY,aAAa,UAAU,GAAG,OAAO,SAAQ,CAAC;AAAA,UAC7F;AAEA,iBAAO,MAAM,UAAU,MAAM,EAAC,YAAY,aAAa,UAAU,GAAG,OAAO,SAAQ,CAAC;AAAA,QACxF;AAEA,iBAAS,eAAe,MAAM;AAC1B,cAAI;AACJ,cAAI,CAAC,QAAQ,KAAK,qBAAqB,OAAO,WAAW,KAAK,CAAC,KAAK,CAAC,OAAO,KAAK,EAAE,MAAM,OAAO,GAAG;AAC/F,mBAAO;AAAA,UACX;AACA,uBAAa,QAAQ;AACrB,iBAAO,QAAQ,QAAQ,QAAQ,KAAK,oBAAoB,OAAO,WAAW,KAAK,CAAC,GAAG;AAC/E,0BAAc,QAAQ;AAAA,UAC1B;AACA,iBAAO;AAAA,QACX;AAEA,iBAAS,eAAe,MAAM;AAC1B,iBAAO,QAAQ,SAAS,QAAQ,KAAK,aAAa,OAAO,WAAW,KAAK,CAAC,KAAK,QAAQ,KAAK,iBAAiB,OAAO,WAAW,KAAK,CAAC,IAAI;AACrI,oBAAQ;AAAA,UACZ;AAAA,QACJ;AAEA,iBAAS,UAAU,MAAM,eAAe,mBAAmB;AACvD,cAAI,OAAO,IACP,aACA;AAGJ,yBAAe,IAAI;AAEnB,cAAI,SAAS,MAAM;AACf,mBAAO;AAAA,UACX;AAEA,cAAI,OAAO,WAAW,KAAK,MAAM,IAAiB;AAC9C,gBAAI,eAAe;AACf,4BAAc;AACd,qBAAO,QAAQ;AAAA,YACnB,OAAO;AACH,qBAAO;AAAA,YACX;AAAA,UACJ;AAEA,kBAAQ,eAAe,IAAI;AAE3B,cAAI,mBAAmB;AACnB,gBAAI,OAAO,WAAW,KAAK,MAAM,OACzB,SAAS,YACT,SAAS,cACT,SAAS,UAAU;AACvB,sBAAQ,QAAQ;AAChB,sBAAQ,eAAe,IAAI;AAAA,YAE/B;AACA,gBAAG,OAAO,WAAW,KAAK,MAAM,MAAmB,OAAO,WAAW,QAAQ,CAAC,MAAM,IAAgB;AAChG,sBAAQ,QAAQ;AAChB,sBAAQ,QAAQ;AAAA,YACpB;AACA,mBAAO,OAAO,WAAW,KAAK,MAAM,MAC5B,OAAO,WAAW,KAAK,MAAM,MAC7B,OAAO,WAAW,KAAK,MAAM,MAC7B,OAAO,WAAW,KAAK,MAAM,MAC7B,OAAO,WAAW,KAAK,MAAM,KAAiB;AAClD,sBAAQ,QAAQ;AAChB,sBAAQ,eAAe,IAAI;AAAA,YAC/B;AAAA,UACJ;AAEA,cAAI,aAAa;AACb,2BAAe,IAAI;AAEnB,gBAAI,OAAO,WAAW,KAAK,MAAM,IAAiB;AAE9C,sBAAQ,QAAQ;AAChB,6BAAe,IAAI;AAEnB,kBAAI;AACJ,kBAAI,eAAe;AAGnB,qBAAO,QAAQ,MAAM;AACjB,qBAAK,OAAO,WAAW,KAAK;AAE5B,oBAAI,QAAQ,KAAK,aAAa,EAAE,GAAG;AAC/B,sBAAI,CAAC,cAAc;AACf,mCAAe,IAAI;AACnB,yBAAK,OAAO,WAAW,KAAK;AAAA,kBAChC;AAAA,gBACJ;AAEA,oBAAI,OAAO,IAAgB;AACvB,sBAAI,CAAC,cAAc;AACf,mCAAe;AAAA,kBACnB,OAAO;AACH,wBAAI,iBAAiB,KAAM;AACvB,qCAAe;AAAA,oBACnB;AAAA,kBACJ;AAAA,gBACJ;AAEA,oBAAI,OAAO,IAAgB;AACvB,sBAAI,CAAC,cAAc;AACf,mCAAe;AAAA,kBACnB,OAAO;AACH,wBAAI,iBAAiB,KAAK;AACtB,qCAAe;AAAA,oBACnB;AAAA,kBACJ;AAAA,gBACJ;AAEA,oBAAI,OAAO,IAAgB;AACvB;AAAA,gBACJ,WAAW,OAAO,MACd,EAAE,iBAAiB,GAAG;AACtB;AAAA,gBACJ;AAEA,wBAAQ,QAAQ;AAAA,cACpB;AAAA,YACJ;AAEA,2BAAe,IAAI;AAEnB,gBAAI,SAAS,QAAQ,OAAO,WAAW,KAAK,MAAM,IAAiB;AAE/D,qBAAO;AAAA,YACX;AAGA,oBAAQ,QAAQ;AAAA,UACpB;AAEA,iBAAO;AAAA,QACX;AAEA,iBAAS,YAAY;AACjB,iBAAO,QAAQ,UAAU,OAAO,WAAW,KAAK,MAAM,IAAiB;AACnE,oBAAQ;AAAA,UACZ;AACA,cAAI,SAAS,QAAQ;AACjB,mBAAO;AAAA,UACX;AACA,kBAAQ;AAAA,YAAO,OAAO,WAAW,KAAK,MAAM;AAAA;AAAA,UAAe;AAC3D,iBAAO;AAAA,QACX;AAEA,iBAAS,aAAa,YAAY;AAC9B,cAAI,WAAW,gBAAgB;AAC3B,mBAAO;AAAA,UACX;AACA,iBAAO,6BAA6B,gBAAgB,UAAU;AAAA,QAClE;AAEA,iBAAS,UAAU,SAAS,OAAO;AAC/B,eAAK,WAAW;AAChB,eAAK,SAAS,MAAM,YAAY;AAChC,eAAK,OAAO;AAAA,YACR;AAAA,YACA,aAAa;AAAA,UACjB;AACA,cAAI,KAAK,SAAS,aAAa;AAC3B,iBAAK,KAAK,aAAa;AAAA,UAC3B;AACA,eAAK,SAAS,QAAQ,MAAM,SAAS;AACrC,eAAK,QAAQ;AAEb,eAAK,SAAS,CAAE;AAAA,QACpB;AAGA,kBAAU,UAAU,WAAW,SAAS,SAAS,WAAW;AACxD,cAAI,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC,GAC9C,MAAM,UAAU;AAAA,YACZ;AAAA,YACA,SAAU,OAAOC,QAAO;AACpB,sBAAQ,OAAOA,SAAQ,KAAK,QAAQ,oCAAoC;AACxE,qBAAO,KAAKA,MAAK;AAAA,YACrB;AAAA,UACJ;AAEJ,cAAI,CAAC,KAAK,KAAK,QAAQ;AACnB,iBAAK,KAAK,SAAS,CAAC;AAAA,UACxB;AACA,cAAI,QAAQ;AACR,oBAAQ,WAAW,GAAG;AAAA,UAC1B;AACA,eAAK,KAAK,OAAO,KAAK,GAAG;AACzB,iBAAO;AAAA,QACX;AAEA,kBAAU,UAAU,YAAY,WAAY;AAExC,cAAI,wBAAwB,KAAK,MAAM,GAAG;AACtC,gBAAI;AACA,mBAAK,KAAK,OAAO,UAAU,KAAK,QAAQ,KAAK,OAAO,KAAK,SAAS,KAAK;AACvE,kBAAI,CAAC,KAAK,KAAK,MAAM;AACjB,oBAAI,CAAC,aAAa,KAAK,MAAM,KAAK,CAAC,cAAc,KAAK,MAAM,GAAG;AAC3D,sBAAI,CAAC,KAAK,SAAS,6BAA6B,GAAG;AAC/C,2BAAO;AAAA,kBACX;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ,SAAS,OAAO;AACZ,mBAAK,KAAK,OAAO;AACjB,kBAAI,CAAC,KAAK,SAAS,MAAM,OAAO,GAAG;AAC/B,uBAAO;AAAA,cACX;AAAA,YACJ;AAAA,UACJ,WAAW,cAAc,KAAK,MAAM,GAAG;AAEnC,gBAAI;AACA,mBAAK,KAAK,OAAO,UAAU,KAAK,QAAQ,KAAK,OAAO,KAAK,SAAS,KAAK;AAAA,YAC3E,SAAS,GAAG;AAAA,YAEZ;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAEA,kBAAU,UAAU,iBAAiB,SAAU,UAAU;AACrD,cAAI;AACJ,iBAAO,UAAU,KAAK,OAAO,UAAU,kBAAkB,KAAK,MAAM,GAAG,IAAI;AAC3E,cAAI,CAAC,MAAM;AACP,gBAAI,CAAC,UAAU;AACX,kBAAI,CAAC,KAAK,SAAS,6BAA6B,GAAG;AAC/C,uBAAO;AAAA,cACX;AAAA,YACJ;AAAA,UACJ;AACA,eAAK,KAAK,OAAO;AACjB,iBAAO;AAAA,QACX;AAEA,kBAAU,UAAU,gBAAgB,WAAY;AAC5C,iBAAO,KAAK,eAAe,KAAK;AAAA,QACpC;AAEA,kBAAU,UAAU,wBAAwB,WAAY;AACpD,iBAAO,KAAK,eAAe,IAAI;AAAA,QACnC;AAGA,kBAAU,UAAU,YAAY,WAAY;AACxC,cAAI,QAAQ;AAGZ,cAAI,cAAc,KAAK,MAAM,GAAG;AAC5B,iBAAK,KAAK,OAAO,UAAU,KAAK,OAAO,UAAU,kBAAkB,KAAK,MAAM,GAAG,gBAAgB,KAAK,MAAM,CAAC;AAC7G,gBAAI,CAAC,KAAK,KAAK,MAAM;AACjB,kBAAI,CAAC,wBAAwB,KAAK,MAAM,GAAG;AACvC,uBAAO;AAAA,cACX;AAKA,kBAAI,aAAa,KAAK,MAAM,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,MAAM;AACpE,qBAAK,OAAO,OAAO,KAAK,KAAK;AAC7B,qBAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AAChC,qBAAK,KAAK,OAAO;AAAA,cACrB,OAAO;AACH,oBAAI,CAAC,KAAK,SAAS,6BAA6B,GAAG;AAC/C,yBAAO;AAAA,gBACX;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,qBAAO,KAAK,KAAK;AACjB,kBAAI,KAAK,OAAO,CAAC,MAAM,OAAO,KAAK,OAAO,KAAK,SAAS,CAAC,MAAM,KAAK;AAGhE,yBAAS,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC,EAAE,MAAM,GAAG;AACrD,oBAAI,OAAO,SAAS,GAAG;AACnB,uBAAK,KAAK,SAAS,IAAI,OAAO,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,gBACnD;AACA,qBAAK,KAAK,OAAO,OAAO,CAAC;AAGzB,oBAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,SAAS,gBAAgB;AAC1D,uBAAK,KAAK,OAAO;AAAA,oBACb,MAAM;AAAA,oBACN,YAAY,KAAK,KAAK;AAAA,kBAC1B;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAGA,iBAAO;AAAA,QACX;AAEA,kBAAU,UAAU,mBAAmB,SAAS,mBAAmB;AAC/D,cAAI,cAAc,YAAY,QAAQ,OAAO,KAAK,KAAK,EAAE,KAAK;AAC9D,cAAI,aAAa;AACb,gBAAK,QAAS,KAAK,WAAW,GAAG;AAC7B,4BAAc,YAAY,UAAU,CAAC;AAAA,YACzC;AACA,iBAAK,KAAK,cAAc;AAAA,UAC5B;AACA,iBAAO;AAAA,QACX;AAEA,kBAAU,UAAU,eAAe,SAAS,mBAAmB;AAC3D,cAAI,cAAc,YAAY,QAAQ,OAAO,KAAK,KAAK,EAAE,KAAK;AAC9D,cAAI,kBAAkB;AACtB,cAAI,gBAAgB;AACpB,cAAI,eAAe,YAAY,QAAQ,eAAe;AACtD,cAAI,aAAa,YAAY,QAAQ,aAAa;AAClD,cAAI,gBAAgB,KAAK,cAAc,GAAG;AACtC,iBAAK,KAAK,UAAU,YAAY;AAAA,cAC5B,eAAe,gBAAgB;AAAA,cAAQ;AAAA,YAAU,EAAE,KAAK;AAC5D,iBAAK,KAAK,cAAc,YAAY,UAAU,aAAa,cAAc,MAAM,EAAE,KAAK;AAAA,UAC1F,OAAO;AACH,iBAAK,KAAK,cAAc;AAAA,UAC5B;AACA,iBAAO;AAAA,QACX;AAEA,kBAAU,UAAU,YAAY,SAAS,YAAY;AACjD,cAAI,MAAM;AACV,kBAAQ;AAAA,YACJ,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,UAAU;AAAA,YACV,aAAa;AAAA,YACb,WAAW;AAAA,UACf;AACA,iBAAO,YAAY,QAAQ,OAAO,KAAK,KAAK,EAAE,KAAK;AACnD,eAAK,KAAK,OAAO;AACjB,cAAI,CAAC,eAAe,OAAO,IAAI,GAAG;AAC9B,gBAAI,CAAC,KAAK,SAAS,0BAA4B,IAAI,GAAG;AAClD,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAEA,kBAAU,UAAU,cAAc,SAAS,cAAc;AACrD,cAAI;AACJ,mBAAS,YAAY,QAAQ,OAAO,KAAK,KAAK,EAAE,KAAK;AACrD,eAAK,KAAK,SAAS;AACnB,cAAI,WAAW,aAAa,WAAW,eAAe,WAAW,UAAU;AACvE,gBAAI,CAAC,KAAK,SAAS,4BAA8B,MAAM,GAAG;AACtD,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAEA,kBAAU,UAAU,YAAY,SAAS,YAAY;AAGjD,cAAI,QAAQ,YAAY,QAAQ,OAAO,KAAK,KAAK,EAAE,KAAK;AACxD,cAAI,SAAS,MAAM,OAAO,CAAC,MAAM,KAAK;AAClC,gBAAI,UAAU,KAAK,UAAU;AAC7B,gBAAI,WAAW,KAAK,KAAK,KAAK,SAAS,oBAAoB,KAAK,KAAK,KAAK,SAAS,aAAa;AAC5F,mBAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AAChC,qBAAO;AAAA,YACX,OAAO;AACH,qBAAO,KAAK,SAAS,uBAAuB;AAAA,YAChD;AAAA,UACJ,OAAO;AACH,mBAAO,KAAK,cAAc;AAAA,UAC9B;AAAA,QACJ;AAEA,kBAAU,UAAU,iBAAiB,SAAS,iBAAiB;AAC3D,cAAI,WAAW;AACf,iBAAO,YAAY,QAAQ,OAAO,KAAK,KAAK,EAAE,KAAK;AACnD,sBAAY,WAAW,MAAM,EAAE;AAC/B,eAAK,KAAK,YAAY;AACtB,cAAI,MAAM,SAAS,GAAG;AAClB,gBAAI,CAAC,KAAK,SAAS,0BAA4B,IAAI,GAAG;AAClD,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAEA,kBAAU,UAAU,YAAY,WAAY;AACxC,cAAI,gBAAgB,YAAY,QAAQ,OAAO,KAAK,KAAK,EAAE,KAAK;AAChE,cAAI,eAAe;AACf,gBAAI,CAAC,KAAK,SAAS,wBAA0B,aAAa,GAAG;AACzD,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAEA,kBAAU,UAAU,WAAW,SAAS,WAAW;AAC/C,cAAI;AAEJ,wBAAc,KAAK,KAAK;AAExB,cAAI,kBAAkB,KAAK,MAAM,KAAK,CAAC,KAAK,KAAK,QAAQ,eAAe,YAAY,OAAO,CAAC,MAAM,KAAK;AACnG,iBAAK,KAAK,OAAO,KAAK,OAAO;AAC7B,gBAAI,CAAC,KAAK,KAAK,MAAM;AACjB,mBAAK,KAAK,OAAO;AAAA,YACrB;AAEA,gBAAI,CAAC,QAAQ;AACT,kBAAI,CAAC,KAAK,SAAS,6BAA6B,GAAG;AAC/C,uBAAO;AAAA,cACX;AAAA,YACJ;AAAA,UACJ;AAEA,iBAAO;AAAA,QACX;AAEA,gBAAQ;AAAA;AAAA,UAEJ,UAAU,CAAC,aAAa;AAAA;AAAA,UAExB,SAAS,CAAC,iBAAiB,WAAW;AAAA;AAAA,UAEtC,YAAY,CAAC,aAAa,yBAAyB,WAAW;AAAA;AAAA,UAE9D,eAAe,CAAC,aAAa,yBAAyB,WAAW;AAAA;AAAA,UAEjE,SAAS,CAAC,aAAa,yBAAyB,WAAW;AAAA;AAAA,UAE3D,WAAW,CAAC,aAAa,yBAAyB,WAAW;AAAA;AAAA,UAE7D,WAAW,CAAC,cAAc;AAAA;AAAA,UAE1B,cAAc,CAAC,kBAAkB;AAAA;AAAA,UAEjC,UAAU,CAAC,WAAW;AAAA;AAAA,UAEtB,SAAS,CAAC,WAAW;AAAA;AAAA,UAErB,YAAY,CAAC,WAAW;AAAA;AAAA,UAExB,QAAQ,CAAC,WAAW;AAAA;AAAA,UAEpB,SAAS,CAAC,iBAAiB,WAAW;AAAA;AAAA,UAEtC,SAAS,CAAC,yBAAyB,WAAW;AAAA;AAAA,UAE9C,UAAU,CAAC,aAAa,yBAAyB,WAAW;AAAA;AAAA,UAE5D,UAAU,CAAC,yBAAyB,WAAW;AAAA;AAAA,UAE/C,UAAU,CAAC,aAAa,yBAAyB,WAAW;AAAA;AAAA,UAE5D,QAAQ,CAAC,yBAAyB,WAAW;AAAA;AAAA,UAE7C,YAAY,CAAC,yBAAyB,WAAW;AAAA;AAAA,UAEjD,OAAO,CAAC,aAAa,yBAAyB,WAAW;AAAA;AAAA,UAEzD,QAAQ,CAAC,iBAAiB,WAAW;AAAA;AAAA,UAErC,aAAa,CAAC,aAAa,yBAAyB,WAAW;AAAA;AAAA,UAE/D,WAAW,CAAC,aAAa,kBAAkB;AAAA;AAAA,UAE3C,aAAa,CAAC,aAAa,kBAAkB;AAAA;AAAA,UAE7C,UAAU,CAAC,aAAa,kBAAkB;AAAA;AAAA,UAE1C,YAAY,CAAC,WAAW;AAAA;AAAA,UAExB,YAAY,CAAC,iBAAiB,WAAW;AAAA;AAAA,UAEzC,SAAS,CAAC,kBAAkB;AAAA;AAAA,UAE5B,UAAU,CAAC,WAAW;AAAA;AAAA,UAEtB,WAAW,CAAC,kBAAkB;AAAA;AAAA,UAE9B,QAAQ,CAAC,aAAa,WAAW;AAAA;AAAA,UAEjC,QAAQ,CAAC,kBAAkB;AAAA;AAAA,UAE3B,WAAW,CAAC,aAAa,uBAAuB;AAAA;AAAA,UAEhD,aAAa,CAAC,gBAAgB;AAAA;AAAA,UAE9B,WAAW,CAAC,kBAAkB;AAAA,QAClC;AAEA,kBAAU,UAAU,QAAQ,SAASC,SAAQ;AACzC,cAAI,GAAG,IAAI,WAAW;AAItB,cAAI,CAAC,KAAK,QAAQ;AACd,gBAAI,CAAC,KAAK,SAAS,0BAA0B,GAAG;AAC5C,qBAAO;AAAA,YACX;AAAA,UACJ;AAGA,eAAK,QAAQ,YAAY,KAAK,MAAM;AAEpC,cAAI,KAAK,SAAS,OAAO;AACrB,iBAAK,KAAK,QAAQ,CAAC,KAAK,QAAQ,OAAO,MAAM,GAAG,KAAK,KAAK,EAAE,QAAQ,QAAQ,EAAE,EAAE,MAAM,EAAE,IAAI,YAAY;AAAA,UAC5G;AAEA,cAAI,eAAe,OAAO,KAAK,MAAM,GAAG;AACpC,wBAAY,MAAM,KAAK,MAAM;AAAA,UACjC,OAAO;AAEH,wBAAY,CAAC,aAAa,aAAa,oBAAoB,UAAU;AAAA,UACzE;AAEA,eAAK,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC5C,qBAAS,UAAU,CAAC;AACpB,gBAAI,CAAC,KAAK,MAAM,EAAE,GAAG;AACjB,qBAAO;AAAA,YACX;AAAA,UACJ;AAEA,iBAAO,KAAK;AAAA,QAChB;AAEA,iBAAS,SAAS,SAAS;AACvB,cAAI,OAAO,QAAQ;AAGnB,cAAI,CAAC,UAAU,GAAG;AACd,mBAAO;AAAA,UACX;AAGA,kBAAQ,UAAU;AAGlB,mBAAS,IAAI,UAAU,SAAS,KAAK;AACrC,gBAAM,OAAO,MAAM;AAGnB,iBAAO,QAAQ,OAAO,OAAO;AACzB,oBAAQ;AAAA,UACZ;AAEA,iBAAO;AAAA,QACX;AAMA,iBAAS,qBAAqB,oBAAoB;AAC9C,cAAI,cAAc,IAAI,IAAI;AAE1B,sBAAY;AACZ,iBAAO,QAAQ,QAAQ;AACnB,iBAAK,OAAO,WAAW,KAAK;AAE5B,gBAAI,aAAa,OAAO,IAAiB;AACrC;AAAA,YACJ;AAEA,gBAAI,QAAQ,KAAK,iBAAiB,EAAE,GAAG;AACnC,0BAAY;AAAA,YAChB,WAAW,aAAa,CAAC,QAAQ,KAAK,aAAa,EAAE,GAAG;AACpD,0BAAY;AAAA,YAChB;AAEA,2BAAe,QAAQ;AAAA,UAC3B;AAEA,iBAAO,qBAAqB,cAAc,YAAY,KAAK;AAAA,QAC/D;AAEA,iBAAS,MAAM,SAAS,SAAS;AAC7B,cAAI,OAAO,CAAC,GAAG,KAAK,aAAa,iBAAiB,GAAG;AAErD,cAAI,YAAY,QAAW;AACvB,sBAAU,CAAC;AAAA,UACf;AAEA,cAAI,OAAO,QAAQ,WAAW,aAAa,QAAQ,QAAQ;AACvD,qBAAS,cAAc,OAAO;AAAA,UAClC,OAAO;AACH,qBAAS;AAAA,UACb;AAEA,2BAAiB;AAGjB,cAAI,QAAQ,MAAM;AACd,gBAAI,MAAM,QAAQ,QAAQ,IAAI,GAAG;AAC7B,gCAAkB,CAAE;AACpB,mBAAK,IAAI,GAAG,KAAK,QAAQ,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC/C,oBAAI,OAAO,QAAQ,KAAK,CAAC,MAAM,UAAU;AACrC,kCAAgB,QAAQ,KAAK,CAAC,CAAC,IAAI;AAAA,gBACvC,OAAO;AACH,0BAAQ,WAAW,+BAA+B,QAAQ,IAAI;AAAA,gBAClE;AAAA,cACJ;AAAA,YACJ,OAAO;AACH,sBAAQ,WAAW,+BAA+B,QAAQ,IAAI;AAAA,YAClE;AAAA,UACJ;AAEA,mBAAS,OAAO;AAChB,kBAAQ;AACR,uBAAa;AACb,wBAAc,QAAQ;AACtB,mBAAS,QAAQ;AACjB,mBAAS,QAAQ;AAEjB,wBAAc,qBAAqB,QAAQ,kBAAkB;AAE7D,iBAAO,MAAM;AACT,kBAAM,SAAS,OAAO;AACtB,gBAAI,CAAC,KAAK;AACN;AAAA,YACJ;AACA,gBAAI,CAAC,mBAAmB,gBAAgB,eAAe,IAAI,KAAK,GAAG;AAC/D,mBAAK,KAAK,GAAG;AAAA,YACjB;AAAA,UACJ;AAEA,iBAAO;AAAA,YACH;AAAA,YACA;AAAA,UACJ;AAAA,QACJ;AACA,QAAAF,SAAQ,QAAQ;AAAA,MACpB,GAAE,QAAQ,CAAC,CAAC;AAEZ,cAAQ,UAAU,QAAQ;AAC1B,cAAQ,QAAQ,MAAM;AACtB,cAAQ,YAAY,MAAM;AAC1B,cAAQ,iBAAiB,MAAM;AAC/B,cAAQ,gBAAgB;AACxB,cAAQ,SAAS,YAAY,MAAM,MAAM;AACzC,cAAQ,QAAQ,QAAQ;AACxB,cAAQ,OAAO;AAAA,QACX,QAAQ,QAAQ;AAAA,QAChB,WAAW,MAAM;AAAA,QACjB,gBAAgB,MAAM;AAAA,QACtB,WAAW,MAAM;AAAA,MACrB;AAAA,IACJ,GAAE;AAAA;AAAA;", "names": ["ch", "previous", "index", "token", "value", "hasOwnProperty", "exports", "index", "parse"]}