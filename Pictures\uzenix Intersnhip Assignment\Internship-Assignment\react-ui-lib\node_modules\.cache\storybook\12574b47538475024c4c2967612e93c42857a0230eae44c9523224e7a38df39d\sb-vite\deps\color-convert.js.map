{"version": 3, "sources": ["../../../../../color-name/index.js", "../../../../../color-convert/conversions.js", "../../../../../color-convert/route.js", "../../../../../color-convert/index.js"], "sourcesContent": ["'use strict'\r\n\r\nmodule.exports = {\r\n\t\"aliceblue\": [240, 248, 255],\r\n\t\"antiquewhite\": [250, 235, 215],\r\n\t\"aqua\": [0, 255, 255],\r\n\t\"aquamarine\": [127, 255, 212],\r\n\t\"azure\": [240, 255, 255],\r\n\t\"beige\": [245, 245, 220],\r\n\t\"bisque\": [255, 228, 196],\r\n\t\"black\": [0, 0, 0],\r\n\t\"blanchedalmond\": [255, 235, 205],\r\n\t\"blue\": [0, 0, 255],\r\n\t\"blueviolet\": [138, 43, 226],\r\n\t\"brown\": [165, 42, 42],\r\n\t\"burlywood\": [222, 184, 135],\r\n\t\"cadetblue\": [95, 158, 160],\r\n\t\"chartreuse\": [127, 255, 0],\r\n\t\"chocolate\": [210, 105, 30],\r\n\t\"coral\": [255, 127, 80],\r\n\t\"cornflowerblue\": [100, 149, 237],\r\n\t\"cornsilk\": [255, 248, 220],\r\n\t\"crimson\": [220, 20, 60],\r\n\t\"cyan\": [0, 255, 255],\r\n\t\"darkblue\": [0, 0, 139],\r\n\t\"darkcyan\": [0, 139, 139],\r\n\t\"darkgoldenrod\": [184, 134, 11],\r\n\t\"darkgray\": [169, 169, 169],\r\n\t\"darkgreen\": [0, 100, 0],\r\n\t\"darkgrey\": [169, 169, 169],\r\n\t\"darkkhaki\": [189, 183, 107],\r\n\t\"darkmagenta\": [139, 0, 139],\r\n\t\"darkolivegreen\": [85, 107, 47],\r\n\t\"darkorange\": [255, 140, 0],\r\n\t\"darkorchid\": [153, 50, 204],\r\n\t\"darkred\": [139, 0, 0],\r\n\t\"darksalmon\": [233, 150, 122],\r\n\t\"darkseagreen\": [143, 188, 143],\r\n\t\"darkslateblue\": [72, 61, 139],\r\n\t\"darkslategray\": [47, 79, 79],\r\n\t\"darkslategrey\": [47, 79, 79],\r\n\t\"darkturquoise\": [0, 206, 209],\r\n\t\"darkviolet\": [148, 0, 211],\r\n\t\"deeppink\": [255, 20, 147],\r\n\t\"deepskyblue\": [0, 191, 255],\r\n\t\"dimgray\": [105, 105, 105],\r\n\t\"dimgrey\": [105, 105, 105],\r\n\t\"dodgerblue\": [30, 144, 255],\r\n\t\"firebrick\": [178, 34, 34],\r\n\t\"floralwhite\": [255, 250, 240],\r\n\t\"forestgreen\": [34, 139, 34],\r\n\t\"fuchsia\": [255, 0, 255],\r\n\t\"gainsboro\": [220, 220, 220],\r\n\t\"ghostwhite\": [248, 248, 255],\r\n\t\"gold\": [255, 215, 0],\r\n\t\"goldenrod\": [218, 165, 32],\r\n\t\"gray\": [128, 128, 128],\r\n\t\"green\": [0, 128, 0],\r\n\t\"greenyellow\": [173, 255, 47],\r\n\t\"grey\": [128, 128, 128],\r\n\t\"honeydew\": [240, 255, 240],\r\n\t\"hotpink\": [255, 105, 180],\r\n\t\"indianred\": [205, 92, 92],\r\n\t\"indigo\": [75, 0, 130],\r\n\t\"ivory\": [255, 255, 240],\r\n\t\"khaki\": [240, 230, 140],\r\n\t\"lavender\": [230, 230, 250],\r\n\t\"lavenderblush\": [255, 240, 245],\r\n\t\"lawngreen\": [124, 252, 0],\r\n\t\"lemonchiffon\": [255, 250, 205],\r\n\t\"lightblue\": [173, 216, 230],\r\n\t\"lightcoral\": [240, 128, 128],\r\n\t\"lightcyan\": [224, 255, 255],\r\n\t\"lightgoldenrodyellow\": [250, 250, 210],\r\n\t\"lightgray\": [211, 211, 211],\r\n\t\"lightgreen\": [144, 238, 144],\r\n\t\"lightgrey\": [211, 211, 211],\r\n\t\"lightpink\": [255, 182, 193],\r\n\t\"lightsalmon\": [255, 160, 122],\r\n\t\"lightseagreen\": [32, 178, 170],\r\n\t\"lightskyblue\": [135, 206, 250],\r\n\t\"lightslategray\": [119, 136, 153],\r\n\t\"lightslategrey\": [119, 136, 153],\r\n\t\"lightsteelblue\": [176, 196, 222],\r\n\t\"lightyellow\": [255, 255, 224],\r\n\t\"lime\": [0, 255, 0],\r\n\t\"limegreen\": [50, 205, 50],\r\n\t\"linen\": [250, 240, 230],\r\n\t\"magenta\": [255, 0, 255],\r\n\t\"maroon\": [128, 0, 0],\r\n\t\"mediumaquamarine\": [102, 205, 170],\r\n\t\"mediumblue\": [0, 0, 205],\r\n\t\"mediumorchid\": [186, 85, 211],\r\n\t\"mediumpurple\": [147, 112, 219],\r\n\t\"mediumseagreen\": [60, 179, 113],\r\n\t\"mediumslateblue\": [123, 104, 238],\r\n\t\"mediumspringgreen\": [0, 250, 154],\r\n\t\"mediumturquoise\": [72, 209, 204],\r\n\t\"mediumvioletred\": [199, 21, 133],\r\n\t\"midnightblue\": [25, 25, 112],\r\n\t\"mintcream\": [245, 255, 250],\r\n\t\"mistyrose\": [255, 228, 225],\r\n\t\"moccasin\": [255, 228, 181],\r\n\t\"navajowhite\": [255, 222, 173],\r\n\t\"navy\": [0, 0, 128],\r\n\t\"oldlace\": [253, 245, 230],\r\n\t\"olive\": [128, 128, 0],\r\n\t\"olivedrab\": [107, 142, 35],\r\n\t\"orange\": [255, 165, 0],\r\n\t\"orangered\": [255, 69, 0],\r\n\t\"orchid\": [218, 112, 214],\r\n\t\"palegoldenrod\": [238, 232, 170],\r\n\t\"palegreen\": [152, 251, 152],\r\n\t\"paleturquoise\": [175, 238, 238],\r\n\t\"palevioletred\": [219, 112, 147],\r\n\t\"papayawhip\": [255, 239, 213],\r\n\t\"peachpuff\": [255, 218, 185],\r\n\t\"peru\": [205, 133, 63],\r\n\t\"pink\": [255, 192, 203],\r\n\t\"plum\": [221, 160, 221],\r\n\t\"powderblue\": [176, 224, 230],\r\n\t\"purple\": [128, 0, 128],\r\n\t\"rebeccapurple\": [102, 51, 153],\r\n\t\"red\": [255, 0, 0],\r\n\t\"rosybrown\": [188, 143, 143],\r\n\t\"royalblue\": [65, 105, 225],\r\n\t\"saddlebrown\": [139, 69, 19],\r\n\t\"salmon\": [250, 128, 114],\r\n\t\"sandybrown\": [244, 164, 96],\r\n\t\"seagreen\": [46, 139, 87],\r\n\t\"seashell\": [255, 245, 238],\r\n\t\"sienna\": [160, 82, 45],\r\n\t\"silver\": [192, 192, 192],\r\n\t\"skyblue\": [135, 206, 235],\r\n\t\"slateblue\": [106, 90, 205],\r\n\t\"slategray\": [112, 128, 144],\r\n\t\"slategrey\": [112, 128, 144],\r\n\t\"snow\": [255, 250, 250],\r\n\t\"springgreen\": [0, 255, 127],\r\n\t\"steelblue\": [70, 130, 180],\r\n\t\"tan\": [210, 180, 140],\r\n\t\"teal\": [0, 128, 128],\r\n\t\"thistle\": [216, 191, 216],\r\n\t\"tomato\": [255, 99, 71],\r\n\t\"turquoise\": [64, 224, 208],\r\n\t\"violet\": [238, 130, 238],\r\n\t\"wheat\": [245, 222, 179],\r\n\t\"white\": [255, 255, 255],\r\n\t\"whitesmoke\": [245, 245, 245],\r\n\t\"yellow\": [255, 255, 0],\r\n\t\"yellowgreen\": [154, 205, 50]\r\n};\r\n", "/* MIT license */\n/* eslint-disable no-mixed-operators */\nconst cssKeywords = require('color-name');\n\n// NOTE: conversions should only return primitive values (i.e. arrays, or\n//       values that give correct `typeof` results).\n//       do not use box values types (i.e. Number(), String(), etc.)\n\nconst reverseKeywords = {};\nfor (const key of Object.keys(cssKeywords)) {\n\treverseKeywords[cssKeywords[key]] = key;\n}\n\nconst convert = {\n\trgb: {channels: 3, labels: 'rgb'},\n\thsl: {channels: 3, labels: 'hsl'},\n\thsv: {channels: 3, labels: 'hsv'},\n\thwb: {channels: 3, labels: 'hwb'},\n\tcmyk: {channels: 4, labels: 'cmyk'},\n\txyz: {channels: 3, labels: 'xyz'},\n\tlab: {channels: 3, labels: 'lab'},\n\tlch: {channels: 3, labels: 'lch'},\n\thex: {channels: 1, labels: ['hex']},\n\tkeyword: {channels: 1, labels: ['keyword']},\n\tansi16: {channels: 1, labels: ['ansi16']},\n\tansi256: {channels: 1, labels: ['ansi256']},\n\thcg: {channels: 3, labels: ['h', 'c', 'g']},\n\tapple: {channels: 3, labels: ['r16', 'g16', 'b16']},\n\tgray: {channels: 1, labels: ['gray']}\n};\n\nmodule.exports = convert;\n\n// Hide .channels and .labels properties\nfor (const model of Object.keys(convert)) {\n\tif (!('channels' in convert[model])) {\n\t\tthrow new Error('missing channels property: ' + model);\n\t}\n\n\tif (!('labels' in convert[model])) {\n\t\tthrow new Error('missing channel labels property: ' + model);\n\t}\n\n\tif (convert[model].labels.length !== convert[model].channels) {\n\t\tthrow new Error('channel and label counts mismatch: ' + model);\n\t}\n\n\tconst {channels, labels} = convert[model];\n\tdelete convert[model].channels;\n\tdelete convert[model].labels;\n\tObject.defineProperty(convert[model], 'channels', {value: channels});\n\tObject.defineProperty(convert[model], 'labels', {value: labels});\n}\n\nconvert.rgb.hsl = function (rgb) {\n\tconst r = rgb[0] / 255;\n\tconst g = rgb[1] / 255;\n\tconst b = rgb[2] / 255;\n\tconst min = Math.min(r, g, b);\n\tconst max = Math.max(r, g, b);\n\tconst delta = max - min;\n\tlet h;\n\tlet s;\n\n\tif (max === min) {\n\t\th = 0;\n\t} else if (r === max) {\n\t\th = (g - b) / delta;\n\t} else if (g === max) {\n\t\th = 2 + (b - r) / delta;\n\t} else if (b === max) {\n\t\th = 4 + (r - g) / delta;\n\t}\n\n\th = Math.min(h * 60, 360);\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tconst l = (min + max) / 2;\n\n\tif (max === min) {\n\t\ts = 0;\n\t} else if (l <= 0.5) {\n\t\ts = delta / (max + min);\n\t} else {\n\t\ts = delta / (2 - max - min);\n\t}\n\n\treturn [h, s * 100, l * 100];\n};\n\nconvert.rgb.hsv = function (rgb) {\n\tlet rdif;\n\tlet gdif;\n\tlet bdif;\n\tlet h;\n\tlet s;\n\n\tconst r = rgb[0] / 255;\n\tconst g = rgb[1] / 255;\n\tconst b = rgb[2] / 255;\n\tconst v = Math.max(r, g, b);\n\tconst diff = v - Math.min(r, g, b);\n\tconst diffc = function (c) {\n\t\treturn (v - c) / 6 / diff + 1 / 2;\n\t};\n\n\tif (diff === 0) {\n\t\th = 0;\n\t\ts = 0;\n\t} else {\n\t\ts = diff / v;\n\t\trdif = diffc(r);\n\t\tgdif = diffc(g);\n\t\tbdif = diffc(b);\n\n\t\tif (r === v) {\n\t\t\th = bdif - gdif;\n\t\t} else if (g === v) {\n\t\t\th = (1 / 3) + rdif - bdif;\n\t\t} else if (b === v) {\n\t\t\th = (2 / 3) + gdif - rdif;\n\t\t}\n\n\t\tif (h < 0) {\n\t\t\th += 1;\n\t\t} else if (h > 1) {\n\t\t\th -= 1;\n\t\t}\n\t}\n\n\treturn [\n\t\th * 360,\n\t\ts * 100,\n\t\tv * 100\n\t];\n};\n\nconvert.rgb.hwb = function (rgb) {\n\tconst r = rgb[0];\n\tconst g = rgb[1];\n\tlet b = rgb[2];\n\tconst h = convert.rgb.hsl(rgb)[0];\n\tconst w = 1 / 255 * Math.min(r, Math.min(g, b));\n\n\tb = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n\n\treturn [h, w * 100, b * 100];\n};\n\nconvert.rgb.cmyk = function (rgb) {\n\tconst r = rgb[0] / 255;\n\tconst g = rgb[1] / 255;\n\tconst b = rgb[2] / 255;\n\n\tconst k = Math.min(1 - r, 1 - g, 1 - b);\n\tconst c = (1 - r - k) / (1 - k) || 0;\n\tconst m = (1 - g - k) / (1 - k) || 0;\n\tconst y = (1 - b - k) / (1 - k) || 0;\n\n\treturn [c * 100, m * 100, y * 100, k * 100];\n};\n\nfunction comparativeDistance(x, y) {\n\t/*\n\t\tSee https://en.m.wikipedia.org/wiki/Euclidean_distance#Squared_Euclidean_distance\n\t*/\n\treturn (\n\t\t((x[0] - y[0]) ** 2) +\n\t\t((x[1] - y[1]) ** 2) +\n\t\t((x[2] - y[2]) ** 2)\n\t);\n}\n\nconvert.rgb.keyword = function (rgb) {\n\tconst reversed = reverseKeywords[rgb];\n\tif (reversed) {\n\t\treturn reversed;\n\t}\n\n\tlet currentClosestDistance = Infinity;\n\tlet currentClosestKeyword;\n\n\tfor (const keyword of Object.keys(cssKeywords)) {\n\t\tconst value = cssKeywords[keyword];\n\n\t\t// Compute comparative distance\n\t\tconst distance = comparativeDistance(rgb, value);\n\n\t\t// Check if its less, if so set as closest\n\t\tif (distance < currentClosestDistance) {\n\t\t\tcurrentClosestDistance = distance;\n\t\t\tcurrentClosestKeyword = keyword;\n\t\t}\n\t}\n\n\treturn currentClosestKeyword;\n};\n\nconvert.keyword.rgb = function (keyword) {\n\treturn cssKeywords[keyword];\n};\n\nconvert.rgb.xyz = function (rgb) {\n\tlet r = rgb[0] / 255;\n\tlet g = rgb[1] / 255;\n\tlet b = rgb[2] / 255;\n\n\t// Assume sRGB\n\tr = r > 0.04045 ? (((r + 0.055) / 1.055) ** 2.4) : (r / 12.92);\n\tg = g > 0.04045 ? (((g + 0.055) / 1.055) ** 2.4) : (g / 12.92);\n\tb = b > 0.04045 ? (((b + 0.055) / 1.055) ** 2.4) : (b / 12.92);\n\n\tconst x = (r * 0.4124) + (g * 0.3576) + (b * 0.1805);\n\tconst y = (r * 0.2126) + (g * 0.7152) + (b * 0.0722);\n\tconst z = (r * 0.0193) + (g * 0.1192) + (b * 0.9505);\n\n\treturn [x * 100, y * 100, z * 100];\n};\n\nconvert.rgb.lab = function (rgb) {\n\tconst xyz = convert.rgb.xyz(rgb);\n\tlet x = xyz[0];\n\tlet y = xyz[1];\n\tlet z = xyz[2];\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? (x ** (1 / 3)) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? (y ** (1 / 3)) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? (z ** (1 / 3)) : (7.787 * z) + (16 / 116);\n\n\tconst l = (116 * y) - 16;\n\tconst a = 500 * (x - y);\n\tconst b = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.hsl.rgb = function (hsl) {\n\tconst h = hsl[0] / 360;\n\tconst s = hsl[1] / 100;\n\tconst l = hsl[2] / 100;\n\tlet t2;\n\tlet t3;\n\tlet val;\n\n\tif (s === 0) {\n\t\tval = l * 255;\n\t\treturn [val, val, val];\n\t}\n\n\tif (l < 0.5) {\n\t\tt2 = l * (1 + s);\n\t} else {\n\t\tt2 = l + s - l * s;\n\t}\n\n\tconst t1 = 2 * l - t2;\n\n\tconst rgb = [0, 0, 0];\n\tfor (let i = 0; i < 3; i++) {\n\t\tt3 = h + 1 / 3 * -(i - 1);\n\t\tif (t3 < 0) {\n\t\t\tt3++;\n\t\t}\n\n\t\tif (t3 > 1) {\n\t\t\tt3--;\n\t\t}\n\n\t\tif (6 * t3 < 1) {\n\t\t\tval = t1 + (t2 - t1) * 6 * t3;\n\t\t} else if (2 * t3 < 1) {\n\t\t\tval = t2;\n\t\t} else if (3 * t3 < 2) {\n\t\t\tval = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n\t\t} else {\n\t\t\tval = t1;\n\t\t}\n\n\t\trgb[i] = val * 255;\n\t}\n\n\treturn rgb;\n};\n\nconvert.hsl.hsv = function (hsl) {\n\tconst h = hsl[0];\n\tlet s = hsl[1] / 100;\n\tlet l = hsl[2] / 100;\n\tlet smin = s;\n\tconst lmin = Math.max(l, 0.01);\n\n\tl *= 2;\n\ts *= (l <= 1) ? l : 2 - l;\n\tsmin *= lmin <= 1 ? lmin : 2 - lmin;\n\tconst v = (l + s) / 2;\n\tconst sv = l === 0 ? (2 * smin) / (lmin + smin) : (2 * s) / (l + s);\n\n\treturn [h, sv * 100, v * 100];\n};\n\nconvert.hsv.rgb = function (hsv) {\n\tconst h = hsv[0] / 60;\n\tconst s = hsv[1] / 100;\n\tlet v = hsv[2] / 100;\n\tconst hi = Math.floor(h) % 6;\n\n\tconst f = h - Math.floor(h);\n\tconst p = 255 * v * (1 - s);\n\tconst q = 255 * v * (1 - (s * f));\n\tconst t = 255 * v * (1 - (s * (1 - f)));\n\tv *= 255;\n\n\tswitch (hi) {\n\t\tcase 0:\n\t\t\treturn [v, t, p];\n\t\tcase 1:\n\t\t\treturn [q, v, p];\n\t\tcase 2:\n\t\t\treturn [p, v, t];\n\t\tcase 3:\n\t\t\treturn [p, q, v];\n\t\tcase 4:\n\t\t\treturn [t, p, v];\n\t\tcase 5:\n\t\t\treturn [v, p, q];\n\t}\n};\n\nconvert.hsv.hsl = function (hsv) {\n\tconst h = hsv[0];\n\tconst s = hsv[1] / 100;\n\tconst v = hsv[2] / 100;\n\tconst vmin = Math.max(v, 0.01);\n\tlet sl;\n\tlet l;\n\n\tl = (2 - s) * v;\n\tconst lmin = (2 - s) * vmin;\n\tsl = s * vmin;\n\tsl /= (lmin <= 1) ? lmin : 2 - lmin;\n\tsl = sl || 0;\n\tl /= 2;\n\n\treturn [h, sl * 100, l * 100];\n};\n\n// http://dev.w3.org/csswg/css-color/#hwb-to-rgb\nconvert.hwb.rgb = function (hwb) {\n\tconst h = hwb[0] / 360;\n\tlet wh = hwb[1] / 100;\n\tlet bl = hwb[2] / 100;\n\tconst ratio = wh + bl;\n\tlet f;\n\n\t// Wh + bl cant be > 1\n\tif (ratio > 1) {\n\t\twh /= ratio;\n\t\tbl /= ratio;\n\t}\n\n\tconst i = Math.floor(6 * h);\n\tconst v = 1 - bl;\n\tf = 6 * h - i;\n\n\tif ((i & 0x01) !== 0) {\n\t\tf = 1 - f;\n\t}\n\n\tconst n = wh + f * (v - wh); // Linear interpolation\n\n\tlet r;\n\tlet g;\n\tlet b;\n\t/* eslint-disable max-statements-per-line,no-multi-spaces */\n\tswitch (i) {\n\t\tdefault:\n\t\tcase 6:\n\t\tcase 0: r = v;  g = n;  b = wh; break;\n\t\tcase 1: r = n;  g = v;  b = wh; break;\n\t\tcase 2: r = wh; g = v;  b = n; break;\n\t\tcase 3: r = wh; g = n;  b = v; break;\n\t\tcase 4: r = n;  g = wh; b = v; break;\n\t\tcase 5: r = v;  g = wh; b = n; break;\n\t}\n\t/* eslint-enable max-statements-per-line,no-multi-spaces */\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.cmyk.rgb = function (cmyk) {\n\tconst c = cmyk[0] / 100;\n\tconst m = cmyk[1] / 100;\n\tconst y = cmyk[2] / 100;\n\tconst k = cmyk[3] / 100;\n\n\tconst r = 1 - Math.min(1, c * (1 - k) + k);\n\tconst g = 1 - Math.min(1, m * (1 - k) + k);\n\tconst b = 1 - Math.min(1, y * (1 - k) + k);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.rgb = function (xyz) {\n\tconst x = xyz[0] / 100;\n\tconst y = xyz[1] / 100;\n\tconst z = xyz[2] / 100;\n\tlet r;\n\tlet g;\n\tlet b;\n\n\tr = (x * 3.2406) + (y * -1.5372) + (z * -0.4986);\n\tg = (x * -0.9689) + (y * 1.8758) + (z * 0.0415);\n\tb = (x * 0.0557) + (y * -0.2040) + (z * 1.0570);\n\n\t// Assume sRGB\n\tr = r > 0.0031308\n\t\t? ((1.055 * (r ** (1.0 / 2.4))) - 0.055)\n\t\t: r * 12.92;\n\n\tg = g > 0.0031308\n\t\t? ((1.055 * (g ** (1.0 / 2.4))) - 0.055)\n\t\t: g * 12.92;\n\n\tb = b > 0.0031308\n\t\t? ((1.055 * (b ** (1.0 / 2.4))) - 0.055)\n\t\t: b * 12.92;\n\n\tr = Math.min(Math.max(0, r), 1);\n\tg = Math.min(Math.max(0, g), 1);\n\tb = Math.min(Math.max(0, b), 1);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.lab = function (xyz) {\n\tlet x = xyz[0];\n\tlet y = xyz[1];\n\tlet z = xyz[2];\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? (x ** (1 / 3)) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? (y ** (1 / 3)) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? (z ** (1 / 3)) : (7.787 * z) + (16 / 116);\n\n\tconst l = (116 * y) - 16;\n\tconst a = 500 * (x - y);\n\tconst b = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.lab.xyz = function (lab) {\n\tconst l = lab[0];\n\tconst a = lab[1];\n\tconst b = lab[2];\n\tlet x;\n\tlet y;\n\tlet z;\n\n\ty = (l + 16) / 116;\n\tx = a / 500 + y;\n\tz = y - b / 200;\n\n\tconst y2 = y ** 3;\n\tconst x2 = x ** 3;\n\tconst z2 = z ** 3;\n\ty = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n\tx = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n\tz = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n\n\tx *= 95.047;\n\ty *= 100;\n\tz *= 108.883;\n\n\treturn [x, y, z];\n};\n\nconvert.lab.lch = function (lab) {\n\tconst l = lab[0];\n\tconst a = lab[1];\n\tconst b = lab[2];\n\tlet h;\n\n\tconst hr = Math.atan2(b, a);\n\th = hr * 360 / 2 / Math.PI;\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tconst c = Math.sqrt(a * a + b * b);\n\n\treturn [l, c, h];\n};\n\nconvert.lch.lab = function (lch) {\n\tconst l = lch[0];\n\tconst c = lch[1];\n\tconst h = lch[2];\n\n\tconst hr = h / 360 * 2 * Math.PI;\n\tconst a = c * Math.cos(hr);\n\tconst b = c * Math.sin(hr);\n\n\treturn [l, a, b];\n};\n\nconvert.rgb.ansi16 = function (args, saturation = null) {\n\tconst [r, g, b] = args;\n\tlet value = saturation === null ? convert.rgb.hsv(args)[2] : saturation; // Hsv -> ansi16 optimization\n\n\tvalue = Math.round(value / 50);\n\n\tif (value === 0) {\n\t\treturn 30;\n\t}\n\n\tlet ansi = 30\n\t\t+ ((Math.round(b / 255) << 2)\n\t\t| (Math.round(g / 255) << 1)\n\t\t| Math.round(r / 255));\n\n\tif (value === 2) {\n\t\tansi += 60;\n\t}\n\n\treturn ansi;\n};\n\nconvert.hsv.ansi16 = function (args) {\n\t// Optimization here; we already know the value and don't need to get\n\t// it converted for us.\n\treturn convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n};\n\nconvert.rgb.ansi256 = function (args) {\n\tconst r = args[0];\n\tconst g = args[1];\n\tconst b = args[2];\n\n\t// We use the extended greyscale palette here, with the exception of\n\t// black and white. normal palette only has 4 greyscale shades.\n\tif (r === g && g === b) {\n\t\tif (r < 8) {\n\t\t\treturn 16;\n\t\t}\n\n\t\tif (r > 248) {\n\t\t\treturn 231;\n\t\t}\n\n\t\treturn Math.round(((r - 8) / 247) * 24) + 232;\n\t}\n\n\tconst ansi = 16\n\t\t+ (36 * Math.round(r / 255 * 5))\n\t\t+ (6 * Math.round(g / 255 * 5))\n\t\t+ Math.round(b / 255 * 5);\n\n\treturn ansi;\n};\n\nconvert.ansi16.rgb = function (args) {\n\tlet color = args % 10;\n\n\t// Handle greyscale\n\tif (color === 0 || color === 7) {\n\t\tif (args > 50) {\n\t\t\tcolor += 3.5;\n\t\t}\n\n\t\tcolor = color / 10.5 * 255;\n\n\t\treturn [color, color, color];\n\t}\n\n\tconst mult = (~~(args > 50) + 1) * 0.5;\n\tconst r = ((color & 1) * mult) * 255;\n\tconst g = (((color >> 1) & 1) * mult) * 255;\n\tconst b = (((color >> 2) & 1) * mult) * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.ansi256.rgb = function (args) {\n\t// Handle greyscale\n\tif (args >= 232) {\n\t\tconst c = (args - 232) * 10 + 8;\n\t\treturn [c, c, c];\n\t}\n\n\targs -= 16;\n\n\tlet rem;\n\tconst r = Math.floor(args / 36) / 5 * 255;\n\tconst g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n\tconst b = (rem % 6) / 5 * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hex = function (args) {\n\tconst integer = ((Math.round(args[0]) & 0xFF) << 16)\n\t\t+ ((Math.round(args[1]) & 0xFF) << 8)\n\t\t+ (Math.round(args[2]) & 0xFF);\n\n\tconst string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.hex.rgb = function (args) {\n\tconst match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n\tif (!match) {\n\t\treturn [0, 0, 0];\n\t}\n\n\tlet colorString = match[0];\n\n\tif (match[0].length === 3) {\n\t\tcolorString = colorString.split('').map(char => {\n\t\t\treturn char + char;\n\t\t}).join('');\n\t}\n\n\tconst integer = parseInt(colorString, 16);\n\tconst r = (integer >> 16) & 0xFF;\n\tconst g = (integer >> 8) & 0xFF;\n\tconst b = integer & 0xFF;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hcg = function (rgb) {\n\tconst r = rgb[0] / 255;\n\tconst g = rgb[1] / 255;\n\tconst b = rgb[2] / 255;\n\tconst max = Math.max(Math.max(r, g), b);\n\tconst min = Math.min(Math.min(r, g), b);\n\tconst chroma = (max - min);\n\tlet grayscale;\n\tlet hue;\n\n\tif (chroma < 1) {\n\t\tgrayscale = min / (1 - chroma);\n\t} else {\n\t\tgrayscale = 0;\n\t}\n\n\tif (chroma <= 0) {\n\t\thue = 0;\n\t} else\n\tif (max === r) {\n\t\thue = ((g - b) / chroma) % 6;\n\t} else\n\tif (max === g) {\n\t\thue = 2 + (b - r) / chroma;\n\t} else {\n\t\thue = 4 + (r - g) / chroma;\n\t}\n\n\thue /= 6;\n\thue %= 1;\n\n\treturn [hue * 360, chroma * 100, grayscale * 100];\n};\n\nconvert.hsl.hcg = function (hsl) {\n\tconst s = hsl[1] / 100;\n\tconst l = hsl[2] / 100;\n\n\tconst c = l < 0.5 ? (2.0 * s * l) : (2.0 * s * (1.0 - l));\n\n\tlet f = 0;\n\tif (c < 1.0) {\n\t\tf = (l - 0.5 * c) / (1.0 - c);\n\t}\n\n\treturn [hsl[0], c * 100, f * 100];\n};\n\nconvert.hsv.hcg = function (hsv) {\n\tconst s = hsv[1] / 100;\n\tconst v = hsv[2] / 100;\n\n\tconst c = s * v;\n\tlet f = 0;\n\n\tif (c < 1.0) {\n\t\tf = (v - c) / (1 - c);\n\t}\n\n\treturn [hsv[0], c * 100, f * 100];\n};\n\nconvert.hcg.rgb = function (hcg) {\n\tconst h = hcg[0] / 360;\n\tconst c = hcg[1] / 100;\n\tconst g = hcg[2] / 100;\n\n\tif (c === 0.0) {\n\t\treturn [g * 255, g * 255, g * 255];\n\t}\n\n\tconst pure = [0, 0, 0];\n\tconst hi = (h % 1) * 6;\n\tconst v = hi % 1;\n\tconst w = 1 - v;\n\tlet mg = 0;\n\n\t/* eslint-disable max-statements-per-line */\n\tswitch (Math.floor(hi)) {\n\t\tcase 0:\n\t\t\tpure[0] = 1; pure[1] = v; pure[2] = 0; break;\n\t\tcase 1:\n\t\t\tpure[0] = w; pure[1] = 1; pure[2] = 0; break;\n\t\tcase 2:\n\t\t\tpure[0] = 0; pure[1] = 1; pure[2] = v; break;\n\t\tcase 3:\n\t\t\tpure[0] = 0; pure[1] = w; pure[2] = 1; break;\n\t\tcase 4:\n\t\t\tpure[0] = v; pure[1] = 0; pure[2] = 1; break;\n\t\tdefault:\n\t\t\tpure[0] = 1; pure[1] = 0; pure[2] = w;\n\t}\n\t/* eslint-enable max-statements-per-line */\n\n\tmg = (1.0 - c) * g;\n\n\treturn [\n\t\t(c * pure[0] + mg) * 255,\n\t\t(c * pure[1] + mg) * 255,\n\t\t(c * pure[2] + mg) * 255\n\t];\n};\n\nconvert.hcg.hsv = function (hcg) {\n\tconst c = hcg[1] / 100;\n\tconst g = hcg[2] / 100;\n\n\tconst v = c + g * (1.0 - c);\n\tlet f = 0;\n\n\tif (v > 0.0) {\n\t\tf = c / v;\n\t}\n\n\treturn [hcg[0], f * 100, v * 100];\n};\n\nconvert.hcg.hsl = function (hcg) {\n\tconst c = hcg[1] / 100;\n\tconst g = hcg[2] / 100;\n\n\tconst l = g * (1.0 - c) + 0.5 * c;\n\tlet s = 0;\n\n\tif (l > 0.0 && l < 0.5) {\n\t\ts = c / (2 * l);\n\t} else\n\tif (l >= 0.5 && l < 1.0) {\n\t\ts = c / (2 * (1 - l));\n\t}\n\n\treturn [hcg[0], s * 100, l * 100];\n};\n\nconvert.hcg.hwb = function (hcg) {\n\tconst c = hcg[1] / 100;\n\tconst g = hcg[2] / 100;\n\tconst v = c + g * (1.0 - c);\n\treturn [hcg[0], (v - c) * 100, (1 - v) * 100];\n};\n\nconvert.hwb.hcg = function (hwb) {\n\tconst w = hwb[1] / 100;\n\tconst b = hwb[2] / 100;\n\tconst v = 1 - b;\n\tconst c = v - w;\n\tlet g = 0;\n\n\tif (c < 1) {\n\t\tg = (v - c) / (1 - c);\n\t}\n\n\treturn [hwb[0], c * 100, g * 100];\n};\n\nconvert.apple.rgb = function (apple) {\n\treturn [(apple[0] / 65535) * 255, (apple[1] / 65535) * 255, (apple[2] / 65535) * 255];\n};\n\nconvert.rgb.apple = function (rgb) {\n\treturn [(rgb[0] / 255) * 65535, (rgb[1] / 255) * 65535, (rgb[2] / 255) * 65535];\n};\n\nconvert.gray.rgb = function (args) {\n\treturn [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];\n};\n\nconvert.gray.hsl = function (args) {\n\treturn [0, 0, args[0]];\n};\n\nconvert.gray.hsv = convert.gray.hsl;\n\nconvert.gray.hwb = function (gray) {\n\treturn [0, 100, gray[0]];\n};\n\nconvert.gray.cmyk = function (gray) {\n\treturn [0, 0, 0, gray[0]];\n};\n\nconvert.gray.lab = function (gray) {\n\treturn [gray[0], 0, 0];\n};\n\nconvert.gray.hex = function (gray) {\n\tconst val = Math.round(gray[0] / 100 * 255) & 0xFF;\n\tconst integer = (val << 16) + (val << 8) + val;\n\n\tconst string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.rgb.gray = function (rgb) {\n\tconst val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n\treturn [val / 255 * 100];\n};\n", "const conversions = require('./conversions');\n\n/*\n\tThis function routes a model to all other models.\n\n\tall functions that are routed have a property `.conversion` attached\n\tto the returned synthetic function. This property is an array\n\tof strings, each with the steps in between the 'from' and 'to'\n\tcolor models (inclusive).\n\n\tconversions that are not possible simply are not included.\n*/\n\nfunction buildGraph() {\n\tconst graph = {};\n\t// https://jsperf.com/object-keys-vs-for-in-with-closure/3\n\tconst models = Object.keys(conversions);\n\n\tfor (let len = models.length, i = 0; i < len; i++) {\n\t\tgraph[models[i]] = {\n\t\t\t// http://jsperf.com/1-vs-infinity\n\t\t\t// micro-opt, but this is simple.\n\t\t\tdistance: -1,\n\t\t\tparent: null\n\t\t};\n\t}\n\n\treturn graph;\n}\n\n// https://en.wikipedia.org/wiki/Breadth-first_search\nfunction deriveBFS(fromModel) {\n\tconst graph = buildGraph();\n\tconst queue = [fromModel]; // Unshift -> queue -> pop\n\n\tgraph[fromModel].distance = 0;\n\n\twhile (queue.length) {\n\t\tconst current = queue.pop();\n\t\tconst adjacents = Object.keys(conversions[current]);\n\n\t\tfor (let len = adjacents.length, i = 0; i < len; i++) {\n\t\t\tconst adjacent = adjacents[i];\n\t\t\tconst node = graph[adjacent];\n\n\t\t\tif (node.distance === -1) {\n\t\t\t\tnode.distance = graph[current].distance + 1;\n\t\t\t\tnode.parent = current;\n\t\t\t\tqueue.unshift(adjacent);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn graph;\n}\n\nfunction link(from, to) {\n\treturn function (args) {\n\t\treturn to(from(args));\n\t};\n}\n\nfunction wrapConversion(toModel, graph) {\n\tconst path = [graph[toModel].parent, toModel];\n\tlet fn = conversions[graph[toModel].parent][toModel];\n\n\tlet cur = graph[toModel].parent;\n\twhile (graph[cur].parent) {\n\t\tpath.unshift(graph[cur].parent);\n\t\tfn = link(conversions[graph[cur].parent][cur], fn);\n\t\tcur = graph[cur].parent;\n\t}\n\n\tfn.conversion = path;\n\treturn fn;\n}\n\nmodule.exports = function (fromModel) {\n\tconst graph = deriveBFS(fromModel);\n\tconst conversion = {};\n\n\tconst models = Object.keys(graph);\n\tfor (let len = models.length, i = 0; i < len; i++) {\n\t\tconst toModel = models[i];\n\t\tconst node = graph[toModel];\n\n\t\tif (node.parent === null) {\n\t\t\t// No possible conversion, or this node is the source model.\n\t\t\tcontinue;\n\t\t}\n\n\t\tconversion[toModel] = wrapConversion(toModel, graph);\n\t}\n\n\treturn conversion;\n};\n\n", "const conversions = require('./conversions');\nconst route = require('./route');\n\nconst convert = {};\n\nconst models = Object.keys(conversions);\n\nfunction wrapRaw(fn) {\n\tconst wrappedFn = function (...args) {\n\t\tconst arg0 = args[0];\n\t\tif (arg0 === undefined || arg0 === null) {\n\t\t\treturn arg0;\n\t\t}\n\n\t\tif (arg0.length > 1) {\n\t\t\targs = arg0;\n\t\t}\n\n\t\treturn fn(args);\n\t};\n\n\t// Preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nfunction wrapRounded(fn) {\n\tconst wrappedFn = function (...args) {\n\t\tconst arg0 = args[0];\n\n\t\tif (arg0 === undefined || arg0 === null) {\n\t\t\treturn arg0;\n\t\t}\n\n\t\tif (arg0.length > 1) {\n\t\t\targs = arg0;\n\t\t}\n\n\t\tconst result = fn(args);\n\n\t\t// We're assuming the result is an array here.\n\t\t// see notice in conversions.js; don't use box types\n\t\t// in conversion functions.\n\t\tif (typeof result === 'object') {\n\t\t\tfor (let len = result.length, i = 0; i < len; i++) {\n\t\t\t\tresult[i] = Math.round(result[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t};\n\n\t// Preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nmodels.forEach(fromModel => {\n\tconvert[fromModel] = {};\n\n\tObject.defineProperty(convert[fromModel], 'channels', {value: conversions[fromModel].channels});\n\tObject.defineProperty(convert[fromModel], 'labels', {value: conversions[fromModel].labels});\n\n\tconst routes = route(fromModel);\n\tconst routeModels = Object.keys(routes);\n\n\trouteModels.forEach(toModel => {\n\t\tconst fn = routes[toModel];\n\n\t\tconvert[fromModel][toModel] = wrapRounded(fn);\n\t\tconvert[fromModel][toModel].raw = wrapRaw(fn);\n\t});\n});\n\nmodule.exports = convert;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,WAAO,UAAU;AAAA,MAChB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,QAAQ,CAAC,GAAG,KAAK,GAAG;AAAA,MACpB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,SAAS,CAAC,GAAG,GAAG,CAAC;AAAA,MACjB,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,QAAQ,CAAC,GAAG,GAAG,GAAG;AAAA,MAClB,cAAc,CAAC,KAAK,IAAI,GAAG;AAAA,MAC3B,SAAS,CAAC,KAAK,IAAI,EAAE;AAAA,MACrB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,cAAc,CAAC,KAAK,KAAK,CAAC;AAAA,MAC1B,aAAa,CAAC,KAAK,KAAK,EAAE;AAAA,MAC1B,SAAS,CAAC,KAAK,KAAK,EAAE;AAAA,MACtB,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,WAAW,CAAC,KAAK,IAAI,EAAE;AAAA,MACvB,QAAQ,CAAC,GAAG,KAAK,GAAG;AAAA,MACpB,YAAY,CAAC,GAAG,GAAG,GAAG;AAAA,MACtB,YAAY,CAAC,GAAG,KAAK,GAAG;AAAA,MACxB,iBAAiB,CAAC,KAAK,KAAK,EAAE;AAAA,MAC9B,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,aAAa,CAAC,GAAG,KAAK,CAAC;AAAA,MACvB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,eAAe,CAAC,KAAK,GAAG,GAAG;AAAA,MAC3B,kBAAkB,CAAC,IAAI,KAAK,EAAE;AAAA,MAC9B,cAAc,CAAC,KAAK,KAAK,CAAC;AAAA,MAC1B,cAAc,CAAC,KAAK,IAAI,GAAG;AAAA,MAC3B,WAAW,CAAC,KAAK,GAAG,CAAC;AAAA,MACrB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,iBAAiB,CAAC,IAAI,IAAI,GAAG;AAAA,MAC7B,iBAAiB,CAAC,IAAI,IAAI,EAAE;AAAA,MAC5B,iBAAiB,CAAC,IAAI,IAAI,EAAE;AAAA,MAC5B,iBAAiB,CAAC,GAAG,KAAK,GAAG;AAAA,MAC7B,cAAc,CAAC,KAAK,GAAG,GAAG;AAAA,MAC1B,YAAY,CAAC,KAAK,IAAI,GAAG;AAAA,MACzB,eAAe,CAAC,GAAG,KAAK,GAAG;AAAA,MAC3B,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,cAAc,CAAC,IAAI,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,IAAI,EAAE;AAAA,MACzB,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,eAAe,CAAC,IAAI,KAAK,EAAE;AAAA,MAC3B,WAAW,CAAC,KAAK,GAAG,GAAG;AAAA,MACvB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,QAAQ,CAAC,KAAK,KAAK,CAAC;AAAA,MACpB,aAAa,CAAC,KAAK,KAAK,EAAE;AAAA,MAC1B,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,SAAS,CAAC,GAAG,KAAK,CAAC;AAAA,MACnB,eAAe,CAAC,KAAK,KAAK,EAAE;AAAA,MAC5B,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,aAAa,CAAC,KAAK,IAAI,EAAE;AAAA,MACzB,UAAU,CAAC,IAAI,GAAG,GAAG;AAAA,MACrB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,aAAa,CAAC,KAAK,KAAK,CAAC;AAAA,MACzB,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,wBAAwB,CAAC,KAAK,KAAK,GAAG;AAAA,MACtC,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,iBAAiB,CAAC,IAAI,KAAK,GAAG;AAAA,MAC9B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,kBAAkB,CAAC,KAAK,KAAK,GAAG;AAAA,MAChC,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,QAAQ,CAAC,GAAG,KAAK,CAAC;AAAA,MAClB,aAAa,CAAC,IAAI,KAAK,EAAE;AAAA,MACzB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,WAAW,CAAC,KAAK,GAAG,GAAG;AAAA,MACvB,UAAU,CAAC,KAAK,GAAG,CAAC;AAAA,MACpB,oBAAoB,CAAC,KAAK,KAAK,GAAG;AAAA,MAClC,cAAc,CAAC,GAAG,GAAG,GAAG;AAAA,MACxB,gBAAgB,CAAC,KAAK,IAAI,GAAG;AAAA,MAC7B,gBAAgB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC9B,kBAAkB,CAAC,IAAI,KAAK,GAAG;AAAA,MAC/B,mBAAmB,CAAC,KAAK,KAAK,GAAG;AAAA,MACjC,qBAAqB,CAAC,GAAG,KAAK,GAAG;AAAA,MACjC,mBAAmB,CAAC,IAAI,KAAK,GAAG;AAAA,MAChC,mBAAmB,CAAC,KAAK,IAAI,GAAG;AAAA,MAChC,gBAAgB,CAAC,IAAI,IAAI,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,eAAe,CAAC,KAAK,KAAK,GAAG;AAAA,MAC7B,QAAQ,CAAC,GAAG,GAAG,GAAG;AAAA,MAClB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,SAAS,CAAC,KAAK,KAAK,CAAC;AAAA,MACrB,aAAa,CAAC,KAAK,KAAK,EAAE;AAAA,MAC1B,UAAU,CAAC,KAAK,KAAK,CAAC;AAAA,MACtB,aAAa,CAAC,KAAK,IAAI,CAAC;AAAA,MACxB,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,iBAAiB,CAAC,KAAK,KAAK,GAAG;AAAA,MAC/B,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,QAAQ,CAAC,KAAK,KAAK,EAAE;AAAA,MACrB,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,UAAU,CAAC,KAAK,GAAG,GAAG;AAAA,MACtB,iBAAiB,CAAC,KAAK,IAAI,GAAG;AAAA,MAC9B,OAAO,CAAC,KAAK,GAAG,CAAC;AAAA,MACjB,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,eAAe,CAAC,KAAK,IAAI,EAAE;AAAA,MAC3B,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,cAAc,CAAC,KAAK,KAAK,EAAE;AAAA,MAC3B,YAAY,CAAC,IAAI,KAAK,EAAE;AAAA,MACxB,YAAY,CAAC,KAAK,KAAK,GAAG;AAAA,MAC1B,UAAU,CAAC,KAAK,IAAI,EAAE;AAAA,MACtB,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,aAAa,CAAC,KAAK,IAAI,GAAG;AAAA,MAC1B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,KAAK,KAAK,GAAG;AAAA,MAC3B,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB,eAAe,CAAC,GAAG,KAAK,GAAG;AAAA,MAC3B,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,OAAO,CAAC,KAAK,KAAK,GAAG;AAAA,MACrB,QAAQ,CAAC,GAAG,KAAK,GAAG;AAAA,MACpB,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,MACzB,UAAU,CAAC,KAAK,IAAI,EAAE;AAAA,MACtB,aAAa,CAAC,IAAI,KAAK,GAAG;AAAA,MAC1B,UAAU,CAAC,KAAK,KAAK,GAAG;AAAA,MACxB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,SAAS,CAAC,KAAK,KAAK,GAAG;AAAA,MACvB,cAAc,CAAC,KAAK,KAAK,GAAG;AAAA,MAC5B,UAAU,CAAC,KAAK,KAAK,CAAC;AAAA,MACtB,eAAe,CAAC,KAAK,KAAK,EAAE;AAAA,IAC7B;AAAA;AAAA;;;ACvJA;AAAA;AAEA,QAAM,cAAc;AAMpB,QAAM,kBAAkB,CAAC;AACzB,eAAW,OAAO,OAAO,KAAK,WAAW,GAAG;AAC3C,sBAAgB,YAAY,GAAG,CAAC,IAAI;AAAA,IACrC;AAEA,QAAM,UAAU;AAAA,MACf,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,MAAM,EAAC,UAAU,GAAG,QAAQ,OAAM;AAAA,MAClC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,MAAK;AAAA,MAChC,KAAK,EAAC,UAAU,GAAG,QAAQ,CAAC,KAAK,EAAC;AAAA,MAClC,SAAS,EAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,EAAC;AAAA,MAC1C,QAAQ,EAAC,UAAU,GAAG,QAAQ,CAAC,QAAQ,EAAC;AAAA,MACxC,SAAS,EAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,EAAC;AAAA,MAC1C,KAAK,EAAC,UAAU,GAAG,QAAQ,CAAC,KAAK,KAAK,GAAG,EAAC;AAAA,MAC1C,OAAO,EAAC,UAAU,GAAG,QAAQ,CAAC,OAAO,OAAO,KAAK,EAAC;AAAA,MAClD,MAAM,EAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,EAAC;AAAA,IACrC;AAEA,WAAO,UAAU;AAGjB,eAAW,SAAS,OAAO,KAAK,OAAO,GAAG;AACzC,UAAI,EAAE,cAAc,QAAQ,KAAK,IAAI;AACpC,cAAM,IAAI,MAAM,gCAAgC,KAAK;AAAA,MACtD;AAEA,UAAI,EAAE,YAAY,QAAQ,KAAK,IAAI;AAClC,cAAM,IAAI,MAAM,sCAAsC,KAAK;AAAA,MAC5D;AAEA,UAAI,QAAQ,KAAK,EAAE,OAAO,WAAW,QAAQ,KAAK,EAAE,UAAU;AAC7D,cAAM,IAAI,MAAM,wCAAwC,KAAK;AAAA,MAC9D;AAEA,YAAM,EAAC,UAAU,OAAM,IAAI,QAAQ,KAAK;AACxC,aAAO,QAAQ,KAAK,EAAE;AACtB,aAAO,QAAQ,KAAK,EAAE;AACtB,aAAO,eAAe,QAAQ,KAAK,GAAG,YAAY,EAAC,OAAO,SAAQ,CAAC;AACnE,aAAO,eAAe,QAAQ,KAAK,GAAG,UAAU,EAAC,OAAO,OAAM,CAAC;AAAA,IAChE;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,YAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,YAAM,QAAQ,MAAM;AACpB,UAAI;AACJ,UAAI;AAEJ,UAAI,QAAQ,KAAK;AAChB,YAAI;AAAA,MACL,WAAW,MAAM,KAAK;AACrB,aAAK,IAAI,KAAK;AAAA,MACf,WAAW,MAAM,KAAK;AACrB,YAAI,KAAK,IAAI,KAAK;AAAA,MACnB,WAAW,MAAM,KAAK;AACrB,YAAI,KAAK,IAAI,KAAK;AAAA,MACnB;AAEA,UAAI,KAAK,IAAI,IAAI,IAAI,GAAG;AAExB,UAAI,IAAI,GAAG;AACV,aAAK;AAAA,MACN;AAEA,YAAM,KAAK,MAAM,OAAO;AAExB,UAAI,QAAQ,KAAK;AAChB,YAAI;AAAA,MACL,WAAW,KAAK,KAAK;AACpB,YAAI,SAAS,MAAM;AAAA,MACpB,OAAO;AACN,YAAI,SAAS,IAAI,MAAM;AAAA,MACxB;AAEA,aAAO,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IAC5B;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC;AAC1B,YAAM,OAAO,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC;AACjC,YAAM,QAAQ,SAAU,GAAG;AAC1B,gBAAQ,IAAI,KAAK,IAAI,OAAO,IAAI;AAAA,MACjC;AAEA,UAAI,SAAS,GAAG;AACf,YAAI;AACJ,YAAI;AAAA,MACL,OAAO;AACN,YAAI,OAAO;AACX,eAAO,MAAM,CAAC;AACd,eAAO,MAAM,CAAC;AACd,eAAO,MAAM,CAAC;AAEd,YAAI,MAAM,GAAG;AACZ,cAAI,OAAO;AAAA,QACZ,WAAW,MAAM,GAAG;AACnB,cAAK,IAAI,IAAK,OAAO;AAAA,QACtB,WAAW,MAAM,GAAG;AACnB,cAAK,IAAI,IAAK,OAAO;AAAA,QACtB;AAEA,YAAI,IAAI,GAAG;AACV,eAAK;AAAA,QACN,WAAW,IAAI,GAAG;AACjB,eAAK;AAAA,QACN;AAAA,MACD;AAEA,aAAO;AAAA,QACN,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,MACL;AAAA,IACD;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AACf,UAAI,IAAI,IAAI,CAAC;AACb,YAAM,IAAI,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC;AAChC,YAAM,IAAI,IAAI,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;AAE9C,UAAI,IAAI,IAAI,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;AAE5C,aAAO,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IAC5B;AAEA,YAAQ,IAAI,OAAO,SAAU,KAAK;AACjC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AAEnB,YAAM,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AACtC,YAAM,KAAK,IAAI,IAAI,MAAM,IAAI,MAAM;AACnC,YAAM,KAAK,IAAI,IAAI,MAAM,IAAI,MAAM;AACnC,YAAM,KAAK,IAAI,IAAI,MAAM,IAAI,MAAM;AAEnC,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAC3C;AAEA,aAAS,oBAAoB,GAAG,GAAG;AAIlC,cACG,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAChB,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAChB,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM;AAAA,IAEpB;AAEA,YAAQ,IAAI,UAAU,SAAU,KAAK;AACpC,YAAM,WAAW,gBAAgB,GAAG;AACpC,UAAI,UAAU;AACb,eAAO;AAAA,MACR;AAEA,UAAI,yBAAyB;AAC7B,UAAI;AAEJ,iBAAW,WAAW,OAAO,KAAK,WAAW,GAAG;AAC/C,cAAM,QAAQ,YAAY,OAAO;AAGjC,cAAM,WAAW,oBAAoB,KAAK,KAAK;AAG/C,YAAI,WAAW,wBAAwB;AACtC,mCAAyB;AACzB,kCAAwB;AAAA,QACzB;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,YAAQ,QAAQ,MAAM,SAAU,SAAS;AACxC,aAAO,YAAY,OAAO;AAAA,IAC3B;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AAGjB,UAAI,IAAI,YAAa,IAAI,SAAS,UAAU,MAAQ,IAAI;AACxD,UAAI,IAAI,YAAa,IAAI,SAAS,UAAU,MAAQ,IAAI;AACxD,UAAI,IAAI,YAAa,IAAI,SAAS,UAAU,MAAQ,IAAI;AAExD,YAAM,IAAK,IAAI,SAAW,IAAI,SAAW,IAAI;AAC7C,YAAM,IAAK,IAAI,SAAW,IAAI,SAAW,IAAI;AAC7C,YAAM,IAAK,IAAI,SAAW,IAAI,SAAW,IAAI;AAE7C,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAClC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,MAAM,QAAQ,IAAI,IAAI,GAAG;AAC/B,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AAEb,WAAK;AACL,WAAK;AACL,WAAK;AAEL,UAAI,IAAI,UAAY,MAAM,IAAI,KAAO,QAAQ,IAAM,KAAK;AACxD,UAAI,IAAI,UAAY,MAAM,IAAI,KAAO,QAAQ,IAAM,KAAK;AACxD,UAAI,IAAI,UAAY,MAAM,IAAI,KAAO,QAAQ,IAAM,KAAK;AAExD,YAAM,IAAK,MAAM,IAAK;AACtB,YAAM,IAAI,OAAO,IAAI;AACrB,YAAM,IAAI,OAAO,IAAI;AAErB,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,MAAM,GAAG;AACZ,cAAM,IAAI;AACV,eAAO,CAAC,KAAK,KAAK,GAAG;AAAA,MACtB;AAEA,UAAI,IAAI,KAAK;AACZ,aAAK,KAAK,IAAI;AAAA,MACf,OAAO;AACN,aAAK,IAAI,IAAI,IAAI;AAAA,MAClB;AAEA,YAAM,KAAK,IAAI,IAAI;AAEnB,YAAM,MAAM,CAAC,GAAG,GAAG,CAAC;AACpB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC3B,aAAK,IAAI,IAAI,IAAI,EAAE,IAAI;AACvB,YAAI,KAAK,GAAG;AACX;AAAA,QACD;AAEA,YAAI,KAAK,GAAG;AACX;AAAA,QACD;AAEA,YAAI,IAAI,KAAK,GAAG;AACf,gBAAM,MAAM,KAAK,MAAM,IAAI;AAAA,QAC5B,WAAW,IAAI,KAAK,GAAG;AACtB,gBAAM;AAAA,QACP,WAAW,IAAI,KAAK,GAAG;AACtB,gBAAM,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM;AAAA,QACvC,OAAO;AACN,gBAAM;AAAA,QACP;AAEA,YAAI,CAAC,IAAI,MAAM;AAAA,MAChB;AAEA,aAAO;AAAA,IACR;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC;AACf,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,UAAI,OAAO;AACX,YAAM,OAAO,KAAK,IAAI,GAAG,IAAI;AAE7B,WAAK;AACL,WAAM,KAAK,IAAK,IAAI,IAAI;AACxB,cAAQ,QAAQ,IAAI,OAAO,IAAI;AAC/B,YAAM,KAAK,IAAI,KAAK;AACpB,YAAM,KAAK,MAAM,IAAK,IAAI,QAAS,OAAO,QAAS,IAAI,KAAM,IAAI;AAEjE,aAAO,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG;AAAA,IAC7B;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,UAAI,IAAI,IAAI,CAAC,IAAI;AACjB,YAAM,KAAK,KAAK,MAAM,CAAC,IAAI;AAE3B,YAAM,IAAI,IAAI,KAAK,MAAM,CAAC;AAC1B,YAAM,IAAI,MAAM,KAAK,IAAI;AACzB,YAAM,IAAI,MAAM,KAAK,IAAK,IAAI;AAC9B,YAAM,IAAI,MAAM,KAAK,IAAK,KAAK,IAAI;AACnC,WAAK;AAEL,cAAQ,IAAI;AAAA,QACX,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,QAChB,KAAK;AACJ,iBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,MACjB;AAAA,IACD;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,OAAO,KAAK,IAAI,GAAG,IAAI;AAC7B,UAAI;AACJ,UAAI;AAEJ,WAAK,IAAI,KAAK;AACd,YAAM,QAAQ,IAAI,KAAK;AACvB,WAAK,IAAI;AACT,YAAO,QAAQ,IAAK,OAAO,IAAI;AAC/B,WAAK,MAAM;AACX,WAAK;AAEL,aAAO,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG;AAAA,IAC7B;AAGA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,UAAI,KAAK,IAAI,CAAC,IAAI;AAClB,UAAI,KAAK,IAAI,CAAC,IAAI;AAClB,YAAM,QAAQ,KAAK;AACnB,UAAI;AAGJ,UAAI,QAAQ,GAAG;AACd,cAAM;AACN,cAAM;AAAA,MACP;AAEA,YAAM,IAAI,KAAK,MAAM,IAAI,CAAC;AAC1B,YAAM,IAAI,IAAI;AACd,UAAI,IAAI,IAAI;AAEZ,WAAK,IAAI,OAAU,GAAG;AACrB,YAAI,IAAI;AAAA,MACT;AAEA,YAAM,IAAI,KAAK,KAAK,IAAI;AAExB,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,cAAQ,GAAG;AAAA,QACV;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAG,cAAI;AAAI,cAAI;AAAI,cAAI;AAAI;AAAA,QAChC,KAAK;AAAG,cAAI;AAAI,cAAI;AAAI,cAAI;AAAI;AAAA,QAChC,KAAK;AAAG,cAAI;AAAI,cAAI;AAAI,cAAI;AAAG;AAAA,QAC/B,KAAK;AAAG,cAAI;AAAI,cAAI;AAAI,cAAI;AAAG;AAAA,QAC/B,KAAK;AAAG,cAAI;AAAI,cAAI;AAAI,cAAI;AAAG;AAAA,QAC/B,KAAK;AAAG,cAAI;AAAI,cAAI;AAAI,cAAI;AAAG;AAAA,MAChC;AAGA,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAClC;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,YAAM,IAAI,KAAK,CAAC,IAAI;AACpB,YAAM,IAAI,KAAK,CAAC,IAAI;AACpB,YAAM,IAAI,KAAK,CAAC,IAAI;AACpB,YAAM,IAAI,KAAK,CAAC,IAAI;AAEpB,YAAM,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC;AACzC,YAAM,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC;AACzC,YAAM,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC;AAEzC,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAClC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAK,IAAI,SAAW,IAAI,UAAY,IAAI;AACxC,UAAK,IAAI,UAAY,IAAI,SAAW,IAAI;AACxC,UAAK,IAAI,SAAW,IAAI,SAAY,IAAI;AAGxC,UAAI,IAAI,WACH,QAAS,MAAM,IAAM,OAAS,QAChC,IAAI;AAEP,UAAI,IAAI,WACH,QAAS,MAAM,IAAM,OAAS,QAChC,IAAI;AAEP,UAAI,IAAI,WACH,QAAS,MAAM,IAAM,OAAS,QAChC,IAAI;AAEP,UAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAC9B,UAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAC9B,UAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAE9B,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,IAClC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AACb,UAAI,IAAI,IAAI,CAAC;AAEb,WAAK;AACL,WAAK;AACL,WAAK;AAEL,UAAI,IAAI,UAAY,MAAM,IAAI,KAAO,QAAQ,IAAM,KAAK;AACxD,UAAI,IAAI,UAAY,MAAM,IAAI,KAAO,QAAQ,IAAM,KAAK;AACxD,UAAI,IAAI,UAAY,MAAM,IAAI,KAAO,QAAQ,IAAM,KAAK;AAExD,YAAM,IAAK,MAAM,IAAK;AACtB,YAAM,IAAI,OAAO,IAAI;AACrB,YAAM,IAAI,OAAO,IAAI;AAErB,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AACf,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,WAAK,IAAI,MAAM;AACf,UAAI,IAAI,MAAM;AACd,UAAI,IAAI,IAAI;AAEZ,YAAM,KAAK,KAAK;AAChB,YAAM,KAAK,KAAK;AAChB,YAAM,KAAK,KAAK;AAChB,UAAI,KAAK,UAAW,MAAM,IAAI,KAAK,OAAO;AAC1C,UAAI,KAAK,UAAW,MAAM,IAAI,KAAK,OAAO;AAC1C,UAAI,KAAK,UAAW,MAAM,IAAI,KAAK,OAAO;AAE1C,WAAK;AACL,WAAK;AACL,WAAK;AAEL,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AACf,UAAI;AAEJ,YAAM,KAAK,KAAK,MAAM,GAAG,CAAC;AAC1B,UAAI,KAAK,MAAM,IAAI,KAAK;AAExB,UAAI,IAAI,GAAG;AACV,aAAK;AAAA,MACN;AAEA,YAAM,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAEjC,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AACf,YAAM,IAAI,IAAI,CAAC;AAEf,YAAM,KAAK,IAAI,MAAM,IAAI,KAAK;AAC9B,YAAM,IAAI,IAAI,KAAK,IAAI,EAAE;AACzB,YAAM,IAAI,IAAI,KAAK,IAAI,EAAE;AAEzB,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,SAAS,SAAU,MAAM,aAAa,MAAM;AACvD,YAAM,CAAC,GAAG,GAAG,CAAC,IAAI;AAClB,UAAI,QAAQ,eAAe,OAAO,QAAQ,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI;AAE7D,cAAQ,KAAK,MAAM,QAAQ,EAAE;AAE7B,UAAI,UAAU,GAAG;AAChB,eAAO;AAAA,MACR;AAEA,UAAI,OAAO,MACN,KAAK,MAAM,IAAI,GAAG,KAAK,IACxB,KAAK,MAAM,IAAI,GAAG,KAAK,IACxB,KAAK,MAAM,IAAI,GAAG;AAErB,UAAI,UAAU,GAAG;AAChB,gBAAQ;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAEA,YAAQ,IAAI,SAAS,SAAU,MAAM;AAGpC,aAAO,QAAQ,IAAI,OAAO,QAAQ,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;AAAA,IACzD;AAEA,YAAQ,IAAI,UAAU,SAAU,MAAM;AACrC,YAAM,IAAI,KAAK,CAAC;AAChB,YAAM,IAAI,KAAK,CAAC;AAChB,YAAM,IAAI,KAAK,CAAC;AAIhB,UAAI,MAAM,KAAK,MAAM,GAAG;AACvB,YAAI,IAAI,GAAG;AACV,iBAAO;AAAA,QACR;AAEA,YAAI,IAAI,KAAK;AACZ,iBAAO;AAAA,QACR;AAEA,eAAO,KAAK,OAAQ,IAAI,KAAK,MAAO,EAAE,IAAI;AAAA,MAC3C;AAEA,YAAM,OAAO,KACT,KAAK,KAAK,MAAM,IAAI,MAAM,CAAC,IAC3B,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAC3B,KAAK,MAAM,IAAI,MAAM,CAAC;AAEzB,aAAO;AAAA,IACR;AAEA,YAAQ,OAAO,MAAM,SAAU,MAAM;AACpC,UAAI,QAAQ,OAAO;AAGnB,UAAI,UAAU,KAAK,UAAU,GAAG;AAC/B,YAAI,OAAO,IAAI;AACd,mBAAS;AAAA,QACV;AAEA,gBAAQ,QAAQ,OAAO;AAEvB,eAAO,CAAC,OAAO,OAAO,KAAK;AAAA,MAC5B;AAEA,YAAM,QAAQ,CAAC,EAAE,OAAO,MAAM,KAAK;AACnC,YAAM,KAAM,QAAQ,KAAK,OAAQ;AACjC,YAAM,KAAO,SAAS,IAAK,KAAK,OAAQ;AACxC,YAAM,KAAO,SAAS,IAAK,KAAK,OAAQ;AAExC,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,QAAQ,MAAM,SAAU,MAAM;AAErC,UAAI,QAAQ,KAAK;AAChB,cAAM,KAAK,OAAO,OAAO,KAAK;AAC9B,eAAO,CAAC,GAAG,GAAG,CAAC;AAAA,MAChB;AAEA,cAAQ;AAER,UAAI;AACJ,YAAM,IAAI,KAAK,MAAM,OAAO,EAAE,IAAI,IAAI;AACtC,YAAM,IAAI,KAAK,OAAO,MAAM,OAAO,MAAM,CAAC,IAAI,IAAI;AAClD,YAAM,IAAK,MAAM,IAAK,IAAI;AAE1B,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,MAAM;AACjC,YAAM,YAAY,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,QAAS,QAC5C,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI,QAAS,MAChC,KAAK,MAAM,KAAK,CAAC,CAAC,IAAI;AAE1B,YAAM,SAAS,QAAQ,SAAS,EAAE,EAAE,YAAY;AAChD,aAAO,SAAS,UAAU,OAAO,MAAM,IAAI;AAAA,IAC5C;AAEA,YAAQ,IAAI,MAAM,SAAU,MAAM;AACjC,YAAM,QAAQ,KAAK,SAAS,EAAE,EAAE,MAAM,0BAA0B;AAChE,UAAI,CAAC,OAAO;AACX,eAAO,CAAC,GAAG,GAAG,CAAC;AAAA,MAChB;AAEA,UAAI,cAAc,MAAM,CAAC;AAEzB,UAAI,MAAM,CAAC,EAAE,WAAW,GAAG;AAC1B,sBAAc,YAAY,MAAM,EAAE,EAAE,IAAI,UAAQ;AAC/C,iBAAO,OAAO;AAAA,QACf,CAAC,EAAE,KAAK,EAAE;AAAA,MACX;AAEA,YAAM,UAAU,SAAS,aAAa,EAAE;AACxC,YAAM,IAAK,WAAW,KAAM;AAC5B,YAAM,IAAK,WAAW,IAAK;AAC3B,YAAM,IAAI,UAAU;AAEpB,aAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AACtC,YAAM,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AACtC,YAAM,SAAU,MAAM;AACtB,UAAI;AACJ,UAAI;AAEJ,UAAI,SAAS,GAAG;AACf,oBAAY,OAAO,IAAI;AAAA,MACxB,OAAO;AACN,oBAAY;AAAA,MACb;AAEA,UAAI,UAAU,GAAG;AAChB,cAAM;AAAA,MACP,WACI,QAAQ,GAAG;AACd,eAAQ,IAAI,KAAK,SAAU;AAAA,MAC5B,WACI,QAAQ,GAAG;AACd,cAAM,KAAK,IAAI,KAAK;AAAA,MACrB,OAAO;AACN,cAAM,KAAK,IAAI,KAAK;AAAA,MACrB;AAEA,aAAO;AACP,aAAO;AAEP,aAAO,CAAC,MAAM,KAAK,SAAS,KAAK,YAAY,GAAG;AAAA,IACjD;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AAEnB,YAAM,IAAI,IAAI,MAAO,IAAM,IAAI,IAAM,IAAM,KAAK,IAAM;AAEtD,UAAI,IAAI;AACR,UAAI,IAAI,GAAK;AACZ,aAAK,IAAI,MAAM,MAAM,IAAM;AAAA,MAC5B;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AAEnB,YAAM,IAAI,IAAI;AACd,UAAI,IAAI;AAER,UAAI,IAAI,GAAK;AACZ,aAAK,IAAI,MAAM,IAAI;AAAA,MACpB;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AAEnB,UAAI,MAAM,GAAK;AACd,eAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,MAClC;AAEA,YAAM,OAAO,CAAC,GAAG,GAAG,CAAC;AACrB,YAAM,KAAM,IAAI,IAAK;AACrB,YAAM,IAAI,KAAK;AACf,YAAM,IAAI,IAAI;AACd,UAAI,KAAK;AAGT,cAAQ,KAAK,MAAM,EAAE,GAAG;AAAA,QACvB,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC,KAAK;AACJ,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG;AAAA,QACxC;AACC,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAG,eAAK,CAAC,IAAI;AAAA,MACtC;AAGA,YAAM,IAAM,KAAK;AAEjB,aAAO;AAAA,SACL,IAAI,KAAK,CAAC,IAAI,MAAM;AAAA,SACpB,IAAI,KAAK,CAAC,IAAI,MAAM;AAAA,SACpB,IAAI,KAAK,CAAC,IAAI,MAAM;AAAA,MACtB;AAAA,IACD;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AAEnB,YAAM,IAAI,IAAI,KAAK,IAAM;AACzB,UAAI,IAAI;AAER,UAAI,IAAI,GAAK;AACZ,YAAI,IAAI;AAAA,MACT;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AAEnB,YAAM,IAAI,KAAK,IAAM,KAAK,MAAM;AAChC,UAAI,IAAI;AAER,UAAI,IAAI,KAAO,IAAI,KAAK;AACvB,YAAI,KAAK,IAAI;AAAA,MACd,WACI,KAAK,OAAO,IAAI,GAAK;AACxB,YAAI,KAAK,KAAK,IAAI;AAAA,MACnB;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,KAAK,IAAM;AACzB,aAAO,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,MAAM,IAAI,KAAK,GAAG;AAAA,IAC7C;AAEA,YAAQ,IAAI,MAAM,SAAU,KAAK;AAChC,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI,CAAC,IAAI;AACnB,YAAM,IAAI,IAAI;AACd,YAAM,IAAI,IAAI;AACd,UAAI,IAAI;AAER,UAAI,IAAI,GAAG;AACV,aAAK,IAAI,MAAM,IAAI;AAAA,MACpB;AAEA,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,IACjC;AAEA,YAAQ,MAAM,MAAM,SAAU,OAAO;AACpC,aAAO,CAAE,MAAM,CAAC,IAAI,QAAS,KAAM,MAAM,CAAC,IAAI,QAAS,KAAM,MAAM,CAAC,IAAI,QAAS,GAAG;AAAA,IACrF;AAEA,YAAQ,IAAI,QAAQ,SAAU,KAAK;AAClC,aAAO,CAAE,IAAI,CAAC,IAAI,MAAO,OAAQ,IAAI,CAAC,IAAI,MAAO,OAAQ,IAAI,CAAC,IAAI,MAAO,KAAK;AAAA,IAC/E;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,aAAO,CAAC,KAAK,CAAC,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI,MAAM,GAAG;AAAA,IACtE;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,aAAO,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;AAAA,IACtB;AAEA,YAAQ,KAAK,MAAM,QAAQ,KAAK;AAEhC,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,aAAO,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC;AAAA,IACxB;AAEA,YAAQ,KAAK,OAAO,SAAU,MAAM;AACnC,aAAO,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;AAAA,IACzB;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,aAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AAAA,IACtB;AAEA,YAAQ,KAAK,MAAM,SAAU,MAAM;AAClC,YAAM,MAAM,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM,GAAG,IAAI;AAC9C,YAAM,WAAW,OAAO,OAAO,OAAO,KAAK;AAE3C,YAAM,SAAS,QAAQ,SAAS,EAAE,EAAE,YAAY;AAChD,aAAO,SAAS,UAAU,OAAO,MAAM,IAAI;AAAA,IAC5C;AAEA,YAAQ,IAAI,OAAO,SAAU,KAAK;AACjC,YAAM,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK;AACzC,aAAO,CAAC,MAAM,MAAM,GAAG;AAAA,IACxB;AAAA;AAAA;;;ACt0BA;AAAA;AAAA,QAAM,cAAc;AAapB,aAAS,aAAa;AACrB,YAAM,QAAQ,CAAC;AAEf,YAAM,SAAS,OAAO,KAAK,WAAW;AAEtC,eAAS,MAAM,OAAO,QAAQ,IAAI,GAAG,IAAI,KAAK,KAAK;AAClD,cAAM,OAAO,CAAC,CAAC,IAAI;AAAA;AAAA;AAAA,UAGlB,UAAU;AAAA,UACV,QAAQ;AAAA,QACT;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAGA,aAAS,UAAU,WAAW;AAC7B,YAAM,QAAQ,WAAW;AACzB,YAAM,QAAQ,CAAC,SAAS;AAExB,YAAM,SAAS,EAAE,WAAW;AAE5B,aAAO,MAAM,QAAQ;AACpB,cAAM,UAAU,MAAM,IAAI;AAC1B,cAAM,YAAY,OAAO,KAAK,YAAY,OAAO,CAAC;AAElD,iBAAS,MAAM,UAAU,QAAQ,IAAI,GAAG,IAAI,KAAK,KAAK;AACrD,gBAAM,WAAW,UAAU,CAAC;AAC5B,gBAAM,OAAO,MAAM,QAAQ;AAE3B,cAAI,KAAK,aAAa,IAAI;AACzB,iBAAK,WAAW,MAAM,OAAO,EAAE,WAAW;AAC1C,iBAAK,SAAS;AACd,kBAAM,QAAQ,QAAQ;AAAA,UACvB;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,KAAK,MAAM,IAAI;AACvB,aAAO,SAAU,MAAM;AACtB,eAAO,GAAG,KAAK,IAAI,CAAC;AAAA,MACrB;AAAA,IACD;AAEA,aAAS,eAAe,SAAS,OAAO;AACvC,YAAM,OAAO,CAAC,MAAM,OAAO,EAAE,QAAQ,OAAO;AAC5C,UAAI,KAAK,YAAY,MAAM,OAAO,EAAE,MAAM,EAAE,OAAO;AAEnD,UAAI,MAAM,MAAM,OAAO,EAAE;AACzB,aAAO,MAAM,GAAG,EAAE,QAAQ;AACzB,aAAK,QAAQ,MAAM,GAAG,EAAE,MAAM;AAC9B,aAAK,KAAK,YAAY,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG,GAAG,EAAE;AACjD,cAAM,MAAM,GAAG,EAAE;AAAA,MAClB;AAEA,SAAG,aAAa;AAChB,aAAO;AAAA,IACR;AAEA,WAAO,UAAU,SAAU,WAAW;AACrC,YAAM,QAAQ,UAAU,SAAS;AACjC,YAAM,aAAa,CAAC;AAEpB,YAAM,SAAS,OAAO,KAAK,KAAK;AAChC,eAAS,MAAM,OAAO,QAAQ,IAAI,GAAG,IAAI,KAAK,KAAK;AAClD,cAAM,UAAU,OAAO,CAAC;AACxB,cAAM,OAAO,MAAM,OAAO;AAE1B,YAAI,KAAK,WAAW,MAAM;AAEzB;AAAA,QACD;AAEA,mBAAW,OAAO,IAAI,eAAe,SAAS,KAAK;AAAA,MACpD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;AC/FA;AAAA;AAAA,QAAM,cAAc;AACpB,QAAM,QAAQ;AAEd,QAAM,UAAU,CAAC;AAEjB,QAAM,SAAS,OAAO,KAAK,WAAW;AAEtC,aAAS,QAAQ,IAAI;AACpB,YAAM,YAAY,YAAa,MAAM;AACpC,cAAM,OAAO,KAAK,CAAC;AACnB,YAAI,SAAS,UAAa,SAAS,MAAM;AACxC,iBAAO;AAAA,QACR;AAEA,YAAI,KAAK,SAAS,GAAG;AACpB,iBAAO;AAAA,QACR;AAEA,eAAO,GAAG,IAAI;AAAA,MACf;AAGA,UAAI,gBAAgB,IAAI;AACvB,kBAAU,aAAa,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,YAAY,IAAI;AACxB,YAAM,YAAY,YAAa,MAAM;AACpC,cAAM,OAAO,KAAK,CAAC;AAEnB,YAAI,SAAS,UAAa,SAAS,MAAM;AACxC,iBAAO;AAAA,QACR;AAEA,YAAI,KAAK,SAAS,GAAG;AACpB,iBAAO;AAAA,QACR;AAEA,cAAM,SAAS,GAAG,IAAI;AAKtB,YAAI,OAAO,WAAW,UAAU;AAC/B,mBAAS,MAAM,OAAO,QAAQ,IAAI,GAAG,IAAI,KAAK,KAAK;AAClD,mBAAO,CAAC,IAAI,KAAK,MAAM,OAAO,CAAC,CAAC;AAAA,UACjC;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAGA,UAAI,gBAAgB,IAAI;AACvB,kBAAU,aAAa,GAAG;AAAA,MAC3B;AAEA,aAAO;AAAA,IACR;AAEA,WAAO,QAAQ,eAAa;AAC3B,cAAQ,SAAS,IAAI,CAAC;AAEtB,aAAO,eAAe,QAAQ,SAAS,GAAG,YAAY,EAAC,OAAO,YAAY,SAAS,EAAE,SAAQ,CAAC;AAC9F,aAAO,eAAe,QAAQ,SAAS,GAAG,UAAU,EAAC,OAAO,YAAY,SAAS,EAAE,OAAM,CAAC;AAE1F,YAAM,SAAS,MAAM,SAAS;AAC9B,YAAM,cAAc,OAAO,KAAK,MAAM;AAEtC,kBAAY,QAAQ,aAAW;AAC9B,cAAM,KAAK,OAAO,OAAO;AAEzB,gBAAQ,SAAS,EAAE,OAAO,IAAI,YAAY,EAAE;AAC5C,gBAAQ,SAAS,EAAE,OAAO,EAAE,MAAM,QAAQ,EAAE;AAAA,MAC7C,CAAC;AAAA,IACF,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;", "names": []}