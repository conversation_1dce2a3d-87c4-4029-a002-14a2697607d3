{"version": 3, "sources": ["../../../../../@storybook/react-dom-shim/dist/react-18.mjs"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom/client';\n\nvar nodes=new Map;function getIsReactActEnvironment(){return globalThis.IS_REACT_ACT_ENVIRONMENT}var WithCallback=({callback,children})=>{let once=React.useRef();return React.useLayoutEffect(()=>{once.current!==callback&&(once.current=callback,callback());},[callback]),children};typeof Promise.withResolvers>\"u\"&&(Promise.withResolvers=()=>{let resolve=null,reject=null;return {promise:new Promise((res,rej)=>{resolve=res,reject=rej;}),resolve,reject}});var renderElement=async(node,el,rootOptions)=>{let root=await getReactRoot(el,rootOptions);if(getIsReactActEnvironment()){root.render(node);return}let{promise,resolve}=Promise.withResolvers();return root.render(React.createElement(WithCallback,{callback:resolve},node)),promise},unmountElement=(el,shouldUseNewRootApi)=>{let root=nodes.get(el);root&&(root.unmount(),nodes.delete(el));},getReactRoot=async(el,rootOptions)=>{let root=nodes.get(el);return root||(root=ReactDOM.createRoot(el,rootOptions),nodes.set(el,root)),root};\n\nexport { renderElement, unmountElement };\n"], "mappings": ";;;;;;;;;;;AAAA,YAAuB;AACvB,eAA0B;AAE1B,IAAI,QAAM,oBAAI;AAAI,SAAS,2BAA0B;AAAC,SAAO,WAAW;AAAwB;AAAC,IAAI,eAAa,CAAC,EAAC,UAAS,SAAQ,MAAI;AAAC,MAAI,OAAW,aAAO;AAAE,SAAa,sBAAgB,MAAI;AAAC,SAAK,YAAU,aAAW,KAAK,UAAQ,UAAS,SAAS;AAAA,EAAG,GAAE,CAAC,QAAQ,CAAC,GAAE;AAAQ;AAAE,OAAO,QAAQ,gBAAc,QAAM,QAAQ,gBAAc,MAAI;AAAC,MAAI,UAAQ,MAAK,SAAO;AAAK,SAAO,EAAC,SAAQ,IAAI,QAAQ,CAAC,KAAI,QAAM;AAAC,cAAQ,KAAI,SAAO;AAAA,EAAI,CAAC,GAAE,SAAQ,OAAM;AAAC;AAAG,IAAI,gBAAc,OAAM,MAAK,IAAG,gBAAc;AAAC,MAAI,OAAK,MAAM,aAAa,IAAG,WAAW;AAAE,MAAG,yBAAyB,GAAE;AAAC,SAAK,OAAO,IAAI;AAAE;AAAA,EAAM;AAAC,MAAG,EAAC,SAAQ,QAAO,IAAE,QAAQ,cAAc;AAAE,SAAO,KAAK,OAAa,oBAAc,cAAa,EAAC,UAAS,QAAO,GAAE,IAAI,CAAC,GAAE;AAAO;AAArR,IAAuR,iBAAe,CAAC,IAAG,wBAAsB;AAAC,MAAI,OAAK,MAAM,IAAI,EAAE;AAAE,WAAO,KAAK,QAAQ,GAAE,MAAM,OAAO,EAAE;AAAG;AAAhY,IAAkY,eAAa,OAAM,IAAG,gBAAc;AAAC,MAAI,OAAK,MAAM,IAAI,EAAE;AAAE,SAAO,SAAO,OAAc,oBAAW,IAAG,WAAW,GAAE,MAAM,IAAI,IAAG,IAAI,IAAG;AAAI;", "names": []}