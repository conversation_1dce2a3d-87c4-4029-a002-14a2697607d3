import "./chunk-7D4SUZUM.js";

// node_modules/@storybook/addon-docs/dist/preview.mjs
var excludeTags = Object.entries(globalThis.TAGS_OPTIONS ?? {}).reduce((acc, entry) => {
  let [tag, option] = entry;
  return option.excludeFromDocsStories && (acc[tag] = true), acc;
}, {});
var parameters = { docs: { renderer: async () => {
  let { DocsRenderer } = await import("./DocsRenderer-PQXLIZUC-WREGB22G.js");
  return new DocsRenderer();
}, stories: { filter: (story) => (story.tags || []).filter((tag) => excludeTags[tag]).length === 0 && !story.parameters.docs?.disable } } };
export {
  parameters
};
//# sourceMappingURL=@storybook_addon-docs_preview.js.map
