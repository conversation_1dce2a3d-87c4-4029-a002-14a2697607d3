import {
  renderElement,
  unmountElement
} from "./chunk-LDWDFDC6.js";
import "./chunk-O4OSO6FZ.js";
import {
  AnchorMdx,
  CodeOrSourceMdx,
  Docs,
  HeadersMdx
} from "./chunk-OWCUSQUP.js";
import "./chunk-MBPMIFF4.js";
import "./chunk-CRBIROEW.js";
import "./chunk-JLBFQ2EK.js";
import "./chunk-PYKVDTIO.js";
import "./chunk-62AE6YUO.js";
import "./chunk-VUQGWFAF.js";
import "./chunk-7ZSBYTHS.js";
import "./chunk-MUGBNPQY.js";
import "./chunk-62Y44TTL.js";
import "./chunk-TTAPWIZ2.js";
import "./chunk-LHOSQLWN.js";
import {
  require_react
} from "./chunk-AZZHOME7.js";
import "./chunk-XZMMCN3X.js";
import "./chunk-ZLWVYHQP.js";
import {
  __toESM
} from "./chunk-7D4SUZUM.js";

// node_modules/@storybook/addon-docs/dist/DocsRenderer-PQXLIZUC.mjs
var import_react = __toESM(require_react(), 1);
var defaultComponents = { code: CodeOrSourceMdx, a: AnchorMdx, ...HeadersMdx };
var ErrorBoundary = class extends import_react.Component {
  constructor() {
    super(...arguments);
    this.state = { hasError: false };
  }
  static getDerivedStateFromError() {
    return { hasError: true };
  }
  componentDidCatch(err) {
    let { showException } = this.props;
    showException(err);
  }
  render() {
    let { hasError } = this.state, { children } = this.props;
    return hasError ? null : import_react.default.createElement(import_react.default.Fragment, null, children);
  }
};
var DocsRenderer = class {
  constructor() {
    this.render = async (context, docsParameter, element) => {
      let components = { ...defaultComponents, ...docsParameter?.components }, TDocs = Docs;
      return new Promise((resolve, reject) => {
        import("./@mdx-js_react.js").then(({ MDXProvider }) => renderElement(import_react.default.createElement(ErrorBoundary, { showException: reject, key: Math.random() }, import_react.default.createElement(MDXProvider, { components }, import_react.default.createElement(TDocs, { context, docsParameter }))), element)).then(() => resolve());
      });
    }, this.unmount = (element) => {
      unmountElement(element);
    };
  }
};
export {
  DocsRenderer,
  defaultComponents
};
//# sourceMappingURL=DocsRenderer-PQXLIZUC-WREGB22G.js.map
