// node_modules/@vitest/mocker/dist/chunk-registry.js
var MockerRegistry = class {
  registryByUrl = /* @__PURE__ */ new Map();
  registryById = /* @__PURE__ */ new Map();
  clear() {
    this.registryByUrl.clear();
    this.registryById.clear();
  }
  keys() {
    return this.registryByUrl.keys();
  }
  add(mock) {
    this.registryByUrl.set(mock.url, mock);
    this.registryById.set(mock.id, mock);
  }
  register(typeOrEvent, raw, id, url, factoryOrRedirect) {
    const type = typeof typeOrEvent === "object" ? typeOrEvent.type : typeOrEvent;
    if (typeof typeOrEvent === "object") {
      const event = typeOrEvent;
      if (event instanceof AutomockedModule || event instanceof AutospiedModule || event instanceof ManualMockedModule || event instanceof RedirectedModule) {
        throw new TypeError(`[vitest] Cannot register a mock that is already defined. Expected a JSON representation from \`MockedModule.toJSON\`, instead got "${event.type}". Use "registry.add()" to update a mock instead.`);
      }
      if (event.type === "automock") {
        const module = AutomockedModule.fromJSON(event);
        this.add(module);
        return module;
      } else if (event.type === "autospy") {
        const module = AutospiedModule.fromJSON(event);
        this.add(module);
        return module;
      } else if (event.type === "redirect") {
        const module = RedirectedModule.fromJSON(event);
        this.add(module);
        return module;
      } else if (event.type === "manual") {
        throw new Error(`Cannot set serialized manual mock. Define a factory function manually with \`ManualMockedModule.fromJSON()\`.`);
      } else {
        throw new Error(`Unknown mock type: ${event.type}`);
      }
    }
    if (typeof raw !== "string") {
      throw new TypeError("[vitest] Mocks require a raw string.");
    }
    if (typeof url !== "string") {
      throw new TypeError("[vitest] Mocks require a url string.");
    }
    if (typeof id !== "string") {
      throw new TypeError("[vitest] Mocks require an id string.");
    }
    if (type === "manual") {
      if (typeof factoryOrRedirect !== "function") {
        throw new TypeError("[vitest] Manual mocks require a factory function.");
      }
      const mock = new ManualMockedModule(raw, id, url, factoryOrRedirect);
      this.add(mock);
      return mock;
    } else if (type === "automock" || type === "autospy") {
      const mock = type === "automock" ? new AutomockedModule(raw, id, url) : new AutospiedModule(raw, id, url);
      this.add(mock);
      return mock;
    } else if (type === "redirect") {
      if (typeof factoryOrRedirect !== "string") {
        throw new TypeError("[vitest] Redirect mocks require a redirect string.");
      }
      const mock = new RedirectedModule(raw, id, url, factoryOrRedirect);
      this.add(mock);
      return mock;
    } else {
      throw new Error(`[vitest] Unknown mock type: ${type}`);
    }
  }
  delete(id) {
    this.registryByUrl.delete(id);
  }
  get(id) {
    return this.registryByUrl.get(id);
  }
  getById(id) {
    return this.registryById.get(id);
  }
  has(id) {
    return this.registryByUrl.has(id);
  }
};
var AutomockedModule = class {
  type = "automock";
  constructor(raw, id, url) {
    this.raw = raw;
    this.id = id;
    this.url = url;
  }
  static fromJSON(data) {
    return new AutospiedModule(data.raw, data.id, data.url);
  }
  toJSON() {
    return {
      type: this.type,
      url: this.url,
      raw: this.raw,
      id: this.id
    };
  }
};
var AutospiedModule = class _AutospiedModule {
  type = "autospy";
  constructor(raw, id, url) {
    this.raw = raw;
    this.id = id;
    this.url = url;
  }
  static fromJSON(data) {
    return new _AutospiedModule(data.raw, data.id, data.url);
  }
  toJSON() {
    return {
      type: this.type,
      url: this.url,
      id: this.id,
      raw: this.raw
    };
  }
};
var RedirectedModule = class _RedirectedModule {
  type = "redirect";
  constructor(raw, id, url, redirect) {
    this.raw = raw;
    this.id = id;
    this.url = url;
    this.redirect = redirect;
  }
  static fromJSON(data) {
    return new _RedirectedModule(data.raw, data.id, data.url, data.redirect);
  }
  toJSON() {
    return {
      type: this.type,
      url: this.url,
      raw: this.raw,
      id: this.id,
      redirect: this.redirect
    };
  }
};
var ManualMockedModule = class _ManualMockedModule {
  cache;
  type = "manual";
  constructor(raw, id, url, factory) {
    this.raw = raw;
    this.id = id;
    this.url = url;
    this.factory = factory;
  }
  async resolve() {
    if (this.cache) {
      return this.cache;
    }
    let exports;
    try {
      exports = await this.factory();
    } catch (err) {
      const vitestError = new Error('[vitest] There was an error when mocking a module. If you are using "vi.mock" factory, make sure there are no top level variables inside, since this call is hoisted to top of the file. Read more: https://vitest.dev/api/vi.html#vi-mock');
      vitestError.cause = err;
      throw vitestError;
    }
    if (exports === null || typeof exports !== "object" || Array.isArray(exports)) {
      throw new TypeError(`[vitest] vi.mock("${this.raw}", factory?: () => unknown) is not returning an object. Did you mean to return an object with a "default" key?`);
    }
    return this.cache = exports;
  }
  static fromJSON(data, factory) {
    return new _ManualMockedModule(data.raw, data.id, data.url, factory);
  }
  toJSON() {
    return {
      type: this.type,
      url: this.url,
      id: this.id,
      raw: this.raw
    };
  }
};

// node_modules/@vitest/mocker/dist/index.js
function mockObject(options, object, mockExports = {}) {
  const finalizers = new Array();
  const refs = new RefTracker();
  const define = (container, key, value) => {
    try {
      container[key] = value;
      return true;
    } catch {
      return false;
    }
  };
  const mockPropertiesOf = (container, newContainer) => {
    const containerType = getType(container);
    const isModule = containerType === "Module" || !!container.__esModule;
    for (const { key: property, descriptor } of getAllMockableProperties(container, isModule, options.globalConstructors)) {
      if (!isModule && descriptor.get) {
        try {
          Object.defineProperty(newContainer, property, descriptor);
        } catch {
        }
        continue;
      }
      if (isSpecialProp(property, containerType)) {
        continue;
      }
      const value = container[property];
      const refId = refs.getId(value);
      if (refId !== void 0) {
        finalizers.push(() => define(newContainer, property, refs.getMockedValue(refId)));
        continue;
      }
      const type = getType(value);
      if (Array.isArray(value)) {
        define(newContainer, property, []);
        continue;
      }
      const isFunction = type.includes("Function") && typeof value === "function";
      if ((!isFunction || value._isMockFunction) && type !== "Object" && type !== "Module") {
        define(newContainer, property, value);
        continue;
      }
      if (!define(newContainer, property, isFunction ? value : {})) {
        continue;
      }
      if (isFunction) {
        let mockFunction = function() {
          if (this instanceof newContainer[property]) {
            for (const { key, descriptor: descriptor2 } of getAllMockableProperties(this, false, options.globalConstructors)) {
              if (descriptor2.get) {
                continue;
              }
              const value2 = this[key];
              const type2 = getType(value2);
              const isFunction2 = type2.includes("Function") && typeof value2 === "function";
              if (isFunction2) {
                const original = this[key];
                const mock2 = spyOn(this, key).mockImplementation(original);
                const origMockReset = mock2.mockReset;
                mock2.mockRestore = mock2.mockReset = () => {
                  origMockReset.call(mock2);
                  mock2.mockImplementation(original);
                  return mock2;
                };
              }
            }
          }
        };
        if (!options.spyOn) {
          throw new Error("[@vitest/mocker] `spyOn` is not defined. This is a Vitest error. Please open a new issue with reproduction.");
        }
        const spyOn = options.spyOn;
        const mock = spyOn(newContainer, property);
        if (options.type === "automock") {
          mock.mockImplementation(mockFunction);
          const origMockReset = mock.mockReset;
          mock.mockRestore = mock.mockReset = () => {
            origMockReset.call(mock);
            mock.mockImplementation(mockFunction);
            return mock;
          };
        }
        Object.defineProperty(newContainer[property], "length", { value: 0 });
      }
      refs.track(value, newContainer[property]);
      mockPropertiesOf(value, newContainer[property]);
    }
  };
  const mockedObject = mockExports;
  mockPropertiesOf(object, mockedObject);
  for (const finalizer of finalizers) {
    finalizer();
  }
  return mockedObject;
}
var RefTracker = class {
  idMap = /* @__PURE__ */ new Map();
  mockedValueMap = /* @__PURE__ */ new Map();
  getId(value) {
    return this.idMap.get(value);
  }
  getMockedValue(id) {
    return this.mockedValueMap.get(id);
  }
  track(originalValue, mockedValue) {
    const newId = this.idMap.size;
    this.idMap.set(originalValue, newId);
    this.mockedValueMap.set(newId, mockedValue);
    return newId;
  }
};
function getType(value) {
  return Object.prototype.toString.apply(value).slice(8, -1);
}
function isSpecialProp(prop, parentType) {
  return parentType.includes("Function") && typeof prop === "string" && [
    "arguments",
    "callee",
    "caller",
    "length",
    "name"
  ].includes(prop);
}
function getAllMockableProperties(obj, isModule, constructors) {
  const { Map: Map2, Object: Object2, Function, RegExp, Array: Array2 } = constructors;
  const allProps = new Map2();
  let curr = obj;
  do {
    if (curr === Object2.prototype || curr === Function.prototype || curr === RegExp.prototype) {
      break;
    }
    collectOwnProperties(curr, (key) => {
      const descriptor = Object2.getOwnPropertyDescriptor(curr, key);
      if (descriptor) {
        allProps.set(key, {
          key,
          descriptor
        });
      }
    });
  } while (curr = Object2.getPrototypeOf(curr));
  if (isModule && !allProps.has("default") && "default" in obj) {
    const descriptor = Object2.getOwnPropertyDescriptor(obj, "default");
    if (descriptor) {
      allProps.set("default", {
        key: "default",
        descriptor
      });
    }
  }
  return Array2.from(allProps.values());
}
function collectOwnProperties(obj, collector) {
  const collect = typeof collector === "function" ? collector : (key) => collector.add(key);
  Object.getOwnPropertyNames(obj).forEach(collect);
  Object.getOwnPropertySymbols(obj).forEach(collect);
}

export {
  MockerRegistry,
  AutomockedModule,
  AutospiedModule,
  RedirectedModule,
  ManualMockedModule,
  mockObject
};
//# sourceMappingURL=chunk-VW3CGIGF.js.map
