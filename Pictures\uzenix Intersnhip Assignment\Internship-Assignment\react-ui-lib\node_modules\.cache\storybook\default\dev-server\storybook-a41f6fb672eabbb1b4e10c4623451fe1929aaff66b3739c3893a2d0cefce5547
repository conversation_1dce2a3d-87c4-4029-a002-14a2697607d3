{"key": "lastEvents", "content": {"boot": {"body": {"eventType": "boot", "eventId": "zXOifXBtI41lDvO1Ft-nj", "sessionId": "nCkezDxANJ3nTn8iw0GU_", "payload": {"eventType": "dev"}, "context": {"isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "storybookVersion": "9.1.3", "cliVersion": "9.1.3"}}, "timestamp": 1756758400758}, "init-step": {"body": {"eventType": "init-step", "eventId": "S-XMrSgDlhou5Uv_n7P6x", "sessionId": "nCkezDxANJ3nTn8iw0GU_", "metadata": {"generatedAt": 1756754414190, "userSince": 1756754396866, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm", "nodeLinker": "node_modules"}, "storybookVersionSpecifier": "9.1.3", "language": "typescript"}, "payload": {"step": "new-user-check", "newUser": true}, "context": {"isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "storybookVersion": "9.1.3", "cliVersion": "9.1.3", "anonymousId": "ebb24e5d0364d3a95504f75205ec9b4e52825c7654f0a385f502558cb5dc1b73"}}, "timestamp": 1756754415176}, "init": {"body": {"eventType": "init", "eventId": "f-tY8XHPkxeQ1qZfYKHQJ", "sessionId": "nCkezDxANJ3nTn8iw0GU_", "metadata": {"generatedAt": 1756754414190, "userSince": 1756754396866, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm", "nodeLinker": "node_modules"}, "storybookVersionSpecifier": "9.1.3", "language": "typescript"}, "payload": {"projectType": "REACT", "features": {"dev": true, "docs": true, "test": true, "onboarding": true}, "newUser": true}, "context": {"isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "storybookVersion": "9.1.3", "cliVersion": "9.1.3", "anonymousId": "ebb24e5d0364d3a95504f75205ec9b4e52825c7654f0a385f502558cb5dc1b73"}}, "timestamp": 1756754483711}, "version-update": {"body": {"eventType": "version-update", "eventId": "YB6Au-tgOTtXlcxhKPkz8", "sessionId": "nCkezDxANJ3nTn8iw0GU_", "metadata": {"generatedAt": 1756754849963, "userSince": 1756754396866, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.1.1", "@storybook/addon-vitest": "9.1.3", "vitest": "3.2.4", "@vitest/browser": "3.2.4", "playwright": "1.55.0", "@vitest/coverage-v8": "3.2.4"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm", "nodeLinker": "node_modules"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.1.3", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.1.3"}, "eslint-plugin-storybook": {"version": "9.1.3"}, "storybook": {"version": "9.1.3"}}, "addons": {"@chromatic-com/storybook": {"version": "4.1.1"}, "@storybook/addon-docs": {"version": "9.1.3"}, "@storybook/addon-onboarding": {"version": "9.1.3"}, "@storybook/addon-a11y": {"version": "9.1.3"}, "@storybook/addon-vitest": {"version": "9.1.3"}}}, "payload": {}, "context": {"isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "storybookVersion": "9.1.3", "cliVersion": "9.1.3", "anonymousId": "ebb24e5d0364d3a95504f75205ec9b4e52825c7654f0a385f502558cb5dc1b73"}}, "timestamp": 1756754853617}, "mocking": {"body": {"eventType": "mocking", "eventId": "Q6HOS3YezG2VvTpG0Uv7Q", "sessionId": "nCkezDxANJ3nTn8iw0GU_", "metadata": {"generatedAt": 1756758403188, "userSince": 1756754396866, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.1.1", "@storybook/addon-vitest": "9.1.3", "vitest": "3.2.4", "@vitest/browser": "3.2.4", "playwright": "1.55.0", "@vitest/coverage-v8": "3.2.4"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm", "nodeLinker": "node_modules"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.1.3", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.1.3"}, "eslint-plugin-storybook": {"version": "9.1.3"}, "storybook": {"version": "9.1.3"}}, "addons": {"@chromatic-com/storybook": {"version": "4.1.1"}, "@storybook/addon-docs": {"version": "9.1.3"}, "@storybook/addon-onboarding": {"version": "9.1.3"}, "@storybook/addon-a11y": {"version": "9.1.3"}, "@storybook/addon-vitest": {"version": "9.1.3"}}}, "payload": {"modulesMocked": 0, "modulesSpied": 0, "modulesManuallyMocked": 0}, "context": {"isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "storybookVersion": "9.1.3", "cliVersion": "9.1.3", "anonymousId": "ebb24e5d0364d3a95504f75205ec9b4e52825c7654f0a385f502558cb5dc1b73"}}, "timestamp": 1756758404385}, "dev": {"body": {"eventType": "dev", "eventId": "Y6vZFgWuRpW_R2IcTWfW5", "sessionId": "nCkezDxANJ3nTn8iw0GU_", "metadata": {"generatedAt": 1756758403249, "userSince": 1756754396866, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.1.1", "@storybook/addon-vitest": "9.1.3", "vitest": "3.2.4", "@vitest/browser": "3.2.4", "playwright": "1.55.0", "@vitest/coverage-v8": "3.2.4"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm", "nodeLinker": "node_modules"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.1.3", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.1.3"}, "eslint-plugin-storybook": {"version": "9.1.3"}, "storybook": {"version": "9.1.3"}}, "addons": {"@chromatic-com/storybook": {"version": "4.1.1"}, "@storybook/addon-docs": {"version": "9.1.3"}, "@storybook/addon-onboarding": {"version": "9.1.3"}, "@storybook/addon-a11y": {"version": "9.1.3"}, "@storybook/addon-vitest": {"version": "9.1.3"}}}, "payload": {"versionStatus": "cached", "storyIndex": {"storyCount": 2, "componentCount": 2, "pageStoryCount": 0, "playStoryCount": 0, "autodocsCount": 0, "mdxCount": 0, "exampleStoryCount": 8, "exampleDocsCount": 3, "onboardingStoryCount": 0, "onboardingDocsCount": 0, "svelteCsfV4Count": 0, "svelteCsfV5Count": 0, "version": 5}, "storyStats": {"factory": 0, "play": 0, "render": 0, "loaders": 0, "beforeEach": 0, "globals": 0, "tags": 0, "storyFn": 0, "mount": 0, "moduleMock": 0}}, "context": {"isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "storybookVersion": "9.1.3", "cliVersion": "9.1.3", "anonymousId": "ebb24e5d0364d3a95504f75205ec9b4e52825c7654f0a385f502558cb5dc1b73"}}, "timestamp": 1756758405064}, "addon-onboarding": {"body": {"eventType": "addon-onboarding", "eventId": "zXrkbviA_wAhSgHuYHawA", "sessionId": "nCkezDxANJ3nTn8iw0GU_", "metadata": {"generatedAt": 1756754849963, "userSince": 1756754396866, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.1.1", "@storybook/addon-vitest": "9.1.3", "vitest": "3.2.4", "@vitest/browser": "3.2.4", "playwright": "1.55.0", "@vitest/coverage-v8": "3.2.4"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm", "nodeLinker": "node_modules"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.1.3", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.1.3"}, "eslint-plugin-storybook": {"version": "9.1.3"}, "storybook": {"version": "9.1.3"}}, "addons": {"@chromatic-com/storybook": {"version": "4.1.1"}, "@storybook/addon-docs": {"version": "9.1.3"}, "@storybook/addon-onboarding": {"version": "9.1.3"}, "@storybook/addon-a11y": {"version": "9.1.3"}, "@storybook/addon-vitest": {"version": "9.1.3"}}}, "payload": {"step": "1:<PERSON><PERSON>", "addonVersion": "9.1.3"}, "context": {"isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "storybookVersion": "9.1.3", "cliVersion": "9.1.3", "anonymousId": "ebb24e5d0364d3a95504f75205ec9b4e52825c7654f0a385f502558cb5dc1b73"}}, "timestamp": 1756754871343}, "error": {"body": {"eventType": "error", "eventId": "HfmsumXYyNqWpF09L54BH", "sessionId": "nCkezDxANJ3nTn8iw0GU_", "metadata": {"generatedAt": 1756754849963, "userSince": 1756754396866, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.1.1", "@storybook/addon-vitest": "9.1.3", "vitest": "3.2.4", "@vitest/browser": "3.2.4", "playwright": "1.55.0", "@vitest/coverage-v8": "3.2.4"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm", "nodeLinker": "node_modules"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.1.3", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.1.3"}, "eslint-plugin-storybook": {"version": "9.1.3"}, "storybook": {"version": "9.1.3"}}, "addons": {"@chromatic-com/storybook": {"version": "4.1.1"}, "@storybook/addon-docs": {"version": "9.1.3"}, "@storybook/addon-onboarding": {"version": "9.1.3"}, "@storybook/addon-a11y": {"version": "9.1.3"}, "@storybook/addon-vitest": {"version": "9.1.3"}}}, "payload": {"code": 6, "name": "SB_PREVIEW_API_0006 (StoryIndexFetchError)", "category": "PREVIEW_API", "eventType": "browser", "errorHash": "902009fbe0e7b7f49bf35e52e02207721b3b37edb62996182fd72a4d39075ac6", "isErrorInstance": true}, "context": {"isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "storybookVersion": "9.1.3", "cliVersion": "9.1.3", "anonymousId": "ebb24e5d0364d3a95504f75205ec9b4e52825c7654f0a385f502558cb5dc1b73"}}, "timestamp": 1756757450840}, "canceled": {"body": {"eventType": "canceled", "eventId": "7cyFjQPGkbuxgmFDD5nqG", "sessionId": "nCkezDxANJ3nTn8iw0GU_", "metadata": {"generatedAt": 1756757488598, "userSince": 1756754396866, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.1.1", "@storybook/addon-vitest": "9.1.3", "vitest": "3.2.4", "@vitest/browser": "3.2.4", "playwright": "1.55.0", "@vitest/coverage-v8": "3.2.4"}, "hasRouterPackage": false, "packageManager": {"type": "npm", "agent": "npm", "nodeLinker": "node_modules"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.1.3", "language": "typescript", "storybookPackages": {"@storybook/react-vite": {"version": "9.1.3"}, "eslint-plugin-storybook": {"version": "9.1.3"}, "storybook": {"version": "9.1.3"}}, "addons": {"@chromatic-com/storybook": {"version": "4.1.1"}, "@storybook/addon-docs": {"version": "9.1.3"}, "@storybook/addon-onboarding": {"version": "9.1.3"}, "@storybook/addon-a11y": {"version": "9.1.3"}, "@storybook/addon-vitest": {"version": "9.1.3"}}}, "payload": {"eventType": "dev", "storyIndex": {"storyCount": 2, "componentCount": 2, "pageStoryCount": 0, "playStoryCount": 0, "autodocsCount": 0, "mdxCount": 0, "exampleStoryCount": 8, "exampleDocsCount": 3, "onboardingStoryCount": 0, "onboardingDocsCount": 0, "svelteCsfV4Count": 0, "svelteCsfV5Count": 0, "version": 5}, "storyStats": {"factory": 0, "play": 0, "render": 0, "loaders": 0, "beforeEach": 0, "globals": 0, "tags": 0, "storyFn": 0, "mount": 0, "moduleMock": 0}}, "context": {"isTTY": true, "platform": "Windows", "nodeVersion": "22.14.0", "storybookVersion": "9.1.3", "anonymousId": "ebb24e5d0364d3a95504f75205ec9b4e52825c7654f0a385f502558cb5dc1b73"}}, "timestamp": 1756757489719}}}