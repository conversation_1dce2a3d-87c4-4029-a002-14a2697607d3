import {
  DocsRenderer
} from "./chunk-ODTCVFXR.js";
import "./chunk-LDWDFDC6.js";
import "./chunk-O4OSO6FZ.js";
import "./chunk-OWCUSQUP.js";
import {
  __export
} from "./chunk-MBPMIFF4.js";
import "./chunk-CRBIROEW.js";
import "./chunk-JLBFQ2EK.js";
import "./chunk-PYKVDTIO.js";
import "./chunk-62AE6YUO.js";
import {
  rc
} from "./chunk-VUQGWFAF.js";
import "./chunk-7ZSBYTHS.js";
import "./chunk-MUGBNPQY.js";
import "./chunk-62Y44TTL.js";
import "./chunk-TTAPWIZ2.js";
import "./chunk-LHOSQLWN.js";
import "./chunk-AZZHOME7.js";
import "./chunk-XZMMCN3X.js";
import "./chunk-ZLWVYHQP.js";
import "./chunk-7D4SUZUM.js";

// node_modules/@storybook/addon-docs/dist/index.mjs
var preview_exports = {};
__export(preview_exports, { parameters: () => parameters });
var excludeTags = Object.entries(globalThis.TAGS_OPTIONS ?? {}).reduce((acc, entry) => {
  let [tag, option] = entry;
  return option.excludeFromDocsStories && (acc[tag] = true), acc;
}, {});
var parameters = { docs: { renderer: async () => {
  let { DocsRenderer: DocsRenderer2 } = await import("./DocsRenderer-3PZUHFFL-SZO4TM3R.js");
  return new DocsRenderer2();
}, stories: { filter: (story) => (story.tags || []).filter((tag) => excludeTags[tag]).length === 0 && !story.parameters.docs?.disable } } };
var index_default = () => rc(preview_exports);
export {
  DocsRenderer,
  index_default as default
};
//# sourceMappingURL=@storybook_addon-docs.js.map
